// All .d.ts automatically goes into the global scope, so there's no need for declare global.
interface Window {
  __APP_VERSION__: string
}

declare const __BASE_URL__: string

declare const __APP_VERSION__: string

declare const __BRAND_NAME__: 'DIPeak' | 'ChinaTelecom'

declare const __DISABLE_LLM_TOGGLE__: boolean

declare const __ENABLE_APP_SWITCH: boolean

declare const ASKBI_VERSION: string

declare const TIANHONG_LOGIN: boolean

declare const ASKDOC_SHOW_UPLOAD: boolean
declare const VITE_XENGINE_ORIGIN: string
