export class DeepSeekData {
  static from(data: DeepSeekData) {
    return new DeepSeekData(data)
  }
  status: 'loading' | 'success' | 'fail'
  content = ''
  trigger: 'button' | 'error' | 'deep-think'
  // 仅在浏览器环境使用
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  reader: ReadableStreamDefaultReader<Uint8Array<ArrayBufferLike>> | null

  constructor({
    status = 'loading',
    content = '',
    trigger = 'button',
    reader = null,
  }: {
    status?: DeepSeekData['status']
    content?: DeepSeekData['content']
    trigger?: DeepSeekData['trigger']
    reader?: DeepSeekData['reader']
  } = {}) {
    this.status = status
    this.content = content
    this.trigger = trigger
    this.reader = reader
  }

  setStatus(v: DeepSeekData['status']) {
    this.status = v
    return this
  }

  setContent(v: DeepSeekData['content']) {
    this.content = v
    return this
  }

  setTrigger(v: DeepSeekData['trigger']) {
    this.trigger = v
    return this
  }

  setReader(v: DeepSeekData['reader']) {
    this.reader = v
    return this
  }
}
