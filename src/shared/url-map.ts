/**
 * @description 所有产品的 URL 地址的映射表
 */

import { chatPath, XEngineHomePath } from './constants'
import { concatBaseUrl } from './common-utils'

/**
 * Ask BI 相关
 */
export const ASK_BI_BASE = ''
/** Ask BI 页面地址汇总 */
export const askBIPageUrlsWithoutBaseUrl = {
  home: `${ASK_BI_BASE}${chatPath}`,
  chatNew: `${ASK_BI_BASE}/chat/new`,
  chatDetail: (conversationId: string) => `${ASK_BI_BASE}/chat/${conversationId}`,
  scenarios: {
    manage: '/manage/scenario',
    create: '/scenario/create',
    detail: `/manage/scenario/detail`,
  },
  metricStore: {
    dimensions: {
      overview: '/metric-store/dimensions/overview',
      manage: '/metric-store/dimensions/manage',
      create: '/metric-store/dimensions/create',
    },
    measures: {
      manage: '/metric-store/measures/manage',
      create: '/metric-store/measures/create',
    },
    metrics: {
      overview: '/metric-store/metrics/overview',
      detail: (modelId: string, metricName: string) => `/metric-store/metrics/detail/${modelId}/${metricName}`,
      manage: '/metric-store/metrics/manage',
      create: '/metric-store/metrics/create',
    },
    metricTree: {
      overview: '/metric-store/metric-tree/overview',
      detail: (projectId: string, treeId: string) => `/metric-store/metric-tree/detail/${projectId}/${treeId}`,
    },
    charts: {
      list: `/metric-store/charts/list`,
      embed: (chartId: string) => `/metric-store/charts/embed/${chartId}`,
      edit: (chartId: string) => `/metric-store/charts/edit/${chartId}`,
      copy: (chartId: string) => `/metric-store/charts/copy/${chartId}`,
      detail: (chartId: string) => `/metric-store/charts/detail/${chartId}`,
    },
    document: {
      list: '/metric-store/document/list',
    },
    askHistory: {
      overview: '/metric-store/ask-history/overview',
    },
    smartReport: {
      templateManagement: '/metric-store/smart-report/template',
      reportManagement: '/metric-store/smart-report/report',
      reportGeneration: '/metric-store/smart-report/template/report-generation',
      scienceCityReportTask: '/metric-store/smart-report/science-city-report-task',
    },
    externalReport: {
      management: '/metric-store/external-report/management',
    },
  },
  manage: {
    roles: '/manage/admin/roles',
    scenarios: {
      manage: '/manage/scenario',
      create: '/scenario/create',
    },
    users: '/manage/admin/users',
    xengine: {
      dataSceneList: '/manage/operation/data-scene',
      dataSceneDetail: '/manage/operation/data-scene/detail',
      sqlQuery: '/manage/SQL-query',
      virtualTable: '/manage/data-model/virtual-table',
      businessDataModel: '/manage/data-model/business-data-model',
      catalogManager: '/manage/data-model/catalog-manager',
      materialViewList: '/manage/materialization/material-view-list',
      jobList: '/manage/materialization/job-list',
      materialViewScan: '/manage/materialization/material-view-scan',
      queryRelatedMv: '/manage/materialization/query-related-mv',
      logFileList: '/manage/operation/advance/log-file-list',
      toolsBox: '/manage/operation/advance/tools-box',
      sqlHistory: '/manage/operation/advance/sql-history',
      sqlQueryManage: '/manage/operation/advance/sql-query-manage',
    },
    permissions: '/manage/admin/permissions',
    datasources: '/manage/admin/datasources',
    systemInfo: '/manage/admin/system-info',
    testOverview: '/manage/admin/test-overview',
    executeSql: '/manage/admin/execute-sql',
    project: '/manage/project/metadata/project',
    sync: '/manage/project/metadata/sync',
    metricModel: {
      list: '/manage/metric-model/list',
    },
    externalDatasource: {
      catalogList: XEngineHomePath,
      tableList: '/manage/external-datasource/table-list',
      tableDetail: '/manage/external-datasource/table-list/table-detail',
      catalogUpdate: '/manage/external-datasource/catalog-list/catalog-update',
    },
    manageProject: {
      list: '/manage/project/list',
      detail: '/manage/project/detail',
    },
    admin: {
      user: '/manage/admin/user',
      role: '/manage/admin/role',
      group: '/manage/admin/group',
      resource: '/manage/admin/resource',
    },
  },
  dashboard: {
    info: '/dashboard/info',
    codeStatistics: '/dashboard/code-statistics',
    codeOverview: '/dashboard/code-overview',
  },
  login: `${ASK_BI_BASE}/login`,
  loginError: `${ASK_BI_BASE}/login-error`,
} as const

export const askBIPageUrls = concatBaseUrl(askBIPageUrlsWithoutBaseUrl)

/** Ask BI API 地址汇总 */
export const askBIApiUrlsWithoutBaseUrl = {
  home: `${ASK_BI_BASE}/`,
  chat: '/api/chats/chat',
  chitchat: '/api/chats/chitchat',
  resultAnalysis: '/api/chats/result-analysis',
  condense: '/api/chats/condense',
  chatNoCheck: '/api/chats/chat-nocheck',
  chatMultiScenes: '/api/chats/chatMultiScenes',
  metricQuery: '/api/chats/metric-query',
  queryMetric: '/api/chats/query-metric',
  suggestions: '/api/chats/suggestions',
  docReport: '/api/chats/docReport',
  chatProgress: '/api/chats/chat-progress',
  chatProgressCallback: '/api/chats/chat-progress-callback',
  chartInsightStream: '/api/chats/insight-streaming',
  attrInsightStream: '/api/chats/attr-analysis-insight-streaming',
  chartTooltip: '/api/charts/tooltip',
  users: {
    list: '/api/users',
    create: '/api/users/',
    detail: (userId: string) => `/api/users/${userId}`,
    update: (userId: string) => `/api/users/${userId}`,
    delete: (userId: string) => `/api/users/${userId}`,
  },

  roles: {
    list: '/api/roles',
    create: '/api/roles/',
    detail: (roleId: string) => `/api/roles/${roleId}`,
    update: (roleId: string) => `/api/roles/${roleId}`,
    delete: (roleId: string) => `/api/roles/${roleId}`,
  },
  llms: {
    all: '/api/llms/',
    listByUsername: '/api/llms/list-by-username',
    byType: (type: string) => `/api/llms/${type}`,
  },
  metrics: {
    list: `/api/metrics/list/`,
    listInScene: (sceneId: string) => `/api/metrics/list/${sceneId}`,
    listInProject: (projectId: string) => `/api/metrics/list/project/${projectId}`,
    recommendQuestion: '/api/metrics/recommend-question',
    create: '/api/metrics/',
    createFromFile: `/api/metrics/upload`,
    updateFromFile: `/api/metrics/upload`,
    update: (metricId: string) => `/api/metrics/${metricId}`,
    detail: (metricId: string) => `/api/metrics/detail/${metricId}`,
    detailWithTime: (sceneId: string, metricName: string) => `/api/metrics/detail/time/${sceneId}/${metricName}`,
    delete: (metricId: string) => `/api/metrics/${metricId}`,
    batchDelete: `/api/metrics/batch`,
    metric2sql2data: '/api/metrics/metric2sql2data',
    metric2sql2dataAttr: '/api/metrics/metric2sql2data-attr',
    trendInScene: (sceneId: string, metricName: string) => `/api/metrics/trend/${sceneId}/${metricName}`,
    deleteByScene: (sceneId: string) => `/api/metrics/scene/${sceneId}`,
    deleteByProject: (projectId: string) => `/api/metrics/project/${projectId}`,
  },
  model: {
    list: '/api/engine/v1/metricmodel/listMeta',
    meta: '/api/engine/v1/metricmodel/meta',
    updateColumnCodeValues: '/api/engine/v1/metricmodel/updateColumnCodeValues',
    updateSynonyms: '/api/engine/v1/metricmodel/updateSynonyms',
    delete: '/api/engine/v1/metricmodel/bi/delete',
    jsonUpload: '/api/engine/v1/metricmodel/bi/json',
    csvUpload: '/api/engine/v1/metricmodel/bi/csv',
    listColumnCodeValues: '/api/engine/v1/metricmodel/listColumnCodeValues',
    CSVDownloadAllColumnCodeValues: '/api/engine/v1/metricmodel/export/allColumnCodeValues',
    DownloadAllColumnCodeValues: '/api/engine/v1/metricmodel/listAllColumnCodeValues',
    batchUpdateColumnCodeValueSynonyms: '/api/engine/v1/metricmodel/import/batchUpdateColumnCodeValueSynonyms',
    updateColumnCodeValueSynonyms: '/api/engine/v1/metricmodel/updateColumnCodeValueSynonyms',
    CSVUploadTemplate: '/api/engine/v1/tool/export/template',
  },
  // scenarios: {
  //   list: '/api/operation/scenes',
  //   create: '/api/operation/scenes',
  //   delete: (id: string) => `/api/operation/scenes/${id}`,
  //   update: (id: string) => `/api/operation/scenes/${id}`,
  //   detail: (id: string) => `/api/operation/scenes/${id}`,
  // },
  tableColumn: {
    list: '/api/table-meta/column',
  },
  measure: {
    listInScene: (sceneId: string) => `/api/measures/list/${sceneId}`,
    create: '/api/measures/',
    createBatch: '/api/measures/batch',
    update: (measureId: string) => `/api/measures/${measureId}`,
    detail: (measureId: string) => `/api/measures/${measureId}`,
    delete: (measureId: string) => `/api/measures/${measureId}`,
  },
  dimension: {
    listInScene: (sceneId: string) => `/api/dimensions/list/${sceneId}`,
    create: '/api/dimensions/',
    createBatch: '/api/dimensions/batch',
    update: (dimensionId: string) => `/api/dimensions/${dimensionId}`,
    detail: (dimensionId: string) => `/api/dimensions/${dimensionId}`,
    delete: (dimensionId: string) => `/api/dimensions/${dimensionId}`,
    trend: (tableName: string, dimensionName: string) => `/api/dimensions/trend/${tableName}/${dimensionName}`,
  },
  externalReport: {
    create: '/api/external-reports/',
    createFromFile: '/api/external-reports/upload',
    updateFromFile: '/api/external-reports/upload',
    delete: (externalReportId: string) => `/api/external-reports/${externalReportId}`,
    deleteBatch: `/api/external-reports/batch`,
    update: (externalReportId: string) => `/api/external-reports/${externalReportId}`,
    list: (externalReportId: string) => `/api/external-reports/${externalReportId}`,
    baowu: '/api/external-reports/baowu-report/detail',
  },
  datasets: {
    sampleData: (sceneId: string) => `/api/datasets/sample-data/${sceneId}`,
    defaultDatasetInfo: '/api/datasets/default',
  },
  charts: {
    list: '/api/charts',
    detail: (chartId: string) => `/api/charts/${chartId}`,
    create: '/api/charts/',
    update: (chartId: string) => `/api/charts/${chartId}`,
    delete: (chartId: string) => `/api/charts/${chartId}`,
  },
  datasources: {
    list: '/api/datasources',
    detail: (datasourceId: string) => `/api/datasources/${datasourceId}`,
    create: '/api/datasources/',
    update: (datasourceId: string) => `/api/datasources/${datasourceId}`,
    delete: (datasourceId: string) => `/api/datasources/${datasourceId}`,
    testMysqlConnection: '/api/datasources/test-mysql-connection',
  },
  login: {
    login: '/api/sso/auth',
    logout: '/api/sso/logout',
    requestSignature: '/api/sso/request-signature',
    appLogin: '/api/sso/app-login',
    currentUserBasicInfo: '/api/sso/current-user-basic-info',
    tianhongLogin: '/api/sso/tianhong-login',
    checkToken: '/api/sso/check-token',
  },
  auth: {
    union: '/api/auth/union',
    login: '/api/auth/login',
    logout: '/api/auth/logout',
    userInfo: '/api/auth/user-info',
    projects: '/api/auth/projects',
    llms: '/api/auth/llms',
    metrics: '/api/auth/metrics',
    enforce: '/api/auth/enforce',
    scene: {
      list: '/api/auth/scene/list',
      rest: '/api/auth/scene',
    },
    project: {
      list: '/api/auth/project/list',
      rest: '/api/auth/project',
    },
    admin: {
      user: {
        rest: '/api/auth/admin/user',
        list: '/api/auth/admin/user/list',
      },
      role: {
        rest: '/api/auth/admin/role',
        list: '/api/auth/admin/role/list',
      },
      group: {
        rest: '/api/auth/admin/group',
        list: '/api/auth/admin/group/list',
      },
      resource: {
        rest: '/api/auth/admin/resource',
        list: '/api/auth/admin/resource/list',
        data: '/api/auth/admin/resource/data',
      },
    },
    resource: {
      rest: '/api/auth/resource',
    },
  },
  env: {
    list: '/api/env/list',
    paramsExtractUrlList: '/api/env/get-params-extract-url',
    getEnvTag: '/api/env/get-tag',
  },
  diProxyLogin: {
    namespace: '/api/ditest/get-kube-namespace',
    service: '/api/ditest/get-kube-service',
    pod: '/api/ditest/get-kube-pod',
    diDefaultProxy: '/api/ditest/get-default-proxy',
  },
  checkContainer: {
    checkContainerStatus: '/api/ditest/check-containers-status',
    checkK8sStatus: '/api/ditest/check-k8s-status',
    operationContainers: (pk: number) => `/api/ditest/operation-containers/${pk}`,
  },
  permission: {
    datasource: {
      detail: (datasourceId: string) => `/api/permission/datasource/${datasourceId}`,
      create: '/api/permission/datasource',
      delete: (permissionDatasourceId: string) => `/api/permission/datasource/${permissionDatasourceId}`,
    },
    llm: {
      detail: (llmId: string) => `/api/permission/llm/${llmId}`,
      create: '/api/permission/llm',
      delete: (permissionLlmId: string) => `/api/permission/llm/${permissionLlmId}`,
    },
    project: {
      detail: (projectId: string) => `/api/permission/project/${projectId}`,
      create: '/api/permission/project',
      delete: (permissionProjectId: string) => `/api/permission/project/${permissionProjectId}`,
    },
  },
  rolesPermission: {
    pageList: '/api/roles-permission/page-list/list',
    datasource: {
      detail: (datasourceId: string) => `/api/roles-permission/datasource/${datasourceId}`,
      create: '/api/roles-permission/datasource',
      delete: (permissionDatasourceId: string) => `/api/roles-permission/datasource/${permissionDatasourceId}`,
    },
    llm: {
      detail: (llmId: string) => `/api/roles-permission/llm/${llmId}`,
      create: '/api/roles-permission/llm',
      delete: (permissionLlmId: string) => `/api/roles-permission/llm/${permissionLlmId}`,
    },
    project: {
      detail: (projectId: string) => `/api/roles-permission/project/${projectId}`,
      create: '/api/roles-permission/project',
      delete: (permissionProjectId: string) => `/api/roles-permission/project/${permissionProjectId}`,
    },
  },
  systemInfo: {
    status: '/api/system-info/status',
    clearCache: '/api/system-info/clear-cache',
  },
  convers: {
    list: '/api/convers/list',
    upsert: `/api/convers/upsert`,
    askHistory: `/api/convers/ask-history`,
    delete: (conversationId: string) => `/api/convers/${conversationId}`,
    update: (conversationId: string) => `/api/convers/${conversationId}`,
    updateAll: (sceneId: string) => `/api/convers/update/all-conversations/${sceneId}`,
  },
  converChats: {
    listByConversationId: (conversationId: string) => `/api/conver-chats/${conversationId}`,
    askHistory: `/api/conver-chats/history-list`,
    dataFeedback: `/api/conver-chats/data-feedback`,
    updateChat: '/api/conver-chats/update-chat',
    updateDocChat: '/api/conver-chats/update-doc-chat',
  },
  semanticModel: {
    detail: (semanticModelId: string) => `/api/semantic-model/${semanticModelId}`,
    update: (semanticModelId: string) => `/api/semantic-model/${semanticModelId}`,
    delete: (semanticModelId: string) => `/api/semantic-model/${semanticModelId}`,
    measuresList: '/api/engine/v1/metricmodel/listMeasureFunction',
    create: '/api/engine/v1/metricmodel/bi',
    modelUpdate: '/api/engine/v1/metricmodel/bi/update',
    listTimePatterns: '/api/engine/v1/metricmodel/listTimePattern',
  },
  semanticMetricTree: {
    create: '/api/semantic-metric-tree/',
    list: (projectId: string) => `/api/semantic-metric-tree/${projectId}`,
    detail: (projectId: string, treeId: string) => `/api/semantic-metric-tree/${projectId}/${treeId}`,
    update: (projectId: string, treeId: string) => `/api/semantic-metric-tree/${projectId}/${treeId}`,
    delete: (projectId: string, treeId: string) => `/api/semantic-metric-tree/${projectId}/${treeId}`,
  },
  semanticProject: {
    list: '/api/semantic-project/list',
    create: '/api/semantic-project',
    update: (projectId: string) => `/api/semantic-project/${projectId}`,
    delete: (projectId: string) => `/api/semantic-project/${projectId}`,
    infoWithTableMeta: '/api/semantic-project/infoWithTableMeta',
    listAllMetrics: (projectId: string) => `/api/semantic-project/listAllMetrics/${projectId}`,
    listAllMetricsWithDisplayValue: (projectId: string) =>
      `/api/semantic-project/listAllMetricsWithDisplayValue/${projectId}`,
    metricDisplayValue: (projectId: string, metricName: string) =>
      `/api/semantic-project/metricDisplayValue/${projectId}/${metricName}`,
  },
  private: {
    executeSql: '/api/execute-sql',
    testOverview: '/api/test-tasks/overview',
    importTestData: '/api/test-tasks/import-data',
  },
  xengineDatasource: {
    add: `/api/engine/v1/datasource/add`,
    catalogList: `/api/engine/v1/datasource/catalogList`,
    tableList: `/api/engine/v1/datasource/tableList`,
    del: `/api/engine/v1/datasource/del`,
    edit: `/api/engine/v1/datasource/edit`,
    testing: `/api/engine/v1/datasource/testing`,
    tableListDetail: `/api/engine/v1/datasource/tableListDetail`,
    createVirtualTable: `/api/engine/v1/datasource/createVirtualTable`,
    getPrincipalList: `/api/engine/v1/datasource/getPrincipalList`,
    tableInfo: `/api/engine/v1/datasource/tableInfo`,
    previewData: `/api/engine/v1/datasource/previewData`,
    saveHiveDDLInFile: `/api/engine/v1/datasource/saveHiveDDLInFile`,
    getCatalogId: `/api/engine/v1/datasource/getCatalogId`,
    databaseList: `/api/engine/v1/datasource/databaseList`,
    partitions: `/api/engine/v1/datasource/partitions`,
    tableListForEditor: `/api/engine/v1/datasource/tableListForEditor`,
    relateMvs: `/api/engine/v1/datasource/relateMvs`,
    listNames: `/api/engine/v1/datasource/listNames`,
    detail: `/api/engine/v1/datasource/detail`,
    executeSql: '/api/engine/v1/sql/query',
  },
  xengine: {
    catalogList: '/api/engine/v1/catalog/list',
    databaseList: '/api/engine/v1/database/list',
    vTableDetail: '/api/engine/v1/vtable/detail',
    vTableList: '/api/engine/v1/vtable/list',
    createPTable: '/api/engine/v1/ptable',
    uploadCSV: '/api/engine/v1/csv',
    inferColumnType: '/api/engine/v1/csv/inferColumnType',
    columnType: '/api/engine/v1/ptable/columnType',
    deleteVTable: '/api/engine/v1/vtable/delete',
    publishStreamVtable: '/api/engine/v1/vtable/publishStreamVtable',
    scenario: {
      list: '/api/engine/v1/scenario/list',
    },
    metricModel: {
      list: '/api/engine/v1/metricmodel/list',
    },
    metric: {
      createRelateMv: '/api/engine/v1/metrics/createRelateMv',
    },
    VTable: {
      createBatchVT: '/api/engine/v1/vtable/createBatchVT',
    },
  },
  xengineSql: {
    queryHistory: '/api/engine/v1/sql/queryHistory',
  },
  xengineScenario: {
    getLineage: '/api/engine/v1/scenario/getLineage',
  },
  xengineVTable: {
    unionType: '/api/engine/v1/vtable/unionType',
    compareOperator: '/api/engine/v1/vtable/compareOperator',
    create: '/api/engine/v1/vtable',
    getStreamVtableModel: '/api/engine/v1/vtable/getStreamVtableModel',
  },
  project: {
    list: '/api/operation/project',
    create: '/api/operation/project',
    delete: (id: string) => `/api/operation/project/${id}`,
    update: (id: string) => `/api/operation/project/${id}`,
    detail: (id: string) => `/api/operation/project/${id}`,
  },
  operationProject: {
    getProjectList: '/api/operation/project',
  },
  ai: {
    measureDimRecommend: '/api/ai/v1/measure_dim_recommend',
  },
  reportGenerate: {
    getColumnValue: '/api/report-generate/column-value', // 获取维度码值_new
    getTemplateList: '/api/report-generate/template/list', // 模板列表
    postOutlineCreate: '/api/report-generate/outline/create', // 生成大纲阶段的保存
    dataFilterOperator: '/api/report-generate/data-filter-operator', // 获取运算符列表_new
    getColumnClassify: '/api/report-generate/column-classify', // 获取指标模型字段分类情况
    getReportList: '/api/report-generate/report/list', // 获取报告列表
    deleteReport: '/api/report-generate/report', // 删除报告
    getReportDetail: '/api/report-generate/report-detail', // 获取报告详情
    saveReport: '/api/report-generate/report/save', // 保存报告
    exportReport: '/api/report-generate/report/export', // 导出到本地
    updateSectionName: '/api/report-generate/section-name', // 段落重命名
    updateOutline: '/api/report-generate/template/outline/update', // 更新大纲接口 这个应该用来替换
    updateSection: '/api/report-generate/section/update', // 更新目标段落
    sectionConfig: '/api/report-generate/template/section-config', // 获取报告段落配置
    templateSectionConfig: '/api/report-generate/template/section-config', // 获取模版段落配置
    reportCopy: '/api/report-generate/report-copy', // 复制报告
    computeTypeList: '/api/report-generate/data-operator-compute-type', // 获取数据算子计算类型列表
    textOperatorType: '/api/report-generate/text-operator-type', // 获取文本算子类型
    dataOperatorType: '/api/report-generate/data-operator-type', // 获取算子的类型列表
    listDataOperatorColumnValue: '/api/report-generate/list-data-operator-column-value', // 获取数据算子的码值
    createOrUpdateDataOperator: '/api/report-generate/create-or-update-data-operator', // 新增或者更新段落数据算子
    createOrUpdateTextOperator: '/api/report-generate/create-or-update-text-operator', //  新增或者更新段落文本算子
    templateDetail: '/api/report-generate/template/detail', // 获取模版详情
    dataOperatorPreview: '/api/report-generate/get-data-operator-preview', // 数据算子预览
    textOperatorPreview: '/api/report-generate/get-text-operator-preview', // 文本算子预览
    timeGranularity: '/api/report-generate/time_granularity', // 分析时间粒度
    getSectionOperators: '/api/report-generate/template/section-operators', // 获取模版段落算子详情 包含文本算子和数据算子
    saveTemplate: '/api/report-generate/template/save', // 保存模板
    createTemplate: '/api/report-generate/template/create', // 新增模板
    deleteTemplate: '/api/report-generate/template/delete', // 删除模板
    getFullReport: '/api/report-generate/full-report', // 获取全文
    getScienceCityTaskList: '/api/report-generate/science-city/get-task-list', // 获取任务列表
    deleteScienceCityTask: '/api/report-generate/science-city/delete-task', // 删除 ScienceCity 任务
  },
  baowu: {
    baowuDatasets: '/api/datasets/baowu/dataset-list',
    baowuRecommendQuestion: '/api/metrics/baowu/recommend-question',
    baowuCheckUserAuth: '/api/auth/baowu/check-user-auth',
    baowuSpeechRecognition: '/api/baowu/audio/createRec',
    baowuLoginLog: '/api/auth/baowu/login-log',
    baowuTestAuth: '/api/baowu/test-auth',
    baowuTestSubCompany: '/api/baowu/test-subCompany',
  },
  pptGenerate: {
    pptTemplateList: '/api/ppt-generate/template/list', // ppt模版列表
    pptGenerate: '/api/ppt-generate/generate', // 生成ppt
  },
  elk: {
    fetchLogByTraceId: (id: string) => `/api/elk/log/${id}`,
  },
  alipaySdk: {
    init: '/api/alipaySdk/init',
    asr: '/api/alipaySdk/asr',
    tts: '/api/alipaySdk/tts',
  },
} as const

export const askBIApiUrls = concatBaseUrl(askBIApiUrlsWithoutBaseUrl)

/**
 * Ask Doc 相关
 */
const ASK_DOC_BASE = '/askdoc'
/** Ask Doc 页面地址汇总 */
export const askDocPageUrls = {
  detail: `${ASK_DOC_BASE}/docs`,
} as const
/** Ask BI API 地址汇总 */
const ASK_DOC_API_BASE = '/api/askdoc'
export const askDocApiUrlsWithoutBaseUrl = {
  askQuestion: `${ASK_DOC_API_BASE}/docs/query-document`,
  suggestions: `${ASK_DOC_API_BASE}/docs/suggestion-questions`,
  fileList: `${ASK_DOC_API_BASE}/docs/file-list`,
  fileListV2: `${ASK_DOC_API_BASE}/docs/list-documents`,
  createFolder: `${ASK_DOC_API_BASE}/docs/create-folder`,
  updateFolderName: `${ASK_DOC_API_BASE}/docs/update-folder`,
  deleteFolder: `${ASK_DOC_API_BASE}/docs/delete-folder`,
  deleteDoc: `${ASK_DOC_API_BASE}/docs/delete-doc`,
  uploadFile: `${ASK_DOC_API_BASE}/docs/upload-file`,
  getLibraryId: `${ASK_DOC_API_BASE}/docs/get-library-id`,
  deleteSceneFile: `${ASK_DOC_API_BASE}/docs/delete-scene-file`,
  editSceneFile: `${ASK_DOC_API_BASE}/docs/edit-file`,
  addSceneFile: `${ASK_DOC_API_BASE}/docs/add-scene-file`, // 关联场景文件
  docColumnClassify: `${ASK_DOC_API_BASE}/docs/column-classify`,
  docGenerateReport: `${ASK_DOC_API_BASE}/docs/generate-report`,
  getReportColumnValue: `${ASK_DOC_API_BASE}/docs/column-value`,
  getReportOpList: `${ASK_DOC_API_BASE}/docs/op-list`,
  fileUrlProxy: (filePath: string, mimeType: string) =>
    `${ASK_DOC_API_BASE}/docs/fileUrlProxy?p=${filePath}&m=${mimeType}`,
  downloadFileProxy: (filePath: string) => `${ASK_DOC_API_BASE}/docs/downloadFileProxy?p=${filePath}`,
} as const

export const askDocApiUrls = concatBaseUrl(askDocApiUrlsWithoutBaseUrl)

/** Ask Doc 调用的 AI 接口 */
export const aiAskDocFileListV2 = `/chatdoc/list-documents-v2`
export const aiDocSuggestions = `/chatdoc/suggestion-questions`
export const aiAskQuestion = `/chatdoc/query-document`
export const aiAskDocCreateFolder = `/chatdoc/create-folder`
export const aiAskDocUploadFile = `/chatdoc/upload-file`
export const aiAskDocUploadFolderName = `/chatdoc/update-folder`
export const aiAskDocDeleteFolder = `/chatdoc/folder`
export const aiAskDocDeleteDoc = `/chatdoc/doc`
export const getAskDocLibraryId = `/chatdoc/get-library-id`
export const getAskDocColumnClassify = `/chatdoc/column-classify` // 获取文档字段分类情况 / 修改文档字段分类情况
export const getAskDocGenerateReport = `/chatdoc/generate-report` // 生成报告
export const aiAskDocDeleteSceneFIle = `/chatdoc/scene-file/delete` // 删除场景文件
export const aiAskDocEditSceneFile = `/chatdoc/scene-file/edit` // 编辑场景文件
export const aiAskDocAddSceneFile = `/chatdoc/scene-file/add` // 关联场景文件
export const getReportColumnValue = `/chatdoc/column-value` // 获取维度码值
export const getReportOpList = `/chatdoc/op-list` // 获取运算符列表

// 不用校验登录的页面
export const loginWhitelist: string[] = [
  askBIApiUrlsWithoutBaseUrl.auth.login,
  askBIApiUrlsWithoutBaseUrl.auth.logout,
  askBIApiUrlsWithoutBaseUrl.login.login,
  askBIApiUrlsWithoutBaseUrl.login.logout,
  askBIApiUrlsWithoutBaseUrl.login.requestSignature,
  askBIApiUrlsWithoutBaseUrl.login.appLogin,
  askBIApiUrlsWithoutBaseUrl.login.currentUserBasicInfo,
  askBIApiUrlsWithoutBaseUrl.login.tianhongLogin,
  askBIApiUrlsWithoutBaseUrl.login.checkToken,
  askBIPageUrlsWithoutBaseUrl.login,
  askBIApiUrlsWithoutBaseUrl.metrics.listInScene(''),
  askBIApiUrlsWithoutBaseUrl.metrics.listInProject(''),
  askBIApiUrlsWithoutBaseUrl.metrics.metric2sql2data, // TODO: remove this
  askBIApiUrlsWithoutBaseUrl.metrics.metric2sql2dataAttr, // TODO: remove this
  askBIApiUrlsWithoutBaseUrl.diProxyLogin.namespace,
  askBIApiUrlsWithoutBaseUrl.diProxyLogin.service,
  askBIApiUrlsWithoutBaseUrl.diProxyLogin.pod,
  askBIApiUrlsWithoutBaseUrl.diProxyLogin.diDefaultProxy,
  askBIApiUrlsWithoutBaseUrl.env.list,
  askBIApiUrlsWithoutBaseUrl.env.getEnvTag,
  askBIApiUrlsWithoutBaseUrl.chatNoCheck,
  askBIApiUrlsWithoutBaseUrl.baowu.baowuRecommendQuestion,
  askBIApiUrlsWithoutBaseUrl.baowu.baowuDatasets,
  askBIApiUrlsWithoutBaseUrl.baowu.baowuCheckUserAuth,
  askBIApiUrlsWithoutBaseUrl.baowu.baowuLoginLog,
  askBIApiUrlsWithoutBaseUrl.baowu.baowuTestAuth,
  askBIApiUrlsWithoutBaseUrl.baowu.baowuTestSubCompany,
  askBIApiUrlsWithoutBaseUrl.chatProgress,
  askBIApiUrlsWithoutBaseUrl.chatProgressCallback,
  askBIApiUrlsWithoutBaseUrl.metricQuery,
  `${ASK_DOC_API_BASE}/docs/fileUrlProxy`,
  `${ASK_DOC_API_BASE}/docs/downloadFileProxy`,
]
