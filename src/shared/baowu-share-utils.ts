import { codeValueSplitChar } from './constants'
import { Dimension } from './metric-types'

export const BaowuReportType = {
  Simplified: 'Simplified',
  ExpenseAndAssets: 'ExpenseAndAssets',
  Overview: 'Overview',
} as const

const reportTypeMapping: { [key: string]: keyof typeof BaowuReportType } = {
  // 三张简表
  BAOWU_DEFAULT_REPORT: BaowuReportType.Simplified,
  BAOWU_ZICHAN_REPORT: BaowuReportType.Simplified,
  BAOWU_LIRUN_REPORT: BaowuReportType.Simplified,
  BAOWU_XIANJIN_REPORT: BaowuReportType.Simplified,

  // 费用支出和资产负债表
  BAOWU_FEIYONGZHICHU_REPORT: BaowuReportType.ExpenseAndAssets,
  BAOWU_ZICHANFUZHAILV_REPORT: BaowuReportType.ExpenseAndAssets,

  // 概况
  BAOWU_GAIKUANG_REPORT: BaowuReportType.Overview,
}

export const determineBaowuReportType = (reportName: string): keyof typeof BaowuReportType => {
  return reportTypeMapping[reportName] || BaowuReportType.Simplified
}

/**
 * 检查是不是同一个公司的码值
 */
export function isValidCodeValuesForOneCompany(codeValues: string[]) {
  if (codeValues.length === 0) {
    return false
  }
  const firstPrefix = codeValues[0].split('-')[0]

  return codeValues.every((i) => i.split('-')[0] === firstPrefix)
}

export const getAllCompaniesFromAllDimensions = (allDimensions: Dimension[]) => {
  const companies =
    allDimensions
      .find((item) => item.name === 'COMPANY_INNER_CODE_DES')
      ?.values?.map((item) => item.split(codeValueSplitChar)[0]) || []

  return companies
}

/**
 * 判断是否是宝武场景
 * @param metricTableName string
 * @returns boolean
 */
export function isBaoWu(metricTableName?: string | string[]) {
  if (!metricTableName) {
    const isNode = typeof process !== 'undefined' && process.versions != null && process.versions.node != null
    // doc场景下会有问题, 仅适用于宝武内部场景
    if (!isNode && window?.location?.search) {
      const search = window.location.search

      // 如果匹配不到则返回 -1,在布尔上下文中 -1 被认为是 true
      if (search.indexOf('isBaoWu=1') !== -1) {
        return true
      }
    }
    return false
  }
  if (typeof metricTableName === 'string') {
    return isBaoWuFinancial(metricTableName) || isBaoWuCost(metricTableName)
  }
  return metricTableName.some((name) => {
    return isBaoWuFinancial(name) || isBaoWuCost(name)
  })
}

export function isBaoWuFinancial(metricTableName?: string) {
  return !!metricTableName && metricTableName.indexOf('T_ADS_FACT_WSSJ_TOTAL_INDEX') > -1
}

export function isBaoWuCost(metricTableName?: string) {
  return !!metricTableName && metricTableName.indexOf('T_ADS_FACT_WSSJ_COST_INDEX') > -1
}

export function getBaoWuCodeValueList(dimensions: Dimension[]) {
  const codeValueList =
    dimensions
      .find((item) => {
        return item.name === 'COMPANY_INNER_CODE_DES'
      })
      ?.values?.map((item) => {
        return item.split(codeValueSplitChar)[0]
      }) || []
  return codeValueList
}
