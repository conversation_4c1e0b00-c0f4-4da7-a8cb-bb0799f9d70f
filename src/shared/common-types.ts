/* eslint-disable @typescript-eslint/naming-convention */
/**
 * @description 客户端和服务端共用的类型定义
 */
import { Datasource as PrismaDatasource, User, Prisma, Role, Conver } from '@prisma/client'
import { ChartThemeType, ECHARTS_CHART_TYPES, LOCAL_CHART_TYPES } from './constants'
import {
  AttrTimeDiffParams,
  Dimension,
  MetricWithPeriodOverPeriod,
  QueryParams,
  QueryParamsVerified,
  TaskType,
  TimeQueryParams,
} from './metric-types'
import { DeepSeekData } from './deepseek'
import { CalculatorData } from './calculator'
import { TryQueryToSqlData } from './try-query-to-sql'

/** 产品名称 */
export const AppNames = ['ask-bi', 'ask-doc'] as const
export type AppName = (typeof AppNames)[number]

export const AskbiNavNames = ['chat-data', 'ask-doc', 'chart-manage', 'metric-store'] as const
export type AskbiNavName = (typeof AskbiNavNames)[number]

export type ThemeType = 'light' | 'dark'

export interface BlobWithRatio extends Blob {
  ratio: number
}

/** 多任务的类型  Gpt必须作为Backend的子集 */

export interface UserBasicInfo {
  id: string
  username: string
  userRoles: RoleItem[]
}

/** chat接口 传递给后端的参数 */
export interface ChatRequestProps {
  conversationId: string | null
  chatId: string
  message: string
  sceneId: string
  projectId: string
  llmType: LlmType
  parentId: string | null
  enableFollowUpQuestion: boolean
  enableMetricExactMatch: boolean
  enableTryQueryUp: boolean
  enableSelectToastWhenEmptyData: boolean
  enableAccMetricToastWhenEmptyData: boolean
  authData?: AuthData
  currentParamsExtractApi: string
  taskId: string
  prevLlmResponse?: any
}

/**
 * 图表大类，对应 4 中数据分析场景:
 * 对比：差异、排名
 * 分布：占比、比例
 * 关系：
 * 时间序列：变化、趋势、周期
 */
export type ChartGroup = 'Comparison' | 'Distribution' | 'Relationship' | 'TimeSeries' | 'Rank' | 'Others'

/** 支持的图表类型枚举 */
export type EChartsChartType = (typeof ECHARTS_CHART_TYPES)[number]

export type LocalChartType = (typeof LOCAL_CHART_TYPES)[number]

export type ChartType = EChartsChartType | LocalChartType

/** 聊天中一条信息的状态 */
export const ChatStatus = {
  /** 已发出请求，还没有收到结果 */
  pending: 'pending',
  /** 成功 */
  success: 'success',
  /** 失败 */
  failure: 'failure',
} as const
export type ChatStatusType = keyof typeof ChatStatus

export interface AttrAnalysisItem {
  label: string
  name: string
  expr_calc: string
  expr_relation: string
  node_type: 'summable' | 'non_summable' // 可加和 不可加和
  attribution_analysis_result: {
    values: [number, number]
    abs_change: number
    rate_change: number
    contribution: number
    rank: number // 排名
  }
  children_names: Array<AttrAnalysisItem>
}

export interface AttrAnalysisDimension {
  metric: string //指标名
  dim: string //维度
  js: number //js散度
  element_pos: string[] // 正向贡献的码值 最多有三个
  element_neg: string[] // 反向贡献的码值 最多有三个
  contribution_pos: number[] // 正向贡献度
  contribution_neg: number[]
  change_pos: number[] // 波动 码值的值的波动
  change_neg: number[]
}

export interface AttrAnalysisResult {
  tree: AttrAnalysisItem
  dimension: AttrAnalysisDimension[]
  base_compare: AttrTimeDiffParams
  attr_params: {
    metric: string[]
    filter: string
  }
  metric_sql: {
    sqlBase: string
    sqlCompare: string
  }
  mockChart?: boolean
}

export interface AttrMetricAnalysisResult {
  reportPrompt: string
  metricName: string
  metricValue: { [key: string]: number }
  metricExpr?: string
  dimensionDetail: {
    name: string
    data: { [key: string]: string | number }[]
  }[]
}

// 指标交互时 传递给后端的参数

/** 大模型 text2sql 返回的类型 */
export interface LlmText2SqlReady {
  ready: true
  sql: string
  // 只有 GPT 会返回 chartTitle，其他模型没有
  chartTitle?: string
}

export interface LlmText2SqlUnready {
  ready: false
  unreadyReason: string
}

export type LlmText2Sql = LlmText2SqlReady | LlmText2SqlUnready

export interface APIResponse<T> {
  code: number
  data?: T
  msg?: string
}

export const DateTypes = [
  {
    value: 'day',
    label: '日',
  },
  {
    value: 'month',
    label: '月',
  },
  {
    value: 'quarter',
    label: '季度',
  },
  {
    value: 'year',
    label: '年',
  },
] as const

export type DateType = (typeof DateTypes)[number]['value']
export type DateTypeLabel = (typeof DateTypes)[number]['label']
// 最小的时间粒度选项：只支持日、月、年
export const TimeGranularityMinDateOptions = [
  {
    value: 'day',
    label: '日',
  },
  {
    value: 'month',
    label: '月',
  },
  {
    value: 'year',
    label: '年',
  },
] as const
export const TimeDimensionFormats = [
  'yyyyMMDD',
  'yyyyMMdd',
  'yyyy',
  'yyyyMM',
  'yyyy-MM',
  'yyyy/MM',
  'yyyy_MM_DD',
  'yyyy_MM_dd',
  'yyyy/MM/DD',
  'yyyy/MM/dd',
  'yyyy-MM-DD',
  'yyyy-MM-dd',
] as const
export const TimeDimensionTypes = ['string', 'date', 'datetime'] as const
export type TimeDimensionFormat = (typeof TimeDimensionFormats)[number]
export type TimeDimensionType = (typeof TimeDimensionTypes)[number]
export type TimeGranularityMinType = (typeof TimeGranularityMinDateOptions)[number]['value']
export const TimeGranularityOptions = [
  {
    value: 'day',
    label: '日',
  },
  {
    value: 'month',
    label: '月',
  },
  {
    value: 'quarter',
    label: '季度',
  },
  {
    value: 'year',
    label: '年',
  },
  {
    value: 'total',
    label: '全部',
  },
] as const
export type TimeGranularityType = (typeof TimeGranularityOptions)[number]['value']

export interface LlmListByUsernameResponse {
  llmList: Llm[]
  defaultLlmType: LlmType
}

/** 用户输入的会话类型，内容只有 string */
export interface AskChatItem {
  role: 'user'
  content: string
  jsonContent: string
  parentId?: string | null
  condenseContent?: string | null
}

export interface Message {
  role: 'system' | 'assistant' | 'user' | 'function'
  content: string
  extra_info?: string
}

export interface Conversation {
  id: string
  /**
   * 用户的问题列表，方便其他地方使用。
   * 注意这个 asks 和 text2SqlMessages 中的 users 并不一定完全对应
   */
  asks: string[]
  /** text2TaskType 的会话历史，ChatGPT 支持多任务使用 */
  /** TODO: 让多任务支持多轮会话 */
  // text2TaskTypeMessages: Message[]
  /**
   * 当前选中的场景，目前只支持1个，以后需要扩展成多个
   * 修改后，需要清空当前的会话历史，也就是清空 asks，text2SqlMessages
   */
  sceneId: string
  /** text2sql 的会话历史，table 变更后会被清空 */
  text2SqlMessages: Message[]
  // TODO: 添加 text2ChartGroupMessages 让图表大类支持多轮会话
}

export interface AnsChatItemBase {
  type:
    | 'chart'
    | 'text'
    | 'hello-text'
    | 'chart-insight'
    | 'sql'
    | 'metric-force-match'
    | 'query-external-report'
    | 'related-question'
    | 'not-exist-metric'
    // TODO: 上面的也改造成 TaskTypes 对应的模式，或许比较好？
    | 'data-overview'
    | 'table-list'
    | 'dimension-list'
    | 'dimension-detail'
    | 'metric-list'
    | 'metric-detail'
    | 'metric-tree'
    | 'chitchat'
    | 'llm-error'
    | 'doc-result'
    | 'doc-report'
    | 'chat-error'
    | 'calculator'
    | 'predict'
    | 'deepseek'
  originLlmResponse?: any
}

// TODO: 是否可以去掉，直接使用 Dimension 和 Metric？
export type RowsMetadataDimension = {
  type: 'dimension'
  value: Dimension
}
export type RowsMetadataMetric = {
  type: 'metric'
  value: MetricWithPeriodOverPeriod
}

export type RowsMetadata = (RowsMetadataDimension | RowsMetadataMetric)[]

// Type guard to check if a RowsMetadata item is a RowsMetadataDimension
export function isDimension(item: RowsMetadataDimension | RowsMetadataMetric): item is RowsMetadataDimension {
  return item.type === 'dimension'
}
// Type guard to check if a RowsMetadata item is a RowsMetadataMetric
export function isMetric(item: RowsMetadataDimension | RowsMetadataMetric): item is RowsMetadataMetric {
  return item.type === 'metric'
}

export type ExtraInfo = {
  // 新增的判断子公司参数↓
  groupbys_with_level: any[]
  // 追问tag
  intent_tags?: string[]
  // 是否连续提问,连续提问采用的实际问题
  sequential_first_question?: string
  // 指标评分
  metric_scores: { [key: string]: number }
}

export interface AssistantChartChatItem extends AnsChatItemBase {
  type: 'chart'
  /** 当前的图表类型，初始值为 originalChartType，用户可能修改为其他类型 */
  chartType: ChartType
  /** 指标提参的结果，包括提到的参数和多余的参数 */
  queryParamsVerified?: QueryParamsVerified
  /** GPT 推荐的图表类型 */
  originalChartType: ChartType
  /** 图表主题类型 */
  chartThemeType?: ChartThemeType
  /** 可展示的图表类型 */
  recommendChartTypes: ChartType[]
  /** 图表的标题 */
  chartTitle: string
  /** 警告的文案 */
  infoTexts: string[]
  /** 基于 sql 做的查询后的数据[可能是排序过的数据]  归因分析的数据，设置成元组类型方便做slice操作*/
  rows: OlapRow[] | [AttrAnalysisResult] | [AttrMetricAnalysisResult] // TODO: 修改，去掉 AttrAnalysisResult
  /** 源数据 immutable */
  originRows: OlapRow[] | [AttrAnalysisResult] | [AttrMetricAnalysisResult]
  /** 图表元信息 类型、是否为空/属于维度 */
  rowsMetadata: RowsMetadata
  /** 查询数据使用的 SQL */
  sql?: string
  /** 场景 id，当 sql 空时 sceneId 也为空；当 sql 非空时 sceneId 也非空 */
  sceneId: string
  /** 标识图表的taskType */
  taskType: TaskType // 这里感觉多余，肯定是指标查询才有图表
  /**是否是只有部分数据 */
  isPartialRow: boolean
  /**只有部分数据时的提示文案 */
  partialRowMsg: string
  /** 是否是预测的数据 */
  isPredict?: boolean
}

/** 为了避免 common-types 和 askdoc-types 循环引用，把这3个类型定义到 common-types */
export interface AskDocTextNode {
  fileId: string
  content: string
  fileName: string
  folderId: string
  nodeId: string
  partName: string
  page: number
  columnIndex?: boolean
}

export interface AskDocImageNode {
  fileId: string
  content: string
  url: string
  page: number
}

export interface AskDocSourceNodes {
  textNodes: AskDocTextNode[]
  imageNodes: AskDocImageNode[]
}

export interface AssistantTextChatItem extends AnsChatItemBase {
  type: 'text'
  text: string
  /** AskDoc会用到这个sourceNodes字段 */
  sourceNodes?: AskDocSourceNodes
}

export interface AssistantHelloTextChatItem extends AnsChatItemBase {
  type: 'hello-text'
  text: string
}

export interface AssistantChartInsightChatItem extends AnsChatItemBase {
  type: 'chart-insight'
  status: ChatStatusType
  respData: ChatResponse
  llmType: string
  sceneId: string
  chatId: string
  traceId: string
  text: string | null
}

export interface AssistantSqlChatItem extends AnsChatItemBase {
  type: 'sql'
  sql: string
  sceneId: string
}

export interface AssistantRelatedQuestionItem extends AnsChatItemBase {
  type: 'related-question'
  suggestionQuestions: string[]
  reason: string
}

export interface AssistantMetricForceMatchChatItem extends AnsChatItemBase {
  type: 'metric-force-match'
  metricNames: string[]
  queryParamsVerified: QueryParamsVerified
}

export interface AssistantExternalReportChatItem extends AnsChatItemBase {
  type: 'query-external-report'
  queryParamsVerified: QueryParamsVerified
  externalReports: string[]
  where: string
  timeQueryParams?: TimeQueryParams
}

export interface AssistantMetricNotExistChatItem extends AnsChatItemBase {
  type: 'not-exist-metric'
  names: string[]
}

export interface AssistantDataOverview extends AnsChatItemBase {
  type: 'data-overview'
  rows: string
  isPartialRow: boolean
  partialRowMsg: string
}

export interface AssistantTableList extends AnsChatItemBase {
  type: 'table-list'
  rows: string
  isPartialRow: boolean
  partialRowMsg: string
}

export interface AssistantDimensionList extends AnsChatItemBase {
  type: 'dimension-list'
  rows: string
  isPartialRow: boolean
  partialRowMsg: string
}

export interface AssistantDimensionDetail extends AnsChatItemBase {
  type: 'dimension-detail'
  rows: string
  isPartialRow: boolean
  partialRowMsg: string
}

export interface AssistantMetricList extends AnsChatItemBase {
  type: 'metric-list'
  rows: string
  isPartialRow: boolean
  partialRowMsg: string
}

export interface AssistantMetricDetail extends AnsChatItemBase {
  type: 'metric-detail'
  rows: string
  isPartialRow: boolean
  partialRowMsg: string
}

export interface AssistantMetricTree extends AnsChatItemBase {
  type: 'metric-tree'
  rows: string
  isPartialRow: boolean
  partialRowMsg: string
}

export interface AssistantChitchat extends AnsChatItemBase {
  type: 'chitchat'
  text: string
}

export interface AssistantLlmError extends AnsChatItemBase {
  type: 'llm-error'
  text: string
}
export interface AssistantChatError extends AnsChatItemBase {
  type: 'chat-error'
  unreadyReason?: string
  errType: ChatResponseError['errType']
  subUnreadyReason?: string
  metricNames?: string[]
  queryParamsVerified?: QueryParamsVerified
  sql?: string
  whereCompany?: string[]
  llmResponse?: any
  infoTexts?: string[]
  tryQueryToSqlData: TryQueryToSqlData
}

export interface DocResultType {
  indexType: 'Document' | 'Folder'
  content: string
  sourceNodes?: AskDocSourceNodes
}

export interface AssistantDocItemPayload {
  msg: string
  conversationId: string
  chatId: string
  llmType: string
  sceneId: string
  cancelTokenSource: AbortController
}
export interface AssistantDocItem extends AnsChatItemBase {
  type: 'doc-result'
  text?: string
  payload?: AssistantDocItemPayload
  result?: DocResultType
}

export interface AssistantCalcItem extends AnsChatItemBase {
  type: 'calculator'
  taskId?: string
  workInProgressData?: ChatProgress[]
  workInProgress?: boolean
  result?: CalculatorResultItem[]
  data: CalculatorData
}

export interface AssistantDeepSeekItem extends AnsChatItemBase {
  type: 'deepseek'
  relativeChatId: string
  data: DeepSeekData
}

export type AssistantChatItem =
  | AssistantChartChatItem
  | AssistantTextChatItem
  | AssistantHelloTextChatItem
  | AssistantChartInsightChatItem
  | AssistantSqlChatItem
  | AssistantRelatedQuestionItem
  | AssistantMetricForceMatchChatItem
  | AssistantExternalReportChatItem
  | AssistantMetricNotExistChatItem
  | AssistantDataOverview
  | AssistantTableList
  | AssistantDimensionDetail
  | AssistantDimensionList
  | AssistantMetricList
  | AssistantMetricDetail
  | AssistantMetricTree
  | AssistantChitchat
  | AssistantLlmError
  | AssistantDocReportAnalyze
  | AssistantChatError
  | AssistantCalcItem
  | AssistantDeepSeekItem

/** 机器人回复的会话类型，内容有多种：文本，图表，SQL */
export interface AnsChatItem {
  role: 'assistant'
  content: AssistantChatItem[]
  sceneId: string
  status: ChatStatusType
  ansTime?: Date
  traceId?: string
}

export type ChatItem = AskChatItem | AnsChatItem

export interface AnsDocChatItem {
  role: 'assistant'
  content: (AssistantHelloTextChatItem | AssistantDocItem | AssistantTextChatItem)[]
  status: ChatStatusType
  ansTime?: Date
}

/** 一次聊天的内容，包含问题和答案的所有信息 */
export interface Chat {
  /** use nanoid */
  id: string
  /** 问题 */
  ask: AskChatItem
  /** 回答 */
  ans: AnsChatItem[]
  /** askdoc的回答 */
  docAns: AnsDocChatItem
  /** 是否为系统提示的信息，不会发给 chatgpt，第一条系统打招呼文案为 true */
  isSystemPrompt?: boolean
  askTime: Date
  selectedSceneId: string

  /** 存放中间的常量 */
  stateMap?: {
    currentConfidenceSelection: { metricNames: string[]; codeValues: string[] } | null
  }
  /** Human Feedback: 喜欢、不喜欢、无 */
  feedback?: 'like' | 'dislike' | 'none'
  /** 反馈的时间 */
  feedbackTime?: Date
  /** feedback 不喜欢的原因： inaccurate 结果不准确，sql 错误，chart 图表错误，其他 */
  feedbackDislikeType?: 'inaccurate' | 'wrong-sql' | 'wrong-chart' | 'other'
  /** feedback 不喜欢的具体原因，feedbackDislikeType 为 other 的时候填写  */
  feedbackDislikeReason?: string
  /** 表示当前任务的ID */
  taskId?: string
}

export interface BaseChatResponse {
  ready: true
  conversationId: string
  sceneId: string
  traceId?: string
  isPartialRow?: boolean
  partialRowMsg?: string
  taskId?: string
}

export interface ChatResponseQueryMetric extends BaseChatResponse {
  taskType: 'query-metric'
  sql?: string
  chartType: ChartType
  chartTitle: string
  queryParamsVerified: QueryParamsVerified
  recommendChartTypes: ChartType[]
  rowsMetadata: RowsMetadata
  /** 警告性文案 */
  infoTexts: string[]
  /** 置信度选择原始数据 */
  confidenceOriginData?: { where: string; isWhereExactMatch: boolean; originWhere: string }
  rows: OlapRow[]
  isPartialRow: boolean
  partialRowMsg: string
  isPredict: boolean
}

export interface ChatResponsePercentage extends BaseChatResponse {
  taskType: 'percentage'
  calculator: any[]
}

export interface ChatResponsePeriodOnPeriod extends BaseChatResponse {
  taskType: 'period-on-period'
  calculator: any[]
}

export interface ChatResponseMetricExactMatch extends BaseChatResponse {
  taskType: 'metric-exact-match'
  metricNames: string[]
  queryParamsVerified: QueryParamsVerified
  confidenceOriginData?: { where: string; isWhereExactMatch: boolean; originWhere: string }
  infoTexts: string[]
}

export interface ChatResponseExternalReport extends BaseChatResponse {
  taskType: 'query-external-report'
  externalReports: string[]
  where: string
  queryParamsVerified: QueryParamsVerified
  timeQueryParams?: TimeQueryParams
  confidenceOriginData?: { where: string; isWhereExactMatch: boolean; originWhere: string }
}

export interface ChatResponseAttributionAnalysis extends BaseChatResponse {
  taskType: 'attribution-analysis'
  chartType: 'AttrAnalysis'
  chartTitle: string
  sql: string
  rows: [AttrAnalysisResult]
  isPartialRow: boolean
  partialRowMsg: string
}

export interface ChatResponseAttributionMetricAnalysis extends BaseChatResponse {
  taskType: 'attribution-metric-analysis'
  chartType: 'AttrMetricAnalysis'
  chartTitle: string
  rows: [AttrMetricAnalysisResult]
  isPartialRow: boolean
  partialRowMsg: string
}

export interface ChatResponseDataOverview extends BaseChatResponse {
  taskType: 'data-overview'
  rows: string
}

export interface ChatResponseTableList extends BaseChatResponse {
  taskType: 'table-list'
  rows: string
}

export interface ChatResponseDimensionList extends BaseChatResponse {
  taskType: 'dimension-list'
  rows: string
}

export interface ChatResponseDimensionDetail extends BaseChatResponse {
  taskType: 'dimension-detail'
  rows: string
}

export interface ChatResponseMetricList extends BaseChatResponse {
  taskType: 'metric-list'
  rows: string
}

export interface ChatResponseMetricDetail extends BaseChatResponse {
  taskType: 'metric-detail'
  rows: string
}

export interface ChatResponseMetricTree extends BaseChatResponse {
  taskType: 'metric-tree'
  rows: string
  isPartialRow: boolean
  partialRowMsg: string
}

export interface ChatResponseChitchat extends BaseChatResponse {
  taskType: 'chitchat'
}

export interface ChatResponseLlmError extends BaseChatResponse {
  taskType: 'llm-error'
  content: string
}
export interface ChatResponseError extends Omit<BaseChatResponse, 'ready'> {
  taskType: 'chat-error'
  ready: false
  errType: ChatResponseErrorStatus
  // 部分错误原因由前端写死的, 所以unreadyReason可以改为可选
  unreadyReason?: string
  subUnreadyReason?: string
  metricNames?: string[]
  whereCompany?: string[]
  queryParamsVerified?: QueryParamsVerified
  sql?: string
  originResponse?: any
  confidenceOriginData?: { where: string; isWhereExactMatch: boolean; originWhere: string }
  infoTexts?: string[]
  llmResponse?: any
  originLlmResponse?: any
}

// 错误类型(包括后端返回的及前端处理的)
export const ChatResponseErrorTypes = {
  E_UNKNOWN: 'E_UNKNOWN', // 其他错误
  LLM_ERROR: 'LLM_ERROR', // 大模型出错；大模型超时
  METRICS_NOT_EXIST: 'METRICS_NOT_EXIST', // 给定项目/场景中不存在指标-数据尚未产生
  DIMENSIONS_TREE_NOT_EXIST: 'DIMENSIONS_TREE_NOT_EXIST', // 给定项目/场景中不存在维度-数据尚未产生
  METRIC_TREE_NOT_EXIST: 'METRIC_TREE_NOT_EXIST', // 归因无法找到指标树
  ATTR_ANALYSIS_NOT_SUPPORT: 'ATTR_ANALYSIS_NOT_SUPPORT', // 不支持的归因类型
  ATTR_ANALYSIS_DATA_NOT_EXIST: 'ATTR_ANALYSIS_DATA_NOT_EXIST', // 归因无法找到相关数据
  CHIT_CHAT: 'CHIT_CHAT', // 意图识别出错
  PROJECT_NOT_EXIST: 'PROJECT_NOT_EXIST', // 项目不存在
  MODEL_NOT_EXIST: 'MODEL_NOT_EXIST', // 场景不存在
  REFRESHING_CACHE: 'REFRESHING_CACHE', // 场景创建中
  PARAM_EXTRACT_EMPTY_RESULT: 'PARAM_EXTRACT_EMPTY_RESULT', // 提参结果为空(指标未录入/不存在)
  LATEST_DATA_NOT_EXIST: 'LATEST_DATA_NOT_EXIST', // 数据正在处理中-前端使用, 本月问上月，但是还差几天没有更新
  FUTURE_DATA_NOT_EXIST: 'FUTURE_DATA_NOT_EXIST', // 数据尚未产生-前端使用, 本月问未来的数据
  NO_DATA_AUTHORITY: 'NO_DATA_AUTHORITY', // 没有数据访问权限 -- 前端判断并使用
  QUESTION_NOT_SUPPORTED: 'QUESTION_NOT_SUPPORTED', // 问题不支持
  LOOKUP_FAILED: 'LOOKUP_FAILED', // 查询指标的值失败
  CALCULATE_FAILED: 'CALCULATE_FAILED', // 查询指标的值失败
  COST_SCENE_ERROR: 'COST_SCENE_ERROR', // 成本场景错误
} as const

// 后端返回的code及错误映射
export const ChatResponseErrorMap = {
  '-1': ChatResponseErrorTypes.E_UNKNOWN,
  '-2': ChatResponseErrorTypes.LLM_ERROR,
  '-3': ChatResponseErrorTypes.METRICS_NOT_EXIST,
  '-4': ChatResponseErrorTypes.DIMENSIONS_TREE_NOT_EXIST,
  '-5': ChatResponseErrorTypes.METRIC_TREE_NOT_EXIST,
  '-6': ChatResponseErrorTypes.ATTR_ANALYSIS_NOT_SUPPORT,
  '-7': ChatResponseErrorTypes.ATTR_ANALYSIS_DATA_NOT_EXIST,
  '-8': ChatResponseErrorTypes.PROJECT_NOT_EXIST,
  '-9': ChatResponseErrorTypes.MODEL_NOT_EXIST,
  '-10': ChatResponseErrorTypes.REFRESHING_CACHE,
  '-11': ChatResponseErrorTypes.PARAM_EXTRACT_EMPTY_RESULT,
  '-12': ChatResponseErrorTypes.QUESTION_NOT_SUPPORTED,
  '-13': ChatResponseErrorTypes.LOOKUP_FAILED,
  '-14': ChatResponseErrorTypes.CALCULATE_FAILED,
}

export type ChatResponseErrorStatus = (typeof ChatResponseErrorTypes)[keyof typeof ChatResponseErrorTypes]

export const ConverChatErrorTypes = {
  NO_METRICS: 'NO_METRICS',
  NO_DATA_AUTHORITY: 'NO_DATA_AUTHORITY',
  OTHER_ERROR: 'OTHER_ERROR',
  CHIT_CHAT: 'CHIT_CHAT',
  LLM_ERROR: 'LLM_ERROR',
  // NOT_ANALYSIS: 'NOT_ANALYSIS',
  LATEST_DATA_NOT_EXIST: 'LATEST_DATA_NOT_EXIST',
  FUTURE_DATA_NOT_EXIST: 'FUTURE_DATA_NOT_EXIST',
  DATA_ERROR_FEEDBACK: 'DATA_ERROR_FEEDBACK',
  QUESTION_NOT_SUPPORTED: 'QUESTION_NOT_SUPPORTED',
  LOOKUP_FAILED: 'LOOKUP_FAILED', // 查询指标的值失败
  CALCULATE_FAILED: 'CALCULATE_FAILED', // 查询指标的值失败
} as const

export const ConverChatErrorTypesMap = {
  [ConverChatErrorTypes.NO_METRICS]: '指标未录入',
  [ConverChatErrorTypes.NO_DATA_AUTHORITY]: '没有数据权限',
  [ConverChatErrorTypes.OTHER_ERROR]: '其他错误',
  [ConverChatErrorTypes.CHIT_CHAT]: '非数据分析问题',
  [ConverChatErrorTypes.LLM_ERROR]: '大模型网络断开',
  // NOT_ANALYSIS: '非数据分析类问题',
  [ConverChatErrorTypes.LATEST_DATA_NOT_EXIST]: '时间段下无数据-暂未更新',
  [ConverChatErrorTypes.FUTURE_DATA_NOT_EXIST]: '时间段下无数据-未来时间',
  [ConverChatErrorTypes.DATA_ERROR_FEEDBACK]: '反馈数据问题',
  [ConverChatErrorTypes.QUESTION_NOT_SUPPORTED]: '问题类型不支持',
} as const

export const AnalyzeReportTypes = {
  analyzing: 'analyzing', // 解析中
  analyzeSuccess: 'analyzeSuccess', // 解析成功
  analyzeFail: 'analyzeFail', // 解析失败
  reportGenerating: 'reportGenerating', // 报告生成中
  reportGeneratedSuccess: 'reportGeneratedSuccess', // 报告生成成功
  reportGenerated: 'reportGenerated', // 报告生成成功 显示报告
  reportGeneratedFailed: 'reportGeneratedFailed', // 报告生成失败
} as const

export type AnalyzeReportStatus = keyof typeof AnalyzeReportTypes

export interface DragTaskItem {
  code: string
  name: string
}

export interface DragTableColumn {
  name: string
  items: DragTaskItem[]
}
// 生成报告模块中的字段修正拖拽
export interface FieldCorrectionColumns {
  timeDimensionsColumn: DragTableColumn
  dimensionsColumn: DragTableColumn
  metricsColumn: DragTableColumn
}

// 生成报告中的参数
export interface DocReportParamsType {
  fileId: string
  chatId: string
  userIntent: string
  focusDimensions: string[]
  focusMetrics: string[]
  timeDimension: string
  timeRangeEnd: string
  timeRangeStart: string
  timeRange: number | null
  isRegenerateReportAgainTag: boolean
}

export interface AssistantDocResultType {
  content: string
  indexType: string
  sourceNodes: AskDocSourceNodes
}

export interface AssistantDocReportAnalyze extends AnsChatItemBase {
  type: 'doc-report'
  status: AnalyzeReportStatus // 报告生成状态
  rows: any
}

export interface ChatResponseDocReport extends BaseChatResponse {
  taskType: 'doc-report'
  rows: any
  isReportGenerated?: boolean
}

export interface ChatResponseDocResult extends BaseChatResponse {
  taskType: 'doc-result'
  content: DocResultType
}

export type ChatResponse =
  | ChatResponseQueryMetric
  | ChatResponseMetricExactMatch
  | ChatResponseExternalReport
  | ChatResponseAttributionAnalysis
  | ChatResponseAttributionMetricAnalysis
  | ChatResponseDataOverview
  | ChatResponseTableList
  | ChatResponseDimensionList
  | ChatResponseDimensionDetail
  | ChatResponseMetricList
  | ChatResponseMetricDetail
  | ChatResponseMetricTree
  | ChatResponseChitchat
  | ChatResponseLlmError
  | ChatResponseDocReport
  | ChatResponseError
  | ChatResponsePercentage
  | ChatResponsePeriodOnPeriod

/** 保存图表的响应格式 */
export interface ReadyChartResponse extends ChatResponseQueryMetric {
  id: string
  username: string
  ask: string
  queryParams: QueryParams
  originRows: OlapRow[] | [AttrAnalysisResult]
  chartThemeType: ChartThemeType
  llmType: LlmType
  createdAt: Date
  updatedAt: Date
  errorMessage?: string
}

export type DatasetDatum = {
  projectId: string
  projectName: string
  sceneId: string
  sceneLabel: string
  tableName: string
  enableFollowUpQuestion: boolean
  enableMetricExactMatch: boolean
  enableTryQueryUp: boolean
  enableSelectToastWhenEmptyData: boolean
  enableAccMetricToastWhenEmptyData: boolean
}

export interface TableDatum {
  /** 对应的数据库名字，这里做一个数据冗余 */
  databaseName: string
  tableName: string
  /** columns 支持异步加载，所以可以为空 */
  columns?: ColumnDatum[]
}

export interface ColumnDatum {
  /** 对应的数据库名和表名，数据冗余方便使用 */
  databaseName: string | null
  tableName: string | null
  columnName: string | null
  columnType: string | null
  columnComment: string | null
}

export type ChartCreateInput = Prisma.ChartCreateInput

export type LlmTypeGPT =
  | 'gpt-3.5-turbo'
  | 'gpt-4-turbo-preview'
  | 'azure-gpt'
  | 'zhipu-glm-4'
  | 'zhipu-chatglm3_32b'
  | 'baichuan2'
  | 'yi_1.5_34b'
  | 'yi_1.5_34b_16k'
  | 'chat_law_7b'
  | 'pre-glm-4-9b'
  | 'deepseek-14b'

/** 图表推荐使用纯规则实现 */
export type MockChartRecommendType = 'chart-type-use-rule'

export type LlmType = LlmTypeGPT | 'vllm-mixtral-8x7b-chat' | MockChartRecommendType

export interface Llm {
  type: LlmType
  id: LlmType
  name: string
  abbrName: string
  tokenLimit: number
  logo: string
  /** 是否禁用模型 */
  disable: boolean
}

/** 导出文件类型 */
export const ExportFileTypes = {
  PNG: 'PNG',
  CSV: 'CSV',
  Excel: 'Excel',
} as const

export type ExportFileType = keyof typeof ExportFileTypes

/* ChatLog 日志的类型，对应包括 src/server/ai/prompts.ts 中的函数名 + 一些外部大模型的直接调用 */
export const ChatLogTypes = {
  nl2Sql: 'nl2Sql',
  chartInsight: 'chartInsight',
} as const
export type ChatLogType = keyof typeof ChatLogTypes

/** Winston日志级别，项目中仅用info和error */
export const WinstonLogLevels = {
  error: 'error', // 0
  warn: 'warn', // 1
  info: 'info', // 2
  http: 'http', // 3
  verbose: 'verbose', // 4 用于提供详细的信息，通常用于调试和诊断目的
  debug: 'debug', // 5 用于提供详细的调试信息，通常用于调试应用程序中的问题。
  silly: 'silly', // 6 用于提供冗长或无关紧要的信息，通常用于开发阶段的详细日志记录。
} as const
export type WinstonLogLevel = keyof typeof WinstonLogLevels

export type Datasource = PrismaDatasource

export type SystemInfoResponse = {
  startTime: string
}

export type RoleItem = {
  roleId: string
  role: Role
}

export type RoleArrayType = Omit<User, 'userRoles'> & {
  userRoles: RoleItem[]
}

export type UserPermissionDatasource = { id: string; username: string; datasourceId: string }
export type UserPermissionLlmType = { id: string; username: string; llmType: string }
export type UserPermissionProject = { id: string; username: string; semanticProjectId: string }

export type FileMimeTypeInfo =
  | 'text/html'
  | 'application/pdf'
  | 'application/msword'
  | 'application/vnd.ms-excel'
  | 'text/csv'
  | 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  | 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  | 'application/vnd.openxmlformats-officedocument.presentationml.presentation'

// conver表返回给前端的每一个Conversation元素类型
export type ConverWithDataset = Conver & {
  dataset: DatasetDatum
}

/** 后端返回的一行数据，和 RowDataPacket 区别是，OlapRow 的 key 只能为 string，不支持 number */
export interface OlapRow {
  constructor: {
    name: 'OlapRow'
  }
  [column: string]: any
}

export interface MenuItem {
  key: string
  label: React.ReactNode
  path?: string
  icon?: React.ReactNode
  children?: MenuItem[]
  type?: 'group' // 当type为group时，会作为分组处理
}

export type BotFeature = 'BI' | 'Doc' | 'Report'

export interface AccessPathsType {
  path: string
  allowed: boolean
}
export type CatalogType = {
  id: number
  name: string
  type: string
}
export type DatabaseType = {
  catalogName: string
  catalogType: string
  id: number
  name: string
}

/**提问输入框json格式 */
export type JsonContentItem = {
  type?: string
  'data-type'?: string
  'data-content'?: string
  'data-id'?: string
  'data-name'?: string
  children?: JsonContentItem[]
  [key: string]: string | JsonContentItem[] | number | undefined
}

export enum STRUCTURED_MESSAGE_DATA_TYPE {
  METRIC = 'metric',
  DIMENSION = 'dimension',
  SUB_DIMENSION = 'sub-dimension',
  TEXT = 'text',
}

export type MessageInputEditorRef = {
  setHtml: (data: JsonContentItem[] | string) => void
  updateCursorPosition?: () => void
}
export interface BrandInfoType {
  appId: string
  logo: string
  chatUserIcon: string
  companyName: string
  brandName: string
  // llm名字 用于替换Dipeak
  llmName?: string
  // 头部信息配置，false代表不展示，undef代表默认展示
  header?:
    | boolean
    | {
        logo?: boolean
        tab?: boolean
        userInfo?: boolean
        themeToggle?: boolean
      }
}

export interface UserInfoType {
  admin: boolean
  token: string
  email: string
  pageAccessResultList: string[] | null
  groups: string[]
  register: boolean
  username: string
  lastLoginAt: string
  jwtToken: string
}

export interface UserInfo {
  id: string
  username: string
  groups: { id: string; groupName: string }[]
}

// 统一的权限数据类型, 方便后续扩展其他的
export type AuthData = BaoWuAuthData

export type BaoWuAuthData = {
  username?: string
  userId?: string
  timestamp?: string
  appId?: string
  token?: string
}

export interface QueryState {
  username?: string
  password?: string
  proxy?: string
  appid?: string
  enableOnlyChat: boolean
  enableAutoLogin: boolean
  enableReloadQueryState: boolean
  hideHeader?: boolean
}

export interface ReportOutlineItemType {
  allowChildren: boolean
  children: ReportOutlineItemType[]
  content: string
  dependsOn: number[]
  id: number
  maxChildrenCount: number
  title: string
}

export interface ReportTemplateType {
  createAt: string
  createUser: string
  id: number
  name: string
  outline: ReportOutlineItemType[]
  thumbnailPath: string
}

export interface ReportListType {
  updateAt: string
  createAt: string
  modelName: string
  reportIntention: string
  templateId: number
  reportTitle: string
  creator: string
  sceneId: string
  reportId: number
  status: string
}

export interface DataFilterParams {
  columnName: string
  columnCode: string
  operator: string
  values: string[]
}

export interface dataTimeParams {
  timeColumn: string
  timeRangeStart: string
  timeRangeEnd: string
}

// 报告详情
export interface ReportDetailType {
  outlineNodes: ReportOutlineItemType[]
  dataFilterParams: DataFilterParams[]
  dataTimeParams: dataTimeParams
  reportInfo: ReportListType
}

export interface OutlineItemType {
  id: number
  key: string
  title: string
  content: string
  allowChildren: boolean
  maxChildrenCount: number
  dependsOn: number[]
  children: OutlineItemType[]
}

export interface DataOpType {
  computeType: string
  dataDescTemplate: string
  dataFilter: string[]
  dataOpId: number
  groupBy: string
  metric: string
  name: string
  operator: string
  operatorDesc: string
  outputDataSectionParams: string[]
  outputLimit: number
  outputOrderBy: string
  reportId: number
  sectionId: number
  timeGranularity: string
  timeRangeEnd: string
  timeRangeStart: string
}

export interface TextOpType {
  name: string
  prompt: string
  reportId: number
  sectionId: number
  textOpId: number
}

export interface SectionConfigType {
  dataOp: DataOperatorType[]
  maxWordLen: number
  minWordLen: number
  reportId: number
  sectionId: number
  sectionIntention: string
  textOp: TextOperatorType[]
}

export type ConcatString<T, S extends string = ''> = T extends string
  ? `${S}${T}`
  : T extends (...args: infer P) => infer R
    ? (...args: P) => ConcatString<R, S>
    : T extends object
      ? {
          [key in keyof T]: ConcatString<T[key], S>
        }
      : T

export type ConcatBaseUrlReturnType<T> = ConcatString<T, '$BASE_URL'>

export interface TextOperatorType {
  name: string
  prompt: string
  templateId: number
  sectionId: number
  textOpId: number
  type: string
  result: string
  inputDataOpIds: number[]
  inputSectionIds: number[]
}

export interface DataFilterType {
  columnName: string
  columnCode: string
  operator: string
  filterType: number
  values: string[] | string
}

export interface DataOperatorType {
  computeType: string
  dataDescTemplate: string
  dataFilter: DataFilterType[]
  dataOpId: number
  groupBy: string
  metric: string
  name: string
  result: string
  operator: string
  operatorDesc: string
  outputDataSectionParams: string[]
  outputLimit: number
  outputOrderBy: string
  enumOrder: string
  segmentationOptions: string
  templateId: number
  sectionId: number
  timeGranularity: string
  timeRangeEnd: string
  timeRangeStart: string
  timeColumn: string
}

export type ElkModuleType =
  | 'params_extract'
  | 'project_params_extract'
  | 'chat-insight-streaming'
  | 'chat-metrics'
  | 'metric-to-sql-to-data'
  | 'sql-to-data'
  | 'express_app_middleware'
  | 'docs-generate-report'
  | 'docs-query-document'
  | 'docs-upload-file'

export interface ElkResponse {
  timestamp: string
  user_id: string
  request_id: string
  host: string
  service_type: 'web_service'
  duration: number
  result_code: number
  module_type: ElkModuleType
  input: any
  output: any
  debug: any
  url: string
  semantic_project_id?: string
  semantic_scene_id?: string
  cluster_id?: string
}

export interface ChatProgress {
  type: 'calculator'
  askbi: boolean
  askdoc: boolean
  nl2intent: number
  nl2intent_msg: string // { "intent": "归因" }
  nl2time_query: number
  nl2time_query_msg: string
  nl2attr_params: number
  nl2attr_params_msg: string
  nl2time_attr: number
  nl2time_attr_msg: string
  nl2meta: number
  nl2meta_msg: string
  nl2metric_group_bys: number
  nl2metric_group_bys_msg: string // { "groupBys": ["value"], "notExistGroupBys": [] }
  nl2metric_where: number
  nl2metric_where_msg: string
  nl2metric_metrics: number
  nl2metric_metrics_msg: string
  nl2metirc_order_bys: number
  nl2metirc_order_bys_msg: string
  nl2calculator_get_expression_msg: string
  nl2calculator_steps_list: [number, CalculatorResultItem['type'], string][]
  nl2agent_build_msg?: string
  nl2agent_steps_list?: [number, string][]
  nl2agent_delta_builds?: [number, string, string][]
  close: number
}

export interface CalculatorResultItemLookupData {
  type: 'lookup_data'
  key: string | undefined
  result: {
    errType?: ChatResponseErrorStatus
    message?: string
    metric_name: string
    metric_value: string | number
    query_metric: QueryParams
    question: string
    row: Record<string, string>
  }
}

export interface CalculatorResultItemCalculator {
  type: 'calculator'
  key: string | undefined
  result: {
    expression: string
    expression_result: number
    expression_to_calculate: string
  }
}

export type CalculatorResultItem = CalculatorResultItemLookupData | CalculatorResultItemCalculator
