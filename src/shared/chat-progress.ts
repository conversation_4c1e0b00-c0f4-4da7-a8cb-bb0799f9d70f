import { QueryParams, TimeQueryParams } from './metric-types'

/* eslint-disable @typescript-eslint/naming-convention */
export type ToolType =
  | 'lookup_data'
  | 'select'
  | 'filter'
  | 'join'
  | 'orderby'
  | 'auto_join'
  | 'calculator'
  | 'time_range'
  | 'time_series_service'

export interface PlanNode {
  data: {
    table: Record<string, any>[]
    schema: {
      column_name: string
      column_type: 'metric' | 'dimension' | 'calculate' | 'unknown' | (string & {})
      metric_name?: string
      dimension_name?: string
      expression?: string
      original_name?: string
      original_param_key?: string
    }[]
  }
  is_leaf: boolean
  meta?: {
    query_metric: QueryParams
    time_range: TimeQueryParams
  }
  param_key: string
  parents: string[]
  tool: ToolType
  tool_params: string[]
}
