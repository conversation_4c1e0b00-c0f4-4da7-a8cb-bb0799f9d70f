import axios from 'axios'
import { TOKEN_BI, TOKEN_RANGER, U_TOKEN_RANGER } from './constants'

const instance = axios.create({})

instance.interceptors.request.use(function (config) {
  const token = localStorage.getItem(TOKEN_BI)
  const rangerToken = localStorage.getItem(TOKEN_RANGER)
  const rangerUToken = localStorage.getItem(U_TOKEN_RANGER)
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`
  }
  if (rangerToken) {
    config.headers[TOKEN_RANGER] = rangerToken
  }
  if (rangerUToken) {
    config.headers[U_TOKEN_RANGER] = rangerUToken
  }
  return config
})

instance.interceptors.response.use(
  function (response) {
    if (response.headers['content-type'] === 'application/octet-stream' || response.data?.code == null) {
      return response
    }
    if (response.data.code === 0) {
      return response.data.data
    }

    return Promise.reject(response.data)
  },
  function (error) {
    return Promise.reject(error)
  },
)

export default instance
