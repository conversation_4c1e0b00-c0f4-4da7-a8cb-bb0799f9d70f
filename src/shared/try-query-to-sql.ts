import { AnsChatItem } from './common-types'
import { QueryParamsVerified } from './metric-types'

export class TryQueryToSqlData {
  queryParamsVerified: QueryParamsVerified | null | undefined
  ansChatItemList: AnsChatItem[] = []
  constructor({
    ansChatItemList = [],
    queryParamsVerified = null,
  }: {
    ansChatItemList?: TryQueryToSqlData['ansChatItemList']
    queryParamsVerified?: TryQueryToSqlData['queryParamsVerified']
  } = {}) {
    this.ansChatItemList = ansChatItemList
    this.queryParamsVerified = queryParamsVerified
  }
  setAnsChatItemList(v: TryQueryToSqlData['ansChatItemList']) {
    this.ansChatItemList = v
    return this
  }
  setQueryParamsVerified(v: TryQueryToSqlData['queryParamsVerified']) {
    this.queryParamsVerified = v
    return this
  }
}
