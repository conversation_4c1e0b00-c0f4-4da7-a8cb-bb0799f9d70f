import { z } from 'zod'

export function extractInfoFromEnforcer(encodedData: string) {
  const [type, id] = encodedData.split(':')
  return { type, id }
}

export const resourceTypeList = [
  { label: '项目', value: 'project' },
  { label: '场景', value: 'scene' },
  { label: 'LLM', value: 'llm' },
  { label: '页面', value: 'page' },
] as const

export type ResourceTypes = (typeof resourceTypeList)[number]['value']

export const resourceTypeMap = resourceTypeList.reduce(
  (o, cur) => {
    o[cur.value] = cur
    return o
  },
  {} as Record<ResourceTypes, (typeof resourceTypeList)[number]>,
)

export const ruleTypeList = [
  { label: '用户', value: 'user' },
  { label: '角色', value: 'role' },
  // { label: '用户组', value: 'group' },
] as const

export type RuleTypes = (typeof ruleTypeList)[number]['value']

export const ruleTypeMap = ruleTypeList.reduce(
  (o, cur) => {
    o[cur.value] = cur
    return o
  },
  {} as Record<RuleTypes, (typeof ruleTypeList)[number]>,
)

export const actionOptions = [
  { label: '读', value: 'read' },
  { label: '写', value: 'write' },
] as const

export type ActionTypes = (typeof actionOptions)[number]['value']

export const actionTypeMap = actionOptions.reduce(
  (o, cur) => {
    o[cur.value] = cur
    return o
  },
  {} as Record<ActionTypes, (typeof actionOptions)[number]>,
)

export const SPLIT_CHAR = ':'

export function contactData(type: string, id: string) {
  return `${type}${SPLIT_CHAR}${id}`
}

export function contactUserData(id: string) {
  return contactData('user', id)
}

export function encodeResourceRule(type: ResourceTypes, typeData: any): string[] {
  if (Array.isArray(typeData)) {
    return typeData.map((v) => encodeResourceRule(type, v)).flat()
  }
  switch (type) {
    case 'page':
    case 'project':
    case 'llm':
    case 'scene':
      return [contactData(type, typeData)]
    default:
      return []
  }
}

export const AuthAdminUserListPostRequestSchema = z
  .object({
    pageSize: z.coerce.number().min(1).readonly(),
    current: z.coerce.number().min(1).readonly(),
    name: z.string().optional().readonly(),
  })
  .readonly()

export type AuthAdminUserListPostRequest = z.infer<typeof AuthAdminUserListPostRequestSchema>
