/* eslint-disable */
// @ts-nocheck
import { ScaledPosition } from 'src/client/pages/AskDoc/DocDetail/PDFView/components/types'
import {
  AskDocImageNode,
  AskDocSourceNodes,
  AskDocTextNode,
  AssistantTextChatItem,
  Chat,
  Message,
} from './common-types'

/** 文档 embed 的状态 Pending异常 Ready解析中 Done解析完成 Fail失败 */
export type DocEmbedStatus = 'Pending' | 'Ready' | 'Done' | 'Fail'

export type AskDocQuestionsIndexType = 'Document' | 'Folder'

export const AskDocQuestions = {
  question: 'question',
  explain: 'explain',
  rewrite: 'rewrite',
} as const

export type AskDocQuestionsType = keyof typeof AskDocQuestions

export interface AskDocMessage extends Message {
  type: AskDocQuestionsType
}

interface AskDocAnsChatItem {
  role: 'assistant'
  /** ask doc 只支持 text 类型的 ans，不同于 ask bi */
  content: AssistantTextChatItem[]
}

export interface AskDocChat extends Chat {
  /** 只有 ask doc 需要 */
  askDocQuestionsType: AskDocQuestionsType
  ans: AskDocAnsChatItem
}

export type TextNode = AskDocTextNode

export type ImageNode = AskDocImageNode

export type SourceNodes = AskDocSourceNodes

export interface AskDocQuestionResponse {
  type: AskDocQuestionsType
  content: string
  sourceNodes: SourceNodes
}

export type Folder = {
  id: string
  parentFolderId: string
  name: string
  createdTime: string
  updatedTime: string
  folders: Folder[]
  files: Doc[]
}

export type FileSourceType = 'file_manager' | 'user_upload'

export type Doc = {
  id: string
  folderId: string
  name: string
  createdTime: string
  updatedTime: string
  mimeType: string
  size: string
  thumbnailUrl: string
  sourceUrl: string
  creator: string
  sceneId: string
  source: FileSourceType
  fileStatus: DocEmbedStatus
  sceneIdList: string[]
}

interface FolderListData {
  folders: Folder[]
  files: Doc[]
}

export type FolderListResponse = FolderListData

// 回答问题时点击参考信息的参数类型 --旧的暂时不用
export interface ChatAnswerLinkType {
  folderId?: string
  docId?: string
  hrefPage?: number
  highlight?: string
}

// 回答问题时点击参考信息的参数类型
export interface ChatNewAnswerLinkType {
  content: string
  fileId: string
  fileName: string
  page: number
  folderId: string
  partName: string
}

export interface PdfHighlightType {
  content?: {
    text?: string
  }
  id?: string
  position?: ScaledPosition
}

export interface DocumentSearchType {
  fileName?: string
  fileType?: string
  sceneId?: string
}
