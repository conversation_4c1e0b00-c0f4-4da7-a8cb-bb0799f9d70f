import { describe, it, expect } from 'vitest'
import { formatNumber, isValidFormatTemplate, padZero, formatNumberWithChineseUnit } from './common-utils'

describe('common-utils 文件下的 padZero', () => {
  it('padZero 可以处理日期的补0', () => {
    expect(pad<PERSON>ero(1, 2)).toEqual('01')
    expect(pad<PERSON>ero(31, 2)).toEqual('31')
    expect(pad<PERSON>ero(131, 2)).toEqual('131')
  })
})

describe('formatNumberWithChineseUnit Can add chinese unit to number', () => {
  it('Can add chinese unit to number tests', () => {
    expect(formatNumberWithChineseUnit({ originNum: 12345 })).toBe('1.23万')
    expect(formatNumberWithChineseUnit({ originNum: 1234567 })).toBe('123.46万')
    expect(formatNumberWithChineseUnit({ originNum: 12345678 })).toBe('1234.57万')
    expect(formatNumberWithChineseUnit({ originNum: 1234567890 })).toBe('12.35亿')
  })
  it('Can add chinese unit to number With decimals tests', () => {
    expect(formatNumberWithChineseUnit({ originNum: 12345, decimals: 3 })).toBe('1.235万')
    expect(formatNumberWithChineseUnit({ originNum: 1234567, decimals: 3 })).toBe('123.457万')
    expect(formatNumberWithChineseUnit({ originNum: 12345678, decimals: 3 })).toBe('1234.568万')
    expect(formatNumberWithChineseUnit({ originNum: 1234567890, decimals: 3 })).toBe('12.346亿')
  })
  it('No chinese unit when suffix is "% "', () => {
    expect(formatNumberWithChineseUnit({ originNum: 123456.789, decimals: 2, suffix: '%' })).toBe('12.35万%')
    expect(formatNumberWithChineseUnit({ originNum: 123456.789, decimals: 2, suffix: '% ' })).toBe('123456.79% ')
  })
  it('formatNumberWithChineseUnit Corner Cases', () => {
    expect(formatNumberWithChineseUnit({ originNum: NaN })).toBe('NaN')
    expect(formatNumberWithChineseUnit({ originNum: '1234567890' as unknown as number })).toBe('1234567890')
    expect(formatNumberWithChineseUnit({ originNum: 'JSON.stringify' as unknown as number })).toBe('JSON.stringify')
  })
})

describe('isValidFormatTemplate will check format template', () => {
  it('isValidFormatTemplate will check format template', () => {
    expect(isValidFormatTemplate('$,.2f')).toBe(true)
    expect(isValidFormatTemplate('%')).toBe(false)
    expect(isValidFormatTemplate('.2f%')).toBe(true)
    expect(isValidFormatTemplate(',.0')).toBe(false)
    expect(isValidFormatTemplate(',.0f')).toBe(true)
    expect(isValidFormatTemplate('.3f')).toBe(true)
    expect(isValidFormatTemplate(',3f')).toBe(false)
    expect(isValidFormatTemplate(',.2')).toBe(false)
  })
})

describe('formatNumber will format number', () => {
  it('formatNumber prefix', () => {
    expect(formatNumber(12345.6789, 'RMB,.2f')).toBe('RMB1.23万')
    expect(formatNumber(12345.6789, '$,.2f')).toBe('$1.23万')
    expect(formatNumber(12345.6789, '￥,.2f')).toBe('￥1.23万')
  })
  it('formatNumber useThousandSeparator', () => {
    expect(formatNumber(12345.6789, ',')).toBe('1.23万')
    expect(formatNumber(12345.6789, ',.2f')).toBe('1.23万')
  })
  it('formatNumber decimals', () => {
    expect(formatNumber(12345.6789, '.0f')).toBe('1万')
    expect(formatNumber(12345.6789, ',.3f')).toBe('1.235万')
    expect(formatNumber(12345.6789, ',.2f')).toBe('1.23万')
    expect(formatNumber(12345.6789, ',.1f')).toBe('1.2万')
    expect(formatNumber(12345.6789, ',.0f')).toBe('1万')
  })
  it('formatNumber suffix', () => {
    expect(formatNumber(12345.6789, '.2fEnd')).toBe('1.23万End')
    expect(formatNumber(12345.6789, 'Start.2fEnd')).toBe('Start1.23万End')
  })
})
