import {  FormInstance } from 'antd'
import { atom } from 'jotai'
import { DataModelDescFilterType } from 'src/shared/xengine-types'

// 判断用户有没有引入事实表

export const hasFactAtom = atom(false)

// 标识 用户当前正在构建的是什么类型的 业务虚拟表 STREAM: 流式er Batch：批式er BATCH_STREAM：批流一体
export const dataModelDescTypeAtom = atom('')

export const erFlowDataAtom = atom<{
  timeLimitModalForm: FormInstance<any> | null
  filterData:  DataModelDescFilterType | null
}>({
  timeLimitModalForm: null,
  filterData: null,
})
