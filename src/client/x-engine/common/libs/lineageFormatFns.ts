// @ts-nocheck
export function formatVTLineageData(data) {
    const spellId = (n: any) => {
        const { catalogName, databaseName, tableName } = n
        const name = `${catalogName}.${databaseName}.${tableName}`
        return name
    }
    const { edges = [], nodes = [] } = data || {}
    if (!Array.isArray(nodes) || !Array.isArray(edges)) {
        return {
            nodes: [],
            edges: [],
        }
    }
    const ansNodes = nodes.map((n) => {
        const id = spellId(n)
        return {
            ...n,
            id: id,
        }
    })
    const ansEdges = edges.map((e) => {
        const { source, target } = e
        const sourceId = spellId(source)
        const targetId = spellId(target)
        return {
            source: sourceId,
            target: targetId,
            sourceKey: source.columnName,
            targetKey: target.columnName,
        }
    })
    return {
        nodes: ansNodes,
        edges: ansEdges,
    }
}
