import React, { useEffect, useState } from 'react'
import { PageHeader } from '@ant-design/pro-layout'
import { Button, message, Modal, Typography, Popover, Space } from 'antd'
import { useRequest } from 'ahooks'
import { Api } from '@api'
import { Search, setSelectAttribute, setSelectOpts } from '@ui/form'
import { columns, formItems, deleteVTableBtn } from './forms-conf/form-items'
import { computeTypeList, virtualTableTypeList } from './forms-conf/constant-conf'
import { Broadcast } from '@libs'
// import { routerMap } from '@XEngineRouter/routerMap'
import { getFiltersEntries, getWinInnerSize } from '@libs/util'
import BatchUpload from '@model/batchUpload'
import { useSearchParams, Link } from 'react-router-dom'
import { formatPathWithBaseUrl } from 'src/client/utils'
import CreateFilterTable, { type DataModelDescType } from 'src/client/components/TableFilter/CreateFilterTable'
import { MoreOutlined } from '@ant-design/icons'
import { nanoid } from 'nanoid'
import CSVUpload from 'src/client/components/CSVUpload/CSVUpload'

const { Text } = Typography

const getSearchFieldMap = (key: string) => {
  const x = {
    gmtCreated: 'modification.gmtCreated',
    creator: 'modification.creator',
  } as const
  return key === 'gmtCreated' || key === 'creator' ? x[key] : key
}

const VirtualTable = () => {
  // const navigate = useNavigate()
  const [searchParams, setSearchParams] = useSearchParams()
  const [searchForm, setSearchForm] = useState<any[]>(formItems)
  const [searchInitParams, setSearchInitParams] = useState({
    catalog: '',
    database: '',
    name: '',
  })

  const [autoLoad, setAutoLoad] = useState<boolean>(false)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [currentTable, setCurrentTable] = useState<any>({})
  const [show, setShow] = useState<boolean>(false)
  const [xWidth, setXWidth] = useState('100%')
  const [reload, setReload] = useState<boolean>(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [CSVUploadOpen, setCSVUploadOpen] = useState(false)
  const [createFilterTableProps, setCreateFilterTableProps] = useState<{
    open: boolean
    tableData?: any
  }>({
    open: false,
  })

  const { runAsync: deleteVTable } = useRequest(Api.apiEngineV1VtableDeleteDelete, {
    manual: true,
    onSuccess: () => {
      setRefreshTrigger((pre) => {
        return pre + 1
      })
    },
  })

  const { runAsync: getCatalog, loading: catalogRunLoading } = useRequest(Api.apiEngineV1CatalogListGet, {
    manual: true,
    refreshDeps: [searchParams.get('catalog'), searchParams.get('database')],
  })
  const { runAsync: getDatabase, loading: databaseRunLoading } = useRequest(Api.apiEngineV1DatabaseListGet, {
    manual: true,
  })

  const handleOk = () => {
    const { catalogName, databaseName, name } = currentTable
    setIsModalOpen(false)
    message.loading({
      content: `虚拟表正在删除，请稍候……`,
      key: 'table_delete_ing',
    })
    deleteVTable({
      catalog: catalogName,
      database: databaseName,
      name: name,
      cascadeDropLikeTable: true,
    })
      .then(() => {
        message.success({
          content: `虚拟表正在删除，请稍候……`,
          key: 'table_delete_ing',
        })
        setReload((bool) => !bool)
      })
      .catch(() => {
        message.loading({
          content: `虚拟表正在删除，请稍候……`,
          key: 'table_delete_ing',
          duration: 0.01,
        })
      })
  }

  const handleCancel = () => {
    setIsModalOpen(false)
  }

  const getDatabaseData = (catalog: string, isUseInitSearchValue: boolean) => {
    if (catalog) {
      getDatabase({
        catalog,
        current: 1,
        pageSize: -1,
      }).then((data: any) => {
        const database = data?.[0]?.name || ''
        const searchDatabase = searchParams.get('database')
        const initDatabase = data?.find((i: { name: string }) => i.name === searchDatabase) ? searchDatabase : database
        resetSearchForm(data, 'database', { value: 'name', label: 'name' })
        setSearchInitParams({
          catalog,
          database: isUseInitSearchValue ? initDatabase : database,
          name: isUseInitSearchValue ? searchParams.get('name') || '' : '',
        })
        setAutoLoad(true)
      })
    }
  }

  // When Search Form value changes
  const onSearchChange = (data: any) => {
    const keys = Object.keys(data)?.join('')
    if (keys === 'catalog') {
      getDatabaseData(data[keys], false)
    }
  }

  // Formats the search forms
  const resetSearchForm = (data: any, dealName: string, options: { value: string; label: string }) => {
    setSelectOpts(
      {
        dataList: data,
        dealName: dealName,
        formItems: searchForm,
        options: options,
      },
      (newFormItems) => {
        setSearchForm(newFormItems)
      },
    )
  }

  const setTableWidth = (width: number) => {
    if (width < 1330) {
      setXWidth('110%')
    } else {
      setXWidth('100%')
    }
  }

  const batchCreateHandle = () => {
    setReload(true)
    const timer = setTimeout(() => {
      clearTimeout(timer)
      setReload(false)
    }, 500)
  }

  useEffect(() => {
    Broadcast.listen('@PAGE/DATAMODEL/VIRTUAL_TABLE', (record: any) => {
      setCurrentTable(record)
      setIsModalOpen(true)
    })

    setTableWidth(getWinInnerSize().innerWidth)

    // Window resize set table scroll x value
    window.addEventListener('resize', (e: any) => {
      setTableWidth(e?.target?.innerWidth)
    })

    getCatalog({
      current: 1,
      pageSize: -1,
    })
      .then((l) => {
        const data = l.filter((item: any) => item.type === 'INTERNAL')
        const searchCatalog = searchParams.get('catalog') || ''
        const initCatalog = data.find((i) => i.name === searchCatalog) ? searchCatalog : data[0]?.name || ''
        setAutoLoad(true)
        // Set Database Select options
        resetSearchForm(data, 'catalog', { value: 'name', label: 'name' })
        setSelectAttribute(
          [
            { name: 'catalog', attribute: 'key', value: initCatalog },
            { name: 'catalog', attribute: 'value', value: initCatalog },
          ],
          searchForm,
          (newFormItems: any) => {
            setSearchForm(newFormItems)
          },
        )
        getDatabaseData(initCatalog, true)
      })
      .catch((err) => {
        console.error(err)
      })
    // Set ComputeType Select options
    resetSearchForm(computeTypeList, 'computeType', { value: 'value', label: 'desc' })
    // Set VirtualTableType Select options
    resetSearchForm(virtualTableTypeList, 'virtualTableType', { value: 'value', label: 'desc' })
  }, [location.href])

  // 添加loading
  useEffect(() => {
    setSelectAttribute(
      [
        { name: 'catalog', attribute: 'loading', value: catalogRunLoading },
        { name: 'database', attribute: 'loading', value: databaseRunLoading },
      ],
      searchForm,
      setSearchForm,
    )
  }, [catalogRunLoading, databaseRunLoading])

  return (
    <>
      <PageHeader
        title="虚拟表"
        extra={
          <Space>
            <Button type="primary" onClick={() => setShow(true)}>
              批量创建贴源虚拟表
            </Button>
            <Button type="primary" onClick={() => setCSVUploadOpen(true)}>
              上传csv文件
            </Button>
          </Space>
        }
      />
      <Search
        key={'vtable-search'}
        items={searchForm}
        onChange={onSearchChange}
        setFieldsValue={searchInitParams}
        reload={reload}
        table={{
          refreshTrigger,
          showHeader: true,
          autoLoad,
          columns: [
            ...columns,
            {
              title: '操作',
              dataIndex: 'vtable-control',
              width: 60,
              fixed: 'right',
              render: (_: any, record: any) => {
                return (
                  <Popover
                    placement="top"
                    content={
                      <div className="w-[74px]">
                        <Button
                          block
                          type="text"
                          danger
                          onClick={() => {
                            deleteVTableBtn(record)
                          }}
                        >
                          删除
                        </Button>
                        <Button
                          block
                          type="text"
                          onClick={() => {
                            setCreateFilterTableProps({
                              open: true,
                              tableData: { ...record, id: nanoid() },
                            })
                          }}
                        >
                          数据清洗
                        </Button>
                      </div>
                    }
                  >
                    <MoreOutlined className="cursor-pointer" />
                  </Popover>
                )
              },
            },
          ],
          api: (arg: { [x: string]: any; sorter: any; filters?: any }) => {
            const { filters, sorter, ...restQueryParam } = arg
            const reqArg = Object.assign(
              getFiltersEntries(filters ?? {}),
              sorter?.order?.length
                ? {
                    sortField: getSearchFieldMap(arg.sorter.field),
                    isReverseOrder: sorter.order === 'descend',
                  }
                : {},
              restQueryParam,
              { virtualTableType: 'LIKE' },
            )
            return Api.apiEngineV1VtableSearchGet(reqArg as any)
          },
          rowKey: 'id',
          scroll: { x: xWidth },
        }}
      />
      <Modal
        title={'删除虚拟表'}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        okText={'确认删除'}
        cancelText={'取消'}
      >
        <p>
          你确认删除 <Text type="danger">{currentTable?.name}</Text> 表吗？
        </p>
      </Modal>
      <BatchUpload
        title="批量创建贴源虚拟表"
        sampleType="likeVT"
        open={show}
        onClose={() => setShow(false)}
        uploadUrl={formatPathWithBaseUrl('/api/engine/v1/vtable/createBatchVT')}
        onConfirm={batchCreateHandle}
      />
      <CreateFilterTable
        {...createFilterTableProps}
        onClose={() =>
          setCreateFilterTableProps({
            open: false,
          })
        }
      />
      <CSVUpload
        open={CSVUploadOpen}
        setOpen={setCSVUploadOpen}
        viewDataAfterSuccessUpload={() => {
          setSearchParams({
            catalog: 'dipeak',
            database: 'csv',
          })
          setReload((reload) => !reload)
          setCSVUploadOpen(false)
        }}
      />
    </>
  )
}

export default VirtualTable
