// @ts-nocheck
import ErFlow from '@model/er-flow'
import { PageHeader } from '@ant-design/pro-layout'
import { Button, Flex, Form, Input, message, Modal } from 'antd'
import React, { useRef, useState } from 'react'
import DBSelect from '@model/DBSelect'
import { erFlowDataAtom } from 'src/client/x-engine/atoms/er'
import { getUnitId } from '@libs'
import { Api } from '@api'
import { useRequest } from 'ahooks'
import { routerMap } from '@XEngineRouter/routerMap'
import { useNavigate } from 'react-router-dom'
import { useAtomValue } from 'jotai'
import { transformFormToRequestBody } from 'src/client/components/TableFilter/TableFilterDrawer'

// 创建 流批一体的 业务虚拟表 模型
export default function CreateBusinessModel() {
  const erFlowData = useAtomValue(erFlowDataAtom)
  const originData = useRef<any>()

  const navigate = useNavigate()

  const [form] = Form.useForm()

  const [show, setShow] = useState<boolean>(false)

  const { loading, run } = useRequest(Api.apiEngineV1VtablePost, {
    manual: true,
    onSuccess: (_: any, params: any) => {
      void message.success('创建业务虚拟表成功')
      navigate(`${routerMap.dataModel.businessVirtualTable.path}?catalog=${params[0]?.table?.catalogName}`)
    },
    onError: () => {
      void message.error('创建业务虚拟表失败')
    },
    onFinally: () => {
      closeHandle()
    },
  })

  const changeHandle = (d: any) => {
    originData.current = d
  }

  const submit = () => {
    const { nodes = [], edges = [], dataModelDescType } = originData.current
    if (nodes.length === 0) {
      void message.error('请选择主表构建模型')
      return
    }
    if (nodes.length > 1 && edges.length === 0 && dataModelDescType !== 'STREAM_BATCH') {
      void message.error('请构建ER模型的join关系')
      return
    }
    setShow(true)
  }

  const closeHandle = () => {
    form.resetFields()
    setShow(false)
  }

  const okHandle = async () => {
    const res = await form.validateFields()
    let factTable = ''
    let unionDagDescUnionType = ''
    const vertices = originData.current.nodes.map((d: any) => {
      const item = d.data
      if (item.unionType) {
        // STREAM_BATCH把TableConfigModel传递出来的unionType放在node里面，需要提取出来给后面请求使用
        unionDagDescUnionType = item.unionType
      }
      if (item.isFact) {
        factTable = `${item.catalogName}.${item.databaseName}.${item.name}`
      }
      return {
        dummy: false,
        id: d.id,
        table: `${item.catalogName}.${item.databaseName}.${item.name}`,
        kind: item.isFact ? 'FACT' : 'DIM',
        timeColumn: item.timeColumn,
        dimensionsColumns: item.columns.map((col) => ({
          name: `${item.catalogName}.${item.databaseName}.${item.name}.${col.name}`,
          vertexId: d.id,
        })),
        tableType: item?.tableEngine === 'Kafka' ? 'STREAM' : item?.computeType || 'BATCH',
      }
    })

    const body = {
      catalogName: res.catalog,
      databaseName: res.database,
      name: res.name,
      dataModelDesc: {
        dataModelDescType: originData.current.dataModelDescType,
        catalog: res.catalog,
        modelName: res.name,
        measures: [],
        factTable,
      },
    }

    if (originData.current.dataModelDescType === 'STREAM_BATCH') {
      body.dataModelDesc.unionDagDesc = { vertices }
      body.dataModelDesc.unionDagDesc.unionType = unionDagDescUnionType
      if (unionDagDescUnionType === 'UNION_UPSERT') {
        body.dataModelDesc.modelType = 'UPDATE_MODEL'
      }
    } else {
      body.dataModelDesc.joinDag = {
        edges: formatEdges(originData.current.edges),
        vertices,
      }
    }

    const { timeLimitModalForm, filterData } = erFlowData
    const timeLimitModalFormData = timeLimitModalForm?.getFieldsValue()
    if (body.dataModelDesc.joinDag) {
      body.dataModelDesc.joinDag.edges[0].joinConditions = { conditions: [] }
    }
    if (timeLimitModalFormData?.['need-time-limit']) {
      if (!body.dataModelDesc.joinDag) {
        body.dataModelDesc.joinDag = { edges: [], vertices }
      }
      const conditions = [
        {
          operator: timeLimitModalFormData[`time-limit1-logic-compare`],
          params: [
            {
              type: 'TIME_INTERVAL_EXPR',
              timeIntervalExprDesc: {
                timeColumn: {
                  name: timeLimitModalFormData['time-limit1-input1-time-limit-select'].split('~')[0],
                  vertexId: timeLimitModalFormData['time-limit1-input1-time-limit-select'].split('~')[1],
                },
                ...(timeLimitModalFormData['time-limit1-input1-offset-select'] === 'NONE'
                  ? {}
                  : {
                      timeIntervalOperator: timeLimitModalFormData['time-limit1-input1-offset-select'],
                      intervalSize: timeLimitModalFormData['time-limit1-input1-time-num'],
                      timeUnit: timeLimitModalFormData['time-limit1-input1-time-select'],
                    }),
              },
            },
            {
              type: 'TIME_INTERVAL_EXPR',
              timeIntervalExprDesc: {
                timeColumn: {
                  name: timeLimitModalFormData['time-limit1-input2-time-limit-select'].split('~')[0],
                  vertexId: timeLimitModalFormData['time-limit1-input2-time-limit-select'].split('~')[1],
                },
                ...(timeLimitModalFormData['time-limit1-input2-offset-select'] === 'NONE'
                  ? {}
                  : {
                      timeIntervalOperator: timeLimitModalFormData['time-limit1-input2-offset-select'],
                      intervalSize: timeLimitModalFormData['time-limit1-input2-time-num'],
                      timeUnit: timeLimitModalFormData['time-limit1-input2-time-select'],
                    }),
              },
            },
          ],
        },
      ]
      if (timeLimitModalFormData['need-add']) {
        conditions.push({
          operator: timeLimitModalFormData[`time-limit2-logic-compare`],
          params: [
            {
              type: 'TIME_INTERVAL_EXPR',
              timeIntervalExprDesc: {
                timeColumn: {
                  name: timeLimitModalFormData['time-limit2-input1-time-limit-select'].split('~')[0],
                  vertexId: timeLimitModalFormData['time-limit2-input1-time-limit-select'].split('~')[1],
                },
                ...(timeLimitModalFormData['time-limit2-input1-offset-select'] === 'NONE'
                  ? {}
                  : {
                      timeIntervalOperator: timeLimitModalFormData['time-limit2-input1-offset-select'],
                      intervalSize: timeLimitModalFormData['time-limit2-input1-time-num'],
                      timeUnit: timeLimitModalFormData['time-limit2-input1-time-select'],
                    }),
              },
            },
            {
              type: 'TIME_INTERVAL_EXPR',
              timeIntervalExprDesc: {
                timeColumn: {
                  name: timeLimitModalFormData['time-limit2-input2-time-limit-select'].split('~')[0],
                  vertexId: timeLimitModalFormData['time-limit2-input2-time-limit-select'].split('~')[1],
                },
                ...(timeLimitModalFormData['time-limit2-input2-offset-select'] === 'NONE'
                  ? {}
                  : {
                      timeIntervalOperator: timeLimitModalFormData['time-limit2-input2-offset-select'],
                      intervalSize: timeLimitModalFormData['time-limit2-input2-time-num'],
                      timeUnit: timeLimitModalFormData['time-limit2-input2-time-select'],
                    }),
              },
            },
          ],
        })
      }
      if (body.dataModelDesc.joinDag && conditions) {
        body.dataModelDesc.joinDag.edges[0].joinConditions.conditions.push(...conditions)
      }
    }
    if (body.dataModelDesc.joinDag && filterData?.conditions) {
      body.dataModelDesc.joinDag.edges[0].joinConditions.conditions.push(...filterData.conditions)
      body.dataModelDesc.joinDag.edges[0].joinConditions.filterExpr = filterData.filterExpr
    }
    run({ table: body })
  }

  const formatEdges = (edges: any) => {
    if (!edges || edges.length === 0) {
      return []
    }
    const sourceTargetIds: string[] = []
    const list: any = []
    edges.forEach((item: any) => {
      const sourceTargetId = item.source.cell + '-' + item.target.cell
      const index = sourceTargetIds.indexOf(sourceTargetId)
      const sourceInfo = item.source.port.split('~')
      const targetInfo = item.target.port.split('~')
      const from = sourceInfo[2]
      const to = targetInfo[2]
      const foreignKey = {
        name: `${item?.data?.sourceData?.catalogName}.${item?.data?.sourceData?.databaseName}.${item?.data?.sourceData?.name}.${sourceInfo[4]}`,
        vertexId: from,
      }
      const primaryKey = {
        name: `${item?.data?.targetData?.catalogName}.${item?.data?.targetData?.databaseName}.${item?.data?.targetData?.name}.${targetInfo[4]}`,
        vertexId: to,
      }
      if (index < 0) {
        sourceTargetIds.push(sourceTargetId)
        list.push({
          foreignKeys: [foreignKey],
          from,
          to,
          id: `e${getUnitId()}`,
          joinType: item?.data?.joinType,
          primaryKeys: [primaryKey],
          properties: {},
        })
      } else {
        list[index].foreignKeys.push(foreignKey)
        list[index].primaryKeys.push(primaryKey)
      }
    })
    return list
  }

  return (
    <Flex vertical style={{ width: '100%', height: '100%' }}>
      <PageHeader
        title="创建业务虚拟表"
        onBack={() => navigate(routerMap.dataModel.businessVirtualTable.path)}
        extra={
          <Button type="primary" onClick={submit}>
            创建
          </Button>
        }
      />
      <Flex flex={1}>
        <ErFlow onChange={changeHandle} />
      </Flex>
      <Modal
        open={show}
        okButtonProps={{
          loading,
        }}
        title={'选择业务虚拟表配置'}
        width={600}
        destroyOnClose
        onCancel={closeHandle}
        onOk={okHandle}
      >
        <Form layout="vertical" form={form}>
          <DBSelect span={24} required={{ catalog: true, database: true }} setFieldValue={form.setFieldValue} />
          <Form.Item
            label="虚拟表名称"
            name="name"
            required
            rules={[{ required: true, message: '请输入业务虚拟表名称' }]}
          >
            <Input placeholder={'请输入虚拟表的名称'} />
          </Form.Item>
        </Form>
      </Modal>
    </Flex>
  )
}
