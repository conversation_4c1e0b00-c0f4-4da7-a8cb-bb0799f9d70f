//virtual-table-list
.paginationWrapper {
  display: flex;
  justify-content: flex-end;
}

.redirectBtn {
  width: 118px;
  padding: 5px;
  cursor: pointer;
}

.redirectBtn:hover {
  background: rgb(239 219 255 / 0.5);
}

.createVTCard {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: calc(100% - 16px);
  min-height: 135px;
  padding-bottom: 16px;
  border-radius: 8px;
  background: white;
}

.virtualTableCardWrapper {
  min-width: 200px;
  margin-bottom: 16px;
}

.virtualTableCardTypeWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 36px;
}

.cardDetailItem {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.btnWrapper {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 48px;
}

.myTab {
  :global {
    .ant-tabs-nav {
      .ant-tabs-nav-list {
        flex: 0 0 99%;
      }

      .ant-tabs-tab {
        display: flex;
        flex: 0 0 50%;
        justify-content: center;
      }
    }
  }
}

.businessVirtualTable {
  display: flex;
  min-height: 570px;

  &Step {
    flex: 0 0 250px;
    margin-right: 16px;
    padding: 16px 24px;
    border: 1px solid rgb(243 243 243 / 1);
    border-radius: 4px;
    background: white;
  }

  &Tabs {
    width: 100%;
  }

  &CardItem {
    margin-bottom: 20px;
    border: 1px solid transparent;
    background-color: rgb(250 250 250 / 1);

    &:hover {
      border: 1px solid rgb(114 46 209 / 1);
      background: rgb(249 240 255 / 0.3);
    }
  }

  &Desc {
    line-height: 100px;
  }

  &FormItemWithBottom {
    margin-bottom: 12px;
  }

  &TimePoint {
    width: 100%;
  }
}

.virtualTableDetail {
  .lineageCon {
    height: 700px;
  }
}

// table-model-recommend
.recommendWrapper {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: flex;
  gap: 30px;
  width: 100%;
  padding: 16px;
  background: rgb(18 18 18 / 0.1);
}

.recommendItem {
  position: relative;
  width: 100%;
  min-width: 28px;
  max-width: 50%;
  height: 150px;
}

.recommendItemTextDiv {
  position: absolute;
  top: 0;
  z-index: 2;
  background: aliceblue;
}

.recommendItemText {
  color: rgb(24 144 255 / 1);
  cursor: pointer;
}

.recommendLineageDiv {
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.recommendModalLineage {
  width: 100%;
  height: 600px;
}

// corn-comp.tsx
.cronTextBlock {
  padding: 0 4px;
  border-radius: 4px;
  background-color: rgb(245 245 245);
  color: rgb(195 197 198);
  line-height: 32px;
  cursor: default;
}

.dataPreviewTableWidth {
  width: calc(100vw - 300px);
}

.tableQualityCon {
  position: relative;
  min-height: 300px;

  &Spin {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%);
  }
}
