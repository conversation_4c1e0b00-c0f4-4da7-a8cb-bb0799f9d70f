// @ts-nocheck
import { CommonProps } from '../create-virtual-table'
import { Form, Row, Input, Col, Button, message, Select, Spin, Typography, Table } from 'antd'
import { SaveOutlined } from '@ant-design/icons'
import { tableEngineTypeList, CREATE_LIKE_TABLE_STEPS } from './constant-conf'
import { useState } from 'react'
import { useRequest } from 'ahooks'
import { Api } from '@api'
import cs from '../data-model.module.scss'

enum configTypeEnum {
    'HIVE' = 'HIVE',
    'KAFKA' = 'KAFKA',
}

const { Title } = Typography

const DataSourceForm = (props: CommonProps) => {
    const { requestParams, setRequestParams, setStep, setOriginData } = props

    const onSubmit = async (name: string, instance: any) => {
        await instance.forms.tableEngine.validateFields()
        if (name) {
            const key = name + 'Params'
            // 所选的表单自动通过验证,数据合并
            await instance.forms[key].validateFields()
            setOriginData({
                ...requestParams,
                [key]: instance.forms[key].getFieldValue() || {},
            })
            setStep(CREATE_LIKE_TABLE_STEPS.QUERY_ADVANCED)
        }
    }

    return (
        <>
            <Form.Provider onFormFinish={onSubmit}>
                <SelectForm {...props} />
                {requestParams.tableEngine && (
                    <>
                        {requestParams.tableEngine !== 'external' && <Title level={5}>连接数据源</Title>}
                        {(() => {
                            switch (requestParams.tableEngine) {
                                case 'kafka':
                                    return <KafkaForm />
                                case 'external':
                                    return <Form name='externalParams' />
                                default:
                                    return (
                                        <HiveForm requestParams={requestParams} setRequestParams={setRequestParams} />
                                    )
                            }
                        })()}
                    </>
                )}
                <Form name={requestParams.tableEngine} className={cs.btnWrapper}>
                    <Button htmlType='submit' type='primary' icon={<SaveOutlined />}>
                        下一步
                    </Button>
                </Form>
            </Form.Provider>
        </>
    )
}

const SelectForm = (props: CommonProps) => {
    const { requestParams, setRequestParams } = props
    const changeHandle = (e: string) => {
        setRequestParams({
            ...requestParams,
            tableEngine: e,
        })
    }

    return (
        <Form layout='vertical' name='tableEngine' autoComplete='off'>
            <Title level={5}>选择数据源类型</Title>
            <Row>
                <Col span={8}>
                    <Form.Item
                        name='tableEngine'
                        rules={[{ required: true, message: '请选择数据源' }]}
                        initialValue={requestParams.tableEngine}
                    >
                        <Select
                            placeholder='请选择数据源'
                            options={tableEngineTypeList.map((e) => ({
                                label: e.desc,
                                value: e.value,
                            }))}
                            onChange={changeHandle}
                        />
                    </Form.Item>
                </Col>
            </Row>
        </Form>
    )
}

const columns = [
    {
        title: '字段名',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: '字段类型',
        dataIndex: 'type',
        key: 'type',
    },
]

const HiveForm = (props: CommonProps) => {
    const { setRequestParams, requestParams } = props
    const [form] = Form.useForm()
    const [dbList, setDbList] = useState<string[]>([])
    const [show, setShow] = useState<boolean>(true)
    const [curList, setCurList] = useState([])
    const [tableList, setTableList] = useState([])

    //hive table List
    const { run: runHiveTablesList } = useRequest(Api.apiEngineV1MetaListExternalHiveTablesGet, {
        manual: true,
        onSuccess(res) {
            setTableList(res)
        },
        onError() {
            message.error('获取hiveTableList失败')
        },
    })

    const { loading: hiveMetaLoading, run: runHiveMeta } = useRequest(Api.apiEngineV1MetaGetExternalHiveTableGet, {
        manual: true,
        onSuccess(res) {
            setCurList(res.columns)
            setRequestParams({
                ...requestParams,
                columns: res.columns.map((e) => ({
                    name: e.name,
                    columnType: e.type,
                    comment: e.comment,
                })),
            })
        },
        onError() {
            message.error('获取hive表信息失败')
        },
    })

    const { loading: hiveDbLoading, run: runHiveDb } = useRequest(Api.apiEngineV1MetaListExternalHiveDatabasesGet, {
        manual: true,
        onSuccess(result) {
            setDbList((pre) => (pre?.length ? pre : result))
        },
        onError() {
            setDbList([])
            message.error('获取hiveDb列表失败')
        },
        onFinally() {
            form.setFieldValue('dbName', null)
        },
    })

    const [selectMode, setSelectMode] = useState<boolean>(true)

    const { loading: getConfigLoading, data: configData } = useRequest(
        () => {
            return Api.apiEngineV1ResourcesGet()
                .then((res) => {
                    return res.filter((i) => {
                        return i.resourceType === configTypeEnum.HIVE
                    })
                })
                .catch(() => {
                    return []
                })
        },
        {
            onSuccess(data) {
                setSelectMode(data.length > 0)
            },
            onError() {
                setSelectMode(false)
                message.error('获取配置失败')
            },
        },
    )

    function getHiveDb(e: string) {
        runHiveDb({ hiveMetastoreUris: e })
    }

    function dbChoose(e: string) {
        if (e) {
            runHiveTablesList({
                hiveMetastoreUris: form.getFieldValue('metastore'),
                database: e,
            })
            setShow(true)
        }
    }

    const pTableOnChange = (e: string) => {
        runHiveMeta({
            hiveMetastoreUris: form.getFieldValue('metastore'),
            database: form.getFieldValue('dbName'),
            table: e,
        })
    }

    return (
        <>
            <Form layout='vertical' name='hiveParams' form={form} autoComplete='off'>
                <Row gutter={[16, 16]}>
                    <Col className={cs.gutter_row} span={8}>
                        <Form.Item
                            name='metastore'
                            label='HIVE metastore地址'
                            rules={[
                                {
                                    required: true,
                                    message: '请输入HIVE metastore地址',
                                },
                            ]}
                        >
                            {configData?.length ? (
                                <Select
                                    loading={getConfigLoading}
                                    onChange={(val) => {
                                        form.resetFields(['dbName'])
                                        const res = configData.find((i) => {
                                            return i.hiveAddress === val
                                        })
                                        setDbList(res.dbNameList)
                                        form.setFieldsValue({
                                            username: res.userName,
                                            password: res.password,
                                            kvValue: res.kvParam,
                                        })
                                    }}
                                    placeholder='请选择HIVE metastore地址'
                                    options={configData.map((i) => ({
                                        key: i.hiveAddress,
                                        label: i.hiveAddress,
                                        value: i.hiveAddress,
                                    }))}
                                />
                            ) : (
                                <Input
                                    placeholder='请输入HIVE metastore地址'
                                    onBlur={() => {
                                        form.getFieldValue('metastore') && getHiveDb(form.getFieldValue('metastore'))
                                    }}
                                />
                            )}
                        </Form.Item>
                    </Col>
                    <Col className={cs.gutter_row} span={8}>
                        <Form.Item
                            name='dbName'
                            label={
                                <div>
                                    数据库名称
                                    {hiveDbLoading && (
                                        <Spin
                                            style={{
                                                width: '18px',
                                                height: '18px',
                                            }}
                                        />
                                    )}
                                </div>
                            }
                            rules={[{ required: true, message: '请选择数据库名称' }]}
                        >
                            <Select
                                placeholder='请选择数据库'
                                options={dbList.map((e) => ({
                                    label: e,
                                    value: e,
                                }))}
                                onChange={dbChoose}
                            />
                        </Form.Item>
                    </Col>
                    <Col className={cs.gutter_row} span={8}>
                        <Form.Item
                            name='username'
                            label='用户名'
                            rules={[{ required: false, message: '请输入用户名' }]}
                        >
                            <Input disabled={selectMode || getConfigLoading} placeholder='请输入用户名' />
                        </Form.Item>
                    </Col>
                    <Col className={cs.gutter_row} span={8}>
                        <Form.Item name='password' label='密码' rules={[{ required: false, message: '请输入密码' }]}>
                            <Input disabled={selectMode || getConfigLoading} placeholder='请输入密码' />
                        </Form.Item>
                    </Col>
                    <Col className={cs.gutter_row} span={8}>
                        <Form.Item
                            name='kvValue'
                            label='kv参数'
                            rules={[
                                {
                                    required: false,
                                    message: '请输入kv参数',
                                },
                            ]}
                        >
                            <Input.TextArea
                                disabled={selectMode || getConfigLoading}
                                rows={1}
                                placeholder='请输入kv参数'
                            />
                        </Form.Item>
                    </Col>
                </Row>
                {show && (
                    <>
                        <Title level={5}>选择表</Title>
                        <Row gutter={[16, 16]}>
                            <Col className={cs.gutter_row} span={8}>
                                <Form.Item name='tableName' rules={[{ required: true, message: '请选择表' }]}>
                                    <Select
                                        placeholder='请选择表'
                                        options={tableList.map((e) => ({
                                            label: e,
                                            value: e,
                                        }))}
                                        onChange={pTableOnChange}
                                    />
                                </Form.Item>
                            </Col>
                            <Col className={cs.gutter_row} span={24}>
                                <Table
                                    loading={hiveMetaLoading}
                                    columns={columns}
                                    dataSource={curList}
                                    rowKey={(record) => record.name}
                                    pagination={{ pageSize: 5 }}
                                />
                            </Col>
                        </Row>
                    </>
                )}
            </Form>
        </>
    )
}
const KafkaForm = () => {
    const [form] = Form.useForm()
    const [selectMode, setSelectMode] = useState<boolean>(true)
    const [kafkaTopicList, setKafkaTopicList] = useState([])
    const { loading: getConfigLoading, data: configData } = useRequest(
        () => {
            return Api.apiEngineV1ResourcesGet()
                .then((res) => {
                    return res.filter((i) => {
                        return i.resourceType === configTypeEnum.KAFKA
                    })
                })
                .catch(() => {
                    return []
                })
        },
        {
            onSuccess(data) {
                setSelectMode(data.length > 0)
            },
            onError() {
                message.error('获取配置失败')
                setSelectMode(false)
            },
        },
    )

    return (
        <Form layout='vertical' name='kafkaParams' form={form} autoComplete='off'>
            <Row gutter={[16, 16]}>
                <Col className={cs.gutter_row} span={8}>
                    <Form.Item
                        name='kafka.broker.list'
                        label='Broker地址'
                        rules={[{ required: true, message: '请输入Broker地址' }]}
                    >
                        {configData?.length ? (
                            <Select
                                loading={getConfigLoading}
                                onChange={(val) => {
                                    form.resetFields(['kafka.topic.list'])
                                    const res = configData.find((i) => {
                                        return i.kafkaBrokerList === val
                                    })
                                    setKafkaTopicList(res.kafkaTopicList)
                                    form.setFieldsValue({
                                        'kafka.group.name': res.kafkaGroupName,
                                        'kafka.format': res.kafkaFormat,
                                        'kafka.num.consumers': res.partitionNum,
                                        kvValue: res.kvParam,
                                    })
                                }}
                                placeholder='请选择地址'
                                options={configData.map((i) => ({
                                    key: i.kafkaBrokerList,
                                    label: i.kafkaBrokerList,
                                    value: i.kafkaBrokerList,
                                }))}
                            />
                        ) : (
                            <Input disabled={selectMode || getConfigLoading} placeholder='localhost:9092' />
                        )}
                    </Form.Item>
                </Col>
                <Col className={cs.gutter_row} span={8}>
                    <Form.Item
                        name='kafka.topic.list'
                        label='Topic列表'
                        rules={[{ required: true, message: '请输入Topic列表' }]}
                    >
                        {configData?.length ? (
                            <Select
                                placeholder='topic1,topic2'
                                options={kafkaTopicList.map((e) => ({
                                    label: e,
                                    value: e,
                                }))}
                            />
                        ) : (
                            <Input disabled={selectMode || getConfigLoading} placeholder='topic1,topic2' />
                        )}
                    </Form.Item>
                </Col>
                <Col className={cs.gutter_row} span={8}>
                    <Form.Item
                        name='kafka.group.name'
                        label='消费组名称'
                        rules={[{ required: true, message: '请输入消费组名称' }]}
                    >
                        <Input disabled={selectMode || getConfigLoading} placeholder='group_name' />
                    </Form.Item>
                </Col>
                <Col className={cs.gutter_row} span={8}>
                    <Form.Item
                        name='kafka.format'
                        label='消息体格式'
                        rules={[{ required: true, message: '请输入消息体格式' }]}
                    >
                        <Input disabled={selectMode || getConfigLoading} placeholder='JSONEachRow' />
                    </Form.Item>
                </Col>
                <Col className={cs.gutter_row} span={8}>
                    <Form.Item
                        name='kafka.num.consumers'
                        label='partition数目'
                        rules={[
                            { required: true, message: '请输入partition数目' },
                            {
                                pattern: new RegExp(/^[0-9]{1,}$/, 'g'),
                                message: 'partition数目只允许是数字',
                            },
                        ]}
                    >
                        <Input disabled={selectMode || getConfigLoading} placeholder='请输入partition数目' />
                    </Form.Item>
                </Col>
                <Col className={cs.gutter_row} span={8}>
                    <Form.Item
                        name='kvValue'
                        label='kv参数'
                        rules={[
                            {
                                required: false,
                                message: '请输入kv参数',
                            },
                        ]}
                    >
                        <Input.TextArea disabled={selectMode || getConfigLoading} rows={1} placeholder='请输入kv参数' />
                    </Form.Item>
                </Col>
            </Row>
        </Form>
    )
}

export { DataSourceForm }
