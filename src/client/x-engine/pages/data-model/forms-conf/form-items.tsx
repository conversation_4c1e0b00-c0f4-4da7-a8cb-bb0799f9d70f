// @ts-nocheck
import { routerMap } from '@XEngineRouter/routerMap'
import { get } from 'lodash-es'
import Columns from '@ui/table/Columns'
import { Button, Popover, Tag, Typography } from 'antd'
import { Link } from 'react-router-dom'
import { Broadcast } from '@libs'
import { computeTypeList } from '@pages/data-model/forms-conf/constant-conf'
import Paragraph from 'antd/es/skeleton/Paragraph'
import { askBIPageUrls } from 'src/shared/url-map'
import { MoreOutlined } from '@ant-design/icons'

enum ITableType {
  STREAM = 'STREAM',
  BATCH = 'BATCH',
}
export const deleteVTableBtn = (record: any) => {
  Broadcast.trigger('@PAGE/DATAMODEL/VIRTUAL_TABLE', record)
}

/**
 * Form items conf
 */
const formItems = [
  {
    key: 'catalog',
    tag: 'Select',
    label: '虚拟表目录',
    name: 'catalog',
  },
  {
    tag: 'Select',
    rules: [{ required: false, message: '请选择虚拟表库' }],
    label: '虚拟表库',
    name: 'database',
    allowClear: true,
  },
  {
    tag: 'Input',
    rules: [{ required: false, message: '请输入指标' }],
    label: '表名称',
    name: 'name',
    allowClear: true,
  },
  {
    tag: 'buttons',
    wrapperCol: { span: 16 },
    children: [
      {
        tag: 'Button',
        name: 'reset',
        label: '重置',
        htmlType: 'button',
      },
      {
        tag: 'Button',
        name: 'submit',
        label: '查询',
        type: 'primary',
        htmlType: 'submit',
      },
    ],
  },
]

// const items = virtualTableTypeList.map((i) => {
//     return { ...i, text: i.desc }
// })
const computeItems = computeTypeList.map((i) => {
  return { ...i, text: i.desc }
})

computeItems.push({
  value: '--',
  text: '--',
  desc: '',
})

const columns = [
  {
    title: '表名称',
    sorter: true,
    fixed: 'left',
    width: '20%',
    ellipsis: true,
    dataIndex: 'name',
    render: (text: string, record: { name: string; catalogName: string; databaseName: string }) => {
      return (
        <Popover content={text} placement="top">
          <Link
            to={`${routerMap.dataModel.virtualTableDetail.path}?name=${record.name}&catalog=${record.catalogName}&database=${record.databaseName}`}
          >
            {text}
          </Link>
        </Popover>
      )
    },
  },
  // {
  //     title: '表类型',
  //     filters: items,
  //     filterMultiple: false,
  //     dataIndex: 'virtualTableType',
  //     render: Columns({ type: 'virtualTableType' }),
  // },
  {
    title: '表目录',
    dataIndex: 'catalogName',
    filterSearch: true,
  },
  {
    title: '表库',
    sorter: true,
    dataIndex: 'databaseName',
  },
  // {
  //     title: '计算类型',
  //     filters: computeItems,
  //     filterMultiple: false,
  //     dataIndex: 'computeType',
  //     render: Columns({ type: 'computeType' }),
  // },
  // {
  //     title: '热度',
  //     dataIndex: 'hot',
  //     sorter: true,
  //     width: 80,
  //     render: Columns({ type: 'hot' }),
  // },
  {
    title: '创建时间',
    sorter: true,
    dataIndex: 'gmtCreated',
    render: Columns({ type: 'gmtCreated' }),
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    width: 150,
    ellipsis: true,
    sorter: true,
    render: (text: string, record: { modification: { creator: any } }) => {
      return <Popover content={record.modification.creator}>{record.modification.creator || '-'}</Popover>
    },
  },
]

const businessTableColumns = ({ publishVTable, filterTable }) => [
  {
    title: '业务虚拟表名称',
    sorter: true,
    fixed: 'left',
    width: '20%',
    ellipsis: true,
    dataIndex: 'name',
    render: (text: string, record: { name: string; catalogName: string; databaseName: string }) => {
      return (
        <Popover content={text} placement="top">
          <Link
            to={`${routerMap.dataModel.businessVirtualTableDetail.path}?name=${record.name}&catalog=${record.catalogName}&database=${record.databaseName}`}
          >
            {text}
          </Link>
        </Popover>
      )
    },
  },
  // {
  //     title: '表类型',
  //     filters: items,
  //     filterMultiple: false,
  //     dataIndex: 'virtualTableType',
  //     render: Columns({ type: 'virtualTableType' }),
  // },
  // {
  //     title: '表目录',
  //     dataIndex: 'catalogName',
  //     filterSearch: true,
  // },
  {
    title: '表库',
    sorter: true,
    dataIndex: 'databaseName',
  },
  {
    title: '关联事实表名称',
    sorter: true,
    // dataIndex: 'databaseName',
    render(record) {
      return get(record, 'dataModelDesc.factTable', '-')
    },
  },
  // {
  //     title: '关联指标名称',
  //     sorter: true,
  //     render(record) {
  //         let text =
  //             get(record, 'dataModelDesc.joinDag.vertices', [])
  //                 .map((item) => {
  //                     return item.metricsColumns
  //                         .map((j) => {
  //                             return j.alias || j.name
  //                         })
  //                         .join(',')
  //                 })
  //                 .join(',') ?? '-'
  //
  //         return (
  //             <Popover content={text} placement='right'>
  //                 {text}
  //             </Popover>
  //         )
  //     },
  // },
  // {
  //     title: '计算类型',
  //     filters: computeItems,
  //     filterMultiple: false,
  //     dataIndex: 'computeType',
  //     render: Columns({ type: 'computeType' }),
  // },
  // {
  //     title: '热度',
  //     dataIndex: 'hot',
  //     sorter: true,
  //     width: 80,
  //     render: Columns({ type: 'hot' }),
  // },
  {
    title: '创建时间',
    sorter: true,
    dataIndex: 'gmtCreated',
    render: Columns({ type: 'gmtCreated' }),
  },
  {
    title: '上线',
    render: (value, record) => {
      const isPublished = 'publishDataModelName' in record.labels
      let text
      if (!isPublished) {
        // 目前只有流表可以上线
        if (record.computeType === ITableType.BATCH) {
          return '批表无需上线'
        } else {
          text = '上线业务虚拟表(流)'
        }
      } else {
        text = (
          <Link
            to={`${askBIPageUrls.manage.xengine.dataSceneDetail}?name=${record.name}&catalog=${record.catalogName}&database=${record.databaseName}`}
          >
            运行详情
          </Link>
        )
      }
      return (
        <Button
          className="p-0"
          type="link"
          onClick={() => {
            if (!isPublished && record.computeType !== ITableType.BATCH) {
              publishVTable(record)
            }
          }}
        >
          {isPublished && <Tag>已上线</Tag>}
          {text}
        </Button>
      )
    },
  },
  // {
  //     title: '创建人',
  //     dataIndex: 'creator',
  //     width: 150,
  //     ellipsis: true,
  //     sorter: true,
  //     render: (text: string, record: { modification: { creator: any } }) => {
  //         return <Popover content={record.modification.creator}>{record.modification.creator || '-'}</Popover>
  //     },
  // },
  {
    title: '操作',
    dataIndex: 'vtable-control',
    width: 60,
    fixed: 'right',
    render: (text: any, record: any) => {
      const isPublished = 'publishDataModelName' in record.labels

      return (
        <Popover
          content={
            <div className="w-[74px]">
              <Button
                block
                type="text"
                danger
                disabled={isPublished}
                onClick={() => {
                  deleteVTableBtn(record)
                }}
              >
                删除
              </Button>
              <Button block type="text" onClick={() => filterTable(record)}>
                数据清洗
              </Button>
            </div>
          }
        >
          <MoreOutlined className="cursor-pointer" />
        </Popover>
      )
    },
  },
]

export { formItems, columns, businessTableColumns }
