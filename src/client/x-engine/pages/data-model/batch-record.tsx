import { PageHeader } from '@ant-design/pro-layout'
import { Card, Table } from 'antd'
import { useState } from 'react'
import { useRequest } from 'ahooks'
import { Api } from '@api'
import { Link } from 'react-router-dom'
import { routerMap } from '@XEngineRouter/routerMap'
import Columns from '@ui/table/Columns'
import dayjs from 'dayjs'

const columns = [
  {
    key: 'batchCreatedTime',
    dataIndex: 'batchCreatedTime',
    title: '操作时间',
    width: 200,
    render: (time: string) => (time ? dayjs(new Date(time)).format('YYYY-MM-DD HH:mm:ss') : '-'),
  },
  {
    key: 'uploadNumber',
    dataIndex: 'uploadNumber',
    title: '上传数量',
  },
  {
    key: 'successfulNumber',
    dataIndex: 'successfulNumber',
    title: '成功数量',
  },
  {
    key: 'virtualTableType',
    dataIndex: 'virtualTableType',
    title: '操作类型',
    render: Columns({ type: 'virtualTableType' }),
  },
  {
    key: 'user',
    dataIndex: 'user',
    title: '操作用户',
  },
  {
    key: 'action',
    dataIndex: 'action',
    title: '操作',
    render: (_: any, record: RecordItemType) => (
      <Link to={routerMap.dataModel.batchRecordDetail.path + '?id=' + record.id}>详情</Link>
    ),
  },
]

interface RecordItemType {
  batchCreatedTime: string
  id: string
  successfulNumber: string
  uploadNumber: string
  user: string
  virtualTableType: string
}

const defaultPageSize = 20

export default function BatchRecord() {
  const [list, setList] = useState<RecordItemType[]>([])

  const [total, setTotal] = useState<number>(0)

  const [pageNumber, setPageNumber] = useState<number>(1)

  const { loading } = useRequest(
    () =>
      Api.apiEngineV1VtableListCreationHistoryGet({
        current: pageNumber,
        pageSize: defaultPageSize,
      }),
    {
      refreshDeps: [pageNumber],
      onSuccess: (res: any) => {
        setList(res.list)
        setTotal(res.total)
      },
    },
  )

  return (
    <>
      <PageHeader title="虚拟表创建记录" />
      <Card>
        <Table
          loading={loading}
          columns={columns}
          rowKey="id"
          pagination={{
            pageSize: defaultPageSize,
            total,
            showSizeChanger: false,
            onChange: (page: number) => setPageNumber(page),
          }}
          dataSource={list}
        />
      </Card>
    </>
  )
}
