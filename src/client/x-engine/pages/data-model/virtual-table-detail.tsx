// @ts-nocheck
import React, { useCallback, useMemo, useState } from 'react'
import { PageHeader } from '@ant-design/pro-layout'
import { Table, message, Descriptions, Button, Popover, Empty } from 'antd'
import { useNavigate, useSearchParams } from 'react-router-dom'

import LayoutCard from '@ui/layoutCard/LayoutCard'
import GLineage from '@model/GLineage'

import { useAntdTable, useRequest } from 'ahooks'
import { Api } from '@api'
import { getUnitId, formatERDataToGLineage } from '@libs'
import { get } from 'lodash-es'

import { ConsoleSqlOutlined, PlusOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import { formatVTLineageData } from '@libs/lineageFormatFns'
import Columns from '@ui/table/Columns'
import { routerMap } from '@XEngineRouter/routerMap'
import {
  VIRTUAL_TABLE_DETAIL_PANELS,
  detailTabItems,
  columnTypeColumns,
  taskListColumns,
} from './forms-conf/constant-conf'
import cs from './data-model.module.scss'

enum IVirtualTableType {
  LIKE = '贴源虚拟表',
  AS = '业务虚拟表',
}
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 100,
    render: Columns({ type: 'id' }),
  },
  {
    title: '名称',
    dataIndex: 'name',
    render: (text: string, record: any) => {
      return (
        <Link
          data-text={text}
          to={
            routerMap.metricsManager.metricsDetail.path +
            '?metricsId=' +
            record.id +
            '&metricsType=' +
            record.metricsType +
            '&metricsName=' +
            record.name
          }
        >
          {text}
        </Link>
      )
    },
  },
  {
    title: '业务系统',
    dataIndex: 'businessType',
    render: Columns({ type: 'businessType' }),
  },
  {
    title: '热度',
    dataIndex: 'hot',
    width: 80,
    render: Columns({ type: 'hot' }),
  },
  {
    title: '关联维度',
    dataIndex: 'dimensions',
    ellipsis: true,
    render: Columns({ type: 'dimensions' }),
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
]

const VIRTUAL_TABLE_DETAIL_PANELS_CHILDREN = {
  LIKE: [
    VIRTUAL_TABLE_DETAIL_PANELS.COLUMN_TYPE,
    VIRTUAL_TABLE_DETAIL_PANELS.DATA_PREVIEW,
    VIRTUAL_TABLE_DETAIL_PANELS.BLOOD_RELATION,
  ],
  AS: [
    VIRTUAL_TABLE_DETAIL_PANELS.ER_RELATION,
    VIRTUAL_TABLE_DETAIL_PANELS.COLUMN_TYPE,
    VIRTUAL_TABLE_DETAIL_PANELS.DATA_PREVIEW,
    // VIRTUAL_TABLE_DETAIL_PANELS.INDEX_RECOMMEND,
    VIRTUAL_TABLE_DETAIL_PANELS.BLOOD_RELATION,
  ],
}
function getDetailPanelTabList(computeType) {
  const isASVT = new RegExp(routerMap.dataModel.businessVirtualTableDetail.path).test(location.href)
  const VTType = isASVT ? 'AS' : 'LIKE'
  const childrenGroupIdxs = VIRTUAL_TABLE_DETAIL_PANELS_CHILDREN[VTType].filter((i) => {
    return computeType !== 'BATCH' ? i !== VIRTUAL_TABLE_DETAIL_PANELS.DATA_PREVIEW : true
  })
  const filterDetailTabItems = detailTabItems.filter((item) => childrenGroupIdxs.includes(+item.key))
  return (filterDetailTabItems || []).map((i) => ({ key: i.key, tab: i.label }))
}

function MoreItemsPopover({ data }: { data: string[] }) {
  const limit = 3
  const joinChar = ','
  if (!Array.isArray(data)) {
    return <>{data ? data : '-'}</>
  }
  if (data.length <= limit) {
    return <>{data.join(joinChar)}</>
  }
  const ctx = data.slice(0, limit).join(joinChar) + '等'
  const completeCtx = data.join(joinChar)
  return <Popover content={completeCtx}>{ctx}</Popover>
}

const common = [
  {
    label: '虚拟表目录',
    key: 'catalogName',
    render: (record: any) => {
      return get(record, 'catalogName', '-') || '-'
    },
  },
  {
    label: '虚拟表数据库',
    key: 'databaseName',
    render: (record: any) => {
      return get(record, 'databaseName', '-') || '-'
    },
  },
  {
    label: '创建时间',
    key: 'createTime',
    render: (record: any) => {
      const time = get(record, 'createTime')
      const createTime = time ? dayjs(new Date(time)).format('YYYY-MM-DD HH:mm:ss') : '-'
      return createTime
    },
  },
  {
    label: '创建人',
    key: 'creator',
    render: (record: any) => {
      return get(record, 'creator', '-') || '-'
    },
  },
  {
    label: '虚拟表类型',
    key: 'virtualTableType',
    render: (record: any) => {
      const virtualTableType = get(record, 'virtualTableType') as 'LIKE' | 'AS'
      const VTType = IVirtualTableType[virtualTableType]
      return VTType ? VTType : '-'
    },
  },
]

const tableInfo = {
  AS: [
    ...common,
    {
      label: '关联事实表',
      render: (data) => {
        const factTableArr = get(data?.dataModelDesc, 'factTable', '-').split('.') || ['-']
        return factTableArr[factTableArr.length - 1]
      },
    },
    {
      label: '时间键',
      key: 'timeColumn',
      render: (data) => {
        const timeColumn = get(data, 'timeColumn', '-')
        return <MoreItemsPopover data={timeColumn} />
      },
    },
  ],
  LIKE: [
    ...common,
    {
      label: '分区键（PARTITION BY）',
      key: 'partitionKey',
      render: (record: any) => {
        const partitionKeys = get(record, 'partitionKey', '-')
        return <MoreItemsPopover data={partitionKeys} />
      },
    },
  ],
  STREAM: [
    {
      label: '数据来源',
      key: 'source',
      render: (record: any) => {
        return record?.source
      },
    },
    {
      label: '来源topic',
      key: 'topic',
      render: (record: any) => {
        return record?.topic
      },
    },
    {
      label: '消费组',
      key: 'group',
      render: (record: any) => {
        return record?.group
      },
    },
    {
      label: '消费体格式',
      key: 'format',
      render: (record: any) => {
        return record?.format
      },
    },
    {
      label: 'partition数量',
      key: 'partition',
      render: (record: any) => {
        return record?.partition
      },
    },
    {
      label: '时间列',
      key: 'timeColumn',
      render: (record: any) => {
        return record?.timeColumn
      },
    },
    {
      label: '分片键',
      key: 'distributedKeys',
      render: (record: any) => {
        return record?.distributedKeys
      },
    },
  ],
}

function VirtualTableDetail() {
  const [searchParams] = useSearchParams()
  const tableName = searchParams.get('name') || ''
  const catalog = searchParams.get('catalog') || 'dipeak'
  const database = searchParams.get('database') || 'default'
  const navigate = useNavigate()
  // const { run: accelerate } = useRequest(Api.apiEngineV1MvAccelerateGet, {
  //     manual: true,
  // })

  const [vtableRawData, setVtableRawData] = useState()

  // column type panel
  const { tableProps: columnTypeProps, loading: getTableDetailLoading } = useAntdTable(
    useCallback(
      () =>
        Api.apiEngineV1VtableDetailGet({
          name: tableName,
          catalog,
          database,
        }).then((res) => {
          setVtableRawData(res)
          return {
            list: res.columns,
            name: res.name,
            total: res.columns.length ?? 0,
          }
        }),
      [tableName, catalog, database],
    ),
    {
      onError(e: any) {
        console.error(e?.msg || '获取虚拟表字段信息出错')
      },
      defaultPageSize: 10,
    },
  )
  // data preview
  const { data: previewData, tableProps: previewDataProps } = useAntdTable(
    useCallback(
      () =>
        Api.apiEngineV1VtablePreviewGet({
          vtableName: tableName,
          catalog,
          database,
          limit: 50,
        }).then((res) => ({
          list: res,
          total: res.length ?? 0,
        })),
      [tableName, catalog, database],
    ),
    {
      ready: Boolean(vtableRawData && vtableRawData.computeType === 'BATCH'),
      onError(e: any) {
        message.error(e?.msg || '获取虚拟表数据出错')
      },
      defaultPageSize: 10,
    },
  )
  detailTabItems[VIRTUAL_TABLE_DETAIL_PANELS.DATA_PREVIEW].children = (
    <Table
      key={VIRTUAL_TABLE_DETAIL_PANELS.DATA_PREVIEW}
      className={cs.dataPreviewTableWidth}
      {...previewDataProps}
      dataSource={previewData?.list}
      columns={Object.keys(previewData?.list[0] ?? []).map((e) => ({
        title: e,
        dataIndex: e,
        width: 300,
      }))}
      scroll={{ x: 'max-content' }}
      rowKey={() => getUnitId()}
    />
  )
  const { data: lineageData } = useRequest(Api.apiEngineV1VtableGetTableLineageGet, {
    defaultParams: [
      {
        catalogName: catalog,
        databaseName: database,
        tableName: tableName,
      },
    ],
  })
  // 字段类型
  detailTabItems[VIRTUAL_TABLE_DETAIL_PANELS.COLUMN_TYPE].children = (
    <Table
      key={VIRTUAL_TABLE_DETAIL_PANELS.COLUMN_TYPE}
      {...columnTypeProps}
      columns={columnTypeColumns}
      rowKey={(record) => record.id}
      scroll={{ x: 'max-content' }}
    />
  )
  // 关联模型
  detailTabItems[VIRTUAL_TABLE_DETAIL_PANELS.ER_RELATION].children = (
    <div style={{ height: '80vh' }}>
      <GLineage typeKey="type" data={formatERDataToGLineage(vtableRawData?.dataModelDesc?.joinDag)} />
    </div>
  )

  const { tableProps: metricsList } = useAntdTable(
    () => {
      return Api.apiEngineV1MetricsListPost({
        vtableMeta: {
          catalogName: catalog,
          databaseName: database,
          virtualTableName: tableName,
        },
        current: 1,
        pageSize: -1,
      })
    },
    {
      defaultPageSize: 10,
      defaultCurrent: 1,
    },
  )
  const { tableProps: MVTableProps } = useAntdTable(
    ({ current, pageSize }) => {
      return Api.apiEngineV1MetricsListPost({
        catalog: catalog,
        database: database,
        table: tableName,
        current,
        pageSize,
      })
    },
    {
      defaultPageSize: 10,
      defaultCurrent: 1,
    },
  )
  // 关联指标
  detailTabItems[VIRTUAL_TABLE_DETAIL_PANELS.INDEX_RECOMMEND].children = (
    <Table key={VIRTUAL_TABLE_DETAIL_PANELS.INDEX_RECOMMEND} columns={columns} rowKey={'id'} {...metricsList} />
  )

  // related mv
  detailTabItems[VIRTUAL_TABLE_DETAIL_PANELS.RELATED_MV].children = (
    <Table tableLayout="fixed" rowKey={'mvId'} columns={taskListColumns} {...MVTableProps} />
  )

  detailTabItems[VIRTUAL_TABLE_DETAIL_PANELS.BLOOD_RELATION].children = (
    <GLineage typeKey="type" data={formatVTLineageData(lineageData)} />
  )

  // detailTabItems[VIRTUAL_TABLE_DETAIL_PANELS.AUTH_MANAGE].children = (
  //     <AuthMange virtualTableName={tableName} catalogName={catalog} databaseName={database} />
  // )

  // detailTabItems[VIRTUAL_TABLE_DETAIL_PANELS.QUALITY_MONTOR].children = (
  //     <VirtualTableQuality catalog={catalog} database={database} vtable={tableName} />
  // )

  const [activeTabKey, setActiveTabKey] = useState<string>(`${VIRTUAL_TABLE_DETAIL_PANELS.COLUMN_TYPE}`)

  return (
    <>
      <PageHeader
        title={'虚拟表：' + tableName}
        onBack={() => {
          const path =
            vtableRawData.virtualTableType === 'AS'
              ? routerMap.dataModel.businessVirtualTable.path
              : routerMap.dataModel.virtualTable.path
          navigate(`${path}?catalog=${catalog}&database=${database}`)
        }}
      />
      {tableInfo[vtableRawData?.virtualTableType] ? (
        <LayoutCard>
          <Descriptions column={4} bordered>
            {tableInfo[vtableRawData?.virtualTableType]?.map((item, index: number) => {
              return (
                <Descriptions.Item label={item.label} key={index}>
                  {item.render ? item.render(vtableRawData) : get(vtableRawData, item.key) || '-'}
                </Descriptions.Item>
              )
            })}
          </Descriptions>
        </LayoutCard>
      ) : (
        ''
      )}
      <LayoutCard
        loading={getTableDetailLoading}
        tabList={getDetailPanelTabList(vtableRawData?.computeType)}
        activeTabKey={activeTabKey}
        tabBarExtraContent={
          parseInt(activeTabKey) === VIRTUAL_TABLE_DETAIL_PANELS.INDEX_RECOMMEND ? (
            <Button
              type="primary"
              onClick={() => navigate(routerMap.metricsManager.metricsCreate.path)}
              icon={<PlusOutlined />}
            >
              创建指标
            </Button>
          ) : null
        }
        onTabChange={(i) => {
          setActiveTabKey(i)
        }}
      >
        {detailTabItems[parseInt(activeTabKey)]?.children || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
      </LayoutCard>
    </>
  )
}

export default VirtualTableDetail
