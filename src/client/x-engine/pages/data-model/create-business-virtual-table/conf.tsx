import { pick } from 'lodash-es'
import PopConfirmSelect, { PropsType as PopConfirmSelectPropsType } from '@ui/popconfirm-select/popconfirm-select'
import { getCharFinalStr, getUnitId } from '@libs/util'

export interface NodeType {
    primaryKeys?: string[]
    foreignKeys?: string[]
    nodes?: { joinType: string; childNode: string; joinKeys: { sourceKey: string; targetKey: string }[] }[]
}
export interface VTableSelectParamsType {
    catalog: string
    database: string
    vTable: string
}
export type FactTableFormPropsType = VTableSelectParamsType & { dimensionsColumns: string[]; metricsColumns: string[] }
export interface ERDataType {
    vertices: {
        id: string
        table: string
        kind: 'FACT' | 'DIM'
        dimensionsColumns: string[]
        metricsColumns: string[]
        // 这里的primaryKeys只是colName的值
        primaryKeys?: string[]
        foreignKeys?: string[]
        commonColumns?: string[]
    }[]
    edges: {
        id?: string
        from: string
        to: string
        joinType: 'LEFT'
        // 一开始是数组只有一个值，后面传递给后端的时候再处理，顺序处理即是pks的第一个对应fks的一个！！！！
        // 后端的primaryKey是db.table.colname，所以在设置的时候已经设置上去了
        primaryKey: string
        foreignKey: string
    }[]
}

export type ERNodesTreeNodeType = {
    database?: string
    children?: ERNodesTreeNodeType[]
    parentJoinKeys?: ERDataType['edges']
    joinKeys?: ERDataType['edges']
} & NodeType &
    ERDataType['vertices'][0]

/**
 * 从ERNodesTreeNode结构体获得NodeModal的初始化值
 * @param ERNodesTree
 * @param id
 * @returns
 */
export function getNodeMoalInitFormValuesFromERNodesTreeNode(ERNodesTree: ERNodesTreeNodeType, id: string) {
    const curERNodesTreeNode = getTreeNodeByNodeId(ERNodesTree, id)
    if (!curERNodesTreeNode) {
        return {}
    }
    const { id: nodeId, table: dbTStr, primaryKeys, foreignKeys, joinKeys = [] } = curERNodesTreeNode
    const [database, table] = dbTStr.split('.')
    const nodesMap = {} as Record<
        string,
        { joinType: string; childNode: string; joinKeys: { sourceKey: string; targetKey: string }[] }
    >
    const length = joinKeys.length
    for (let i = 0; i < length; ++i) {
        const joinKeyItem = joinKeys[i]
        const { to, primaryKey, foreignKey, joinType } = joinKeyItem
        const toTable = getCharFinalStr(to)
        if (!nodesMap[to]) {
            nodesMap[to] = {
                joinType,
                childNode: toTable,
                joinKeys: [],
            }
        }
        nodesMap[to].joinKeys.push({
            sourceKey: foreignKey,
            targetKey: primaryKey,
        })
    }
    const nodes = Object.values(nodesMap)

    return {
        id: nodeId,
        table,
        database,
        primaryKeys,
        foreignKeys,
        nodes,
    }
}

// PopConfirmSelect组件的FormItem表单项组件
export const PopConfirmSelectFormItem: React.FC<{
    value?: any[]
    onChange?: (value: any[]) => any
    popConfirmSelectProps: PopConfirmSelectPropsType
}> = ({ value = {}, onChange, popConfirmSelectProps }) => {
    function triggerChange(changeValue: any[]) {
        onChange(changeValue)
    }
    return <PopConfirmSelect {...popConfirmSelectProps} value={value} onChange={triggerChange} />
}

/**
 * 获得VTable的列项
 * @param vTableList
 * @param VTable
 * @returns
 */
export function getColumnsAfterVTable(vTableList, VTable) {
    if (!Array.isArray(vTableList)) {
        return []
    }
    const VT = vTableList.find((vt) => vt.name === VTable) || {}
    return VT.columns || []
}

/**
 * GLineage format函数
 * 将ERDataType结构体的数据转化为GLineage组件可用的数据
 * 将api接口数据转换为GLineage组件可用数据，用于format显示数据
 * @param data
 * @returns
 */
export function formatERDataToLineage(data: ERDataType) {
    const { vertices: nodes, edges } = data
    if (!Array.isArray(nodes) || !Array.isArray(edges)) {
        return { nodes: [], edges: [] }
    }

    const ansEdges = edges.reduce((pre, e) => {
        const primaryKey = e.primaryKey
        const foreignKey = e.foreignKey
        const fromColName = getCharFinalStr(foreignKey, '.')
        const toColName = getCharFinalStr(primaryKey, '.')
        const es = {
            source: e.from,
            sourceKey: fromColName,
            target: e.to,
            targetKey: toColName,
        }
        pre.push(es)
        return pre
    }, [])

    const ansNodes = nodes.map((n) => {
        // 校验数据
        if (n.id === undefined || n.id === null) {
            return n
        }
        // primaryKey
        const primaryKeys = Array.isArray(n.primaryKeys) ? n.primaryKeys : []

        // foreignKeys
        const foreignKeys = Array.isArray(n.foreignKeys) ? n.foreignKeys : []

        const selfCommonColumns = (n.commonColumns = [...new Set(primaryKeys.concat(foreignKeys))].filter(
            (k) => !(n.dimensionsColumns.includes(k) || n.metricsColumns.includes(k)),
        ))

        // 操作数据
        // dimensionColumns 是 直接column的数组
        const dimensionsColumns = (n.dimensionsColumns || [])
            .sort((a, b) => {
                return (a ?? '').toLowerCase().localeCompare((b ?? '').toLowerCase())
            })
            .map((col) => {
                const colName = col || ''
                const isPk = primaryKeys.includes(colName)
                const isFk = foreignKeys.includes(colName)
                const portExtraText = isPk ? 'pk' : isFk ? 'fk' : ''
                return { name: colName, subInfo: [portExtraText] }
            })

        const metricsColumns = (n.metricsColumns || [])
            .sort((a, b) => {
                return (a ?? '').toLowerCase().localeCompare((b ?? '').toLowerCase())
            })
            .map((col) => {
                const colName = col || ''
                const isPk = primaryKeys.includes(colName)
                const isFk = foreignKeys.includes(colName)
                const portExtraText = isPk ? 'pk' : isFk ? 'fk' : ''
                return { name: colName, subInfo: portExtraText }
            })

        const commonColumns = selfCommonColumns
            .sort((a, b) => {
                return a.toLowerCase().localeCompare(b.toLowerCase())
            })
            .map((col) => {
                const isPk = primaryKeys.includes(col)
                const isFk = foreignKeys.includes(col)
                const portExtraText = isPk ? 'pk' : isFk ? 'fk' : ''
                return {
                    name: col || '',
                    subInfo: portExtraText,
                }
            })

        return {
            id: n.id,
            table: n.table,
            type: n.kind,
            dimensionsColumns,
            metricsColumns,
            commonColumns,
            // pks: primaryKeys,
            // fks: foreignKeys.filter((k) => !primaryKeys.includes(k)),
        }
    })
    return {
        nodes: ansNodes,
        edges: ansEdges,
    }
}

/**
 * 将ERDataType结构体的数据转化为可传递给后端的标准数据
 * @param data
 * @returns
 */
export function formatERDataToExactJoinDag(data: ERDataType) {
    const { edges = [], vertices = [] } = data
    const edgesMap: Record<string, any> = {}
    const ansVertices = vertices.map((v) => {
        return {
            id: v.id,
            table: v.table,
            kind: v.kind,
            dummy: false,
            dimensionsColumns: (v.dimensionsColumns || []).map((col) => ({
                name: `${v.table}.${col}`,
            })),
            metricsColumns: (v.metricsColumns || []).map((col) => ({
                name: `${v.table}.${col}`,
            })),
        }
    })
    let joinType = ''
    edges.forEach((e) => {
        const nodeLink = `${e.from}>>${e.to}`
        if (!edgesMap[nodeLink]) {
            edgesMap[nodeLink] = {
                primaryKeys: [],
                foreignKeys: [],
            }
        }
        const [, fromdb, fromVT] = (e.from || '').split('.')
        const [, todb, toVT] = (e.to || '').split('.')
        joinType = e.joinType
        if (fromdb && fromVT && todb && toVT) {
            const primaryKey = `${todb}.${toVT}.${e.primaryKey}`
            const foreignKey = `${fromdb}.${fromVT}.${e.foreignKey}`
            edgesMap[nodeLink].primaryKeys.push({ name: primaryKey })
            edgesMap[nodeLink].foreignKeys.push({ name: foreignKey })
        }
    })
    const ansEdges = (Object.keys(edgesMap) || []).map((nodeLink) => {
        const [from, to] = nodeLink.split('>>')
        const { primaryKeys = [], foreignKeys = [] } = edgesMap[nodeLink]
        return {
            id: `e${getUnitId()}`,
            from,
            to,
            joinType: joinType,
            primaryKeys: primaryKeys,
            foreignKeys: foreignKeys,
            properties: {},
        }
    })
    return {
        vertices: ansVertices,
        edges: ansEdges,
    }
}

/**
 * 根据nodeId从TreeNode获得node
 * @param ERNodesTree
 * @param nodeId
 * @returns
 */
export function getTreeNodeByNodeId(
    ERNodesTree: ERNodesTreeNodeType | null,
    nodeId: string,
): ERNodesTreeNodeType | null {
    if (!ERNodesTree) {
        return null
    }
    const { id, children } = ERNodesTree
    if (id === nodeId) {
        return ERNodesTree
    }
    if (!Array.isArray(children)) {
        return null
    }
    for (let i = 0; i < children.length; ++i) {
        const ansNodeTree = getTreeNodeByNodeId(children[i], nodeId)
        if (ansNodeTree) {
            return ansNodeTree
        }
    }
    return null
}

/**
 * 从ERNodesTreeNodeType的类似树结构数据中获取nodeId层及以上的node节点
 * @param ERNodesTree
 * @param nodeId
 * @returns
 */
export function getParentNodes(ERNodesTree: ERNodesTreeNodeType, nodeId: string): ERNodesTreeNodeType[] {
    const parentNodes: ERNodesTreeNodeType[] = []
    const queue = [ERNodesTree]
    let isFindLayer = false
    while (queue.length !== 0 && !isFindLayer) {
        let size = queue.length
        while (size--) {
            const ERNode = queue.shift()
            ERNode && parentNodes.push(ERNode)
            const { children, id } = ERNode || {}
            if (id !== nodeId) {
                if (Array.isArray(children)) {
                    const length = children.length
                    for (let i = 0; i < length; ++i) {
                        children[i] && queue.push(children[i])
                    }
                }
            } else {
                isFindLayer = true
            }
        }
    }
    return parentNodes
}

/**
 * 遍历整个ERNodesTreeNodeType结构体获得全部的node的id值，去矫正ERTree
 * @param ERNodesTree
 * @returns
 */
export function traverseERNodesTreeToGetNodeIds(ERNodesTree: ERNodesTreeNodeType | Record<string, never>) {
    const { id, children } = ERNodesTree
    if (!id || typeof id !== 'string') {
        return []
    }
    const nodeIds = [id]
    if (!Array.isArray(children)) {
        return nodeIds
    }
    const length = children.length
    for (let i = 0; i < length; ++i) {
        const ids = traverseERNodesTreeToGetNodeIds(children[i])
        nodeIds.push(...ids)
    }
    return nodeIds
}

/**
 * 清除不含在nodeIds中的parentJoinKeys, joinKeys的项
 * @param ERNodesTree
 * @param nodeIds
 * @returns
 */
export function clearInexistNodes(ERNodesTree: ERNodesTreeNodeType | Record<string, never>, nodeIds: string[]) {
    if (!Array.isArray(nodeIds) || nodeIds.length <= 0) {
        return
    }
    const { parentJoinKeys, joinKeys, children } = ERNodesTree
    if (Array.isArray(parentJoinKeys)) {
        const resParentJoinKeys = parentJoinKeys.filter((e) => nodeIds.includes(e.from) && nodeIds.includes(e.to))
        ERNodesTree.parentJoinKeys = resParentJoinKeys
    }
    if (Array.isArray(joinKeys)) {
        const resJoinKeys = joinKeys.filter((e) => nodeIds.includes(e.from) && nodeIds.includes(e.to))
        ERNodesTree.joinKeys = resJoinKeys
    }
    if (Array.isArray(children)) {
        const length = children.length
        for (let i = 0; i < length; ++i) {
            clearInexistNodes(children[i], nodeIds)
        }
    }
}
/**
 * 提交表单后，更新对应的ERNodesTree的值
 * @param ERNodesTree
 * @param updateTreeNode
 * @returns
 */
// 原地改变值，parent.key = value 改变parent.key的值
export function updateERTreeNode(
    ERNodesTree: ERNodesTreeNodeType | Record<string, never>,
    updateTreeNode: ERDataType['vertices'][0] & {
        deleteChildNodeJoinKeys: Partial<ERNodesTreeNodeType>[]
        addChildNodesJoinKeys: ERNodesTreeNodeType[]
    },
): ERNodesTreeNodeType | Record<string, never> {
    const { id: targetId } = updateTreeNode
    const { id, children = [] } = ERNodesTree
    if (!targetId || typeof targetId !== 'string') {
        return null
    }
    if (id === targetId) {
        // console.log('updateTreeNode______', updateTreeNode, targetId)
        Object.keys(updateTreeNode).forEach((key) => {
            switch (key) {
                case 'metricsColumns':
                case 'dimensionsColumns':
                case 'primaryKeys': {
                    ERNodesTree[key] = updateTreeNode[key]
                    break
                }
                case 'foreignKeys': {
                    ERNodesTree[key] = updateTreeNode[key].filter((k) => !ERNodesTree.primaryKeys.includes(k))
                    break
                }
                case 'addChildNodesJoinKeys': {
                    const addChildNodesJoinKeys = updateTreeNode.addChildNodesJoinKeys
                    for (let i = 0; i < addChildNodesJoinKeys.length; ++i) {
                        const addChildNode = addChildNodesJoinKeys[i]
                        const { id, parentJoinKeys } = addChildNode
                        let targetChildNode = children.find((n) => n.id === id)
                        const isChildNodeFlag = !!targetChildNode
                        if (!targetChildNode) {
                            const node = getTreeNodeByNodeId(ERNodesTree as ERNodesTreeNodeType, id)
                            targetChildNode = node ? node : addChildNode
                            if (!Array.isArray(targetChildNode.parentJoinKeys)) {
                                targetChildNode.parentJoinKeys = []
                            }
                            children.push(targetChildNode)
                            ERNodesTree.children = children
                        }
                        if (!ERNodesTree.joinKeys) {
                            ERNodesTree.joinKeys = []
                        }
                        ERNodesTree.joinKeys.push(...parentJoinKeys)
                        const originParentJoinKeys = targetChildNode.parentJoinKeys || []
                        targetChildNode.parentJoinKeys = originParentJoinKeys.concat(
                            isChildNodeFlag ? addChildNode.parentJoinKeys : [],
                        )
                        targetChildNode.primaryKeys = targetChildNode.parentJoinKeys.map((e) => e.primaryKey) || []
                    }
                    break
                }
                case 'deleteChildNodeJoinKeys': {
                    const deleteChildNodeJoinKeys = updateTreeNode.deleteChildNodeJoinKeys
                    for (let i = 0; i < deleteChildNodeJoinKeys.length; ++i) {
                        const deleteChildNode = deleteChildNodeJoinKeys[i]
                        const { id, parentJoinKeys } = deleteChildNode
                        const targetChildNodeIdx = children.findIndex((n) => n.id === id)
                        const targetChildNode = children[targetChildNodeIdx]
                        if (!targetChildNode) {
                            continue
                        }
                        const originParentJoinKeys = targetChildNode.parentJoinKeys
                        // parentJoinKeys的to是本身，joinKeys的from是本身，此两项是固定的
                        const resParentJoinKeys = originParentJoinKeys.filter(
                            (e) =>
                                !parentJoinKeys.find(
                                    (delE) =>
                                        e.from === targetId &&
                                        delE.foreignKey === e.foreignKey &&
                                        delE.primaryKey === e.primaryKey,
                                ),
                        )
                        targetChildNode.parentJoinKeys = resParentJoinKeys

                        const isLinked = !!resParentJoinKeys.find((e) => e.from === targetId && e.primaryKey !== 'dt')
                        if (!isLinked) {
                            ERNodesTree.children = (children.slice(0, targetChildNodeIdx) || []).concat(
                                children.slice(targetChildNodeIdx + 1) || [],
                            )
                            ERNodesTree.joinKeys = ERNodesTree.joinKeys.filter(
                                (e) =>
                                    !parentJoinKeys.find(
                                        (delE) =>
                                            e.to === delE.to &&
                                            ((delE.foreignKey === e.foreignKey && delE.primaryKey === e.primaryKey) ||
                                                (e.foreignKey === 'dt' && e.primaryKey === 'dt')),
                                    ),
                            )
                            targetChildNode.parentJoinKeys = targetChildNode.parentJoinKeys.filter(
                                (e) => !(e.from === targetId && e.primaryKey === 'dt' && e.foreignKey === 'dt'),
                            )
                            // todo：直接遍历树，获得ids；然后再遍历树，删除不存在的joinKeys、parentJoinKeys；from和to不能是不存在的id
                            // 游离节点进行级联删除
                            const nodeIds = traverseERNodesTreeToGetNodeIds(ERNodesTree)
                            clearInexistNodes(ERNodesTree, nodeIds)
                        } else {
                            ERNodesTree.joinKeys = ERNodesTree.joinKeys.filter(
                                (e) =>
                                    !parentJoinKeys.find(
                                        (delE) =>
                                            e.to === delE.to &&
                                            delE.foreignKey === e.foreignKey &&
                                            delE.primaryKey === e.primaryKey,
                                    ),
                            )
                        }
                        targetChildNode.primaryKeys = targetChildNode.parentJoinKeys.map((e) => e.primaryKey) || []
                    }
                }
            }
        })
        if (!ERNodesTree.primaryKeys.includes('dt') && !ERNodesTree.foreignKeys.includes('dt')) {
            ERNodesTree.foreignKeys.push('dt')
        }
        return ERNodesTree as ERNodesTreeNodeType
    }
    const length = children.length
    for (let i = 0; i < length; ++i) {
        id !== targetId && updateERTreeNode(children[i], updateTreeNode)
    }
    // 返回ERData需要用到的节点值
    return ERNodesTree
}

/**
 * 将ERNodesTree转化为ERData的格式
 * @param ERNodesTree
 * @returns
 */
export function transformERNodesTreeToERData(
    ERNodesTree: ERNodesTreeNodeType | Record<string, never> | null,
): ERDataType {
    if (!ERNodesTree) {
        return { vertices: [], edges: [] }
    }
    const { id, children, joinKeys } = ERNodesTree
    if (!id || typeof id !== 'string') {
        return { vertices: [], edges: [] }
    }
    const vertices: ERDataType['vertices'] = []
    const edges: ERDataType['edges'] = Array.isArray(joinKeys) ? [...joinKeys] : []
    const vertice: ERDataType['vertices'][0] = pick(ERNodesTree, [
        'id',
        'table',
        'kind',
        'dimensionsColumns',
        'metricsColumns',
        'primaryKeys',
        'foreignKeys',
    ])
    vertices.push(vertice)
    if (Array.isArray(children)) {
        const length = children.length
        for (let i = 0; i < length; ++i) {
            const { edges: es = [], vertices: vs = [] } = transformERNodesTreeToERData(children[i])
            vertices.push(...vs)
            edges.push(...es)
        }
    }
    return {
        edges,
        vertices,
    }
}
