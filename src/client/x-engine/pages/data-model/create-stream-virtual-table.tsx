// @ts-nocheck
import { PageHeader } from '@ant-design/pro-layout'
import { Button, Col, Form, Input, message, Result, Row, Select } from 'antd'
import React, { useState } from 'react'
import LayoutCard from '@ui/layoutCard/LayoutCard'
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom'
import DbWithTable from '../../../pages/MetricStore/ExternalDatasource/components/DbWithTable'
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons'
import { useRequest } from 'ahooks'
import { Api } from '@api'
import { columnTypeList } from './forms-conf/constant-conf'
// import { catalogTypeList, catalogFormMap } from './conf/constant'
import TimeDuration from 'src/client/components/TimeDuration'

const Option = Select.Option
export default function CatalogUpdate() {
  const location = useLocation()
  const [searchParams] = useSearchParams()
  const mode = searchParams.get('mode')
  const catalog = searchParams.get('catalog')
  const database = searchParams.get('database')
  const name = searchParams.get('table')

  const navigate = useNavigate()

  const [form] = Form.useForm()
  const [catalogName, setCatalogName] = useState<string>()
  const [success, setSuccess] = useState<boolean>(false)

  const continueTask = () => {
    setSuccess(false)
    if (!location.state.catalogName) {
      form.resetFields()
      setCatalogName('')
    }
  }

  const { data: tableDetail } = useRequest(
    () =>
      Api.apiEngineV1PtableDetailGet({
        catalog,
        database,
        name,
      }),
    {
      ready: mode === 'edit',
      onSuccess: (data) => {
        form.setFieldsValue({
          catalogName: data.catalogName,
          properties: {
            'kafka.topic.list': data.properties?.['kafka.topic.list'],
            'kafka.format': data.properties?.['kafka.format'],
            'kafka.time.column': data.properties?.['kafka.time.column'],
          },
          name: data.name,
          columns: data.columns,
        })
      },
    },
  )
  const { run: updateTable } = useRequest(
    () => {
      const data = form.getFieldsValue()
      return Api.apiEngineV1PtablePut({
        table: {
          id: tableDetail.id,
          catalogName: data.catalogName,
          databaseName: 'default',
          name: data.name,
          tableEngine: 'Kafka',
          columns: data.columns,
          properties: data.properties,
          tableType: 'stream',
        },
      })
    },
    {
      manual: true,
      ready: mode === 'edit',
    },
  )

  const { data: kafkaList } = useRequest(() => Api.apiEngineV1DatasourceCatalogListGet({ type: 'kafka' }), {})
  const { run: createStreamTable, loading: isCreateStreamTableLoading } = useRequest(
    (data) => {
      return Api.apiEngineV1PtablePost({
        table: {
          catalogName: data.catalogName,
          databaseName: 'default',
          name: data.name,
          tableEngine: 'Kafka',
          columns: data.columns,
          properties: data.properties,
          tableType: 'stream',
        },
        cascadeCreateVTable: false,
      })
    },
    {
      manual: true,
      onSuccess: () => {
        message.success('创建成功')
        navigate(-1)
      },
    },
  )

  return (
    <>
      <PageHeader title={`${mode === 'edit' ? '编辑' : '新建'}流式表`} onBack={() => window.history.back()} />
      <LayoutCard>
        {success ? (
          <Result
            status="success"
            title={(location.state?.catalogName ? '修改' : '创建') + '数据源成功'}
            extra={[
              <Button type="primary" onClick={() => history.back()}>
                返回上级
              </Button>,
              <Button onClick={continueTask}>{location.state?.catalogName ? '继续修改' : '继续创建'}</Button>,
            ]}
          />
        ) : (
          <Form
            form={form}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 16 }}
            style={{ maxWidth: 600 }}
            onFinish={(val) => {
              mode === 'edit' ? updateTable(val) : createStreamTable(val)
            }}
          >
            <Form.Item
              label="Kafka数据源"
              name="catalogName"
              rules={[{ required: true, message: '请选择Kafka数据源' }]}
            >
              <Select placeholder="请选择Kafka数据源">
                {kafkaList?.list?.map?.((i) => {
                  return <Option value={i.name}>{i.name}</Option>
                })}
              </Select>
            </Form.Item>
            <Form.Item
              label="Topic"
              name={['properties', 'kafka.topic.list']}
              rules={[{ required: true, message: '请输入Topic' }]}
            >
              <Input />
            </Form.Item>

            <Form.Item
              label="消费体格式"
              name={['properties', 'kafka.format']}
              initialValue="JSON"
              rules={[{ required: true, message: '请输入消费体格式' }]}
            >
              <Select>
                <Option value="JSON">JSON</Option>
              </Select>
            </Form.Item>

            <Form.Item
              label="表名称"
              name="name"
              rules={[
                { required: true, message: '请输入虚拟表名称' },
                {
                  pattern: new RegExp(/[A-Za-z_0-9]+$/, 'g'),
                  message: '支持英文、数字、下划线',
                },
              ]}
            >
              <Input />
            </Form.Item>

            <Form.Item label="字段" required>
              <Form.List name="columns" initialValue={[{ name: '' }]}>
                {(fields, { add, remove }) => {
                  return (
                    <>
                      {fields.map(({ name, ...restField }) => (
                        <div>
                          <Row style={{ width: '100%', display: 'flex', marginBottom: 8 }} gutter={[10, 10]}>
                            <Col flex={1}>
                              <Form.Item noStyle shouldUpdate>
                                {() => (
                                  <Form.Item
                                    {...restField}
                                    name={[name, 'name']}
                                    rules={[
                                      {
                                        required: true,
                                        message: '请输入字段名',
                                      },
                                    ]}
                                  >
                                    <Input placeholder="请输入字段名" />
                                  </Form.Item>
                                )}
                              </Form.Item>
                            </Col>
                            <Col flex={1}>
                              <Form.Item
                                {...restField}
                                name={[name, 'columnType']}
                                rules={[
                                  {
                                    required: true,
                                    message: '请选择字段类型',
                                  },
                                ]}
                              >
                                <Select
                                  placeholder={'字段类型'}
                                  options={columnTypeList.map((e) => ({
                                    label: e.desc,
                                    value: e.value,
                                  }))}
                                />
                              </Form.Item>
                            </Col>
                          </Row>
                          <Row style={{ width: '100%', display: 'flex', marginBottom: 8 }}>
                            <Col flex={'90%'}>
                              <Form.Item {...restField} name={[name, 'comment']}>
                                <Input.TextArea rows={1} placeholder="请输入注释" />
                              </Form.Item>
                            </Col>
                            <Col flex={'10%'} className="mt-[8px] flex items-baseline justify-center">
                              <MinusCircleOutlined
                                className="ml-1"
                                onClick={() => {
                                  if (fields.length > 1) {
                                    remove(name)
                                  } else {
                                    message.error('最少需要一个字段')
                                  }
                                }}
                              />
                            </Col>
                          </Row>
                        </div>
                      ))}
                      <Row className="w-full">
                        <Col span={16}>
                          <Form.Item>
                            <Button type="dashed" onClick={() => add({})} block icon={<PlusOutlined />}>
                              添加字段
                            </Button>
                          </Form.Item>
                        </Col>
                      </Row>
                    </>
                  )
                }}
              </Form.List>
            </Form.Item>

            <Form.Item shouldUpdate noStyle>
              {({ getFieldValue }) => {
                return (
                  <Form.Item
                    label="时间列"
                    name={['properties', 'kafka.time.column']}
                    rules={[{ required: true, message: '请选择时间列' }]}
                  >
                    <Select>
                      {getFieldValue('columns').map((i) => {
                        return <Option value={i.name}>{i.name}</Option>
                      })}
                    </Select>
                  </Form.Item>
                )
              }}
            </Form.Item>
            <Form.Item
              label="watermark时配置"
              name={['properties', 'kafka.watermark']}
              rules={[{ required: true, message: '请配置watermark' }]}
            >
              <TimeDuration />
            </Form.Item>
            <Form.Item wrapperCol={{ offset: 6, span: 14 }}>
              <Button type="primary" htmlType="submit" loading={isCreateStreamTableLoading}>
                {mode === 'edit' ? '保存' : '创建'}
              </Button>
              <Button
                style={{ marginLeft: 8 }}
                onClick={() => {
                  navigate(-1)
                }}
              >
                取消
              </Button>
            </Form.Item>
          </Form>
        )}
      </LayoutCard>

      <DbWithTable catalogName={catalogName || ''} />
    </>
  )
}
