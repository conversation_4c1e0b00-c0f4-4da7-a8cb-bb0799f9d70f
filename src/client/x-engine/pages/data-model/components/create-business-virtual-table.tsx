// @ts-nocheck
/* eslint-disable no-mixed-spaces-and-tabs */
import { useState } from 'react'
import {
  Empty,
  message,
  Button,
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  Result,
  Row,
  Select,
  Steps,
  Tabs,
  Typography,
} from 'antd'
import { Link } from 'react-router-dom'
import { routerMap } from '@XEngineRouter/routerMap'
import { createBusinessVirtualTableStepItemsText } from '@pages/data-model/forms-conf/constant-conf'
import { Content } from 'antd/es/layout/layout'
import cs from '@pages/data-model/data-model.module.scss'
import { PageHeader } from '@ant-design/pro-layout'
import DBSelect from '@model/DBSelect'
import { Api } from '@api'
import { SaveOutlined } from '@ant-design/icons'
import { useRequest } from 'ahooks'
import { useImmer } from 'use-immer'
import moment from 'moment'
import RootGraph from '@model/VTgraph'
import LayoutCard from '@ui/layoutCard/LayoutCard'

const { Option } = Select
const { Title } = Typography

enum ITableTypeEnum {
  multiTableLike = 'multiTableLike',
  streamBatchJoin = 'streamBatchJoin',
}

export enum ICreateTableStep {
  chooseTableType,
  makeTableModel,
  configTable,
  createTableSuccess,
}

const templateList = [
  {
    title: '多表关联',
    key: ITableTypeEnum.multiTableLike,
    desc: 'Select * from A join B on A.c1 = B.c1',
  },
  {
    title: '流批融合',
    key: ITableTypeEnum.streamBatchJoin,
    desc: '流表 1 批表 1 => 流批一体表 1',
  },
]

enum IOriginTableTypeEnum {
  batch = 'batch',
  stream = 'stream',
}

export const CreateBusinessVirtualTable = () => {
  /**
   * @kongsa 提升状态到这个组件
   */
  const [multiReqProps, setMultiReqProps] = useState<{
    catalog: string
    database: string
    joinVO: any
    columns: any
    timeColumnList: string[]
  }>()

  const [createBusinessVirtualTableStep, setCreateBusinessVirtualTableStep] = useState<number>(
    ICreateTableStep.chooseTableType,
  )
  const [tableType, setTableType] = useState<keyof typeof ITableTypeEnum>(ITableTypeEnum.streamBatchJoin)

  const [streamTableForm] = Form.useForm()
  const [batchTableForm] = Form.useForm()
  const [integrateTableConfigForm] = Form.useForm()
  const [multiLinkTableConfigForm] = Form.useForm()

  const [VTableList, setVTableList] = useImmer({
    [IOriginTableTypeEnum.batch]: [],
    [IOriginTableTypeEnum.stream]: [],
  })
  const { run: createStreamBatchTable, loading: createStreamBatchTableLoading } = useRequest(
    Api.apiEngineV1VtablePost,
    {
      manual: true,
      onSuccess() {
        setCreateBusinessVirtualTableStep(ICreateTableStep.createTableSuccess)
        message.success('创建虚拟表成功')
      },
    },
  )

  const { run: createMultiTable, loading: createMultiTbaleLoading } = useRequest(Api.apiEngineV1VtablePost, {
    manual: true,
    onSuccess() {
      setCreateBusinessVirtualTableStep(ICreateTableStep.createTableSuccess)
      message.success('创建虚拟表成功')
    },
  })

  const { run: getVtableList } = useRequest(
    (args) =>
      Api.apiEngineV1VtableListGet({
        current: 1,
        pageSize: -1,
        ...args,
      }),
    {
      manual: true,
      onSuccess(data, args) {
        setVTableList((draft) => {
          draft[args?.[0]?.type] = data.list
        })
      },
    },
  )

  const resetFormAndGetVtableOptions = (form, type, val) => {
    form.resetFields(['table'])
    if (val.database?.length) {
      const { catalog, database } = form.getFieldsValue(['catalog', 'database'])
      getVtableList({
        type,
        catalog,
        database,
      })
    }
  }

  const isSameTableConstruct = (table1, table2) => {
    const table1ColumnNameCollect = {}
    if (table2.columns.length === table1.columns.length) {
      let res = true
      table1.columns.forEach((i) => {
        table1ColumnNameCollect[i.name] = true
      })
      table2.columns.find((i) => {
        if (table1ColumnNameCollect[i.name] !== true) {
          res = false
          return true
        }
      })
      return res
    }
    return false
  }

  const isSameTable = (table1, table2) => {
    return (
      table1.catalogName === table2.catalogName &&
      table1.databaseName === table2.databaseName &&
      table1.id === table2.id
    )
  }

  function getOptionsList(type: keyof typeof IOriginTableTypeEnum) {
    const { batchTable, streamTable } = getStreamAndBatchTable()
    if (type === IOriginTableTypeEnum.stream) {
      const t = batchTable
        ? VTableList[IOriginTableTypeEnum.stream].filter((i) => {
            return isSameTableConstruct(i, batchTable) && !isSameTable(i, batchTable)
          })
        : VTableList[IOriginTableTypeEnum.stream]
      return t.filter((j) => {
        return j.computeType === type.toUpperCase()
      })
    } else if (type === IOriginTableTypeEnum.batch) {
      const t = streamTable
        ? VTableList[IOriginTableTypeEnum.batch].filter((i) => {
            return isSameTableConstruct(i, streamTable) && !isSameTable(i, streamTable)
          })
        : VTableList[IOriginTableTypeEnum.batch]
      return t.filter((j) => {
        return j.computeType === type.toUpperCase()
      })
    }
  }

  function getStreamAndBatchTable() {
    const batchTableId = batchTableForm.getFieldValue('tableName')
    const streamTableId = streamTableForm.getFieldValue('tableName')
    const batchTable = VTableList[IOriginTableTypeEnum.batch].find((i) => {
      return i.id === batchTableId
    })

    const streamTable = VTableList[IOriginTableTypeEnum.stream].find((i) => {
      return i.id === streamTableId
    })
    return { batchTable, streamTable }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [forceUpdateCount, setForceUpdateCount] = useState(0)

  const tableChange = () => {
    setForceUpdateCount((pre) => pre + 1)
  }

  const getTimeColumnOptions = () => {
    const { batchTable, streamTable } = getStreamAndBatchTable()
    return [
      ...(batchTable?.columns ?? []).map((i) => {
        return `${batchTable.name}.${i.name}`
      }),
      ...(streamTable?.columns ?? []).map((j) => {
        return `${streamTable.name}.${j.name}`
      }),
    ]
  }

  const createBusinessVirtualTableTabItems: any = [
    {
      key: ICreateTableStep.chooseTableType.toString(),
      children: (
        <div>
          <Title level={5}>选择表模型</Title>
          <br />
          {templateList.map(({ key, title, desc }) => {
            return (
              <Card
                className={cs.businessVirtualTableCardItem}
                onClick={() => {
                  setTableType(key)
                  setCreateBusinessVirtualTableStep(ICreateTableStep.makeTableModel)
                }}
                title={title}
                hoverable
                headStyle={{
                  minHeight: '24px',
                  padding: '14px 24px 8px 24px',
                }}
                bodyStyle={{ padding: '0px 16px 14px 16px' }}
              >
                <div className={cs.businessVirtualTableDesc}>{desc}</div>
              </Card>
            )
          })}
        </div>
      ),
    },
    {
      key: ICreateTableStep.makeTableModel.toString(),
      children: (
        <>
          {tableType === ITableTypeEnum.streamBatchJoin ? (
            <>
              <div style={{ padding: '0px 216px' }}>
                <Form
                  layout="vertical"
                  form={streamTableForm}
                  autoComplete="off"
                  onFinish={() => setCreateBusinessVirtualTableStep(ICreateTableStep.configTable)}
                >
                  <h3>选择流表</h3>
                  <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <DBSelect
                      span={8}
                      // params={requestParams}
                      setParams={(val) =>
                        resetFormAndGetVtableOptions(streamTableForm, IOriginTableTypeEnum.stream, val)
                      }
                      setFieldValue={(key) => {
                        streamTableForm.resetFields([key])
                      }}
                    />
                    <Col span={8}>
                      <Form.Item
                        className={cs.businessVirtualTableFormItemWithBottom}
                        name="tableName"
                        label="数据表"
                        rules={[
                          {
                            required: true,
                            message: '请选择数据表',
                          },
                        ]}
                        tooltip={'当前限制流批表结构一致'}
                      >
                        <Select
                          allowClear
                          getPopupContainer={(triggerNode) => triggerNode.parentElement}
                          placeholder={'请选择数据表'}
                          onChange={tableChange}
                          notFoundContent={
                            <Empty
                              image={Empty.PRESENTED_IMAGE_SIMPLE}
                              description={VTableList[IOriginTableTypeEnum.stream]?.length ? '无匹配表' : 'No data'}
                            />
                          }
                        >
                          {getOptionsList(IOriginTableTypeEnum.stream).map((i) => {
                            return (
                              <Option key={i.id} value={i.id}>
                                {i.name}
                              </Option>
                            )
                          })}
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
                <Form
                  layout="vertical"
                  form={batchTableForm}
                  autoComplete="off"
                  onFinish={() => setCreateBusinessVirtualTableStep(ICreateTableStep.configTable)}
                >
                  <h3>选择批表</h3>
                  <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <DBSelect
                      span={8}
                      // params={requestParams}
                      setParams={(val) => resetFormAndGetVtableOptions(batchTableForm, IOriginTableTypeEnum.batch, val)}
                      setFieldValue={(key) => {
                        batchTableForm.resetFields([key])
                      }}
                    />
                    <Col span={8}>
                      <Form.Item
                        className={cs.businessVirtualTableFormItemWithBottom}
                        name="tableName"
                        label="数据表"
                        rules={[
                          {
                            required: true,
                            message: '请选择数据表',
                          },
                        ]}
                        tooltip={'当前限制流批表结构一致'}
                      >
                        <Select
                          allowClear
                          getPopupContainer={(triggerNode) => triggerNode.parentElement}
                          placeholder={'请选择数据表'}
                          onChange={tableChange}
                          notFoundContent={
                            <Empty
                              image={Empty.PRESENTED_IMAGE_SIMPLE}
                              description={VTableList[IOriginTableTypeEnum.batch]?.length ? '无匹配表' : 'No data'}
                            />
                          }
                        >
                          {getOptionsList(IOriginTableTypeEnum.batch).map((i) => {
                            return (
                              <Option key={i.id} value={i.id}>
                                {i.name}
                              </Option>
                            )
                          })}
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
              </div>
              <div className={cs.btnWrapper}>
                <Button
                  onClick={() => {
                    batchTableForm.resetFields()
                    streamTableForm.resetFields()
                  }}
                >
                  重置
                </Button>
                <Button onClick={() => setCreateBusinessVirtualTableStep(ICreateTableStep.chooseTableType)}>
                  上一步
                </Button>
                <Button
                  onClick={() => {
                    Promise.all([streamTableForm.validateFields(), batchTableForm.validateFields()]).then(() => {
                      setCreateBusinessVirtualTableStep(ICreateTableStep.configTable)
                    })
                  }}
                  type="primary"
                  icon={<SaveOutlined />}
                >
                  下一步
                </Button>
              </div>
            </>
          ) : (
            <div>
              <RootGraph
                params={multiReqProps}
                setParams={setMultiReqProps}
                setStep={setCreateBusinessVirtualTableStep}
              />
            </div>
          )}
        </>
      ),
    },
    {
      key: ICreateTableStep.configTable.toString(),
      children: (
        <>
          {tableType === ITableTypeEnum.streamBatchJoin ? (
            <Form
              preserve={false}
              form={integrateTableConfigForm}
              onFinish={(val) => {
                const { timeColumn, timePoint, name, catalog, database } = val
                const { streamTable, batchTable } = getStreamAndBatchTable()
                createStreamBatchTable({
                  table: {
                    catalogName: catalog,
                    databaseName: database,
                    timeColumn,
                    name,
                    computeType: 'STREAM_AND_BATCH',
                    virtualTableType: 'AS',
                    streamBatchVO: {
                      timePoint: moment(timePoint).valueOf().toString(),
                    },
                    unionVO: {
                      selectQueryList: [
                        {
                          database: streamTable.databaseName,
                          catalog: streamTable.catalogName,
                          tableName: streamTable.name,
                          selectColumns: streamTable.columns.map((i) => {
                            return {
                              name: i.name,
                              computeType: i.columnType,
                            }
                          }),
                        },
                        {
                          database: batchTable.databaseName,
                          catalog: batchTable.catalogName,
                          tableName: batchTable.name,
                          selectColumns: batchTable.columns.map((i) => {
                            return {
                              name: i.name,
                              computeType: i.columnType,
                            }
                          }),
                        },
                      ],
                    },
                  },
                })
              }}
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 8 }}
            >
              <Form.Item
                label="表名称"
                name="name"
                rules={[
                  {
                    required: true,
                    message: '请输入正确的表名称(小写字母开头)',
                    pattern: /[a-z].*/,
                  },
                ]}
              >
                <Input placeholder="请输入表名称" />
              </Form.Item>
              <DBSelect
                span={24}
                setFieldValue={(key) => {
                  batchTableForm.resetFields([key])
                }}
              />

              <Form.Item
                label="时间列"
                name="timeColumn"
                rules={[
                  {
                    required: true,
                    message: '请选择时间列',
                  },
                ]}
              >
                <Select getPopupContainer={(triggerNode) => triggerNode.parentElement} placeholder={'请选择时间列'}>
                  {getTimeColumnOptions()?.map((i) => (
                    <Option key={i} value={i}>
                      {i}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item
                label="拼接时间点"
                name="timePoint"
                rules={[
                  {
                    required: true,
                    message: '请选择拼接时间点',
                  },
                ]}
              >
                <DatePicker
                  className={cs.businessVirtualTableTimePoint}
                  placeholder="请选择时间"
                  showTime
                  format="YYYY-MM-DD HH:mm:ss"
                />
              </Form.Item>
            </Form>
          ) : (
            <Form
              preserve={false}
              form={multiLinkTableConfigForm}
              onFinish={(val) => {
                createMultiTable({
                  table: {
                    computeType: 'BATCH',
                    virtualTableType: 'AS',
                    catalogName: multiReqProps.catalog,
                    databaseName: multiReqProps.database,
                    joinVO: multiReqProps.joinVO,
                    columns: multiReqProps.columns,
                    name: val.name,
                    timeColumn: val.timeColumn,
                  },
                })
              }}
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 8 }}
            >
              <Form.Item
                label="表名称"
                name="name"
                rules={[
                  {
                    required: true,
                    message: '请输入',
                  },
                ]}
              >
                <Input placeholder="请输入表名称" />
              </Form.Item>
              <Form.Item
                label="时间列"
                name="timeColumn"
                rules={[
                  {
                    required: false,
                    message: '请选择',
                  },
                ]}
              >
                <Select getPopupContainer={(triggerNode) => triggerNode.parentElement} placeholder={'请选择时间列'}>
                  {multiReqProps?.timeColumnList?.map((i) => (
                    <Option key={i} value={i}>
                      {i}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Form>
          )}
          <div className={cs.btnWrapper}>
            <Button onClick={() => setCreateBusinessVirtualTableStep(ICreateTableStep.makeTableModel)}>上一步</Button>
            <Button
              onClick={() => {
                integrateTableConfigForm.submit()
                tableType === ITableTypeEnum.multiTableLike && multiLinkTableConfigForm.submit()
              }}
              loading={createStreamBatchTableLoading || createMultiTbaleLoading}
              type="primary"
              icon={<SaveOutlined />}
            >
              创建虚拟表
            </Button>
          </div>
        </>
      ),
    },
    {
      key: ICreateTableStep.createTableSuccess.toString(),
      children: (
        <Result
          status="success"
          title={'创建成功'}
          subTitle="你的业务虚拟表创建成功，点击「查看虚拟表列表」查看所有的虚拟表"
          extra={[
            <Link to={routerMap.dataModel.virtualTable.path}>
              <Button key={'table_list'} type="primary">
                查看虚拟表列表
              </Button>
            </Link>,
          ]}
        />
      ),
    },
  ]

  return (
    <>
      <PageHeader title={`创建业务虚拟表`} onBack={() => window.history.back()} />

      <LayoutCard>
        <Steps
          direction="horizontal"
          current={createBusinessVirtualTableStep}
          items={createBusinessVirtualTableStepItemsText}
          onChange={(e) => setCreateBusinessVirtualTableStep(e)}
        />
      </LayoutCard>

      <LayoutCard>
        <div className={cs.businessVirtualTable}>
          <Content>
            <Tabs
              activeKey={createBusinessVirtualTableStep.toString()}
              items={createBusinessVirtualTableTabItems}
              tabBarStyle={{ display: 'none' }}
              className={cs.businessVirtualTableTabs}
            />
          </Content>
        </div>
      </LayoutCard>
    </>
  )
}
