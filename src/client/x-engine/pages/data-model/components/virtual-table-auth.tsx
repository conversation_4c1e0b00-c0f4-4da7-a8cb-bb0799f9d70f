import React, { useEffect, useState } from 'react'
import { Col, Row, Space, Typography, Button, Table, Modal, message } from 'antd'
import { authUserListColumns } from '../forms-conf/constant-conf'
import { Api } from '@api'
import { useRequest } from 'ahooks'
import { AuthPageProps, authUser } from '../forms-conf/interface'
import AddAuthUser from './virtual-table-auth-user'

const VirtualTableAuth = (props: AuthPageProps) => {
    const [disabled, setDisabled] = useState(true)
    const [list, setList] = useState<authUser[]>([])
    const [selectedList, setSelectedList] = useState<authUser[]>([])
    const [open, setOpen] = useState<boolean>(false)
    const [data, setData] = useState<authUser>(null)

    const { run } = useRequest(Api.apiAuthQueryVirtualTablePrivPost, {
        defaultParams: [props],
        onSuccess: (res) => {
            setList(res)
        },
    })

    function rowSelectChange(selectedRowKeys: React.Key[], selectedRows: authUser[]) {
        setSelectedList(selectedRows)
    }

    function onRowChange(record: authUser) {
        return {
            onClick: (e: any) => {
                // 点击行
                if (e.target.innerHTML && e.target.dataset.type) {
                    const type = e.target.dataset.type
                    if (type === 'update') {
                        setOpen(true)
                        setData(record)
                    }
                    if (type === 'delete') {
                        Modal.confirm({
                            content: `确认删除 ${record.userName} 的权限吗？`,
                            onOk() {
                                Api.apiAuthRevokeVirtualTablePrivPost({
                                    ...props,
                                    userPrivileges: [
                                        {
                                            userId: record.userId,
                                            userPrivileges: record.privilegeName,
                                        },
                                    ],
                                }).then(() => {
                                    message.success('操作成功')
                                    if (selectedList.length > 0) {
                                        const current = [...selectedList]
                                        const index = current.findIndex((item) => {
                                            return record.userId === item.userId
                                        })
                                        current.splice(index, 1)
                                        setSelectedList(current)
                                    }
                                    run(props)
                                })
                            },
                        })
                    }
                }
            },
        }
    }

    function removeSeleted() {
        Modal.confirm({
            title: '提示',
            content: '是否删除所选用户的权限？',
            onOk: () => {
                Api.apiAuthRevokeVirtualTablePrivPost({
                    ...props,
                    userPrivileges: selectedList.map((item) => {
                        return {
                            userId: item.userId,
                            userPrivileges: item.privilegeName,
                        }
                    }),
                }).then(() => {
                    message.success('操作成功')
                    setSelectedList([])
                    run(props)
                })
            },
        })
    }

    function closeHandle() {
        setData(null)
        setOpen(false)
    }

    useEffect(() => {
        setDisabled(selectedList.length === 0)
    }, [selectedList])

    return (
        <>
            <Space direction='vertical' size='large' style={{ width: '100%' }}>
                <Typography.Text>虚拟表元信息共享范围：全局</Typography.Text>
                <Typography.Text>虚拟表元信息修改权限：user_name</Typography.Text>
                <Row align='middle' justify='space-between'>
                    <Col flex={1}>
                        <Typography.Text>虚拟表查询权限：</Typography.Text>
                    </Col>
                    <Col flex='none'>
                        <Space wrap>
                            <Button disabled={disabled} onClick={removeSeleted}>
                                批量删除
                            </Button>
                            <Button type='primary' onClick={() => setOpen((pre) => !pre)}>
                                权限授予
                            </Button>
                        </Space>
                    </Col>
                </Row>
                <Table
                    columns={authUserListColumns}
                    dataSource={list}
                    rowKey='userId'
                    rowSelection={{
                        type: 'checkbox',
                        selectedRowKeys: selectedList.map((item) => item.userId),
                        onChange: rowSelectChange,
                    }}
                    onRow={onRowChange}
                />
            </Space>
            <AddAuthUser open={open} data={data} {...props} onClose={closeHandle} onConfirm={() => run(props)} />
        </>
    )
}

export default VirtualTableAuth
