import { useEffect, useState } from 'react'
import { DatePicker, Typography, Button, Table, Modal, message, Form, Select } from 'antd'
import { Api } from '@api'
import { useRequest } from 'ahooks'
import { AuthPageProps, authUser, virtualAuthUser } from '../forms-conf/interface'
import dayjs, { Dayjs } from 'dayjs'
import type { RangePickerProps } from 'antd/es/date-picker'
import './virtual-table-auth-user.scss'

interface propsCommonType {
    open: boolean
    data?: authUser
    onConfirm: () => void
    onClose: () => void
}

type authUserPorps = propsCommonType & AuthPageProps

interface stateType {
    userList: Array<virtualAuthUser>
    selectUserList: Array<virtualAuthUser>
}

export default function VirtualTableAuthUser(props: authUserPorps) {
    const [form] = Form.useForm()

    const { open = false, virtualTableName, catalogName, databaseName, data, onClose, onConfirm } = props

    const [state, setState] = useState<stateType>({
        userList: [],
        selectUserList: [],
    })

    const { loading, run } = useRequest(Api.apiAuthPrivliegeToUserPost, {
        manual: true,
        onSuccess: successHandle,
    })

    const { loading: updateLoading, run: updateRun } = useRequest(Api.apiAuthUpdateVirtualTablePrivPost, {
        manual: true,
        onSuccess: successHandle,
    })

    function successHandle() {
        message.success('操作成功')
        closeHandle()
        onConfirm && onConfirm()
    }

    function closeHandle() {
        onClose && onClose()
        form.resetFields()
        setState((pre) => {
            return { ...pre, selectUserList: [] }
        })
    }

    function getNextYearTime() {
        const now = new Date()
        now.setFullYear(now.getFullYear() + 1)
        return now.getTime()
    }

    function onTimeChange(value: number | string, index: number) {
        const currentList = [...state.selectUserList]
        const current = { ...currentList[index] }
        if (value) {
            current.expireTime = value
        } else {
            delete current.expireTime
        }
        currentList.splice(index, 1, current)
        setState((pre) => {
            return { ...pre, selectUserList: [...currentList] }
        })
    }

    function selectChange(value: string, options: any) {
        const _list = options.map((item: { label: string; value: string }) => {
            return {
                userName: item.label,
                userId: item.value,
                privilegeName: '读取查询权限',
                expireTime: getNextYearTime(),
            }
        })
        setState((pre) => {
            return { ...pre, selectUserList: _list }
        })
    }

    function deleteItem(index: number) {
        const current = [...state.selectUserList]
        current.splice(index, 1)
        form.setFieldValue(
            'userName',
            current.map((item) => item.userId),
        )
        setState((pre) => {
            return { ...pre, selectUserList: current }
        })
    }

    async function onOk() {
        await form.validateFields()
        if (data) {
            updateRun({
                ...state.selectUserList[0],
                expireDate: state.selectUserList[0].expireTime,
                virtualTableName,
                catalogName,
                databaseName,
            })
        } else {
            const data: any = state.selectUserList.map((item) => {
                const _item = {
                    ...item,
                    virtualTableName,
                    catalogName,
                    databaseName,
                }
                delete _item.userName
                return _item
            })
            run(data)
        }
    }

    useEffect(() => {
        Api.apiAuthUserGet().then((res) => {
            setState((pre) => {
                return { ...pre, userList: res }
            })
        })
    }, [])

    useEffect(() => {
        // data 存在代表修改
        if (data) {
            // 需要把数据格式成可以用的
            const _data: virtualAuthUser = {
                userId: data.userId,
                userName: data.userName,
                privilegeName: data.privilegeName,
            }
            if (data.expireDate) {
                _data.expireTime = data.expireDate
            }
            console.log(_data)
            setState((pre) => {
                return {
                    ...pre,
                    selectUserList: [_data],
                }
            })
        }
    }, [data])

    return (
        <Modal
            title={data ? '修改权限过期时间' : '权限授予'}
            open={open}
            okText='确认'
            cancelText='取消'
            width={800}
            confirmLoading={loading || updateLoading}
            onCancel={closeHandle}
            onOk={onOk}
        >
            <Form form={form} labelCol={{ span: 3 }} wrapperCol={{ span: 12 }} labelAlign='left' layout='horizontal'>
                {!data && (
                    <>
                        <Form.Item
                            label='用户名'
                            name='userName'
                            required
                            rules={[{ required: true, message: '请选择用户' }]}
                        >
                            <Select
                                disabled={!!data}
                                mode='multiple'
                                allowClear
                                style={{ width: '100%' }}
                                placeholder='请输入'
                                onChange={selectChange}
                                options={state.userList.map((item) => {
                                    return { label: item.userName, value: item.userId }
                                })}
                            />
                        </Form.Item>
                        <Typography.Text>授权列表：</Typography.Text>
                    </>
                )}
                <Table
                    style={{ marginTop: '20px' }}
                    columns={[
                        {
                            title: '用户名',
                            dataIndex: 'userName',
                        },
                        {
                            title: '授予权限',
                            dataIndex: 'privilegeName',
                        },
                        {
                            title: '权限到期时间',
                            dataIndex: 'expireTime',
                            render: (value: string, record: virtualAuthUser, index: number) => (
                                <DatePickerComponet value={value} index={index} onChange={onTimeChange} />
                            ),
                        },
                        {
                            title: '操作',
                            key: 'anctions',
                            align: 'center',
                            render: (_: any, record: virtualAuthUser, index: number) => {
                                return (
                                    <>
                                        {!data ? (
                                            <Button type='link' onClick={() => deleteItem(index)}>
                                                删除
                                            </Button>
                                        ) : null}
                                    </>
                                )
                            },
                        },
                    ]}
                    scroll={{ y: 300 }}
                    dataSource={state.selectUserList}
                />
            </Form>
        </Modal>
    )
}

interface DatePickerComponentProps {
    value: string | number
    index: number
    onChange: (v: string | number, index: number) => void
}

function DatePickerComponet(props: DatePickerComponentProps) {
    const disabledDate: RangePickerProps['disabledDate'] = (current) => {
        return current && current < dayjs().endOf('day')
    }

    function chooseDate(e: Dayjs) {
        props.onChange && props.onChange(e ? e.valueOf() : null, props.index)
    }

    return (
        <DatePicker
            className='custom-date-picker'
            defaultValue={dayjs(props.value)}
            showTime
            placeholder='永不过期'
            disabledDate={disabledDate}
            showNow={false}
            onChange={chooseDate}
        />
    )
}
