import React from 'react'
import { useRequest } from 'ahooks'
import { Api, ColumnQualityVo, TableQualityDetailVo } from '@api'
import { Spin, Table, Button, Typography, Descriptions } from 'antd'
import { ColumnsType } from 'antd/lib/table/interface'
import styles from '../data-model.module.scss'
import { convertVType } from '@pages/data-model/forms-conf/util'

const { Title } = Typography
const columnQualityColumn:
    | ColumnsType<ColumnQualityVo>
    | {
          dataIndex: keyof ColumnQualityVo
      }[] = [
    {
        title: '字段名',
        dataIndex: 'columnName',
    },
    {
        title: '字段类型',
        dataIndex: 'columnType',
    },
    {
        title: '字段数量',
        dataIndex: 'rowNotNullCount',
    },
    {
        title: '缺失值',
        dataIndex: 'nullCount',
    },
    {
        title: '最大值',
        align: 'center',
        dataIndex: 'max',
    },
    {
        title: '最小值',
        dataIndex: 'min',
    },
    {
        title: '平均值',
        dataIndex: 'avg',
    },
]

const sourceTables:
    | ColumnsType<TableQualityDetailVo>
    | Array<{
          dataIndex: keyof TableQualityDetailVo
      }> = [
    {
        title: '名称',
        dataIndex: 'name',
    },
    {
        title: '类型',
        dataIndex: 'tableType',
        render: (type: any) => {
            return convertVType(type)
        },
    },
    {
        title: '数据总行数',
        dataIndex: 'dataRowNumber',
    },
    {
        title: '重复数据量',
        dataIndex: 'duplicateRowNumber',
    },
    {
        title: '操作',
        dataIndex: 'importerStatus',
        fixed: 'right',
        align: 'center',
        width: 150,
        render: (_, record) => {
            return (
                <Button
                    size={'small'}
                    type={'link'}
                    onClick={() => {
                        document.getElementById(record.name).scrollIntoView({ behavior: 'smooth' })
                    }}
                >
                    详情
                </Button>
            )
        },
    },
]

export function VirtualTableQuality(props: { catalog: string; database: string; vtable: string }) {
    const { data: tableQuality, loading: getTableQualityLoading } = useRequest(
        () =>
            Api.apiEngineV1VtableGetTableQualityGet({
                ...props,
            }),
        {
            onError(e) {
                console.log(e)
            },
        },
    )

    return (
        <div className={styles.tableQualityCon}>
            {getTableQualityLoading && !tableQuality?.tableQualityDetailList?.length ? (
                <Spin className={styles.tableQualityConSpin} />
            ) : (
                false
            )}
            {tableQuality?.tableQualityDetailList?.length > 1 && (
                <>
                    <Title level={4}>上游数据来源</Title>
                    <Table
                        size='middle'
                        columns={sourceTables}
                        dataSource={tableQuality?.tableQualityDetailList ?? []}
                        rowKey='physicalTableId'
                        scroll={{ x: '100%' }}
                    />
                </>
            )}
            {tableQuality?.tableQualityDetailList?.map((i) => {
                return (
                    <div id={i.name}>
                        <Descriptions title={i.name} column={5}>
                            <Descriptions.Item label='类型'>{convertVType(i?.tableType)}</Descriptions.Item>
                            <Descriptions.Item label='数据总行数'>{i?.dataRowNumber}</Descriptions.Item>
                            {/*<Descriptions.Item label='数据大小绝对值'>{i?.dataSize}</Descriptions.Item>*/}
                            <Descriptions.Item label='重复行数'>{i?.duplicateRowNumber}</Descriptions.Item>
                        </Descriptions>

                        <Title level={5}>字段质量</Title>
                        <Table
                            size='middle'
                            columns={columnQualityColumn}
                            dataSource={i?.columnQualityVOList ?? []}
                            rowKey='physicalTableId'
                            scroll={{ x: '100%' }}
                        />
                    </div>
                )
            })}
        </div>
    )
}
