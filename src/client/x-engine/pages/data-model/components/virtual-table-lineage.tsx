import Lineage from '@model/Lineage'
import { useRequest } from 'ahooks'
import { Api } from '@api'

interface VTLineagePropsType {
  tableName: string
  catalog: string
  database: string
}

function VTLineage(props: VTLineagePropsType) {
  const { tableName, catalog, database } = props
  const { data: lineageData } = useRequest(() => {
    return Api.apiEngineV1VtableQueryTableLineageGet({
      vtableName: tableName,
      catalog,
      database,
    }).then((res) => {
      res.edges.forEach((i) => {
        i.source.name = i.source.table
        i.target.name = i.target.table
        delete i.source.table
        delete i.target.table
      })
      return res
    })
  })

  return (
    <>
      {lineageData && (
        <div
          style={{
            height: '700px',
          }}
        >
          <Lineage
            data={lineageData}
            typeKey="type"
            layout="dagre"
            layoutConf={{
              type: 'dagre',
              rankdir: 'LR',
              controlPoints: true,
              nodesepFunc: (node: Record<string, any>) => {
                return node.height * 0.4
              },
              ranksepFunc: (node: Record<string, any>) => {
                return node.width * 0.5
              },
            }}
            legend={true}
            popoverTipParams={{ name: 'name' }}
          />
        </div>
      )}
    </>
  )
}

export default VTLineage
