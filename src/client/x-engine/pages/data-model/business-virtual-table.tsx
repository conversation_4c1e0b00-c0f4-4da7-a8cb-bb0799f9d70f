// @ts-nocheck
import React, { useEffect, useState } from 'react'
import { PageHeader } from '@ant-design/pro-layout'
import { App, Button, Form, Modal, Typography, Input } from 'antd'
import { useRequest } from 'ahooks'
import { Api, ResourceDesc } from '@api'
import { Search, setSelectAttribute, setSelectOpts } from '@ui/form'
import { businessTableColumns, formItems } from './forms-conf/form-items'
import { computeTypeList, virtualTableTypeList } from './forms-conf/constant-conf'
import { Broadcast } from '@libs'
import { routerMap } from '@XEngineRouter/routerMap'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { getFiltersEntries, getWinInnerSize } from '@libs/util'
import DataResolveFormItem from './components/DataProcessFormItem'
import DetailOutputSettingsFormItem from './components/DetailOutputSettingsFormItem'
import StreamSettingFormItem from './components/StreamSettingsFormItem'
import SceneInfoFormItem from './components/SceneInfoFormItem'
import { askBIApiUrls } from 'src/shared/url-map'
import request from 'src/shared/xengineAxios'
import { DataModelForStreamVirtualTable, OutputDesc } from './type'
import { type OriginColumnsType } from 'src/client/components/TableFilter/TableFilterDrawer'
import CreateFilterTable from 'src/client/components/TableFilter/CreateFilterTable'
import { customAlphabet } from 'nanoid'

const numericalID = customAlphabet('1234567890', 10)

export type DimensionType = { select: [string, string]; description?: string }
export type MeasureType = {
  measuresAgg: string
  measuresColumn: string
  measuresValue: string
  measuresDescription?: string
}

export interface ModelFormValueType {
  catalog: string
  database: string
  mainVTable: string
  windowTimeColumn: string
  measures: MeasureType[]
  dimensions: DimensionType[]
  scanStartupMode?: string
  outputTarget?: string
  defaultDtPartition: boolean
}

const { Text } = Typography

const getSearchFieldMap = (key: string) => {
  const x = {
    gmtCreated: 'modification.gmtCreated',
    creator: 'modification.creator',
  } as const
  return key === 'gmtCreated' || key === 'creator' ? x[key] : key
}

const VirtualTable = () => {
  const [searchForm, setSearchForm] = useState<any[]>(formItems)
  const [searchParams] = useSearchParams()
  const queryCatalog = searchParams.get('catalog')
  const [searchInitParams, setSearchInitParams] = useState({
    catalog: '',
    database: '',
    name: '',
  })
  const [autoLoad, setAutoLoad] = useState<boolean>(false)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [publishStreamVTableModalOpen, setPublishStreamVTableModalOpen] = useState(false)
  const [currentTable, setCurrentTable] = useState<any>({})
  const [currentPublishTable, setCurrentPublishTable] = useState<any>({})
  const [xWidth, setXWidth] = useState('100%')

  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const navigate = useNavigate()
  const { message: antdMessage } = App.useApp()
  const [createFilterTableProps, setCreateFilterTableProps] = useState<{
    open: boolean
    tableData?: OriginColumnsType
  }>({
    open: false,
  })

  const publishVTable = (record: OriginColumnsType) => {
    setCurrentPublishTable(record)
    setPublishStreamVTableModalOpen(true)
  }

  const { runAsync: deleteVTable } = useRequest(Api.apiEngineV1VtableDeleteDelete, {
    manual: true,
    onSuccess() {
      setRefreshTrigger((pre) => {
        return pre + 1
      })
    },
  })

  const { runAsync: getCatalog, loading: catalogRunLoading } = useRequest(Api.apiEngineV1CatalogListGet, {
    manual: true,
  })
  const { runAsync: getDatabase, loading: databaseRunLoading } = useRequest(Api.apiEngineV1DatabaseListGet, {
    manual: true,
  })

  const handleOk = () => {
    const { catalogName, databaseName, name } = currentTable
    setIsModalOpen(false)
    antdMessage.loading({
      content: `虚拟表正在删除，请稍候……`,
      key: 'table_delete_ing',
    })
    deleteVTable({
      catalog: catalogName,
      database: databaseName,
      name: name,
      cascadeDropLikeTable: true,
    })
      .then(() => {
        setRefreshTrigger((pre) => {
          return pre + 1
        })
        antdMessage.success({
          content: `虚拟表删除成功`,
          key: 'table_delete_ing',
        })
      })
      .catch((e) => {
        antdMessage.loading({
          content: e?.message || `虚拟表删除失败`,
          key: 'table_delete_ing',
          duration: 0.01,
        })
      })
  }

  const handleCancel = () => {
    setIsModalOpen(false)
  }
  /**
   * 处理StreamingDesc请求参数
   * @param type
   * @param info
   * @returns
   */
  function resolveStreamingDescParams(
    type: string,
    info: { timeBoundaryStrategy?: string; scanStartupMode?: string; specificTime?: string },
  ) {
    type = type?.toUpperCase()
    switch (type) {
      case 'STREAM': {
        const { scanStartupMode, specificTime } = info
        const params = Object.assign(
          { scanStartupMode: scanStartupMode },
          scanStartupMode?.toUpperCase() === 'SPECIFIC' ? { specificTime: +(specificTime || 0) } : {},
        )
        return params
      }
      case 'STREAM_BATCH': {
        const { timeBoundaryStrategy } = info
        const params = { timeBoundaryStrategy: timeBoundaryStrategy }
        return params
      }
    }
    return null
  }

  const { run: publishStreamVTable, loading: isPublishStreamVTable } = useRequest(
    (data) => request.post(askBIApiUrls.xengine.publishStreamVtable, data),
    {
      manual: true,
      onSuccess() {
        antdMessage.success('上线成功')
        setPublishStreamVTableModalOpen(false)
        configForm.resetFields()
        setRefreshTrigger((pre) => {
          return pre + 1
        })
      },
      onError() {
        antdMessage.error('上线失败')
      },
    },
  )
  const publishStreamVTableModalOk = () => {
    const IS_UPDATE_MODEL = currentPublishTable?.dataModelDesc?.modelType === 'UPDATE_MODEL'
    const value = configForm.getFieldsValue()
    const outputDesc: OutputDesc = {
      outputTarget: value.outputTarget,
      brokers: value.brokers,
      topic: value.topic,
      outputFormat: value.outputFormat, // no
      catalog: value.outputCatalog,
      database: value.outputDatabase,
      table: value.outputTable,
    }
    const resourceDesc: ResourceDesc = {
      cpu: value.cpu,
      memory: value.memory,
      disk: value.disk,
    }
    const dataModel: DataModelForStreamVirtualTable = IS_UPDATE_MODEL
      ? { streamingDesc: {}, outputDesc: {} }
      : {
          dataProcessDesc:
            value.outputTarget === 'ASKDI'
              ? {
                  strategyList: value.dataProcessDesc.map(
                    (i: { strategyType: string; primaryKeys: string[]; version: string[] }) => ({
                      strategyType: i.strategyType,
                      primaryKeys: i.primaryKeys.map((j: string) => ({
                        name: j,
                        vertexId: j,
                      })),
                      version: i.version.map((j: string) => ({
                        name: j,
                        vertexId: j,
                      }))[0],
                    }),
                  ),
                }
              : {},
          outputDesc,
          resourceDesc,
          streamingDesc: resolveStreamingDescParams(currentPublishTable.computeType, value as any),
        }

    return publishStreamVTable({
      catalog: currentPublishTable.catalogName,
      database: currentPublishTable.databaseName,
      projectName: value.projectName,
      scenarioName: value.scenarioName,
      table: currentPublishTable.name,
      publishMv: true,
      dataModel,
    })
  }

  const cancelPublishStreamVTableModal = () => {
    configForm.resetFields()
    setPublishStreamVTableModalOpen(false)
  }

  const getDatabaseData = (catalog: string, isUseInitSearchValue: boolean) => {
    if (catalog) {
      getDatabase({
        catalog,
        current: 1,
        pageSize: -1,
      }).then((data: any) => {
        const database = data?.[0]?.name || ''
        const searchDatabase = searchParams.get('database')
        const initDatabase = data?.find((i: { name: string }) => i.name === searchDatabase) ? searchDatabase : database
        resetSearchForm(data, 'database', { value: 'name', label: 'name' })
        setSearchInitParams({
          catalog,
          database: isUseInitSearchValue ? initDatabase : database,
          name: isUseInitSearchValue ? searchParams.get('name') || '' : '',
        })
        setAutoLoad(true)
      })
    }
  }

  // When Search Form value changes
  const onSearchChange = (data: any) => {
    const keys = Object.keys(data)?.join('')
    if (keys === 'catalog') {
      getDatabaseData(data[keys], false)
    }
  }

  // Formats the search forms
  const resetSearchForm = (data: any, dealName: string, options: { value: string; label: string }) => {
    setSelectOpts(
      {
        dataList: data,
        dealName: dealName,
        formItems: searchForm,
        options: options,
      },
      (newFormItems) => {
        setSearchForm(newFormItems)
      },
    )
  }

  const setTableWidth = (width: number) => {
    if (width < 1330) {
      setXWidth('110%')
    } else {
      setXWidth('100%')
    }
  }
  useEffect(() => {
    Broadcast.listen('@PAGE/DATAMODEL/VIRTUAL_TABLE', (record: any) => {
      setCurrentTable(record)
      setIsModalOpen(true)
    })

    setTableWidth(getWinInnerSize().innerWidth)

    // Window resize set table scroll x value
    window.addEventListener('resize', (e) => {
      setTableWidth(e?.target?.innerWidth)
    })

    getCatalog({
      current: 1,
      pageSize: -1,
    })
      .then((l) => {
        const data = l.filter((item: any) => item.type === 'INTERNAL')
        const searchCatalog = searchParams.get('catalog')
        const initCatalog = data.find((i) => i.name === searchCatalog) ? searchCatalog : data[0]?.name || ''

        // Set Database Select options
        resetSearchForm(data, 'catalog', { value: 'name', label: 'name' })
        setSelectAttribute(
          [
            { name: 'catalog', attribute: 'key', value: initCatalog },
            { name: 'catalog', attribute: 'value', value: initCatalog },
          ],
          searchForm,
          (newFormItems: any) => {
            setSearchForm(newFormItems)
          },
        )
        getDatabaseData(initCatalog, true)
      })
      .catch((err) => {
        console.error(err)
      })
    // Set ComputeType Select options
    resetSearchForm(computeTypeList, 'computeType', { value: 'value', label: 'desc' })
    // Set VirtualTableType Select options
    resetSearchForm(virtualTableTypeList, 'virtualTableType', { value: 'value', label: 'desc' })
  }, [location.href])
  // 添加loading
  useEffect(() => {
    setSelectAttribute(
      [
        { name: 'catalog', attribute: 'loading', value: catalogRunLoading },
        { name: 'database', attribute: 'loading', value: databaseRunLoading },
      ],
      searchForm,
      setSearchForm,
    )
  }, [catalogRunLoading, databaseRunLoading])

  const [configForm] = Form.useForm<DataModelForStreamVirtualTable>()

  const { data: vTableList, run: getVTableList } = useRequest(
    async (args: { catalog: string; database: string }) => {
      if (!args.database || !args.catalog) {
        return []
      }
      const vTableListAns = await Api.apiEngineV1VtableSearchGet({
        current: 1,
        pageSize: -1,
        ...args,
      })
      return (vTableListAns?.list || []).filter((vt) => vt?.virtualTableType === 'AS')
    },
    {
      manual: true,
    },
  )
  function handleFormValuesChange(changeValue: Record<string, any>, values: Record<string, any>) {
    const changeKey = Object.keys(changeValue)[0]
    switch (changeKey) {
      case 'catalog':
      case 'database': {
        const { database, catalog } = values
        getVTableList({
          catalog,
          database: changeKey === 'catalog' ? undefined : database,
        })
        configForm.resetFields()
        configForm.setFieldsValue({
          database,
          catalog,
        })
        break
      }
      case 'mainVTable': {
        const { database, catalog, mainVTable } = values
        configForm.resetFields()
        configForm.setFieldsValue({
          database,
          catalog,
          mainVTable,
        })
        break
      }
    }
  }

  const renderPublishMVCtx = () => {
    const IS_UPDATE_MODEL = currentPublishTable?.dataModelDesc?.modelType === 'UPDATE_MODEL'
    if (IS_UPDATE_MODEL) {
      return <SceneInfoFormItem />
    }
    return (
      <>
        <DetailOutputSettingsFormItem form={configForm} />

        <DataResolveFormItem
          columns={
            currentPublishTable?.columns?.map((j) => {
              const str = `${currentPublishTable.catalogName}.${currentPublishTable.databaseName}.${currentPublishTable.name}.${j.name}`
              return {
                label: str,
                value: str,
              }
            }) || []
          }
        />

        <StreamSettingFormItem type={currentPublishTable.computeType} form={configForm} isShowUnit={false} />
        <SceneInfoFormItem />
      </>
    )
  }
  return (
    <>
      <PageHeader
        title="虚拟表"
        extra={
          <Button
            type="primary"
            onClick={() => {
              navigate(routerMap.dataModel.createBusinessVirtualTable.path)
            }}
          >
            创建虚拟表
          </Button>
        }
      />
      <Search
        key={'vtable-search'}
        items={searchForm}
        onChange={onSearchChange}
        setFieldsValue={searchInitParams}
        table={{
          refreshTrigger,
          showHeader: true,
          autoLoad,
          columns: businessTableColumns({
            publishVTable,
            filterTable: (record: OriginColumnsType) => {
              setCreateFilterTableProps({
                open: true,
                tableData: { ...record, id: numericalID() },
              })
            },
          }),
          api: (arg: { [x: string]: any; sorter: any; filters?: any }) => {
            const { filters, sorter, ...restQueryParam } = arg
            const reqArg = Object.assign(
              getFiltersEntries(filters ?? {}),
              sorter?.order?.length
                ? {
                    sortField: getSearchFieldMap(arg.sorter.field),
                    isReverseOrder: sorter.order === 'descend',
                  }
                : {},
              restQueryParam,
              { virtualTableType: 'AS' },
            )
            getVTableList({
              catalog: restQueryParam.catalog,
              database: restQueryParam.database,
            })
            return Api.apiEngineV1VtableSearchGet(reqArg)
          },
          rowKey: 'id',
          scroll: { x: xWidth },
        }}
      />
      <Modal
        title={'删除虚拟表'}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        okText={'确认删除'}
        cancelText={'取消'}
      >
        <p>
          你确认删除 <Text type="danger">{currentTable?.name}</Text> 表吗？
        </p>
      </Modal>
      <Modal
        title={'上线业务虚拟表'}
        open={publishStreamVTableModalOpen}
        onOk={publishStreamVTableModalOk}
        onCancel={cancelPublishStreamVTableModal}
        okText={'确定'}
        okButtonProps={{
          loading: isPublishStreamVTable,
        }}
        cancelText={'取消'}
      >
        <Form
          form={configForm}
          layout="vertical"
          initialValues={{
            dataProcessDesc: [{}],
            outputTarget: 'ASKDI',
          }}
          onValuesChange={handleFormValuesChange}
        >
          {renderPublishMVCtx()}
        </Form>
      </Modal>
      <CreateFilterTable
        {...createFilterTableProps}
        onClose={() =>
          setCreateFilterTableProps({
            open: false,
          })
        }
      />
    </>
  )
}

export default VirtualTable
