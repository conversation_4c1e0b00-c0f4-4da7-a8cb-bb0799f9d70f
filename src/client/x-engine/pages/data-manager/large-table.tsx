import { useState } from 'react'
import { useAntdTable } from 'ahooks'
import { message, Table, Tag } from 'antd'
import { PageHeader } from '@ant-design/pro-layout'
import { ColumnsType } from 'antd/lib/table/interface'

import { useSearchParams } from 'react-router-dom'

import { Api } from '@api'

type Temp = ColumnsType[number]
type AntdColumn<T> = Temp & {
  dataIndex?: keyof T
}
function Index() {
  const [searchParams] = useSearchParams()
  const tableName = searchParams.get('name') || ''
  const catalog = searchParams.get('catalog') || 'default'
  const database = searchParams.get('database') || 'default'
  const [vTableName, setVTableName] = useState()

  const { tableProps: vTableProps } = useAntdTable(
    () => {
      // /api/engine/v1/vtable/{name}
      return Api.apiEngineV1VtableNameGet({ name: tableName, catalog, database }, {}).then((data: any) => ({
        list: data.columns,
        name: data.name,
        total: data.columns.length,
      }))
    },
    {
      cacheKey: 'useAntdTableCache',
      onError(e: any) {
        console.error(e?.msg || '获取虚拟表出错')
      },
      onSuccess(data: any) {
        setVTableName(data?.name)
      },
      defaultPageSize: 20,
    },
  )

  const columns: AntdColumn[] = [
    {
      title: '字段ID',
      dataIndex: 'id',
    },
    {
      title: '字段名',
      dataIndex: 'name',
    },
    {
      title: '字段类型',
      dataIndex: 'columnType',
    },
    {
      title: '允许NULL',
      dataIndex: 'not_allow_null',
      render(data) {
        return data ? '是' : '否'
      },
    },
  ]

  return (
    <div>
      <PageHeader
        ghost={false}
        onBack={() => window.history.back()}
        title="表详情"
        tags={[<Tag color="blue">表名称：{vTableName}</Tag>]}
      ></PageHeader>
      <Table columns={columns} rowKey="id" {...vTableProps} />
    </div>
  )
}

export default Index
