import React from 'react'
import { Button } from 'antd'
import { ColumnsType } from 'antd/lib/table/interface'

export const getColumns = (jump): ColumnsType[number][] => [
    {
        title: '表ID',
        dataIndex: 'id',
    },
    {
        title: '表名称',
        dataIndex: 'name',
        render: (text, data: any) => (
            <Button
                type={'link'}
                onClick={() =>
                    jump({
                        name: data?.name,
                        catalog: data?.catalogName,
                        database: data?.databaseName,
                    })
                }
            >
                {text}
            </Button>
        ),
    },
    {
        title: '目录',
        dataIndex: 'catalogName',
    },
    {
        title: '数据库',
        dataIndex: 'databaseName',
    },
    {
        title: '热度',
        dataIndex: 'hot',
    },
]
