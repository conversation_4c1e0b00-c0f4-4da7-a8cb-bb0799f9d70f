import React from 'react'
import { useRequest } from 'ahooks'
import { Button, Col, Form, Input, message, notification, Popconfirm, Row, Select } from 'antd'
import { PageHeader } from '@ant-design/pro-layout'
import { useNavigate } from 'react-router-dom'
import { Api } from '@api'

const { Option } = Select

const openNotification = (jump: () => void) => {
    const btn = (
        <Button type='primary' onClick={jump}>
            跳转虚拟表
        </Button>
    )
    const args = {
        message: '生成虚拟表成功',
        btn,
        duration: 15,
    }
    return notification.open(args)
}

function Index() {
    const navigate = useNavigate()
    const [loadTableForm] = Form.useForm()
    const [form] = Form.useForm()

    // project 能从这里弄到
    const { data: listDataSourceData } = useRequest(Api.apiListDatasourcesGet, {
        onError() {
            message.error('查询出错')
        },
    })
    // 导入表
    const { run: loadDataSource, loading: loadDataSourceLoading } = useRequest(Api.apiLoadDatasourcePost, {
        manual: true,
        onSuccess() {
            message.success('导入成功')
        },
        onError() {
            message.error('查询出错')
        },
    })
    // 生成虚拟表
    const { run: createVirtualTable, loading: VtableLoading } = useRequest(Api.apiCreateVirtualTablePost, {
        manual: true,
        onSuccess(data) {
            const query = Object.keys(data)
                .map((key) => `${key}=${data[key]}`)
                .join('&')
            openNotification(() => {
                navigate(`/data-manager/virtual-data-manager/offline-data-import/offline-large-table?${query}`)
                notification.destroy()
            })
        },
        onError() {
            message.error('虚拟表生成出错')
        },
    })

    return (
        <>
            <PageHeader style={{ paddingLeft: 0 }} onBack={() => null} backIcon={false} title='导入表' />

            <Form
                form={loadTableForm}
                onFinish={({ datasourceIds }) => {
                    const loadDatasourceParam = datasourceIds.map((id) => {
                        const { projectId, id: datasourceId } = listDataSourceData.find((i) => i.id === id) ?? {}

                        return { projectId, datasourceId }
                    })
                    loadDataSource({ loadDatasourceParam })
                }}
            >
                <Row gutter={24}>
                    <Col span={12}>
                        <Form.Item
                            label='选择表名称'
                            name='datasourceIds'
                            rules={[
                                {
                                    required: true,
                                    message: '请选择表名称',
                                },
                            ]}
                        >
                            <Select style={{ marginRight: 16 }} placeholder='请选择表名称' mode='multiple'>
                                {listDataSourceData?.map?.((i) => (
                                    <Option value={i.id} key={i.id}>
                                        {i.name}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col>
                        <Popconfirm
                            title='数据已导入，是否再次导入？'
                            onConfirm={loadTableForm.submit}
                            okButtonProps={{
                                danger: true,
                            }}
                            okText='仍要导入'
                            cancelText='取消'
                        >
                            <Button type='primary' loading={loadDataSourceLoading}>
                                确认导入
                            </Button>
                        </Popconfirm>
                    </Col>
                </Row>
            </Form>
            <PageHeader style={{ paddingLeft: 0 }} onBack={() => null} backIcon={false} title='生成虚拟表' />

            <Form
                form={form}
                onFinish={(data) => {
                    const { frequency, vtableName } = data
                    const t = loadTableForm.getFieldsValue()
                    const idPairs = t?.datasourceIds
                        ?.map((id) => {
                            const { projectId, id: importerId } = listDataSourceData.find((x) => x.id === id) ?? {}
                            return { projectId, importerId }
                        })
                        .filter((k) => k.projectId)
                    createVirtualTable({ idPairs, frequency, vtableName })
                }}
            >
                <Col span={12}>
                    <Form.Item
                        label='更新周期'
                        name='frequency'
                        rules={[
                            {
                                required: true,
                                message: '请选择更新周期',
                            },
                        ]}
                    >
                        <Select style={{ marginRight: 16 }} placeholder='请选择更新周期'>
                            <Option value='1_day'>每天</Option>
                            <Option value='1_hour'>每小时</Option>
                            <Option value='1_minute'>每分钟</Option>
                            <Option value='1_month'>每月</Option>
                        </Select>
                    </Form.Item>
                </Col>

                <Col span={12}>
                    <Form.Item
                        label='虚拟表名称'
                        name='vtableName'
                        rules={[
                            {
                                required: true,
                                message: '请输入虚拟表英文名称',
                            },
                        ]}
                    >
                        <Input placeholder='请输入虚拟表英文名称' />
                    </Form.Item>
                </Col>

                <Col>
                    <Button type='primary' onClick={form.submit} loading={VtableLoading}>
                        生成虚拟表
                    </Button>
                </Col>
            </Form>
        </>
    )
}

export default Index
