// @ts-nocheck
import { PageHeader } from '@ant-design/pro-layout'
import { Card, Result, Table } from 'antd'
import { like_batch_columns } from '@pages/batch-info/conf/columns'
import { useLocation } from 'react-router-dom'

const data: any = {
    like: {
        tableKey: 'failedTableInfos',
        columns: like_batch_columns,
        title: '批量创建贴源虚拟表失败记录',
    },
}

export default function BatchErrorInfo() {
    const location = useLocation()

    if (!location.state || !location.state?.type || !location.state?.result) {
        return <Result status='warning' title='缺少必要参数' />
    }

    const type = location.state?.type
    const result = location.state?.result

    return (
        <>
            <PageHeader title={data[type].title} onBack={() => history.back()} />
            <Card>
                <Table
                    scroll={{ x: 'max-content' }}
                    columns={data[type].columns}
                    pagination={{
                        defaultPageSize: 20,
                        showSizeChanger: true,
                    }}
                    dataSource={result[data[type].tableKey]}
                />
            </Card>
        </>
    )
}
