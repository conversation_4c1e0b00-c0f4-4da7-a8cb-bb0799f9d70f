export interface MLDataType {
    key: string | number
    displayName: string
    mvName: string
    creator: string
    viewType: string
    scheduleType: string
    scheduleFrequency: string
    revenue: number
}

export interface MLPageParamsType {
    current: number
    pageSize: number
    total: number
}

export interface MLSearchOptionsType {
    catalogName?: string
    dbName?: string
    displayName?: string
    scheduleType?: string
    viewType?: string
    creator?: string
    current?: number
    pageSize?: number
}
