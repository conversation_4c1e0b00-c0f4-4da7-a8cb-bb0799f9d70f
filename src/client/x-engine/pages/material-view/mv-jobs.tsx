// @ts-nocheck
import { PageHeader } from '@ant-design/pro-layout'
import { type TableColumnsType, message, Typography } from 'antd'
import { Api, ApiEngineV1SchedulerTasksGet200ResponseData } from '@api'
import React, { useState } from 'react'
import { Link } from 'react-router-dom'
import { splitQueryToPath, deleteEmptyValueFromObject, getMVNameFromDpName } from '@libs/util'
import { routerMap } from '@XEngineRouter/routerMap'
import Columns from '@ui/table/Columns'
import { dpStatusMap } from '@constant/enums'
import dayjs from 'dayjs'
import { Search } from '@ui/form'
import LoadingText from '@ui/LoadingText'
import { LogLink } from './components/LogLink'

const jobsTableColumns = ({ onKillSuccess }: { onKillSuccess?: () => any }) =>
  [
    {
      title: '物化视图',
      dataIndex: 'mvDisplayName',
      ellipsis: true,
      render: (_, record) => {
        const mvName = getMVNameFromDpName(record.name)
        const queryObjectInfo = {
          catalogName: 'dipeak',
          dbName: 'materialize',
          mvName: mvName,
        }
        const to = splitQueryToPath(routerMap.smartx.materialViewDetail.path, queryObjectInfo, [
          'catalogName',
          'dbName',
          'mvName',
        ])
        return (
          <>
            {record?.mvExist ? (
              <Link
                to={to}
                style={{
                  cursor: 'pointer',
                }}
              >
                {mvName}
              </Link>
            ) : (
              <Typography.Text>{mvName}</Typography.Text>
            )}
          </>
        )
      },
    },
    {
      title: 'partition',
      dataIndex: 'partition',
      render: Columns({ type: 'timeformat' }),
      width: '120px',
    },
    {
      title: '当前状态',
      dataIndex: 'state',
      render: Columns({ type: 'mvJobStatus' }),
      width: '120px',
    },
    {
      title: '开始执行时间',
      dataIndex: 'startTime',
      width: '200px',
      render(startTime) {
        return startTime ? dayjs(startTime).format('YYYY-MM-DD HH:mm:ss') : '-'
      },
    },
    {
      title: '结束执行时间',
      dataIndex: 'endTime',
      width: '200px',
      render(endTime) {
        return endTime ? dayjs(endTime).format('YYYY-MM-DD HH:mm:ss') : '-'
      },
    },
    {
      title: '日志',
      dataIndex: 'logViewUrl',
      render: (_, record) => {
        return <LogLink data={record} />
      },
      width: '80px',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: '80px',
      render: (_, record) => {
        return (
          <>
            <LoadingText
              disabled={record.state !== 'RUNNING_EXECUTION'}
              type="link"
              api={() => {
                return Api.apiEngineV1SchedulerStopTaskIdPost({
                  id: record.id,
                })
              }}
              onSuccess={() => {
                if (typeof onKillSuccess === 'function') {
                  onKillSuccess()
                }
                message.success('操作成功')
              }}
              onFail={() => {
                message.error('操作失败')
              }}
            >
              kill
            </LoadingText>
          </>
        )
      },
    },
  ] as TableColumnsType<ApiEngineV1SchedulerTasksGet200ResponseData['list'][0]>

const mbJobsFormItems = [
  {
    tag: 'Select',
    label: '状态选择',
    name: 'state',
    options: [{ label: '全部', value: 'ALL' }].concat(
      Object.keys(dpStatusMap).map((k) => ({
        label: dpStatusMap[k as keyof typeof dpStatusMap],
        value: k,
      })),
    ),
  },
  {
    tag: 'Input',
    label: '物化视图',
    name: 'searchVal',
    allowClear: true,
  },
  {
    tag: 'DatePicker',
    label: '开始执行时间',
    name: 'startDate',
    showTime: true,
  },
  {
    tag: 'DatePicker',
    label: '结束执行时间',
    name: 'endDate',
    showTime: true,
  },
  {
    tag: 'buttons',
    children: [
      {
        tag: 'Button',
        name: 'submit',
        label: '查询',
        type: 'primary',
        htmlType: 'submit',
      },
    ],
  },
]

export default function () {
  const [reloadMVJobs, setReloadMVJobs] = useState(false)
  return (
    <>
      <PageHeader title="任务列表" />
      <Search
        reload={reloadMVJobs}
        key={'mvjobs-search'}
        items={mbJobsFormItems}
        table={{
          columns: jobsTableColumns({
            onKillSuccess: () => {
              const setTimeid = setTimeout(() => {
                setReloadMVJobs((reload) => !reload)
                clearTimeout(setTimeid)
              }, 1200)
            },
          }),
          rowKey: 'id',
          api: (params: Record<string, any>) => {
            const { state, startDate, endDate } = params
            const req = {
              ...params,
              state: state === 'ALL' ? '' : state,
              startDate: startDate ? dayjs(startDate).format('YYYY-MM-DD HH:mm:ss') : '',
              endDate: endDate ? dayjs(endDate).format('YYYY-MM-DD HH:mm:ss') : '',
            }
            return Api.apiEngineV1SchedulerTasksGet(deleteEmptyValueFromObject(req))
          },
        }}
        initialValues={{
          state: 'ALL',
        }}
      />
    </>
  )
}
