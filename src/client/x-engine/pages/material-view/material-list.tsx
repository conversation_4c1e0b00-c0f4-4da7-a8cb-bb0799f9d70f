// @ts-nocheck
import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Col, Row, Typography, message, Statistic, Dropdown, Table } from 'antd'
import { PageHeader } from '@ant-design/pro-layout'
import {
  Api,
  type ApiEngineV1MvCountOverviewGet200ResponseData,
  type ApiEngineV1MvHitOverviewGet200ResponseData,
  type ApiEngineV1MvTaskOverviewGet200ResponseData,
  type ApiEngineV1MvStorageOverviewGet200ResponseData,
} from '@api'
import { getMLTableColumns, BroadcastIdManage } from './conf/dataConf'
import { routerMap } from '@XEngineRouter/routerMap'
import { useAntdTable, useRequest } from 'ahooks'
import LayoutCard, { GhostCard } from '@ui/layoutCard/LayoutCard'
import { formatByteSmart, getFiltersEntries } from '@libs/util'
import cs from './material-view.module.scss'
import { useImmer } from 'use-immer'
import { IUnit, IUnitObj } from '../../static.d'
import { DownOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import MVRecordsModal from '@model/material-view/MVRecordsModal'
// import MaterialErrorList from '@pages/material-view/components/material-error-list'

const { Title } = Typography

enum IOperateEnum {
  create = 'create',
  disable = 'disable',
}

// type IMvOverviewKey = keyof ApiEngineV1MvOverviewGet200ResponseData
type IMvOverviewKey =
  | keyof ApiEngineV1MvHitOverviewGet200ResponseData
  | keyof ApiEngineV1MvCountOverviewGet200ResponseData
  | keyof ApiEngineV1MvTaskOverviewGet200ResponseData
  | keyof ApiEngineV1MvStorageOverviewGet200ResponseData

interface IDescriptionInnerItem<T extends keyof IDataMapType> {
  label: string
  key: keyof IDataMapType[T]['data']
  render?: (key: IMvOverviewKey) => React.ReactNode
}

type IDataMapType = {
  mvCount: {
    data: ApiEngineV1MvCountOverviewGet200ResponseData
    item: IDescriptionInnerItem<'mvCount'>
  }
  hitCount: {
    data: ApiEngineV1MvHitOverviewGet200ResponseData
    item: IDescriptionInnerItem<'hitCount'>
  }
  storeCount: {
    data: ApiEngineV1MvStorageOverviewGet200ResponseData
    item: IDescriptionInnerItem<'storeCount'>
  }
  jobCount: {
    data: ApiEngineV1MvTaskOverviewGet200ResponseData
    item: IDescriptionInnerItem<'jobCount'>[]
  }
}
interface IDescriptionItem<T extends keyof IDataMapType> {
  span: number
  title: string
  items: IDataMapType[T]['item'][]
}

enum IDataType {
  mvCount = 'mvCount',
  hitCount = 'hitCount',
  slowNodesCount = 'slowNodesCount',
  analyse = 'analyse',
  storeCount = 'storeCount',
  jobCount = 'jobCount',
}
type DescriptionListType = {
  [K in keyof IDataMapType]: IDescriptionItem<K>
}
const descriptionList: DescriptionListType = {
  mvCount: {
    title: '物化视图总数',
    span: 8,
    items: [
      // {
      //     label: '人工生成',
      //     key: 'manualCount',
      // },
      // {
      //     label: '贴源导入',
      //     key: 'mirrorCount',
      // },
      // {
      //     label: '智能物化',
      //     key: 'smartCount',
      // },
      {
        label: '',
        key: 'totalCount',
      },
    ],
  },
  // hitCount: {
  //     title: '物化视图命中次数',
  //     span: 18,
  //     items: [
  //         {
  //             label: '近7天物化视图命中数',
  //             key: 'mvQueries',
  //         },
  //         {
  //             label: '近7天查询总数',
  //             key: 'queries',
  //         },
  //         {
  //             label: '近7天物化命中率',
  //             key: 'hitRate',
  //         },
  //     ],
  // },
  storeCount: {
    span: 16,
    title: '物化视图存储占用',
    items: [
      {
        label: '总占用存储',
        key: 'mvOnDisk',
      },
      {
        label: '近7日新增存储',
        key: 'recentMvOnDisk',
      },
    ],
  },
  jobCount: {
    span: 24,
    title: '物化视图任务信息',
    items: [
      [
        {
          label: '总任务数',
          key: 'totalTaskCount',
        },
        {
          label: '隶属视图',
          key: 'totalTaskRelateMvCount',
        },
      ],
      [
        {
          label: '近7天新增任务数',
          key: 'latestDaysTaskCount',
        },
        {
          label: '隶属视图',
          key: 'latestDaysTaskRelateMvCount',
        },
      ],
      [
        {
          label: '近7天失败任务数',
          key: 'latestDaysFailedTaskCount',
        },
        {
          label: '隶属视图',
          key: 'latestDaysFailedTaskRelateMvCount',
        },
      ],
      [
        {
          key: 'runningTaskCount',
          label: '当前运行中任务量',
        },
      ],
      [
        {
          key: 'waitingTaskCount',
          label: '当前排队中任务量',
        },
      ],
    ],
  },
  // slowNodesCount: {
  //     span: 6,
  //     title: '物化视图耗时节点命中总数',
  //     items: [
  //         {
  //             label: '近七天',
  //             key: 'slowNodesCount',
  //         },
  //     ],
  // },
  // analyse: {
  //     span: 18,
  //     title: '物化视图耗时节点分析',
  //     items: [
  //         {
  //             label: '近7天SQL包含join节点数量',
  //             key: 'joinNodesCount',
  //         },
  //         {
  //             label: '近7天SQL包含agg节点数量',
  //             key: 'aggNodesCount',
  //         },
  //         {
  //             label: '近7天SQL包含distinct节点数量',
  //             key: 'distinctNodesCount',
  //         },
  //         {
  //             label: '物化命中join节点数量',
  //             key: 'joinHitsCount',
  //         },
  //         {
  //             label: '物化命中agg节点数量',
  //             key: 'aggregationHitsCount',
  //         },
  //         {
  //             label: '物化命中distinct节点数量',
  //             key: 'distinctHitsCount',
  //         },
  //     ],
  // },
}

const MaterialList: React.FC = () => {
  const [unit, setUnit] = useImmer<IUnitObj>({
    uncompressedSize: IUnit.Byte,
    compressedSize: IUnit.Byte,
  })

  const { tableProps, refresh } = useAntdTable(
    (arg) => {
      delete arg.extra
      const { filters, sorter, ...restQueryParam } = arg
      const reqArg = Object.assign(
        getFiltersEntries(filters ?? {}),
        sorter?.order?.length
          ? {
              sortField: arg.sorter.field,
              isReverseOrder: sorter.order === 'descend',
            }
          : {},
        restQueryParam,
      )
      return Api.apiEngineV1MvListGet(reqArg)
    },
    {
      defaultCurrent: 1,
      defaultPageSize: 10,
    },
  )

  const { run: changeBlackListStatus } = useRequest(Api.apiEngineV1MvBlacklistManagePost, {
    manual: true,
    onSuccess(data) {
      if (data) {
        message.success('已加入黑名单')
        refresh()
      }
    },
  })

  const { run: changeMVActive } = useRequest(Api.apiEngineV1MvActivePost, {
    manual: true,
    onSuccess(data, arg) {
      if (data) {
        refresh()
        message.success(arg[0].active ? '已启用' : '已禁用')
      }
    },
  })

  // proper noun
  // const { loading: loadingOverView, data: MvOverviewData } = useRequest(Api.apiEngineV1MvOverviewGet)
  const { loading: mvCountOverviewLoading, data: mvCountOverviewData } = useRequest(Api.apiEngineV1MvCountOverviewGet)
  // const { loading: mvHitOverviewLoading, data: mvHitOverviewData } = useRequest(Api.apiEngineV1MvHitOverviewGet)
  const { loading: mvTaskOverviewLoading, data: mvTaskOverviewData } = useRequest(Api.apiEngineV1MvTaskOverviewGet)
  const { loading: mvStorageOverviewLoading, data: mvStorageOverviewData } = useRequest(
    Api.apiEngineV1MvStorageOverviewGet,
  )

  const {
    run: getActiveStatus,
    data: activeStatus,
    loading: getActiveStatusLoading,
  } = useRequest(Api.apiEngineV1MvActiveStatusGet)
  const { run: changeGlobalMVActive, loading: changeGlobalMVActiveLoading } = useRequest(Api.apiEngineV1MvActivePost, {
    manual: true,
    onSuccess: () => {
      refresh()
      getActiveStatus()
    },
  })
  const PageHeaderExtra = () => {
    const navigate = useNavigate()

    const [selectedMenuKey, setSelectedMenuKey] = useState(IOperateEnum.create)

    const menuItems = [
      {
        label: '创建物化视图',
        key: IOperateEnum.create,
        onClick: () => {
          navigate(routerMap.smartx.createMaterialView.path)
        },
      },
      {
        label: activeStatus ? '全局禁用物化视图' : '全局启用用物化视图',
        key: IOperateEnum.disable,
        onClick: () => {
          changeGlobalMVActive({ active: !activeStatus })
        },
      },
    ]

    return (
      <>
        <Dropdown.Button
          style={{ display: 'none' }}
          icon={<DownOutlined />}
          loading={changeGlobalMVActiveLoading || getActiveStatusLoading}
          type={'primary'}
          size={'middle'}
          menu={{
            selectable: true,
            items: menuItems,
            selectedKeys: [selectedMenuKey],
            defaultSelectedKeys: [IOperateEnum.create],
            onClick(e) {
              setSelectedMenuKey(e.key)
            },
          }}
          onClick={() => {
            menuItems
              .find((i) => {
                return i.key === selectedMenuKey
              })
              .onClick()
          }}
        >
          {
            menuItems.find((item) => {
              return item.key === selectedMenuKey
            })?.label
          }
        </Dropdown.Button>
      </>
    )
  }

  function handleDeleteMVSuccess() {
    message.success('已删除')
    refresh()
  }

  return (
    <>
      <MVRecordsModal broadcastIdManage={BroadcastIdManage} />
      <PageHeader
        title={
          <div className={cs.pageHeaderTitle}>
            物化视图
            {!activeStatus && <span className={cs.statusWarning}>已全局禁用物化视图,请全局启用后再使用</span>}
          </div>
        }
        extra={<PageHeaderExtra />}
      ></PageHeader>
      <Row gutter={[18, 18]} wrap={false} align="stretch">
        {/*<MaterialErrorList />*/}
        <Col flex={'auto'}>
          <Row gutter={[18, 18]} className={cs.MVDetailContainer}>
            {Object.keys(descriptionList).map((key) => {
              const item = descriptionList[key as keyof IDataMapType]
              return (
                <Col span={item.span} key={key}>
                  <LayoutCard
                    title={
                      <>
                        <span>{item.title}</span>&nbsp;&nbsp;
                        {/*{key === IDataType.hitCount && (*/}
                        {/*    <span className='text-sm text-gray-600 font-light'>*/}
                        {/*        {mvHitOverviewData?.updateTime &&*/}
                        {/*            `(数据更新于${dayjs(mvHitOverviewData?.updateTime).format(*/}
                        {/*                'YYYY-MM-DD HH:mm:ss',*/}
                        {/*            )})`}*/}
                        {/*    </span>*/}
                        {/*)}*/}
                        {key === IDataType.jobCount && (
                          <span className="text-sm font-light text-gray-600">
                            {mvTaskOverviewData?.updateTime &&
                              `(数据更新于${dayjs(mvTaskOverviewData?.updateTime).format('YYYY-MM-DD HH:mm:ss')})`}
                          </span>
                        )}
                      </>
                    }
                    style={{
                      // Due to internal constrains, this cannot be rewritten using the classname property
                      margin: 0,
                      padding: 0,
                      width: '100%',
                      height: '100%',
                    }}
                  >
                    {/*{key === IDataType.hitCount && (*/}
                    {/*    <GhostCard loading={mvHitOverviewLoading}>*/}
                    {/*        <Row gutter={30}>*/}
                    {/*            {item.items.map((j, index) => {*/}
                    {/*                return (*/}
                    {/*                    <Col key={index}>*/}
                    {/*                        <Statistic*/}
                    {/*                            title={j.label}*/}
                    {/*                            value={mvHitOverviewData?.[j.key]}*/}
                    {/*                        />*/}
                    {/*                    </Col>*/}
                    {/*                )*/}
                    {/*            })}*/}
                    {/*        </Row>*/}
                    {/*    </GhostCard>*/}
                    {/*)}*/}
                    {/* {key === IDataType.slowNodesCount && (
                                            <Row gutter={30}>
                                                    {item.items.map((j) => {
                                                        return (
                                                            <Col>
                                                                <Statistic
                                                                    title={j.label}
                                                                    value={MvOverviewData?.[j.key]}
                                                                />
                                                            </Col>
                                                        )
                                                    })}
                                            </Row>
                                        )} */}
                    {key === IDataType.mvCount && (
                      <GhostCard loading={mvCountOverviewLoading}>
                        <Title className={cs.MVCountTitle} level={3}>
                          {item.items
                            .map((i) => {
                              return mvCountOverviewData?.[i.key]?.toString() || '-'
                            })
                            .join('/')}
                        </Title>
                        <div className={cs.MVCountLabel}>
                          {item.items
                            .map((i) => {
                              return i.label
                            })
                            .join('/')}
                        </div>
                      </GhostCard>
                    )}
                    {key === IDataType.storeCount && (
                      <GhostCard loading={mvStorageOverviewLoading}>
                        <Title className={cs.MVCountTitle} level={3}>
                          {item.items
                            .map((i) => {
                              return formatByteSmart(mvStorageOverviewData?.[i.key] || 0)
                            })
                            .join('/')}
                        </Title>
                        <div className={cs.MVCountLabel}>
                          {item.items
                            .map((i) => {
                              return i.label
                            })
                            .join('/')}
                        </div>
                      </GhostCard>
                    )}

                    {/* {key === IDataType.analyse && (
                                            <Descriptions>
                                                {descriptionList[IDataType.analyse].items.map((j) => {
                                                    return (
                                                        <Descriptions.Item label={j.label}>
                                                            {MvOverviewData?.[j.key]}
                                                        </Descriptions.Item>
                                                    )
                                                })}
                                            </Descriptions>
                                        )} */}
                    {key === IDataType.jobCount && (
                      <GhostCard loading={mvTaskOverviewLoading}>
                        <Row gutter={30}>
                          {item.items.map((j, index) => {
                            return (
                              <Col key={index}>
                                <Statistic
                                  title={j.map((k) => k.label).join('/')}
                                  value={j.map((k) => mvTaskOverviewData?.[k.key]).join('/')}
                                />
                              </Col>
                            )
                          })}
                        </Row>
                      </GhostCard>
                    )}
                  </LayoutCard>
                </Col>
              )
            })}
          </Row>
        </Col>
      </Row>
      <LayoutCard>
        <Table
          tableLayout="fixed"
          rowKey={'mvId'}
          columns={getMLTableColumns(unit, setUnit, changeBlackListStatus, changeMVActive, handleDeleteMVSuccess)}
          {...tableProps}
          pagination={{
            ...tableProps.pagination,
            showSizeChanger: true,
          }}
        />
      </LayoutCard>
    </>
  )
}

export default MaterialList
