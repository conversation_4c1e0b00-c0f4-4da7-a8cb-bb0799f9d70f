// @ts-nocheck
import { CheckCard, ProCard } from '@ant-design/pro-components'
import { CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons'
import { Empty, List, Timeline, Typography, message } from 'antd'
import { useAntdTable, useRequest } from 'ahooks'
import { Api } from '@api'
import GLineage from '@model/GLineage'
import React, { useEffect, useState } from 'react'
import dayjs from 'dayjs'
import { useNavigate } from 'react-router-dom'
import { routerMap } from '@XEngineRouter/routerMap'

interface PropsType {
  catalogName: string
  dbName: string
  mvName: string
}

export default function MaterialDetailJobProgress(props: PropsType) {
  const { catalogName, dbName, mvName } = props

  const [partition, setPartition] = useState<string>('')
  const navigate = useNavigate()

  const formatData = (data: any) => {
    if (!data || data.length === 0) return []
    const _list = [...data]
    // 根据partition 倒序
    _list.sort((a: any, b: any) => {
      const aVal = !Number.isNaN(+a['partitionName']) ? +a['partitionName'] : +new Date(a['partitionName'])
      const bVal = !Number.isNaN(+b['partitionName']) ? +b['partitionName'] : +new Date(b['partitionName'])
      return bVal - aVal
    })
    // 根据partition 去重
    return _list.reduce((acc, current) => {
      const x = acc.find((item) => item.partitionName === current.partitionName)
      if (!x) {
        return acc.concat([current])
      } else {
        return acc
      }
    }, [])
  }

  const { tableProps: MvPartitionInfoTableProps, loading: MvPartitionInfoTableLoading } = useAntdTable(
    () => {
      return Api.apiEngineV1MvPartitionsRelatedToMvGet({
        mvName,
      }).then((res: any) => {
        const _list = formatData(res)
        if (!partition && _list.length > 0) {
          setPartition(_list[0]?.partitionName)
        }
        return { list: _list, total: _list.total }
      })
    },
    {
      defaultPageSize: 20,
    },
  )

  const {
    data: MvRelationData,
    loading: MvRelationDataLoading,
    run: getPartitionLineage,
  } = useRequest(
    () =>
      Api.apiEngineV1MvPartitionLineageGet({
        catalogName: catalogName,
        dbName: dbName,
        mvName,
        partition,
      }),
    {
      manual: true,
    },
  )

  useEffect(() => {
    if (partition) {
      getPartitionLineage()
    }
  }, [partition])

  const getTime = (time: number) => {
    if (!time) return '-'
    return dayjs(fixedTime(time)).format('YYYY-MM-DD HH:mm:ss')
  }

  const fixedTime = (time: number) => {
    if (!time) return ''
    return time.toString().length === 10 ? time * 1000 : time
  }

  const sortStartTime = (data: any) => {
    if (!data) return []
    return data.sort((a, b) => {
      const startTimeA = fixedTime(a?.startTime)
      const startTimeB = fixedTime(b?.startTime)
      if (!startTimeA && startTimeB) {
        return 1
      } else if (startTimeA && !startTimeB) {
        return -1
      } else if (!startTimeA && !startTimeB) {
        // 判断对象名称开头三个字符是否为rmv或amv
        if (
          (a.mvName.substring(0, 3) === 'rmv' && b.mvName.substring(0, 3) === 'amv') ||
          (a.mvName.substring(0, 3) === 'amv' && b.mvName.substring(0, 3) === 'rmv')
        ) {
          return a.mvName.substring(0, 3) === 'amv' ? -1 : 1
        } else {
          return 0
        }
      } else {
        return startTimeA - startTimeB
      }
    })
  }

  if (!catalogName || !dbName || !mvName) return null

  if (!MvPartitionInfoTableProps?.dataSource || MvPartitionInfoTableProps?.dataSource?.length === 0) {
    return (
      <ProCard loading={MvPartitionInfoTableLoading}>
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </ProCard>
    )
  }

  return (
    <ProCard split="vertical" bordered style={{ height: 600, overflow: 'hidden' }}>
      <ProCard
        size="small"
        loading={MvPartitionInfoTableLoading}
        colSpan={{
          xs: '236px',
          sm: '236px',
          md: '236px',
          lg: '236px',
          xl: '236px',
        }}
        style={{ height: '100%', overflowY: 'scroll' }}
      >
        <CheckCard.Group size="small" value={partition} onChange={setPartition}>
          <List
            size="small"
            {...MvPartitionInfoTableProps}
            pagination={false}
            renderItem={(item: { partitionName: string; available: boolean }, index: number) => (
              <CheckCard
                title={'partition：' + item.partitionName}
                style={{ margin: '0 0 10px 0' }}
                key={index}
                value={item.partitionName}
                avatar={
                  item.available ? (
                    <CheckCircleOutlined style={{ color: '#52c41a', fontSize: 16 }} />
                  ) : (
                    <ExclamationCircleOutlined style={{ color: '#FADB14', fontSize: 16 }} />
                  )
                }
              ></CheckCard>
            )}
          ></List>
        </CheckCard.Group>
      </ProCard>
      {MvRelationData ? (
        <ProCard loading={MvRelationDataLoading} split="vertical" style={{ height: '100%', overflow: 'hidden' }}>
          <ProCard
            colSpan={{
              xs: '240px',
              sm: '240px',
              md: '240px',
              lg: '240px',
              xl: '240px',
              xxl: '240px',
            }}
            size="small"
            style={{ height: '100%', overflowY: 'scroll', overflowX: 'hidden' }}
          >
            <Timeline
              items={sortStartTime(MvRelationData?.nodes).map((item: any) => ({
                children: (
                  <>
                    <Typography.Paragraph>{item.mvName}</Typography.Paragraph>
                    {item.startTime && (
                      <Typography.Paragraph type="secondary" style={{ fontSize: 12 }}>
                        开始时间：{getTime(item.startTime)}
                      </Typography.Paragraph>
                    )}
                    {item.endTime && (
                      <Typography.Paragraph type="secondary" style={{ fontSize: 12 }}>
                        结束时间：{getTime(item.endTime)}
                      </Typography.Paragraph>
                    )}
                  </>
                ),
              }))}
            />
          </ProCard>
          <ProCard style={{ height: '100%', overflow: 'hidden' }}>
            <GLineage
              key="GLinegae"
              data={((d) => {
                const { edges = [], nodes = [] } = d || {}
                const initNodes = nodes.map((n) => {
                  return {
                    id: n.id,
                    type: 'materializedView',
                    name: n.mvName,
                    validLink: n.validLink,
                    columnExpression: [`开始时间: ${getTime(n.startTime)}`, `结束时间: ${getTime(n.endTime)}`],
                  }
                })
                const initEdges = edges.map((e) => {
                  return {
                    source: e.source.id,
                    target: e.target.id,
                  }
                })

                return {
                  nodes: initNodes,
                  edges: initEdges,
                }
              })(MvRelationData)}
              menuProps={{
                content: '<div style="cursor: pointer;">跳转到物化视图</div>',
                handleClickMenu(_, itemInfo) {
                  const MVItemInfo = itemInfo.origin
                  if (!MVItemInfo.validLink) {
                    message.info('该物化视图不可跳转')
                    return
                  }
                  const MVName = MVItemInfo.name
                  navigate(
                    `${routerMap.smartx.materialViewDetail.path}?catalogName=${catalogName}&dbName=${dbName}&mvName=${MVName}`,
                  )
                },
              }}
              typeKey="type"
            />
          </ProCard>
        </ProCard>
      ) : (
        <ProCard loading={MvRelationDataLoading}>
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </ProCard>
      )}
    </ProCard>
  )
}
