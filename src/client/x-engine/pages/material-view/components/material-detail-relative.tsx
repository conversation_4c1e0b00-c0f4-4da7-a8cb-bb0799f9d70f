import Relative from '@ui/relative/relative'
import { Api, ApiEngineV1MvLineageGet200ResponseData } from '@api'
// import { mvLineageData2 } from '@pages/demo/relative/data'
import { Row, Col, Button } from 'antd'
import { useSearchParams, Link } from 'react-router-dom'
import { Search } from '@ui/form'
import { useRequest } from 'ahooks'
import Columns from '@ui/table/Columns'
import { splitQueryToPath } from '@libs/util'
import { routerMap } from '@XEngineRouter/routerMap'
import cs from '../material-view.module.scss'
import { Empty } from 'antd'

const columns = [
  { dataIndex: 'mvName', title: '物化视图名称' },
  { dataIndex: 'relationWithSourceMv', title: '关系', render: Columns({ type: 'relationWithSourceMv' }) },
  { dataIndex: 'usability', title: '状态', render: Columns({ type: 'usability' }) },
  {
    dataIndex: 'operation',
    title: '操作',
    render: (_, record: Record<string, any>) => {
      const path = splitQueryToPath(routerMap.smartx.materialViewDetail.path, record, ['mvName'])
      return (
        <>
          <Link to={path}>
            <Button type="link">详情</Button>
          </Link>
        </>
      )
    },
  },
]

function formatMVLineageDataToTableData(mvLineageData: ApiEngineV1MvLineageGet200ResponseData): {
  list: Record<string, unknown>[] | []
  total: number
} {
  const tableItems = mvLineageData.nodes
  return {
    list: tableItems || [],
    total: tableItems?.length || 0,
  }
}

function formatMvLineageData(mvLineageData: ApiEngineV1MvLineageGet200ResponseData) {
  return {
    ...mvLineageData,
    nodes: mvLineageData?.nodes?.map((node) => {
      const relation = node.relationWithSourceMv
      return {
        ...node,
        type: relation === 'SOURCE' ? 'frontselfSourceMV' : 'materializedView',
      }
    }),
  }
}

function MVDetailRelative() {
  const [querys] = useSearchParams()
  const mvName = querys.get('mvName')
  const { data: mvLineageData } = useRequest(Api.apiEngineV1MvLineageGet, {
    defaultParams: [
      {
        catalogName: '',
        dbName: '',
        mvName,
      },
    ],
  })
  return (
    <>
      {mvLineageData && mvLineageData.nodes && mvLineageData.nodes.length !== 0 ? (
        <Row gutter={[14, 14]}>
          <Col span={12}>
            <div style={{ height: 600 }}>
              <Relative
                miniMapSize={{ height: 100, width: 120 }}
                data={formatMvLineageData(mvLineageData)}
                typeKey="type"
                defaultType="mv"
                layout="dagre"
                layoutConf={{
                  type: 'dagre',
                  rankdir: 'BT',
                  align: 'DR',
                  controlPoints: true,
                  nodesepFunc: (node: Record<string, any>) => {
                    return node.width * 0.5
                  },
                  ranksepFunc: (node: Record<string, any>) => {
                    return node.height * 0.5
                  },
                }}
                showArrow={true}
                legend={false}
              ></Relative>
            </div>
          </Col>
          <Col span={12}>
            <div style={{ height: 600, overflow: 'auto' }} className={cs.MVDetailRelativeTableWrap}>
              <Search
                key={'search'}
                items={null}
                showSearch={false}
                table={{
                  columns,
                  rowKey: 'mvName',
                  api: () => {
                    return Promise.resolve(formatMVLineageDataToTableData(mvLineageData))
                  },
                }}
              ></Search>
            </div>
          </Col>
        </Row>
      ) : (
        <Empty description="无依赖关系" image={Empty.PRESENTED_IMAGE_SIMPLE}></Empty>
      )}
    </>
  )
}

export default MVDetailRelative
