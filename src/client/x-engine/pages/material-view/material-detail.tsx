// @ts-nocheck
import React, { useEffect, useState, useRef } from 'react'
import { useSearchParams } from 'react-router-dom'
import { PageHeader } from '@ant-design/pro-layout'
import { Descriptions, Dropdown, message, Modal, Table, Form, DatePicker, Button } from 'antd'
import { Api, ApiEngineV1MvMvDetailGet200ResponseData, MaterializedViewHitDetailsVo } from '@api'
import { FullEditorInModal } from '@model/sql'
import { editor } from 'monaco-editor'
import {
  createMVDetailDataMap,
  filterFormat,
  MDTableColumns,
  MVTaskTableColumns,
  partitionColumn,
  tableColumn,
  MVDimensionTableColumn,
  MVMeasureTableColumn,
} from './conf/dataConf'
import LayoutCard from '@ui/layoutCard/LayoutCard'
import { Search } from '@ui/form'
import { useAntdTable, useRequest } from 'ahooks'
import { DownOutlined, LoadingOutlined } from '@ant-design/icons'
import MaterialDetailOverview from './components/material-detail-overview'
import { formatSql, isEmptyValue, assignIDToArray } from '@libs/util'
import MaterialDetailJobProgress from '@pages/material-view/components/material-detail-job-progress'

function MVDesc(mvProps: {
  mv: ApiEngineV1MvMvDetailGet200ResponseData
  dataMap: {
    label: string
    key: keyof ApiEngineV1MvMvDetailGet200ResponseData
    render: React.FC<{ v: any }>
  }[]
}) {
  const { mv, dataMap } = mvProps
  return (
    <Descriptions>
      {dataMap.map((item, index) => {
        const mvValue = mv[item.key as keyof typeof mv]
        const value = (isEmptyValue(mvValue) ? '-' : mvValue) as string
        return (
          <Descriptions.Item key={index} label={item.label}>
            {item.render ? <item.render v={value} /> : value}
          </Descriptions.Item>
        )
      })}
    </Descriptions>
  )
}

enum IOperateEnum {
  disable = 'disable',
  addToBlackList = 'addToBlackList',
  delete = 'delete',
  suppleDP = 'suppleDP',
}

const MaterialDetail: React.FC = () => {
  const [query] = useSearchParams()
  const catalogName = query.get('catalogName')
  const dbName = query.get('dbName')
  const mvName = query.get('mvName')

  const [editor, setEditor] = useState<editor.IStandaloneCodeEditor | null>(null)

  const [scheduleForm] = Form.useForm()
  const [modalOpen, setModalOpen] = useState(false)
  const [editorValue, setEditorValue] = useState('')
  const [appendDataModalOpen, setAppendDataModalOpen] = useState(false)
  const [appendDataList, setAppendDataList] = useState([])
  const [scheduleModalOpen, setScheduleModalOpen] = useState(false)
  const [jobTableReload, setJobTableReload] = useState(false)
  const [selectedMenuKey, setSelectedMenuKey] = useState(IOperateEnum.disable)
  const MvHitDetailsDataCacheRef = useRef(null)

  const {
    data: mvData = {},
    loading: MVDetailDataLoading,
    run: refreshMVDetailData,
  } = useRequest(() => {
    return Api.apiEngineV1MvMvDetailGet({
      catalogName,
      dbName,
      mvName,
    })
  })
  function fillData(originData: any[], fillData: any[], start: number) {
    if (!originData || !fillData) {
      return []
    }
    const initData = [...originData]
    const length = initData.length || 0
    if (length < start) {
      initData.push(...Array(start - length))
    }
    initData.splice(start, fillData.length, ...fillData)
    return initData
  }

  function isSectionEmpty(data: any[], start: number, end: number) {
    if (!data) {
      return true
    }
    const isEmpty = !(data[start] && data[end] && data[start + 1] && data[end - 1])
    return isEmpty
  }

  const { tableProps } = useAntdTable(async (args) => {
    const { current = 1, pageSize = 10 } = args
    const CacheMaxSize = 2000
    const cacheData = MvHitDetailsDataCacheRef.current
    const start = (current <= 0 ? 0 : current - 1) * pageSize
    const end =
      (cacheData?.total !== undefined && current * pageSize > cacheData?.total ? cacheData.total : current * pageSize) -
      1
    const isLackData = isSectionEmpty(cacheData?.list, start, end)
    const curSize = current * pageSize
    const size = cacheData?.total && curSize > cacheData.total ? cacheData.total : curSize
    if (!isLackData && MvHitDetailsDataCacheRef.current) {
      return {
        list: [...(cacheData?.list || []).slice(start, start + pageSize)],
        total: cacheData?.total || 0,
      }
    }
    const requestCurrent = Math.ceil(size / CacheMaxSize)
    const requestParams = {
      current: requestCurrent,
      pageSize: CacheMaxSize,
    }
    const apiMvHitDetailsData = await Api.apiEngineV1MvHitDetailsGet({
      mvName,
      ...requestParams,
    })

    if (apiMvHitDetailsData) {
      const cacheList = cacheData?.list || []
      const list = apiMvHitDetailsData.list || []
      const finalList = fillData(cacheList, list, (requestCurrent > 0 ? requestCurrent - 1 : 0) * CacheMaxSize)
      const ans = {
        list: finalList,
        total: apiMvHitDetailsData?.total || 0,
      }
      MvHitDetailsDataCacheRef.current = ans
      return {
        list: [...ans.list.slice(start, start + pageSize)],
        total: ans.total || 0,
      }
    }
    return {
      list: [],
      total: MvHitDetailsDataCacheRef.current?.total || 0,
    }
  })

  const { tableProps: MvPartitionInfoTableProps, loading: MvPartitionInfoTableLoading } = useAntdTable(
    (params: any) => {
      return Api.apiEngineV1MvGetMvPartitionInfoGet({
        mvName,
      }).then((res) => {
        const list = res?.list || []
        const filterList = params.filters ? params.filters[1] || [] : []
        const order = params?.sorter?.order || 'ascend'
        const sortField = params?.sorter?.field || 'partitionName'
        const _list = filterFormat({ list, filterList, filterField: 'partitionStatus', order, sortField })
        return { list: _list, total: _list.length }
      })
    },
  )

  const { tableProps: MvOriginalTablesTableProps, loading: MvOriginalTablesTableLoading } = useAntdTable(async () => {
    const originalTablesData = await Api.apiEngineV1MvOriginalTablesGet({
      catalog: catalogName,
      database: dbName,
      mvName,
    })
    const list = assignIDToArray(originalTablesData?.list)
    return {
      list,
      total: originalTablesData?.total || 0,
    }
  })

  useEffect(() => {
    if (editor && modalOpen) {
      try {
        const { res: formatValue } = formatSql(editorValue || '')
        editor.setValue(formatValue)
      } catch (err) {
        editor.setValue(editorValue)
      }
    }
  }, [modalOpen, editor, editorValue])

  const { run: changeMVActive, loading: changeMVActiveLoading } = useRequest(Api.apiEngineV1MvActivePost, {
    manual: true,
    onSuccess(data, arg) {
      if (data) {
        message.success(arg[0].active ? '已启用' : '已禁用')
        refreshMVDetailData()
      }
    },
  })

  // 新增补数
  const { run: scheduleRunTask, loading: scheduleRunTaskLoading } = useRequest(
    (scheduleTime: string) =>
      Api.apiEngineV1SchedulerRunTaskPost({
        taskName: '__dip_' + mvName,
        execType: 'COMPLEMENT_DATA',
        scheduleTime: scheduleTime,
        startParams: JSON.stringify({ dt: '$[yyyyMMdd]', client: 'front' }),
      }),
    {
      onSuccess: () => {
        message.success('补数成功')
        const timeoutId = setTimeout(() => {
          setJobTableReload((reload) => !reload)
          clearTimeout(timeoutId)
        }, 800)

        setScheduleModalOpen(false)
      },
      manual: true,
    },
  )

  // const { run: changeBlackListStatus } = useRequest(Api.apiEngineV1MvBlacklistManagePost, {
  //     manual: true,
  //     onSuccess(data) {
  //         if (data) {
  //             message.success('已加入黑名单')
  //         }
  //     },
  // })
  // const { run: MVDrop } = useRequest(Api.apiEngineV1MvDropDelete, {
  //     manual: true,
  //     onSuccess(data) {
  //         if (data.success) {
  //             message.success('已删除')
  //         }
  //     },
  // })
  const menuItems = [
    {
      label: mvData.active ? '禁用物化' : '启用物化',
      key: IOperateEnum.disable,
      icon: changeMVActiveLoading && <LoadingOutlined />,
      disabled: false,
      onClick: () => {
        changeMVActive({ active: !mvData.active, mvName })
      },
      loading: changeMVActiveLoading,
    },
    {
      label: '新增补数',
      key: IOperateEnum.suppleDP,
      icon: scheduleRunTaskLoading && <LoadingOutlined />,
      onClick: () => {
        setScheduleModalOpen(true)
      },
      loading: scheduleRunTaskLoading,
    },
    // {
    //     label: '加入黑名单',
    //     key: IOperateEnum.addToBlackList,
    //     onClick: () => {
    //         changeBlackListStatus({ mvName, blacklist: true })
    //     },
    // },
    // {
    //     label: '删除',
    //     key: IOperateEnum.delete,
    //     onClick: () => {
    //         MVDrop({ mvName, displayName })
    //     },
    // },
  ]

  return (
    <React.Fragment key={mvName}>
      <PageHeader
        title="物化详情"
        onBack={() => window.history.back()}
        extra={
          <Dropdown.Button
            loading={
              menuItems.find((item) => {
                return item.key === selectedMenuKey
              })?.loading
            }
            icon={<DownOutlined />}
            type={'primary'}
            size={'middle'}
            className={'lepg37n78po'}
            menu={{
              selectable: true,
              items: menuItems,
              selectedKeys: [selectedMenuKey],
              defaultSelectedKeys: [IOperateEnum.disable],
              onClick(e) {
                setSelectedMenuKey(e.key)
              },
            }}
            onClick={() => {
              menuItems
                .find((i) => {
                  return i.key === selectedMenuKey
                })
                .onClick()
            }}
          >
            {
              menuItems.find((item) => {
                return item.key === selectedMenuKey
              })?.label
            }
          </Dropdown.Button>
        }
      />

      <LayoutCard title={`物化视图名称：${mvName || '-'}`} style={{ marginBottom: 0 }} loading={MVDetailDataLoading}>
        {mvData && (
          <MVDesc
            mv={mvData}
            dataMap={createMVDetailDataMap({
              showSQL: (SQL: string) => {
                setModalOpen(true)
                setEditorValue(SQL)
              },
              showAppendDataDetail: (appendDataList) => {
                setAppendDataModalOpen(true)
                setAppendDataList(appendDataList)
              },
            })}
          ></MVDesc>
        )}
      </LayoutCard>
      <LayoutCard
        tabList={[
          {
            label: '任务列表',
            key: 'jobTable',
            children: (
              <Search
                showTableWithCard={false}
                showSearch={false}
                reload={jobTableReload}
                key={'job-search'}
                items={[]}
                table={{
                  columns: MVTaskTableColumns({
                    setReload: setJobTableReload,
                    onClickAppendData: () => {
                      scheduleForm.resetFields()
                      setScheduleModalOpen(true)
                    },
                  }),
                  rowKey: 'id',
                  api: (search) => {
                    if (!mvName) {
                      return Promise.resolve({ list: [], total: 0 })
                    }
                    return Api.apiEngineV1SchedulerTasksGet({
                      isWithPartitions: true,
                      searchVal: `__dip_${mvName}`,
                      ...search,
                    }).then((res) => {
                      const list = res?.list || []
                      const order = search?.sorter?.order || 'ascend'
                      const sortField = search?.sorter?.field || 'partition'
                      const filterList = search.filters ? search.filters['state'] || [] : []
                      const _list = filterFormat({
                        list,
                        filterList,
                        filterField: 'state',
                        order,
                        sortField,
                      })
                      return { list: _list, total: res?.total || _list.length }
                    })
                  },
                }}
              />
            ),
          },
          { label: '物化资源总览', key: 'overview', children: <MaterialDetailOverview mvName={mvName} /> },
          {
            label: '命中列表',
            key: 'list',
            children: (
              <Table
                columns={MDTableColumns((record: MaterializedViewHitDetailsVo) => {
                  setModalOpen(true)
                  setEditorValue(record?.hitSqlScript || '')
                })}
                {...tableProps}
              />
            ),
          },
          {
            label: 'Partition 分区状态',
            key: 'partition',
            children: (
              <Table
                columns={partitionColumn}
                rowKey="partitionName"
                {...MvPartitionInfoTableProps}
                loading={MvPartitionInfoTableLoading}
              />
            ),
          },
          {
            label: '物化视图依赖关系',
            key: 'dependencies',
            children: <MaterialDetailJobProgress catalogName={catalogName} dbName={dbName} mvName={mvName} />,
          },
          {
            label: '物化视图来源数据表',
            key: 'MVOriginlTables',
            children: (
              <Table
                columns={tableColumn}
                rowKey="Id"
                {...MvOriginalTablesTableProps}
                loading={MvOriginalTablesTableLoading}
              />
            ),
          },
          {
            label: '度量',
            key: 'MVMeasure',
            children: (
              <Table
                columns={MVMeasureTableColumn}
                rowKey="name"
                dataSource={mvData?.modelDesc?.measures?.map?.((i) => {
                  return { name: i }
                })}
              />
            ),
          },
          {
            label: '维度',
            key: 'MVDimension',
            children: (
              <Table
                columns={MVDimensionTableColumn}
                rowKey="name"
                dataSource={mvData?.modelDesc?.dimensionColumns?.map?.((i) => {
                  return { name: i }
                })}
              />
            ),
          },
        ]}
      />

      <FullEditorInModal
        readOnly={true}
        cancelButtonProps={{ style: { display: 'none' } }}
        onOk={() => setModalOpen(false)}
        onCancel={() => setModalOpen(false)}
        showSearchTableTree={false}
        editor={editor}
        setEditor={setEditor}
        open={modalOpen}
      />

      <Modal
        width={800}
        open={appendDataModalOpen}
        onCancel={() => setAppendDataModalOpen(false)}
        title="补数详情"
        footer={null}
      >
        <Table
          dataSource={appendDataList}
          columns={[
            { dataIndex: 'appendDataStartTime', title: '补数开始时间' },
            { dataIndex: 'appendDataEndTime', title: '补数结束时间' },
          ]}
        ></Table>
      </Modal>
      {/* 新增补数Modal */}
      <Modal
        width={500}
        open={scheduleModalOpen}
        onCancel={() => {
          scheduleForm.resetFields()
          setScheduleModalOpen(false)
        }}
        title="补数"
        footer={null}
      >
        <Form
          form={scheduleForm}
          onFinish={(values) => {
            const { scheduleTime } = values
            scheduleRunTask(
              JSON.stringify({
                complementStartDate: scheduleTime[0].format('YYYY-MM-DD 00:00:00'),
                complementEndDate: scheduleTime[1].format('YYYY-MM-DD 00:00:00'),
              }),
            )
          }}
        >
          <Form.Item
            name="scheduleTime"
            label="补数时间"
            rules={[
              {
                required: true,
                message: '请选择补数时间',
              },
            ]}
          >
            <DatePicker.RangePicker format="YYYY-MM-DD 00:00:00" />
          </Form.Item>
          <Form.Item
            style={{
              textAlign: 'end',
            }}
          >
            <Button type="primary" htmlType="submit" loading={scheduleRunTaskLoading}>
              新增补数
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </React.Fragment>
  )
}
export default MaterialDetail
