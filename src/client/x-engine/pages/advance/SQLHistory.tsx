// @ts-nocheck
import { Button, Tag, Modal, Space, Typography } from 'antd'
import dayjs from 'dayjs'

import Broadcast from '@libs/broadcast'

const infoBox = (text: string) => {
  Modal.info({
    title: 'SQL脚本：',
    centered: true,
    content: (
      <div>
        <p>{text}</p>
      </div>
    ),
  })
}

const descriptionBox = (record: any) => {
  const items: { key: number; label: string; children: any }[] = []

  Object.keys(record).forEach((keys, index) => {
    if (keys === 'plan' || keys === 'sql') {
      return
    } else {
      items.push({
        key: index,
        label: keys,
        children: record[keys],
      })
    }
  })

  Broadcast.trigger('@page-sql-history', items)
}

const tableColumns = [
  {
    title: 'ID',
    dataIndex: 'queryId',
    key: 'queryId',
    render: (text: string) => {
      return text
    },
  },
  {
    title: '表',
    dataIndex: 'relatedTables',
    ellipsisL: true,
    render(relatedTables: string[] = []) {
      return relatedTables.toString() || '-'
    },
  },
  {
    title: '状态',
    dataIndex: 'result',
    width: 60,
    render: (text: string) => {
      return text === 'success' ? <Tag color="green">成功</Tag> : <Tag color="red">失败</Tag>
    },
  },
  {
    title: '系统耗时',
    dataIndex: 'sysCost',
    key: 'sysCost',
  },
  {
    title: 'CPU',
    dataIndex: 'cpuCost',
    key: 'cpuCost',
  },
  {
    title: '用户',
    dataIndex: 'userName',
    key: 'userName',
    render: (text: string) => (text == 'null' ? '-' : text),
  },
  {
    title: '开始/结束时间',
    dataIndex: 'endTime',
    key: 'endTime',
    render: (_: any, record: { startTime: string; endTime: string }) => {
      const { startTime, endTime } = record
      return (
        <>
          <Tag>{startTime ? dayjs(startTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</Tag>
          <Tag>{endTime ? dayjs(endTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</Tag>
        </>
      )
    },
  },
  {
    title: '操作',
    width: 100,
    render: (_: any, record: any) => {
      const { sql } = record
      return (
        <Space>
          <Typography.Link
            onClick={() => {
              infoBox(sql)
            }}
          >
            SQL
          </Typography.Link>
          <Typography.Link
            onClick={() => {
              descriptionBox(record)
            }}
          >
            详情
          </Typography.Link>
        </Space>
      )
    },
  },
]

const searchItems = [
  {
    label: '表目录',
    tag: 'Input',
    name: 'catalog',
    allowClear: true,
  },
  {
    label: '表库',
    tag: 'Input',
    name: 'database',
    allowClear: true,
  },
  {
    label: '表名称',
    tag: 'Input',
    name: 'table',
    allowClear: true,
  },
  {
    label: '状态',
    tag: 'Select',
    name: 'state',
    allowClear: true,
    options: [
      {
        label: '成功',
        value: 'success',
      },
      {
        label: '失败',
        value: 'failed',
      },
    ],
  },
  {
    tag: 'Input',
    label: '用户',
    name: 'userName',
    allowClear: true,
  },
  {
    showTime: true,
    label: '开始时间',
    tag: 'DatePicker',
    name: 'startTime',
    placeholder: '2024-08-01 12:00:00',
    format: 'YYYY-MM-DD HH:mm:ss',
    allowClear: true,
  },
  {
    showTime: true,
    label: '结束时间',
    tag: 'DatePicker',
    name: 'endTime',
    placeholder: '2024-08-01 12:00:00',
    format: 'YYYY-MM-DD HH:mm:ss',
    allowClear: true,
  },
  {
    tag: 'Input',
    label: '耗时大于',
    name: 'costTime',
    placeholder: '0.002',
    allowClear: true,
  },
  {
    tag: 'buttons',
    wrapperCol: { span: 16 },
    children: [
      {
        tag: 'Button',
        name: 'reset',
        label: '重置',
        htmlType: 'button',
      },
      {
        tag: 'Button',
        name: 'submit',
        label: '查询',
        type: 'primary',
        htmlType: 'submit',
      },
    ],
  },
]

export { searchItems, tableColumns }
