import { Api } from '@api'
import { Search } from '@ui/form'
import Broadcast from '@libs/broadcast'
import { tableColumns, searchItems } from './SQLHistory'
import React, { useEffect, useState } from 'react'
import { Descriptions, Modal } from 'antd'
import { PageHeader } from '@ant-design/pro-layout'
import dayjs from 'dayjs'
import { pickBy } from 'lodash'
import { askBIApiUrls } from 'src/shared/url-map'
import axios from 'axios'
import { APIResponse } from 'src/shared/common-types'

type TableSearchType = {
  startTime: string
  endTime: string
}

type SQLHistoryTableDataType = {
  queryId: string
  relatedTables: string[]
  result: 'success' | 'failed'
  sysCost: string
  cpuCost: string
  userName: string
  startTime: string
  endTime: string
}
const SQLHistory = () => {
  const [infoModal, setInfoModal] = useState(false)
  const [descItems, setDescItems] = useState([
    {
      key: '1',
      label: 'Product',
      children: 'Cloud Database',
    },
  ])

  const setInfoModalStatus = (type: boolean) => {
    setInfoModal(type)
  }

  useEffect(() => {
    Broadcast.listen('@page-sql-history', (data: any) => {
      setDescItems(data)
      setInfoModal(true)
    })
  }, [location])

  return (
    <>
      <PageHeader title="sql查询历史" />
      <Search
        key={'p-sql-history'}
        items={searchItems}
        table={{
          columns: tableColumns,
          rowKey: 'queryId',
          api: async (params: TableSearchType) => {
            if (params.startTime) {
              params.startTime = dayjs(params.startTime).format('YYYY-MM-DD HH:mm:ss')
            }
            if (params.endTime) {
              params.endTime = dayjs(params.endTime).format('YYYY-MM-DD HH:mm:ss')
            }
            const response = await axios.get<APIResponse<{ list: SQLHistoryTableDataType[]; total: number }>>(
              askBIApiUrls.xengineSql.queryHistory,
              {
                params: pickBy(params, Boolean),
              },
            )
            return response.data?.data
          },
        }}
      />
      <Modal
        title="SQL查询详情"
        open={infoModal}
        width={'80%'}
        onOk={() => setInfoModalStatus(false)}
        onCancel={() => setInfoModalStatus(false)}
      >
        <Descriptions bordered={true}>
          {descItems.map((item) => {
            return <Descriptions.Item label={item.label}>{item.children}</Descriptions.Item>
          })}
        </Descriptions>
      </Modal>
    </>
  )
}

export default SQLHistory
