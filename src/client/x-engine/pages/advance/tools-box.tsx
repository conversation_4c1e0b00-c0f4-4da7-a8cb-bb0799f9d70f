// @ts-nocheck
import React, { useState } from 'react'
import LayoutCard from '@ui/layoutCard/LayoutCard'
import { PageHeader } from '@ant-design/pro-layout'
import { Api } from '@api'
import { useRequest } from 'ahooks'
import { Input, Button, Form, Col, Row, Select } from 'antd'
import cs from './advance.module.scss'
import { getCommandsInfo, CommandsEnum } from './conf'
import HostSelect from './components/HostSelect'
import EditorModal from './EditorModal'
import PutSettingModal from './components/PutSettingModal'

export default function ToolsBox() {
  const [editorModalOpen, setEditorModalOpen] = useState(false)
  const [PutSettingModalProps, setPutSettingModalProps] = useState<{
    host: string
    name: string
    initValue: string
  }>({
    host: '',
    name: '',
    initValue: '',
  })
  const [PutSettingModalOpen, setPutSettingModalOpen] = useState(false)

  const [form] = Form.useForm()
  const [selectCommand, setSelectCommand] = useState<keyof typeof CommandsEnum>('listReplicas')
  const [host, setHost] = useState('')
  const [isChangeCommandReady, setIsChangeCommandReady] = useState(true)
  const {
    loading: getCommandDataLoading,
    run: getCommandDataRun,
    data: commandData,
  } = useRequest(
    ({
      host,
      command,
      name,
      refreshThreadPoolName,
      keyWords,
    }: {
      host: string
      command: string
      name?: string
      refreshThreadPoolName?: string
      keyWords?: string
    }) => {
      const api = getCommandsInfo.find((i) => i.commandText === command)?.api
      if (!api) {
        return Promise.resolve({})
      }
      const req: { address: string; name?: string; keyWords: string } = {
        address: host,
      }
      if (name) {
        req.name = name
      }
      if (refreshThreadPoolName) {
        req.name = refreshThreadPoolName
      }
      if (keyWords) {
        req.keyWords = keyWords
      }
      api.prototype = Api
      return api.call(Api, req).then(
        (res) => res,
        (err) => err,
      )
    },
    {
      manual: true,
    },
  )

  const {
    data: detailData,
    run: getDetailData,
    loading: detailInfoLoading,
  } = useRequest(
    async (info: Record<string, any>) => {
      const type = info.type
      setEditorModalOpen(true)

      switch (type) {
        case CommandsEnum.listReplicas: {
          return Api.apiEngineV1ReplicaGetGet({
            address: host,
            name: info.name,
          }).then((res) => {
            if (typeof res?.replicaInfo === 'string') {
              return res.replicaInfo
            }
          })
        }
        case CommandsEnum.listTasks: {
          return Api.apiEngineV1TaskGetGet({
            address: host,
            taskName: info.taskName,
          }).then((res) => {
            if (typeof res?.queryInfo === 'string') {
              return res?.queryInfo
            }
          })
        }
      }
    },
    {
      manual: true,
    },
  )
  const CommandChildren = getCommandsInfo.find((i) => i.commandText === selectCommand)?.children
  return (
    <>
      <PageHeader title="工具箱" />
      <LayoutCard
        className={cs.host}
        style={{
          padding: '14px 0 0 0',
        }}
      >
        <Form
          layout="horizontal"
          form={form}
          wrapperCol={{ span: 24 }}
          initialValues={{
            command: 'listReplicas',
          }}
          onFinish={(values) => {
            setIsChangeCommandReady(true)
            getCommandDataRun(values)
            const { host = '' } = values
            setHost(host.trim())
          }}
        >
          <Row gutter={[20, 0]}>
            <Col span={8}>
              <Form.Item
                label="命令选择"
                name="command"
                rules={[
                  {
                    required: true,
                    message: '请选择命令',
                  },
                ]}
              >
                <Select
                  onChange={(command) => {
                    setSelectCommand(command)
                    setIsChangeCommandReady(false)
                  }}
                  options={Object.keys(CommandsEnum).map((c) => {
                    const label = CommandsEnum[c as keyof typeof CommandsEnum] || c
                    return {
                      label: label,
                      value: c,
                    }
                  })}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="地址"
                name="host"
                rules={[
                  {
                    required: true,
                    message: '请输入地址',
                  },
                ]}
              >
                <HostSelect nodeTypes={['compute_nodes']} />
              </Form.Item>
            </Col>
            {CommandsEnum[selectCommand] === CommandsEnum.getSetting && (
              <Col span={8}>
                <Form.Item
                  label="name参数"
                  name="name"
                  rules={[
                    {
                      required: true,
                      message: '请输入setting的name值',
                    },
                  ]}
                >
                  <Input placeholder="请输入setting的name的值" />
                </Form.Item>
              </Col>
            )}
            {CommandsEnum[selectCommand] === CommandsEnum.refreshThreadPool && (
              <Col span={8}>
                <Form.Item
                  label="name参数"
                  name="refreshThreadPoolName"
                  rules={[
                    {
                      required: true,
                      message: '请选择',
                    },
                  ]}
                >
                  <Select
                    options={[
                      { label: 'mpp', value: 'mpp' },
                      { label: 'mv', value: 'mv' },
                      { label: 'statistics', value: 'statistics' },
                    ]}
                  />
                </Form.Item>
              </Col>
            )}
            {CommandsEnum[selectCommand] === CommandsEnum.listSettings && (
              <Col span={8}>
                <Form.Item label="关键字" name="keyWords">
                  <Input placeholder="请输入搜索关键字" />
                </Form.Item>
              </Col>
            )}
            <Col span={24}>
              <Form.Item
                wrapperCol={{ flex: 'none' }}
                style={{
                  textAlign: 'end',
                }}
              >
                <Button type="primary" htmlType="submit" loading={getCommandDataLoading}>
                  执行
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>

        <CommandChildren
          data={isChangeCommandReady && !getCommandDataLoading ? commandData : undefined}
          address={host}
          loading={getCommandDataLoading}
          onClickDetail={(info = {}) => {
            getDetailData(info)
          }}
          onClickSetOperation={(info = {}) => {
            const type = info.type
            switch (type) {
              case CommandsEnum.listSettings: {
                setPutSettingModalOpen(true)
                setPutSettingModalProps({
                  initValue: info.value,
                  name: info.name,
                  host: host,
                })
                break
              }
            }
          }}
        />
      </LayoutCard>
      <EditorModal
        loading={detailInfoLoading}
        open={editorModalOpen}
        value={detailData}
        onClose={() => {
          setEditorModalOpen(false)
        }}
      />
      <PutSettingModal
        open={PutSettingModalOpen}
        {...PutSettingModalProps}
        onSuccess={() => {
          getCommandDataRun({ host, command: selectCommand })
          setPutSettingModalOpen(false)
        }}
        onClose={() => {
          setPutSettingModalOpen(false)
        }}
      />
    </>
  )
}
