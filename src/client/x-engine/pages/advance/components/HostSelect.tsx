// @ts-nocheck
import React, { useState, useRef } from 'react'
import { useLocalStorageState, useRequest } from 'ahooks'
import { Tooltip, Divider, Space, Input, Button, Select, Popover, Empty } from 'antd'
import { PlusOutlined, InfoCircleOutlined } from '@ant-design/icons'
import cs from '../advance.module.scss'
import { CloseOutlined } from '@ant-design/icons'
import { Api } from '@api'

function NodeIPsSelectBtn({
  btnText,
  nodeType,
  onClickNodeIPItem,
}: {
  btnText: string
  nodeType: 'compute_nodes' | 'xengine_nodes'
  onClickNodeIPItem?: (host: string) => any
}) {
  const portMap = {
    compute_nodes: '9011',
    xengine_nodes: '8044',
  }
  const { data: initNodeData, loading } = useRequest(Api.apiEngineV1DiagnosisNodesListGet, {
    defaultParams: [{ nodeType: nodeType }],
  })
  const nodeData = (Array.isArray(initNodeData) ? initNodeData : [])
    .filter((n = {}) => typeof n.host === 'string')
    .map((n) => `${n.host}:${portMap[nodeType]}`)

  return (
    <Popover
      title={btnText}
      trigger="click"
      placement="right"
      content={
        <div style={{ width: '180px' }}>
          {nodeData.length > 0 ? (
            nodeData.map((n, i) => (
              <Button
                key={i}
                type="text"
                block
                onClick={() => {
                  if (typeof onClickNodeIPItem === 'function') {
                    onClickNodeIPItem(n)
                  }
                }}
              >
                {n}
              </Button>
            ))
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </div>
      }
    >
      <Button type="dashed" block loading={loading}>
        {btnText}
      </Button>
    </Popover>
  )
}

function HostSelect({
  value,
  onChange,
  nodeTypes = [],
}: {
  value?: string
  onChange?: (value: string) => any
  nodeTypes?: string[]
}) {
  const inputRef = useRef(null)
  const [name, setName] = useState('')
  const [selectValue, setSelectValue] = useState(value || '')
  const onNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setName(event.target.value)
  }
  const triggerChangeEvent = (value: string) => {
    setSelectValue(value)
    onChange(value)
  }
  const addItem = (e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => {
    e.preventDefault()
    if (name.length) {
      setItems([...new Set([...items, name])])
      setName('')
      setTimeout(() => {
        inputRef.current?.focus()
      }, 0)
    }
  }
  const [items, setItems] = useLocalStorageState('logsHost', {
    defaultValue: [],
  })

  return (
    <Select
      value={selectValue}
      style={{ width: '100%' }}
      showSearch
      className={cs.hostSelect}
      placeholder="输入筛选"
      optionRender={(optionItem) => {
        const { label, value } = optionItem
        return (
          <div
            style={{
              overflow: 'hidden',
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <span>{label}</span>
            <CloseOutlined
              style={{
                fontSize: '12px',
                cursor: 'pointer',
              }}
              onClick={(e) => {
                e.stopPropagation()
                setItems(items.filter((v) => v !== value))
              }}
            />
          </div>
        )
      }}
      onChange={triggerChangeEvent}
      dropdownRender={(menu) => {
        return (
          <>
            {menu}
            <Divider style={{ margin: '8px 0' }} />
            {nodeTypes.map((nodeType) => {
              switch (nodeType) {
                case 'compute_nodes':
                case 'xengine_nodes': {
                  return (
                    <>
                      <NodeIPsSelectBtn
                        btnText={nodeType}
                        nodeType={nodeType}
                        onClickNodeIPItem={(host: string) => {
                          triggerChangeEvent(host)
                        }}
                      />
                      <Divider style={{ margin: '8px 0' }} />
                    </>
                  )
                }
              }
            })}
            <Space
              className={cs.hostInnerSpace}
              style={{
                padding: '0 8px 4px',
                width: '100%',
              }}
            >
              <Input
                placeholder="**************:18042"
                ref={inputRef}
                value={name}
                onChange={onNameChange}
                onKeyDown={(e) => e.stopPropagation()}
                style={{
                  width: '100%',
                }}
                suffix={
                  <Tooltip title="点击添加样例数据 **************:18042">
                    <InfoCircleOutlined
                      onClick={() => {
                        setName('**************:18042')
                      }}
                      className={cs.hostIcon}
                      style={{ color: 'rgba(0,0,0,.45)' }}
                    />
                  </Tooltip>
                }
              />
              <Button type="text" icon={<PlusOutlined />} onClick={addItem}></Button>
            </Space>
          </>
        )
      }}
      options={items.map((item) => ({
        label: item,
        value: item,
      }))}
    />
  )
}

export default HostSelect
