// @ts-nocheck
import { Card, theme, CardProps } from 'antd'
import type { ReactNode } from 'react'
import './layoutCard.module.scss'
import ghostCS from './ghostCard.module.scss'

interface LayputCardType {
    children?: ReactNode[] | ReactNode
    style?: Record<string, string | number>
    isBorderHeadType?: boolean
}

const LayoutCard = (props: LayputCardType & CardProps) => {
    const {
        token: { padding, margin, borderRadius },
    } = theme.useToken()

  const originStyles: Record<string, Record<string, number | string>> = {
        style: {
            margin: `${margin}px 0`,
            borderRadius,
        },
        bodyStyle: {
            margin: 0,
            padding: props.title ? `0 ${padding}px ${padding}px ${padding}px` : padding,
        },
        headStyle: {
            border: 0,
            margin: 0,
            padding,
        },
    }
    if (props.isBorderHeadType) {
        originStyles.bodyStyle = {
            margin: 0,
            padding,
        }
        originStyles.headStyle = {
            border: '',
        }
    }

    for (const styleKey in originStyles) {
        if (Object.hasOwnProperty.call(props, styleKey)) {
            originStyles[styleKey] = {
                ...originStyles[styleKey],
                ...props[styleKey as keyof props],
            }
        }
    }

    return (
        <>
            <Card {...props} {...originStyles} className={`xengine-laypoutcard ${props.className}`}>
                <>{props.children}</>
            </Card>
        </>
    )
}

export function GhostCard(props: CardProps) {
    return (
        <Card
            className={ghostCS.ghostCard}
            bodyStyle={{
                backgroundColor: 'transparent',
                padding: 0,
                borderRadius: 0,
            }}
            {...props}
        >
            {props.children}
        </Card>
    )
}

export default LayoutCard
