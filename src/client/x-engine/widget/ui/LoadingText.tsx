import { Typography, Popconfirm } from 'antd'
import type { PopconfirmProps } from 'antd'
import React, { ReactNode } from 'react'
import { useRequest } from 'ahooks'
import { LoadingOutlined } from '@ant-design/icons'

export default function LoadingText({
    api,
    onSuccess,
    onFail,
    children,
    type,
    disabled,
    popconfirmProps,
}: {
    children: string | ReactNode
    type: 'link' | 'danger' | 'secondary' | 'success' | 'warning'
    api: (...argvs: any[]) => Promise<any>
    disabled?: boolean
    onSuccess?: (...agrvs: any[]) => any
    onFail?: (...agrvs: any[]) => any
    popconfirmProps?: PopconfirmProps | boolean
}) {
    const { loading, run } = useRequest(api, {
        manual: true,
        onSuccess(res) {
            if (typeof onSuccess === 'function') {
                onSuccess(res)
            }
        },
        onError(err) {
            if (typeof onFail === 'function') {
                onFail(err)
            }
        },
    })
    switch (type) {
        case 'danger':
        case 'secondary':
        case 'success':
        case 'warning': {
            return (
                <Typography.Text
                    style={{
                        cursor: 'pointer',
                    }}
                    type={type}
                    onClick={() => !popconfirmProps && !loading && run()}
                    disabled={disabled}
                >
                    {typeof popconfirmProps === 'object' ? (
                        <Popconfirm
                            {...popconfirmProps}
                            title={popconfirmProps.title || ''}
                            onConfirm={() => !loading && run()}
                        >
                            {loading && <LoadingOutlined />} {children}
                        </Popconfirm>
                    ) : (
                        <>
                            {loading && <LoadingOutlined />} {children}
                        </>
                    )}
                </Typography.Text>
            )
        }
        case 'link': {
            return (
                <Typography.Link onClick={() => !popconfirmProps && !loading && run()} disabled={disabled}>
                    {typeof popconfirmProps === 'object' ? (
                        <Popconfirm title={popconfirmProps.title || ''} onConfirm={() => !loading && run()}>
                            {loading && <LoadingOutlined />} {children}
                        </Popconfirm>
                    ) : (
                        <>
                            {loading && <LoadingOutlined />} {children}
                        </>
                    )}
                </Typography.Link>
            )
        }
    }
}
