import React, { useEffect, useRef, useState } from 'react'
import { useSize, useToggle, useThrottleFn } from 'ahooks'
import { Updater, useImmer } from 'use-immer'
import styles from './reflex.module.scss'
import { ReflexContainer, ReflexSplitter, ReflexElement, ReflexElementProps } from 'react-reflex'
import { LeftOutlined } from '@ant-design/icons'
import UIDivider from '@ui/divider/UIDivider'

enum IIndex {
    first = 'first',
    second = 'second',
}

type flexInfo = Record<keyof typeof IIndex, number>

type ReflexElementPropsWidthChildren = ReflexElementProps & {
    children?: React.ReactNode
    id: string
    minSize: number
    setFlexInfo: Updater<Partial<flexInfo>>
    name: keyof typeof IIndex
}

const FIRST_MIN_SIZE = 200
const SECOND_MIN_SIZE = FIRST_MIN_SIZE

enum IOrientation {
    horizontal = 'horizontal',
    vertical = 'vertical',
}

interface IFakeSpliter {
    type: keyof typeof IOrientation
    operateBtn: React.ReactNode
}

const FakeSpliter = ({ type, operateBtn }: IFakeSpliter) => {
    return (
        <div className={`${styles.fakeSpliter} ${type}`}>
            <UIDivider type={type} />
            {operateBtn}
        </div>
    )
}

const CustomCom = (props: ReflexElementPropsWidthChildren & { innerRef: any }) => {
    const cur = useRef()
    const { run } = useThrottleFn(
        () => {
            props.setFlexInfo?.((draft) => {
                draft[props.name] = props.flex
            })
        },
        { wait: 100 },
    )
    useEffect(() => {
        return run()
    }, [props.flex])
    return (
        <ReflexElement ref={cur} {...props}>
            <div className='pane-content' ref={cur}>
                <div className='ctrl-pane-content'>{props.children}</div>
            </div>
        </ReflexElement>
    )
}

const ControlledElement = React.forwardRef((props: ReflexElementPropsWidthChildren, ref) => {
    return <CustomCom innerRef={ref} {...props} />
})

interface ITwoColumnFlexProps {
    first: {
        flex?: number
        children: React.ReactNode
    }
    second: {
        children: React.ReactNode
    }
    orientation?: keyof typeof IOrientation
    willHiddenIndex?: keyof typeof IIndex
    onSizeChange?: (size: { height: number; width: number } | undefined) => void
}

const DISTANT_TO_SCREEN_BOTTOM = 26
// the flex will not use, when minSize is set. so you  must change the minSize before changing the flex and  after shrink this button will not be able to drag
// there is two status  one is draggable the other is non-draggable
// Firstly, it is essential to have a method that can control both non-draggable and its corresponding status;
enum IVisibleStatus {
    visible = 'visible',
    hidden = 'hidden',
}

enum ISplitStatus {
    none = 'none',
    showFake = 'showFake',
    showReal = 'showReal',
}

const useCompoentShowControl = (
    willHiddenIndex: keyof typeof IIndex,
    visibleStatus: keyof typeof IVisibleStatus,
    first: ITwoColumnFlexProps['first'],
    second: ITwoColumnFlexProps['second'],
) => {
    let firstShow = true
    let secondShow = true

    // set firstShow and secondShow
    if (visibleStatus === IVisibleStatus.hidden) {
        if (willHiddenIndex === IIndex.first) {
            firstShow = false
        }
        if (willHiddenIndex === IIndex.second) {
            secondShow = false
        }
    }
    if (!first.children) {
        firstShow = false
    }
    if (!second.children) {
        secondShow = false
    }

    // set spliterStatus
    let spliterSatus: ISplitStatus
    if (first?.children && second?.children) {
        if (visibleStatus === IVisibleStatus.visible) {
            spliterSatus = ISplitStatus.showReal
        } else {
            spliterSatus = ISplitStatus.showFake
        }
    } else {
        spliterSatus = ISplitStatus.none
    }

    return { firstShow, secondShow, spliterSatus }
}
const TwoColumnFlex = ({
    first,
    second,
    onSizeChange,
    orientation = IOrientation.vertical,
    willHiddenIndex = IIndex.first,
}: ITwoColumnFlexProps) => {
    const [firstPaneFlexValue, setFirstPaneFlexValue] = useImmer<number>(1)
    useEffect(() => {
        setFirstPaneFlexValue(first.flex ?? 1)
    }, [first.flex])

    // save flex value information for button direction judgement
    const [flexInfo, setFlexInfo] = useImmer<Partial<flexInfo>>({})
    // save flex value for recover when  shrink or grow button before is clicked
    // mainly limit minSize for drag
    const [flexConfig, setFlexConfig] = useImmer<Record<keyof typeof IIndex, ReflexElementPropsWidthChildren>>({
        first: {
            name: 'first',
            direction: 1,
            id: 'first',
            minSize: FIRST_MIN_SIZE,
            setFlexInfo,
        },
        second: {
            name: 'second',
            direction: -1,
            id: 'second',
            minSize: SECOND_MIN_SIZE,
            setFlexInfo,
        },
    })

    useEffect(() => {
        setFlexConfig((draft) => {
            draft.first.minSize = first.children ? FIRST_MIN_SIZE : 0
            draft.second.minSize = second.children ? SECOND_MIN_SIZE : 0
        })
    }, [first, second])
    const queryContainer = useRef(null)
    const [domHeight, setDomHeight] = useState<string>()
    useEffect(() => {
        if (queryContainer.current?.ref?.current) {
            const dom = queryContainer.current.ref.current
            const top = dom.getBoundingClientRect().top
            // this will be change when this component be used at SQL-query, the distant will be dynamic calculate
            setDomHeight(`calc(100vh - ${top + DISTANT_TO_SCREEN_BOTTOM}px)`)
        }
    }, [queryContainer.current])

    const size = useSize(queryContainer.current?.ref?.current)
    const { run } = useThrottleFn(
        () => {
            onSizeChange(size)
        },
        { wait: 100 },
    )
    run()

    const [visibleStatus, { toggle: setVisibleStatus }] = useToggle(IVisibleStatus.visible, IVisibleStatus.hidden)
    const { firstShow, secondShow, spliterSatus } = useCompoentShowControl(
        willHiddenIndex,
        visibleStatus,
        first,
        second,
    )
    // there is only one element (first or second)
    // show one  can not slide not show spliter
    // there are two elements (first and second)
    // show tow show spliter
    // show one can not slide show fake spliter
    return (
        <ReflexContainer
            ref={queryContainer}
            orientation={orientation}
            className={styles.twoColumnFlex}
            style={{
                width: '100%',
                height: domHeight,
                minHeight: 230, // Avoid height will be 0 in small screen.
                background: 'white',
            }}
        >
            {firstShow && (
                <ControlledElement key={1} flex={firstPaneFlexValue} {...flexConfig.first}>
                    {first.children}
                </ControlledElement>
            )}
            {spliterSatus === ISplitStatus.showReal && (
                <ReflexSplitter className={styles.reflexSpliter}>
                    <LeftOutlined
                        className={styles.spliterOperateBtn}
                        onClick={() => {
                            // will close
                            if (visibleStatus === IVisibleStatus.visible) {
                                if (willHiddenIndex === IIndex.first) {
                                    setFirstPaneFlexValue(0)
                                } else {
                                    setFirstPaneFlexValue(1)
                                }
                            }
                            setVisibleStatus()
                        }}
                    />
                </ReflexSplitter>
            )}
            {spliterSatus === ISplitStatus.showFake && (
                <FakeSpliter
                    type={IOrientation.vertical}
                    operateBtn={
                        <LeftOutlined
                            className={styles.fakeSpliterOperateBtn}
                            onClick={() => {
                                setFirstPaneFlexValue(flexInfo[IIndex.first])
                                setVisibleStatus()
                            }}
                        />
                    }
                />
            )}
            {secondShow && (
                <ControlledElement key={3} flex={1 - firstPaneFlexValue} {...flexConfig.second}>
                    {second.children}
                </ControlledElement>
            )}
        </ReflexContainer>
    )
}

export default TwoColumnFlex
