// @ts-nocheck
import moment from 'moment'
import { Tag, Typography, Popover } from 'antd'
import dayjs from 'dayjs'
const { Text } = Typography
// import { dpStatusMap } from '@constant/enums'

// material module
export const viewTypeMap = {
    CUSTOM: '人工物化',
    IMPORT: '物化补数',
    AUTO: '智能物化',
}

interface Props {
    type?: string
}

const relationWithSourceMvMap = {
    DOWNSTREAM: '下游',
    UPSTREAM: '上游',
    SOURCE: '本身',
}
// 物化任务状态信息
const mvJobStatusColumnTagMap = {
    SUBMITTED_SUCCESS: {
        text: '提交成功',
        color: 'success',
    },
    RUNNING_EXECUTION: {
        text: '正在运行',
        color: 'processing',
    },
    PAUSE: {
        text: '暂停',
        color: 'default',
    },
    FAILURE: {
        text: '失败',
        color: 'error',
    },
    SUCCESS: {
        text: '成功',
        color: 'success',
    },
    NEED_FAULT_TOLERANCE: {
        text: '需要容错',
        color: 'magenta',
    },
    KILL: {
        text: 'KILL',
        color: 'volcano',
    },
    DELAY_EXECUTION: {
        text: '延时执行',
        color: 'orange',
    },
    FORCED_SUCCESS: {
        text: '强制成功',
        color: 'success',
    },
    DISPATCH: {
        text: '派发',
        color: 'geekblue',
    },
}
const Columns = (props: Props) => {
    const { type } = props
    const translateMetricsType = (text: string) => {
        return text === 'atomic' ? '原子指标' : text === 'derived' ? '派生指标' : '复合指标'
    }

    switch (type.toLowerCase()) {
        case 'id':
        case 'businesstype':
            return (text: any) => {
                return <Tag>{text}</Tag>
            }
        case 'metricstype':
            return (text: any) => {
                return (
                    <Tag color={text === 'atomic' ? '#87d068' : text === 'derived' ? '#2db7f5' : '#f50'}>
                        {translateMetricsType(text)}
                    </Tag>
                )
            }
        case 'hot':
            return (text: any) => {
                const hot = parseInt(text / 20)
                const HOT_COLOR = ['#999', '#090', '#09F', '#00F', '#F75', '#F00']
                return <Tag color={HOT_COLOR[hot]}>{text}</Tag>
            }
        case 'dimensions':
        case 'textcode':
            return (text: any[]) => {
                const tagArr: JSX.Element[] = []
                text.map((v) => {
                    tagArr.push(<Tag>{v}</Tag>)
                })
                return <Popover content={tagArr}>{text.join('、')}</Popover>
            }
        case 'time':
            return (time: string | number) => {
                return <Text>{time ? moment(new Date(time)).format('YYYY-MM-DD HH:mm:ss') : '-'}</Text>
            }
        case 'timeformat':
            return (time: string | number) => {
                return <Text>{time ? moment(time).format('YYYYMMDD') : '-'}</Text>
            }
        case 'viewtype':
            return (viewType: keyof typeof viewTypeMap) => {
                const colors = ['green', 'purple', 'blue']
                const colorIdx = Object.keys(viewTypeMap).findIndex((key) => {
                    return viewType === key
                })
                return (
                    <>
                        {colorIdx === -1 ? (
                            viewType
                        ) : (
                            <Tag color={colorIdx === -1 ? 'blue' : colors[colorIdx]}>
                                {viewTypeMap[viewType] || viewType}
                            </Tag>
                        )}
                    </>
                )
            }
        case 'computetype':
            return (text: 'STREAM' | 'BATCH' | 'STREAM_AND_BATCH') => {
                const conf = {
                    STREAM: <Tag color='cyan'>流计算</Tag>,
                    BATCH: <Tag color='green'>批计算</Tag>,
                    STREAM_AND_BATCH: <Tag color='gold'>流批一体</Tag>,
                }
                return <>{conf[text] || '-'} </>
            }
        case 'virtualtabletype':
            return (text: 'LIKE' | 'AS') => {
                const conf = {
                    LIKE: <Tag color='blue'>贴源虚拟表</Tag>,
                    AS: <Tag color='purple'>业务虚拟表</Tag>,
                }
                return <>{conf[text] || '-'}</>
            }
        case 'gmtcreated':
            return (text: number, record) => {
                return <>{dayjs(record.modification.gmtCreated).format('YYYY/MM/DD HH:mm:ss')}</>
            }
        case 'relationwithsourcemv':
            return (text: 'SOURCE' | 'DOWNSTREAM' | 'UPSTREAM') => {
                return <>{relationWithSourceMvMap[text]}</>
            }
        case 'usability':
            return (usability: boolean) => {
                return <>{usability ? <Tag color='green'>可用</Tag> : <Tag color='red'>不可用</Tag>}</>
            }
        case 'partitionstatus':
            return (record: { partitionStatus: string; partitionStatusZn: string }) => {
                const { partitionStatus, partitionStatusZn } = record || {}
                return <Tag color={partitionStatus === 'UNAVAILABLE' ? 'red' : 'green'}>{partitionStatusZn}</Tag>
            }
        case 'mvjobstatus': {
            return (status: string) => {
                const { text = status, color = 'default' } = mvJobStatusColumnTagMap[status] || {}
                return <Tag color={color}>{text}</Tag>
            }
        }
        default:
            break
    }
}

export default Columns
