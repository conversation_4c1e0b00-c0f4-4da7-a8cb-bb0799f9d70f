// Dp的状态
//
export const dpStatusMap = {
  SUBMITTED_SUCCESS: '提交成功',
  RUNNING_EXECUTION: '正在运行',
  // PAUSE: '暂停',
  FAILURE: '失败',
  SUCCESS: '成功',
  // NEED_FAULT_TOLERANCE: '需要容错',
  KILL: 'KILL',
  // DELAY_EXECUTION: '延时执行',
  // FORCED_SUCCESS: '强制成功',
  // DISPATCH: '派发',
}

// 模型 - 流式时间策略
export const StreamScanMap = {
  EARLIEST: '最早',
  LATEST: '最新',
  SPECIFIC: '固定时间',
}

// 模型 - 流批一体时间策略
export const StreamBatchScanMap = {
  BATCH_FIRST: '批优先',
  // STREAM_FIRST: '流优先',
  // SPECIFIC: '固定时间点'
}

// 模型 - 输出配置
export const TimeUnitMap = {
  SECONDS: '秒',
  MINUTES: '分',
  HOURS: '小时',
  DAYS: '天',
}
