// @ts-nocheck
// TODO：dbxiao Will Delete at March
import Code from './code'
import Strings from './strings'

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
}

const partitionFormatOptions = [
  'yyyy-MM-dd HH:mm:ss',
  'yyyy-MM-dd HH:mm:ss.SSS',
  'yyyy-MM-dd',
  'yyyy-MM-dd HH',
  'yyyy-MM-dd HH:mm',
  'yyyy-MM',
  'yyyy',
  'yyyyMM',
  'yyyyMMdd',
  'yyyyMMddHHmmss',
]

const metricsPartitionOptions = ['yyyy-MM-dd', 'yyyyMMdd']

const PREFIX = ''
const Basename = ``

export { Code, Strings, formItemLayout, partitionFormatOptions, metricsPartitionOptions, Basename, PREFIX }

export const TABLE_COMMON_PROPS = {
  rowClassName: (record: unknown, index: number) => {
    return index % 2 ? 'tableRowOdd' : 'tableRowEven'
  },
}

export const SQL_QUERY_ROW_KEY = '0sgus7rvrlo'
