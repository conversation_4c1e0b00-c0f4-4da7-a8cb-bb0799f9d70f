// @ts-nocheck
/**
 * @module routerMap
 * @description Define the dependencies and configuration information for routes
 */
import React from 'react'
import { cachedDynamicImportComponent } from 'src/shared/common-utils'
import { formatPathWithBaseUrl } from 'src/client/utils'

// SQL Editor
const QueryPage = cachedDynamicImportComponent(() => import('@pages/data-manager/SQL-query'))
const QueryDetail = cachedDynamicImportComponent(() => import('@pages/data-manager/SQL-query-detail'))

// Data Model
const VirtualTable = cachedDynamicImportComponent(() => import('@pages/data-model/virtual-table'))
const BusinessVirtualTable = cachedDynamicImportComponent(() => import('@pages/data-model/business-virtual-table'))
const CreateVirtualTable = cachedDynamicImportComponent(() => import('@pages/data-model/create-virtual-table'))
const VirtualTableDetail = cachedDynamicImportComponent(() => import('@pages/data-model/virtual-table-detail'))
const CreateBusinessVirtualTable = cachedDynamicImportComponent(() => import('@pages/data-model/create-business-model'))
const CreateStreamVirtualTable = cachedDynamicImportComponent(
  () => import('@pages/data-model/create-stream-virtual-table'),
)
const CatalogManager = cachedDynamicImportComponent(() => import('@pages/data-model/catalog-manager'))

// Material View
const CreateMaterialView = cachedDynamicImportComponent(() => import('@pages/material-view/create-material-view'))
const MaterialViewList = cachedDynamicImportComponent(() => import('@pages/material-view/material-list'))
const MaterialViewDetail = cachedDynamicImportComponent(() => import('@pages/material-view/material-detail'))
const MVJobList = cachedDynamicImportComponent(() => import('@pages/material-view/mv-jobs'))
const QueryRelatedMV = cachedDynamicImportComponent(() => import('@pages/material-view/query-relatedMV'))

// Smart
const SmartQueryList = cachedDynamicImportComponent(() => import('@pages/smartx/smart-query-list'))
const SmartQueryDetail = cachedDynamicImportComponent(() => import('@pages/smartx/smart-query-detail'))
const SmartQueryHiveDetail = cachedDynamicImportComponent(() => import('@pages/smartx/smart-query-hive-detail'))
const SmartQueryTableDetail = cachedDynamicImportComponent(() => import('@pages/smartx/smart-query-table-detail'))
const SmartMVScan = cachedDynamicImportComponent(() => import('@pages/smartx/smart-mv-scan'))

// advance
const LogFileList = cachedDynamicImportComponent(() => import('@pages/advance/log-file-list'))
const ToolsBox = cachedDynamicImportComponent(() => import('@pages/advance/tools-box'))
const OpSqlHistory = cachedDynamicImportComponent(() => import('@pages/advance/sql-history'))
const OpSqlQuery = cachedDynamicImportComponent(() => import('@pages/advance/sql-query'))

// auth
const RoleManage = cachedDynamicImportComponent(() => import('@pages/auth-manager/role-manage'))
const RoleDetail = cachedDynamicImportComponent(() => import('@pages/auth-manager/role-detail'))
const AuthManage = cachedDynamicImportComponent(() => import('@pages/auth-manager/auth-manage'))
const UserManage = cachedDynamicImportComponent(() => import('@pages/auth-manager/user-manage'))
const UserDetail = cachedDynamicImportComponent(() => import('@pages/auth-manager/user-detail'))
// batch
const BatchErrorInfo = cachedDynamicImportComponent(() => import('@pages/batch-info/batch-error-info'))
// permission
const PermissionAudit = cachedDynamicImportComponent(() => import('@pages/auth-manager/audit'))
// Demo
// const DemoRelative =cachedDynamicImportComponent(() => import('@pages/demo/relative'))
// const DemoSearch =cachedDynamicImportComponent(() => import('@pages/demo/search'))
const HttpErrorShow = cachedDynamicImportComponent(() => import('@pages/error/http-error-show'))
// const EditorWithTreeAndResultDemoPage =cachedDynamicImportComponent(() => import('@pages/demo/editorWithTreeAndResult'))
// const DemoUpload =cachedDynamicImportComponent(() => import('@pages/demo/upload'))
// const DemoERDnd =cachedDynamicImportComponent(() => import('@pages/demo/er'))

// Route Map
export const routerMap = {
  // SQL Query
  SQLQuery: {
    key: 'SQL-query',
    name: 'SQL查询',
    path: formatPathWithBaseUrl('/manage/SQL-query'),
    element: <QueryPage />,
  },
  SQLQueryDetail: {
    key: 'SQL-query-detail',
    name: 'SQL查询',
    path: formatPathWithBaseUrl('/manage/SQL-query-detail'),
    element: <QueryDetail queryInfo={undefined} />,
  },

  // Data Model
  dataModel: {
    key: 'data-model',
    path: formatPathWithBaseUrl('/manage/data-model'),
    name: '虚拟表',
    virtualTable: {
      key: 'virtual-table',
      name: '贴源虚拟表',
      path: formatPathWithBaseUrl('/manage/data-model/virtual-table'),
      element: <VirtualTable />,
    },
    businessVirtualTable: {
      key: 'business-data-model',
      name: '业务虚拟表',
      path: formatPathWithBaseUrl('/manage/data-model/business-data-model'),
      element: <BusinessVirtualTable />,
    },
    createBusinessVirtualTable: {
      key: 'create-business-data-model',
      name: '创建业务虚拟表',
      path: formatPathWithBaseUrl('/manage/data-model/business-data-model/create-business-data-model'),
      element: <CreateBusinessVirtualTable />,
    },
    createVirtualTable: {
      key: 'create-virtual-table',
      name: '创建虚拟表',
      path: formatPathWithBaseUrl('/manage/data-model/virtual-table/create-virtual-table'),
      element: <CreateVirtualTable />,
    },
    createStreamVirtualTable: {
      key: 'create-stream-virtual-table',
      name: '创建流式表',
      path: formatPathWithBaseUrl('/manage/data-model/virtual-table/create-stream-virtual-table'),
      element: <CreateStreamVirtualTable />,
    },
    virtualTableDetail: {
      key: 'virtual-table-detail',
      name: '虚拟表详情',
      path: formatPathWithBaseUrl('/manage/data-model/virtual-table/virtual-table-detail'),
      element: <VirtualTableDetail />,
    },
    businessVirtualTableDetail: {
      key: 'business-virtual-table-detail',
      name: '虚拟表详情',
      path: formatPathWithBaseUrl('/manage/data-model/business-data-model/business-virtual-table-detail'),
      element: <VirtualTableDetail />,
    },
    catalogManager: {
      key: 'catalog-manager',
      name: '虚拟表目录管理',
      path: formatPathWithBaseUrl('/manage/data-model/catalog-manager'),
      element: <CatalogManager />,
    },
  },

  // SMART X
  smartx: {
    key: 'smartx',
    path: formatPathWithBaseUrl('/manage/smartx'),
    name: 'Smart X',
    createMaterialView: {
      key: 'create-material-view',
      name: '创建物化视图',
      path: formatPathWithBaseUrl('/manage/materialization/create-material-view'),
      element: <CreateMaterialView />,
    },
    materialViewList: {
      key: 'material-view-list',
      name: '智能物化',
      path: formatPathWithBaseUrl('/manage/materialization/material-view-list'),
      element: <MaterialViewList />,
    },
    materialViewDetail: {
      key: 'material-view-detail',
      name: '物化详情',
      path: formatPathWithBaseUrl('/manage/materialization/material-view-list/material-view-detail'),
      element: <MaterialViewDetail />,
    },
    smartQueryList: {
      key: 'smart-query-list',
      name: '智能数据收集',
      path: formatPathWithBaseUrl('/manage/materialization/smart-query-list'),
      element: <SmartQueryList />,
    },
    smartQueryDetail: {
      key: 'smart-query-detail',
      name: '智能加速',
      path: formatPathWithBaseUrl('/manage/materialization/smart-query-detail'),
      element: <SmartQueryDetail />,
    },
    smartQueryHiveDetail: {
      key: 'smart-query-hive-detail',
      name: '智能加速',
      path: formatPathWithBaseUrl('/manage/materialization/smart-query-hive-detail'),
      element: <SmartQueryHiveDetail />,
    },
    smartQueryTableDetail: {
      key: 'smart-query-table-detail',
      name: '智能加速',
      path: formatPathWithBaseUrl('/manage/materialization/smart-query-table-detail'),
      element: <SmartQueryTableDetail />,
    },
    smartMVScan: {
      key: 'material-view-scan',
      name: '智能物化发现',
      path: formatPathWithBaseUrl('/manage/materialization/material-view-scan'),
      element: <SmartMVScan />,
    },
    mvJobList: {
      key: 'job-list',
      name: '任务列表',
      path: formatPathWithBaseUrl('/manage/materialization/job-list'),
      element: <MVJobList />,
    },
    queryRelatedMV: {
      key: 'query-related-mv',
      name: '关联物化查询',
      path: formatPathWithBaseUrl('/manage/materialization/query-related-mv'),
      element: <QueryRelatedMV />,
    },
  },
  advance: {
    key: 'advance',
    path: formatPathWithBaseUrl('/manage/advance'),
    name: '运维管理',
    logFileList: {
      key: 'log-file-list',
      name: '日志',
      path: formatPathWithBaseUrl('/manage/operation/advance/log-file-list'),
      element: <LogFileList />,
    },
    toolsBox: {
      key: 'tools-box',
      name: '工具箱',
      path: formatPathWithBaseUrl('/manage/operation/advance/tools-box'),
      element: <ToolsBox />,
    },
    sqlHistory: {
      key: 'sql-history',
      name: 'sql查询',
      path: formatPathWithBaseUrl('/manage/operation/advance/sql-history'),
      element: <OpSqlHistory />,
    },
    sqlQuery: {
      key: 'sql-query-manage',
      name: '大查询管理',
      path: formatPathWithBaseUrl('/manage/operation/advance/sql-query-manage'),
      element: <OpSqlQuery />,
    },
  },
  error: {
    key: 'error',
    name: 'error',
    httpErrorShow: {
      key: 'http-error-show',
      name: '错误展示',
      path: formatPathWithBaseUrl('/manage/error/http-error-show'),
      element: <HttpErrorShow />,
    },
  },

  auth: {
    key: 'auth-manager',
    name: '资源管理',
    UserManage: {
      key: 'user-manage',
      name: '用户管理',
      path: formatPathWithBaseUrl('/manage/auth-manager/user-manage'),
      element: <UserManage />,
    },
    UserDetail: {
      key: 'user-detail',
      name: '用户详情',
      path: formatPathWithBaseUrl('/manage/auth-manager/user-detail/:userName'),
      element: <UserDetail />,
    },
    roleManage: {
      key: 'role-manage',
      name: '角色管理',
      path: formatPathWithBaseUrl('/manage/auth-manager/role-manage'),
      element: <RoleManage />,
    },
    roleDetail: {
      key: 'role-detail',
      name: '角色详情',
      path: formatPathWithBaseUrl('/manage/auth-manager/role-detail/:roleName'),
      element: <RoleDetail />,
    },
    authManage: {
      key: 'auth-manage',
      name: '角色授权管理',
      path: formatPathWithBaseUrl('/manage/auth-manager/auth-manage'),
      element: <AuthManage />,
    },
  },

  // permission
  permission: {
    key: 'permission',
    path: formatPathWithBaseUrl('/manage/permission'),
    name: '权限管理',
    access: 'access_xengine_ranger',
    admin: {
      // path:formatPathWithBaseUrl( `${window.location.origin}/xengine/ranger`),
      name: '管理后台',
    },
    audit: {
      path: formatPathWithBaseUrl('/manage/permission/audit'),
      name: '用户审批',
      element: <PermissionAudit />,
    },
  },

  // 批量上传结果查询
  batchInfo: {
    key: 'batch-info',
    name: '批量上传',
    path: formatPathWithBaseUrl('/manage/data-model/virtual-table/batch-info'),
    element: <BatchErrorInfo />,
  },

  // Demo . all component demos put here
  // demo: {
  //   key: 'demo',
  //   path:formatPathWithBaseUrl( '/demo'),
  //   name: 'demo演示',
  //   demoRelative: {
  //     key: 'demo-relative',
  //     name: '血缘组件',
  //     path:formatPathWithBaseUrl( '/demo/demo-relative'),
  //     element: <DemoRelative />,
  //   },
  //   demoEditorWithTree: {
  //     key: 'demo-editor-with-tree',
  //     name: '编辑器',
  //     path:formatPathWithBaseUrl( '/demo/demo-editor-with-tree'),
  //     element: <EditorWithTreeAndResultDemoPage />,
  //   },
  //   demoSearch: {
  //     key: 'demo-search',
  //     name: '搜索组件',
  //     path:formatPathWithBaseUrl( '/demo/demo-search'),
  //     element: <DemoSearch />,
  //   },
  //   demoUpload: {
  //     key: 'demo-upload',
  //     name: '上传组件',
  //     path:formatPathWithBaseUrl( '/demo/demo-upload'),
  //     element: <DemoUpload />,
  //   },
  //   demoERDnd: {
  //     name: 'ER 拖拽建模',
  //     path:formatPathWithBaseUrl( '/demo/demo-er'),
  //     element: <DemoERDnd />,
  //   },
  // },
}
