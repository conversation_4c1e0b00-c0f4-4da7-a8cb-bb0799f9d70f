import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, Typo<PERSON>, message } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { instanceItems } from './conf/itemConf'
import { Api } from '@api'
import { Froms, setSelectOpts } from '@ui/form'
import { useRequest } from 'ahooks'
import { clone } from 'lodash-es'
import { CreateMetriceInstance } from './interface'
const { Title } = Typography
import Enum from '@model/enum/Enum'

const CreateMetrics: React.FC = (props: CreateMetriceInstance) => {
  const [open, setOpen] = useState(false)
  const [formItem, setFormItem] = useState<any[]>([])
  const { onSuccess } = props
  const [metricsList, setMetricsList] = useState([])
  const [metricsType, setMetricsType] = useState('')
  const { getEnums } = Enum()

  const { runAsync: getMetricsList } = useRequest(Api.apiEngineV1MetricsListPost, {
    manual: true,
  })
  const { runAsync: getVTalbeList } = useRequest(Api.apiEngineV1VtableListGet, {
    manual: true,
  })
  const { runAsync: getMetricsDetail } = useRequest(Api.apiEngineV1MetricsGet, {
    manual: true,
  })
  const { runAsync: postMetricsInstance } = useRequest(Api.apiEngineV1MetricsInstancePost, {
    manual: true,
  })

  const onClose = () => {
    setOpen(false)
  }

  /**
   * @param vtable
   * @param name
   */
  const renderSelectOptions = (dataList: any[], name: string, opts?: { label?: string; value?: any }) => {
    setSelectOpts(
      {
        dataList,
        dealName: name,
        formItems: formItem,
        options: opts,
      },
      (newformItems) => {
        setFormItem(newformItems)
      },
    )
  }

  /**
   * @description Create metrics instance
   */
  const onMetricsInstanceClick = () => {
    setFormItem(instanceItems)
    setOpen(true)
  }

  const onValuesChange = (data: object, form: any) => {
    const key: string = Object.keys(data).join('')
    const params = form?.getFieldValue()
    const vTParams = clone(params)
    let metricsType: string | undefined = ''

    metricsList.map((v) => {
      if (v.id === vTParams.metricsId) {
        metricsType = v.metricsType
      }
    })

    switch (key) {
      case 'database':
        vTParams['metricsVtable'] = true
        getVTalbeList(vTParams).then((data: any) => {
          renderSelectOptions(data?.list, 'virtualTableName', { value: 'name', label: 'name' })
        })
        break
      case 'virtualTableName':
        getMetricsList({
          vtableMeta: {
            catalogName: vTParams.catalog,
            databaseName: vTParams.database,
            virtualTableName: vTParams.virtualTableName,
          },
        }).then((data: any) => {
          setMetricsList(data?.list)
          renderSelectOptions(data?.list, 'metricsId', { value: 'id', label: 'name' })
        })
        break
      case 'metricsId':
        setMetricsType(metricsType)
        vTParams['metricsType'] = metricsType
        delete vTParams.dimensions

        getMetricsDetail(vTParams).then((data: any) => {
          // const dataArr: any[] = new Array(data)
          renderSelectOptions(data.dimensions, 'dimensions')
        })
        break
      default:
        break
    }
  }

  /**
   * Submit
   * @param form Object params
   */
  const fromSubmit = (form?: any) => {
    const dataParams = formatParams(form)
    message.loading({
      content: '正在创建指标实例，请稍后',
      key: 'postMetricsInstanceLoading',
    })
    postMetricsInstance(dataParams).then(() => {
      message.success({
        content: '指标实例创建成功',
        key: 'postMetricsInstanceLoading',
      })
      setOpen(false)
      onSuccess()
    })
  }

  /**
   * Format params
   * @returns Object
   */
  const formatParams = (form?: any) => {
    const params = form?.getFieldsValue()
    const data = {
      vtableMeta: {
        catalogName: params?.catalog,
        databaseName: params?.database,
        virtualTableName: params?.virtualTableName,
      },
      instanceWindowVO: {
        windowType: params?.windowType,
        windowGranularity: params?.windowGranularity,
        windowSize: params?.windowSize,
        updateGranularity: params?.updateGranularity,
        updateGranularityValue: params?.updateGranularityValue,
      },
      metricsType,
      ...params,
    }
    return data
  }

  /**
   * useEffect
   */
  useEffect(() => {
    getEnums((data: any) => {
      renderSelectOptions(data?.businessType, 'businessType', { value: 'value', label: 'value' })
      renderSelectOptions(data?.metricsFunction, 'function', { value: 'value', label: 'desc' })
      renderSelectOptions(data?.conditionType, 'condition', { value: 'value', label: 'desc' })
      renderSelectOptions(data?.metricsWindow, 'windowType', { value: 'value', label: 'desc' })
      renderSelectOptions(data?.timeUnit, 'windowGranularity', { value: 'value', label: 'desc' })
      renderSelectOptions(data?.timeUnit, 'updateGranularity', { value: 'value', label: 'desc' })
    })
  }, [open])

  return (
    <>
      <Button type="primary" icon={<PlusOutlined />} onClick={onMetricsInstanceClick}>
        创建指标实例
      </Button>
      <Drawer title={'创建指标实例'} placement="right" onClose={onClose} open={open} width="1040">
        <Title level={5}>选择指标</Title>
        <Froms
          key={'metrics-create'}
          items={formItem}
          DBSelect={true}
          colSpan={8}
          onFinish={fromSubmit}
          onValuesChange={onValuesChange}
        />
      </Drawer>
    </>
  )
}

export default CreateMetrics
