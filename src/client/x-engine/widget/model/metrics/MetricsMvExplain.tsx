import React, { useState } from 'react'
import LayoutCard, { GhostCard } from '@ui/layoutCard/LayoutCard'
import { Descriptions, Table } from 'antd'
import { metricsDetailQueryWithMVColumns } from './conf/itemConf'
import GLineage from '@model/GLineage'
import { formatERDataToGLineage } from '@libs'
import { useRequest } from 'ahooks'
import { Api } from '@api'
import { formatVTLineageData } from '@libs/lineageFormatFns'
import { useSearchParams } from 'react-router-dom'
import { CheckOutlined } from '@ant-design/icons'

interface PropsType {
  data?: any
  autoLoad?: boolean
}

const MetricsMvExplain: React.FC<PropsType> = (props) => {
  return (
    <>
      <Table
        tableLayout="fixed"
        rowKey={'mvName'}
        columns={metricsDetailQueryWithMVColumns}
        dataSource={props?.data?.mvNames.map((item: string) => ({ mvName: item }))}
      />
    </>
  )
}

function MetricsDetailBloodRelation() {
  const [searchParams] = useSearchParams()
  const tableName = searchParams.get('name') || ''
  const catalogName = searchParams.get('catalog') || ''
  const databaseName = searchParams.get('database') || ''
  const { data: lineageData, loading } = useRequest(async () => {
    if (catalogName && databaseName && tableName) {
      return Api.apiEngineV1VtableGetTableLineageGet({
        catalogName,
        databaseName,
        tableName,
      })
    }
    return { nodes: [], edges: [] }
  })
  return (
    <GhostCard loading={loading}>
      <GLineage typeKey="type" data={formatVTLineageData(lineageData)} />
    </GhostCard>
  )
}

function ERModel(props: { data: any }) {
  return (
    <>
      {props?.data?.dataModelDesc?.joinDag && (
        <GLineage typeKey="type" data={formatERDataToGLineage(props.data.dataModelDesc.joinDag, true)} />
      )}
    </>
  )
}

function MeasuresTable(props: { data: any }) {
  const measures = (props?.data?.dataModelDesc?.measures || []).map((item: Record<string, any> = {}) => {
    return {
      ...item,
      ...(item.function || {}),
    }
  })
  return (
    <>
      <Table
        tableLayout="fixed"
        rowKey={'name'}
        columns={[
          {
            title: '度量名',
            dataIndex: 'name',
            width: 200,
          },
          {
            title: '度量表达式',
            dataIndex: 'expressionDesc',
          },
          {
            title: '度量说明',
            dataIndex: 'comment',
          },
        ]}
        dataSource={measures}
      />
    </>
  )
}
function getColumnName(name: string) {
  return name.split('.').pop()
}
function ModelConfigTable(props: { data: any }) {
  const configList = (props?.data?.dataModelDesc?.dataProcessDesc?.strategyList || []).map(
    (strategyItem: Record<string, any> = {}) => {
      return props?.data?.columns.map(({ name, columnType }: { name: string; columnType: string }) => {
        return {
          name,
          columnType,
          pk: strategyItem.primaryKeys.some(({ name: primaryName }: { name: string }) => {
            return getColumnName(primaryName) === name
          }) ? (
            <CheckOutlined />
          ) : (
            ''
          ),
          version: getColumnName(strategyItem.version.name) === name ? <CheckOutlined /> : '',
        }
      })
    },
  )
  return (
    <>
      {configList.map((configItem: Record<string, string>[]) => {
        return (
          <div>
            <Descriptions>
              <Descriptions.Item label={'数据处理策略'}>数据更新</Descriptions.Item>
            </Descriptions>
            <Table
              tableLayout="fixed"
              rowKey={'name'}
              columns={[
                {
                  title: '字段名称',
                  dataIndex: 'name',
                  width: 200,
                },
                {
                  title: '字段类型',
                  dataIndex: 'columnType',
                },
                {
                  title: '主键',
                  dataIndex: 'pk',
                },
                {
                  title: '版本号',
                  dataIndex: 'version',
                },
              ]}
              dataSource={configItem}
            />
          </div>
        )
      })}
    </>
  )
}

function DimensionsTable(props: { data: any }) {
  const vertices = props?.data?.dataModelDesc?.joinDag?.vertices || []
  const dimensions = vertices
    .filter((v: { dimensionsColumns: string[] }) => Array.isArray(v?.dimensionsColumns))
    .map((v: { dimensionsColumns: string[] }) => v.dimensionsColumns)
  return (
    <>
      <Table
        tableLayout="fixed"
        rowKey={'name'}
        columns={[
          {
            title: '维度列',
            dataIndex: 'name',
          },
        ]}
        dataSource={dimensions.flat(1)}
      />
    </>
  )
}
function TimeDimensionTable(props: { data: any }) {
  return (
    <Table
      tableLayout="fixed"
      rowKey={'timeColumn'}
      columns={[
        {
          title: '时间维度名',
          dataIndex: 'timeColumn',
        },
      ]}
      dataSource={[{ timeColumn: props?.data?.dataModelDesc?.windowDesc?.windowTimeColumn?.name || '-' }]}
    />
  )
}
const METRICS_DETAIL_PANELS_MAP = {
  QUERYMV: 0, // 关联物化分析
  BLOOD_RELATION: 1, // 血缘关系
  ER_MODEL: 2, // ER模型
  METRICS: 3, // 度量
  DIMENSIONS: 4, // 维度
  TIME_DIMENSION: 5, // 时间维度
  MODEL_CONFIG: 6, // 模型配置
}

function MetricsDetailPanel(props: { data: any }) {
  const data = props.data
  console.log(data, 'data')
  let restPanels = [
    {
      label: 'ER模型',
      key: String(METRICS_DETAIL_PANELS_MAP.ER_MODEL),
      children: <ERModel data={data} />,
    },
    {
      label: '度量',
      key: String(METRICS_DETAIL_PANELS_MAP.METRICS),
      children: <MeasuresTable data={data} />,
    },
    {
      label: '维度',
      key: String(METRICS_DETAIL_PANELS_MAP.DIMENSIONS),
      children: <DimensionsTable data={data} />,
    },
    {
      label: '时间维度',
      key: String(METRICS_DETAIL_PANELS_MAP.TIME_DIMENSION),
      children: <TimeDimensionTable data={data} />,
    },
  ]
  if (data?.dataModelDesc?.modelType === 'DETAIL_MODEL') {
    restPanels = [
      {
        label: '模型配置',
        key: String(METRICS_DETAIL_PANELS_MAP.MODEL_CONFIG),
        children: <ModelConfigTable data={data} />,
      },
    ]
  }
  const MetricsDetailPanelItems = [
    {
      label: '关联物化分析',
      key: String(METRICS_DETAIL_PANELS_MAP.QUERYMV),
      children: <MetricsMvExplain data={data} />,
    },
    {
      label: '血缘关系',
      key: String(METRICS_DETAIL_PANELS_MAP.BLOOD_RELATION),
      children: <MetricsDetailBloodRelation />,
    },
    ...restPanels,
  ]
  const [activeTabKey, setActiveTabKey] = useState(String(METRICS_DETAIL_PANELS_MAP.QUERYMV))

  return (
    <LayoutCard
      key={Math.random() * 100}
      tabList={MetricsDetailPanelItems}
      activeTabKey={activeTabKey}
      onTabChange={(i) => {
        setActiveTabKey(i)
      }}
    />
  )
}

export default MetricsDetailPanel
