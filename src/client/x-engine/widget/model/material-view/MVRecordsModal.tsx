// @ts-nocheck
import React, { useEffect, useState } from 'react'
import { Button, Modal, List, message } from 'antd'
import { Broadcast, copyToClipboard } from '@libs'
type BroadcastIdManageType = {
    openMVRecordsModalBId: string
    setMVRecordsBId: string
    reloadMVListBId: string
}

export default function MVRecordsModal({ broadcastIdManage }: { broadcastIdManage: BroadcastIdManageType }) {
    const [dataList, setDataList] = useState([])
    const [isOpen, setIsOpen] = useState(false)
    useEffect(() => {
        Broadcast.listen(broadcastIdManage.openMVRecordsModalBId, () => {
            setIsOpen(true)
        })
        Broadcast.listen(broadcastIdManage.setMVRecordsBId, ({ mvRecordList }: { mvRecordList: string[] }) => {
            setDataList(mvRecordList)
        })
    }, [])
    return (
        <>
            <Modal
                width={900}
                open={isOpen}
                destroyOnClose
                footer={null}
                onCancel={() => {
                    setIsOpen(false)
                }}
            >
                <List
                    header={<div className='font-medium text-base'>去重后的SQL列表：</div>}
                    dataSource={dataList}
                    pagination={{}}
                    renderItem={(mvSql) => (
                        <List.Item>
                            <div className='w-[100%] flex justify-between items-center'>
                                <span className='break-all'>{mvSql}</span>
                                <Button
                                    type='link'
                                    onClick={() => {
                                        copyToClipboard(mvSql)
                                        message.success('复制成功')
                                    }}
                                >
                                    复制
                                </Button>
                            </div>
                        </List.Item>
                    )}
                ></List>
            </Modal>
        </>
    )
}
