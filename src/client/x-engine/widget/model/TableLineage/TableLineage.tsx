import React, { useEffect, useRef } from 'react'
import G6, { Graph } from '@antv/g6'
import './shape-conf'
import { askBIPageUrls } from 'src/shared/url-map'
import { routerMap as XEngineRouterMap } from '@XEngineRouter/routerMap'

type PropsType = {
  data: TableLineageOriginDataType
}

export type TableLineageOriginDataType = {
  nodes: {
    id: string
    title: string
    nodeType: string
    extra?: string
    tableType?: string
    type: string
    relationType?: string
    size?: [number, number]
  }[]
  edges: {
    source: string
    target: string
    style?: { endArrow?: boolean }
  }[]
}
const tableTypeNavPathMap = {
  likeVirtualTable: XEngineRouterMap.dataModel.virtualTableDetail.path,
  asVirtualTable: XEngineRouterMap.dataModel.businessVirtualTableDetail.path,
  externalTable: askBIPageUrls.manage.externalDatasource.tableDetail,
  dataModel: askBIPageUrls.manage.xengine.dataSceneList,
  physicalTable: '',
}

export default function TableLineage({ data }: PropsType) {
  const containerRef = useRef<HTMLDivElement>(null)
  const graphRef = useRef<Graph>()
  useEffect(() => {
    const container = containerRef.current
    if (container) {
      // tooltip plugin
      const tooltip = new G6.Tooltip({
        offsetX: 10,
        offsetY: 20,
        shouldBegin(e) {
          const shape = e?.shape
          if (shape) {
            const type = shape.get('type')
            const name = shape.get('name')
            const isElliptical = shape.attr('isElliptical')
            if (name === 'relation-filter') {
              return true
            }
            if (type === 'text' && isElliptical) {
              return true
            }
            if (name === 'table-type-icon') {
              return true
            }
          }
          return false
        },
        getContent(e) {
          const shape = e?.shape
          if (!shape) {
            return ''
          }
          let text = ''
          const name = shape.get('name')
          switch (name) {
            case 'title-text':
            case 'extra-text': {
              text = shape.get('meta')?.text ?? ''
              break
            }
            case 'table-type-icon': {
              text = shape.get('meta')?.tableTypeLabel
              break
            }
            case 'relation-filter': {
              text = shape.get('meta')?.title
            }
          }
          const outDiv = document.createElement('div')
          outDiv.innerHTML = text
          return outDiv
        },
        itemTypes: ['node'],
      })
      const plugins = [tooltip]
      const width = container.scrollWidth
      const height = container.scrollHeight || 500
      const graph = new G6.Graph({
        container,
        width,
        height,
        // translate the graph to align the canvas's center, support by v3.5.1
        fitCenter: true,
        defaultNode: {
          // size: ,
          type: 'table-rect',
          anchorPoints: [
            [0, 0.5],
            [1, 0.5],
          ],
        },
        defaultEdge: {
          type: 'cubic-horizontal',
        },
        modes: {
          default: ['drag-node', 'drag-canvas'],
        },
        plugins,
        layout: {
          type: 'self-layout',
          // rankdir: 'LR',
          // align: 'DR',
          // controlPoints: true,
          // begin: [1, 1],
          // nodesepFunc: () => 20,
          // ranksepFunc: () => 20,
        },
        // fitView: true,
      })

      graph.data(data)
      graph.render()
      graphRef.current = graph
      graph.on('node:click', ({ shape }) => {
        const meta = shape.get('meta') || {}
        const { tableType, title: table, extra } = meta
        const navPath = tableTypeNavPathMap[tableType as keyof typeof tableTypeNavPathMap]
        if (navPath) {
          const [catalog, database] = extra?.split('.') || []
          if (table && catalog && database) {
            window.open(`${navPath}?catalog=${catalog}&database=${database}&name=${table}`)
          }
        }
      })
      graph.on('canvas:dragstart', () => {
        const canvasElement = graph.get('canvas').get('el')
        canvasElement.style.cursor = 'grabbing'
      })

      // canvas:dragend
      graph.on('canvas:dragend', () => {
        const canvasElement = graph.get('canvas').get('el')
        canvasElement.style.cursor = 'grab'
      })

      // todo: window onresize
      if (typeof window !== 'undefined')
        window.onresize = () => {
          if (!graph || graph.get('destroyed')) return
          if (!container || !container.scrollWidth || !container.scrollHeight) return
          graph.changeSize(container.scrollWidth, container.scrollHeight)
        }
    }
    return () => {
      if (graphRef.current) {
        graphRef.current.destroy()
      }
    }
  }, [])
  return (
    <div className="h-full w-full">
      <div ref={containerRef} className="h-full w-full bg-[#F8F8F8]" />
    </div>
  )
}
