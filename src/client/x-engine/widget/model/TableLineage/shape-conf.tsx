import G6 from '@antv/g6'
import tableLineageIcon from './tableLineageIcon.png'
import relationFilterIcon from './filterIcon.png'

const fittingString = (str: string, maxWidth: number, fontSize: number) => {
  const ellipsis = '...'
  const ellipsisLength = G6.Util.getTextSize(ellipsis, fontSize)[0]
  let currentWidth = 0
  let res = str
  const pattern = new RegExp('[\u4E00-\u9FA5]+') // distinguish the Chinese characters and letters
  str?.split('')?.forEach((letter, i) => {
    if (currentWidth > maxWidth - ellipsisLength) return
    if (pattern.test(letter)) {
      // Chinese characters
      currentWidth += fontSize
    } else {
      // get the width of single letter according to the fontSize
      currentWidth += G6.Util.getLetterWidth(letter, fontSize)
    }
    if (currentWidth > maxWidth - ellipsisLength) {
      res = `${str.substring(0, i + 1)}${ellipsis}`
    }
  })
  return res
}

const tableTypeMap = {
  likeVirtualTable: '贴源虚拟表',
  asVirtualTable: '业务虚拟表',
  externalTable: '外部数据表',
  dataModel: '数据模型',
  physicalTable: '物理表',
}

G6.registerNode('table-rect', {
  draw(cfg, group) {
    const { title, tableType, extra } = cfg
    const fittingTitle = fittingString(title as string, 194, 14)
    const fittingExtra = fittingString(extra as string, 172, 12)
    const isTitleElliptical = fittingTitle !== title
    const isExtraElliptical = fittingExtra !== extra

    const meta = {
      title,
      extra,
      tableType,
    }

    const keyShape = group.addShape('rect', {
      attrs: {
        x: 0,
        y: 0,
        width: 226,
        height: 69,
        stroke: '#eee',
        fill: '#fff',
        radius: [6, 6, 6, 6],
        cursor: 'move',
      },
      draggable: true,
      name: 'table-lineage-wrap',
      meta,
    })
    group.addShape('text', {
      attrs: {
        fontSize: 14,
        x: 16,
        y: 32,
        text: fittingTitle,
        fill: '#171717',
        isElliptical: isTitleElliptical,
        cursor: isTitleElliptical ? 'pointer' : '',
      },
      name: 'title-text',
      meta: {
        ...meta,
        text: title,
      },
    })
    group.addShape('text', {
      attrs: {
        fontSize: 12,
        x: 38,
        y: 52,
        text: fittingExtra,
        fill: '#575757',
        isElliptical: isExtraElliptical,
        cursor: isExtraElliptical ? 'pointer' : '',
      },
      meta: {
        ...meta,
        text: extra,
      },
      name: 'extra-text',
    })
    group.addShape('image', {
      attrs: {
        x: 16,
        y: 38,
        img: tableLineageIcon,
        cursor: 'pointer',
        width: 16,
        height: 16,
      },
      meta: {
        ...meta,
        tableTypeLabel: tableTypeMap[tableType as keyof typeof tableTypeMap] ?? tableType,
      },
      name: 'table-type-icon',
    })
    return keyShape
  },
})

G6.registerNode('relation', {
  options: {
    size: [36, 36],
  },
  draw(cfg, group) {
    const { title } = cfg
    const keyShape = group.addShape('image', {
      attrs: {
        img: relationFilterIcon,
        cursor: 'pointer',
        width: 36,
        height: 36,
      },
      meta: {
        title,
      },
      name: 'relation-filter',
    })
    return keyShape
  },
})

G6.registerLayout('self-layout', {
  // 默认参数
  getDefaultCfg() {
    return {
      rankdir: 'LR',
      align: 'DR',
      controlPoints: true,
      begin: [1, 1],
      nodesepFunc: () => 20,
      ranksepFunc: () => 20,
      // center: [0, 0], // 布局的中心
      // biSep: 100, // 两部分的间距
      // nodeSep: 20, // 同一部分的节点间距
      // direction: 'horizontal', // 两部分的分布方向
      // nodeSize: 20, // 节点大小
    }
  },
  // 执行布局
  execute() {
    const { nodes, edges } = this
    const data = { nodes, edges }
    const dagre = new G6.Layout['dagre']()
    dagre.updateCfg({
      rankdir: 'LR',
      align: 'DR',
      controlPoints: true,
      begin: [1, 1],
      nodesepFunc: () => 20,
      ranksepFunc: () => 20,
    })
    dagre.layout(data)
  },
})
