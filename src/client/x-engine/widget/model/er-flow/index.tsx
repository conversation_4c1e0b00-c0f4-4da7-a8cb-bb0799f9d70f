// @ts-ignore
import {
  <PERSON><PERSON><PERSON>,
  XFlowGraph,
  Grid,
  Background,
  Snapline,
  // Minimap,
  Control,
} from '@antv/xflow'
import { PlusOutlined } from '@ant-design/icons'
import React, { useEffect, useMemo, useState } from 'react'
import { useBoolean } from 'ahooks'
import { InitNode } from './InitNode'
import './index.scss'
import TableDnd from './TableDnd'
import { TimeLimitModal } from './TimeLimitModal'
import { Button, Form } from 'antd'
import TableFilterDrawer, {
  formatTableFilterDrawerColumnOptionsData,
} from 'src/client/components/TableFilter/TableFilterDrawer'
import { useSetAtom } from 'jotai'
import { erFlowDataAtom } from 'src/client/x-engine/atoms/er'

interface Props {
  onChange?: (d: any) => void
}

/**
 * 桩点 id 规则 catalogName~db_name~tableId~isFact(boolean)~columnsName~parent~joinType~position
 * eg: dipeak~public~333~true~wind_fundcode~222~LEFT~left
 * @constructor
 */
export default function ErFlow(props: Props) {
  const [filterDrawerForm] = Form.useForm()
  const [timeLimitModalForm] = Form.useForm()
  const setErFlowData = useSetAtom(erFlowDataAtom)
  useEffect(() => {
    setErFlowData({
      timeLimitModalForm,
      filterData: null,
    })
  }, [])
  const [filterDrawerVisible, filterDrawerVisibleOps] = useBoolean(false)
  const [currentFilterDrawerFormValues, setCurrentFilterDrawerFormValues] = useState({})

  const [timeLimitModalVisible, timeLimitModalVisibleOps] = useBoolean(false)
  const [originData, setOriginData] = useState<any>()

  const [mask, setMask] = useState<boolean>(true)

  const { onChange } = props

  useEffect(() => {
    if (originData) {
      onChange && onChange(originData)
      const { nodes = [] } = originData
      if (nodes.length > 0) {
        setMask(false)
      } else {
        setMask(true)
      }
    }
    // 当节点数据发生改变的时候，进行清空filter数据
    filterDrawerForm.resetFields()
    setErFlowData({
      timeLimitModalForm,
      filterData: null,
    })
    setCurrentFilterDrawerFormValues({})
  }, [originData])

  const validateEdge = (graph: any) => {
    const { source, target } = graph.edge.store.data
    // 目标点没有桩点
    if (!target || !target.port) {
      return false
    }
    const targetPortInfo = target.port.split('~')
    const sourcePortInfo = source.port.split('~')
    // 同侧的桩点不能相连
    if (targetPortInfo[targetPortInfo.length - 1] === sourcePortInfo[sourcePortInfo.length - 1]) {
      return false
    }
    // 纬度表不能链接到主表上
    if (JSON.parse(targetPortInfo[3])) {
      return false
    }
    // 副表 只能被定义的主表连接
    const targetParent = targetPortInfo[5]
    const sourceId = sourcePortInfo[2]
    if (targetParent !== sourceId) {
      return false
    }
    return true
  }

  return (
    <div className="xflow-container">
      <XFlow>
        <XFlowGraph
          pannable
          zoomable
          minScale={0.5}
          connectionEdgeOptions={{
            attrs: {
              line: {
                stroke: '#C2C8D5',
                strokeWidth: 1,
                targetMarker: {
                  name: 'classic',
                },
              },
            },
            selected: false,
            animated: true,
            zIndex: -1,
          }}
          connectionOptions={{
            snap: true,
            connector: {
              name: 'rounded',
              args: {
                radius: 50,
              },
            },
            router: {
              name: 'er',
            },
            allowBlank: false,
            allowMulti: 'withPort',
            allowEdge: false,
            allowLoop: false,
            validateEdge,
          }}
        />
        <Grid type="dot" options={{ color: '#ccc', thickness: 1 }} />
        <Background color="#F8F8F8" />
        <Snapline sharp />
        <div className="xflow-absolute xflow-control">
          <Control
            direction="vertical"
            placement="right"
            items={['zoomOut', 'zoomTo', 'zoomIn', 'zoomToFit', 'zoomToOrigin']}
          />
        </div>
        {/*<div className='xflow-absolute xflow-map'>*/}
        {/*    <Minimap width={200} height={200} />*/}
        {/*</div>*/}
        {mask && <div className="xflow-absolute xflow-mask">请从左侧栏中拖入一张表作为模型的主表</div>}
        <div className="xflow-absolute xflow-dnd">
          <TableDnd />
        </div>
        {/* 工具栏 */}
        <div className="xflow-absolute xflow-operation flex items-center gap-[4px]">
          <Button type="link" icon={<PlusOutlined />} onClick={timeLimitModalVisibleOps.setTrue}>
            时间约束条件
          </Button>
          <Button
            type="link"
            icon={<PlusOutlined />}
            onClick={() => {
              filterDrawerForm.setFieldsValue(currentFilterDrawerFormValues)
              filterDrawerVisibleOps.setTrue()
            }}
          >
            filter条件
          </Button>
        </div>
        <InitNode onChange={setOriginData} />
        <TimeLimitModal
          form={timeLimitModalForm}
          originData={originData}
          visible={timeLimitModalVisible}
          setVisible={timeLimitModalVisibleOps.set}
        />
        <TableFilterDrawer
          form={filterDrawerForm}
          open={filterDrawerVisible}
          onClose={() => {
            filterDrawerVisibleOps.setFalse()
          }}
          onConfirm={async (data) => {
            setErFlowData({
              timeLimitModalForm,
              filterData: data,
            })
            filterDrawerVisibleOps.setFalse()
            setCurrentFilterDrawerFormValues(filterDrawerForm.getFieldsValue)
          }}
          columnOptionsData={formatTableFilterDrawerColumnOptionsData(originData?.nodes.map((v: any) => v.data) ?? [])}
        />
      </XFlow>
    </div>
  )
}
