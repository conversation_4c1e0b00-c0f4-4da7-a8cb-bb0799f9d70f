import { useGraphEvent, useGraphInstance, useGraphStore } from '@antv/xflow'
import React, { useEffect, useCallback, useState } from 'react'
import { register } from '@antv/x6-react-shape'
import ReactERNode from './ReactERNode'
import { getPortsData, isSchemaEqual } from '@model/er-flow/conf/utils'
import { JOIN_ENUM } from '@model/er-flow/conf/constant'
import * as _ from 'lodash-es'
import { useAtom } from 'jotai'
import { hasFactAtom, dataModelDescTypeAtom } from '@atoms/er'
import TableConfigModal from '@model/er-flow/TableConfigModel'
import { Button, message, Modal, Space } from 'antd'
import { customAlphabet } from 'nanoid'
const numericalID = customAlphabet('1234567890', 10)

interface PropsType {
  onChange?: (d: any) => void
}

register({
  shape: 'er-node',
  component: ReactERNode,
  effect: ['data'],
})

export const InitNode = (props: PropsType): any => {
  const [hasFact, setHasFact] = useAtom(hasFactAtom)
  const [dataModelDescType, setDataModelDescType] = useAtom(dataModelDescTypeAtom)
  const [tableCfgModelOpen, setTableCfgModelOpen] = useState<boolean>(false)
  const [tableInfo, setTableInfo] = useState<any>()
  const { onChange } = props

  const initData = useGraphStore((state) => state.initData)
  const removeNodes = useGraphStore((state) => state.removeNodes)
  const addNodes = useGraphStore((state) => state.addNodes)
  const addEdges = useGraphStore((state) => state.addEdges)
  const removeEdges = useGraphStore((state) => state.removeEdges)
  const nodes = useGraphStore((state) => state.nodes)
  const edges = useGraphStore((state) => state.edges)
  const graph = useGraphInstance()

  // todo 自定义react label（目前是使用原生的label，自定义节点暂未解决位置的问题）
  // useEffect(() => {
  //     if (graph) {
  //         graph.options.onEdgeLabelRendered = (args) => {
  //             const { selectors } = args
  //             const content = selectors.foContent as HTMLDivElement
  //             if (content) {
  //                 ReactDOM.createRoot(content).render(<p style={{width: '60px', height: '30px'}}>left join</p>)
  //             }
  //         }
  //     }
  // }, [graph]);

  // 监听节点添加
  useGraphEvent('node:added', async ({ node }) => {
    const data = node.getData()
    const position = node.getPosition()
    if (node.id === 'placeholder') {
      removeNodes([node.id])
      // 为什么是数组 存 edge id。解决添加/删除边的时候 判断这个columns 是否还是其他 columns 的pk/fk
      data.columns.forEach((item: any) => {
        item.pk = []
        item.fk = []
      })
      const info = {
        data,
        x: position.x,
        y: position.y,
      }
      // 验证这张表是否可以被添加到画布中
      const res = await validateNodes(info)
      if (!res) return
      // 如果添加的是副表，则需要指定时间列,相对父表,join方式
      info.data.id = numericalID()
      if (hasFact) {
        setTableCfgModelOpen(true)
        setTableInfo(info)
      } else {
        createNewReactErNode(info, dataModelDescType)
      }
    }
  })

  // 监听连线
  useGraphEvent('edge:connected', ({ edge }) => {
    const sourceData = _.cloneDeep(edge?.getSourceNode()?.data)
    const targetData = _.cloneDeep(edge?.getTargetNode()?.data)
    const targetPortInfo = edge?.getTargetPortId()?.split('~')
    const info = {
      source: {
        cell: edge?.getSourceCellId(),
        port: edge?.getSourcePortId(),
      },
      sourceData,
      target: {
        cell: edge?.getTargetCellId(),
        port: edge?.getTargetPortId(),
      },
      targetData,
      joinType: targetPortInfo?.length && targetPortInfo[targetPortInfo.length - 2],
    }
    /** 拖拽过程中会生成一条无实际业务含义的线, 需要手动删除 */
    if (!edge.getData()) {
      removeEdges([edge.id])
      createRelationHandle(info)
    }
  })

  // 监听边的增加
  useGraphEvent('edge:added', ({ edge }) => {
    // 排除那条无意义的虚线
    if (edge.getData()) {
      const sourceData = _.cloneDeep(edge?.getSourceNode()?.data)
      const targetData = _.cloneDeep(edge?.getTargetNode()?.data)
      const info = {
        source: {
          cell: edge?.getSourceCellId(),
          port: edge?.getSourcePortId(),
        },
        sourceData,
        target: {
          cell: edge?.getTargetCellId(),
          port: edge?.getTargetPortId(),
        },
        targetData,
      }
      // 更新两个节点的状态(增加pk/fk 字段)
      const sourceColumnName = info.source.port?.split('~')[4]
      const targetColumnName = info.target.port?.split('~')[4]
      const index1 = info.sourceData.columns.findIndex((item: any) => item.name === sourceColumnName)
      const sourceColumns = _.cloneDeep([...info.sourceData.columns])
      if (!sourceColumns[index1].fk.includes(edge.id)) {
        sourceColumns[index1].fk.push(edge.id)
      }
      const index2 = info.targetData.columns.findIndex((item: any) => item.name === targetColumnName)
      const targetColumns = _.cloneDeep([...info.targetData.columns])
      if (!targetColumns[index2].pk.includes(edge.id)) {
        targetColumns[index2].pk.push(edge.id)
      }
      edge?.getSourceNode()?.updateData({ ...info.sourceData, columns: sourceColumns })
      edge?.getTargetNode()?.updateData({ ...info.targetData, columns: targetColumns })
    }
  })

  // 监听边被删除
  useGraphEvent('edge:removed', ({ edge }) => {
    // 排除那条无意义的虚线
    if (edge?.getData()) {
      const sourceNode = graph?.getCellById(edge?.getData().sourceData.id)
      const targetNode = graph?.getCellById(edge?.getData().targetData.id)
      // 更新两个节点的状态(删除pk/fk 字段)
      if (sourceNode) {
        const sourceData = _.cloneDeep(sourceNode?.getData())
        const sourceColumnName = edge?.getSourcePortId()?.split('~')[4]
        const index1 = sourceData.columns.findIndex((item: any) => item.name === sourceColumnName)
        const sourceColumns = _.cloneDeep([...sourceData.columns])
        const edgeIndex1 = sourceColumns[index1].fk.indexOf(edge.id)
        edgeIndex1 > -1 && sourceColumns[index1].fk.splice(edgeIndex1, 1)
        sourceNode.updateData({ ...sourceData, columns: sourceColumns })
      }
      if (targetNode) {
        const targetData = _.cloneDeep(targetNode?.getData())
        const targetColumnName = edge?.getTargetPortId()?.split('~')[4]
        const index2 = targetData.columns.findIndex((item: any) => item.name === targetColumnName)
        const targetColumns = _.cloneDeep([...targetData.columns])
        const edgeIndex2 = targetColumns[index2].pk.indexOf(edge.id)
        edgeIndex2 > -1 && targetColumns[index2].pk.splice(edgeIndex2, 1)
        targetNode.updateData({ ...targetData, columns: targetColumns })
      }
    }
  })

  // 动态添加删除按钮
  useGraphEvent('edge:mouseenter', ({ edge }) => {
    edge.addTools({
      name: 'button-remove',
      args: { distance: 0.5 },
    })
  })
  useGraphEvent('edge:mouseleave', ({ edge }) => {
    if (edge.hasTool('button-remove')) {
      edge.removeTool('button-remove')
    }
  })

  // 监听节点鼠标移入移出
  useGraphEvent('node:mouseenter', () => {
    changePortsVisible(true)
  })
  useGraphEvent('node:mouseleave', () => {
    changePortsVisible(false)
  })

  const changePortsVisible = (visible: boolean) => {
    const ports = document.body.querySelectorAll('.x6-port-body') as NodeListOf<SVGElement>
    for (let i = 0, len = ports.length; i < len; i = i + 1) {
      ports[i].style.visibility = visible ? 'visible' : 'hidden'
    }
  }

  // 创建节点与节点的关系
  const createRelationHandle = (info: any) => {
    addEdges([
      {
        target: info?.target,
        source: info?.source,
        selected: false,
        animated: false,
        zIndex: 1,
        data: {
          sourceData: info?.sourceData,
          targetData: info?.targetData,
          joinType: info.joinType,
        },
        attrs: {
          line: {
            stroke: '#C2C8D5',
            strokeWidth: 1,
            targetMarker: {
              name: 'classic',
            },
          },
        },
        label: JOIN_ENUM[info.joinType as keyof typeof JOIN_ENUM],
        // label: { // todo 自定义react label
        //     draggable: false,
        //     movable: false,
        //     position: {
        //         distance: 0.5,
        //         args: {
        //             keepGradient: true,
        //             ensureLegibility: true,
        //         },
        //     },
        //     markup: Markup.getForeignObjectMarkup(),
        //     attrs: {
        //         fo: {
        //             width: 60,
        //             height: 30,
        //             x: 30,
        //             y: -15,
        //         },
        //     },
        // }
      },
    ])
  }

  // 创建er节点
  const createNewReactErNode = (data: any, type?: string) => {
    const _dataModelDescType = type || dataModelDescType
    const info = data.data
    info.isFact = !hasFact
    // 流批一体不给设置链接桩
    const portsArray = _dataModelDescType === 'STREAM_BATCH' ? [] : getPortsData(info)
    addNodes([
      {
        id: info.id,
        shape: 'er-node',
        data: info,
        x: data.x,
        y: data.y,
        ports: {
          groups: {
            group1: {
              attrs: {
                circle: {
                  magnet: true,
                  stroke: '#31d0c6',
                  fill: '#fff',
                },
              },
              // 文档：https://x6.antv.vision/zh/docs/api/registry/port-layout#absolute
              position: {
                name: 'absolute',
              },
            },
          },
          items: _.flatten(portsArray),
        },
      },
    ])

    if (!hasFact) {
      setHasFact(true)
    }
  }

  // 校验表
  const validateNodes = (info: any) => {
    return new Promise((resolve) => {
      //@todo 这一块在backend 无法解决 流表 computeType 的时候，先简单处理保证测试
      const computeType = info.data.tableEngine === 'Kafka' ? 'STREAM' : info?.data?.computeType || 'BATCH'
      // nodes 为0 代表 画布上面目前一张表都没有
      if (nodes.length === 0) {
        // 主表是批表，则默认是构建基于批表的er
        if (computeType === 'BATCH') {
          setDataModelDescType(computeType)
          resolve(computeType)
        } else {
          Modal.confirm({
            title: '检测到您使用流表作为主表，请选择您期望构建的模型',
            okButtonProps: {
              hidden: true,
            },
            cancelText: '关闭',
            onCancel: () => resolve(false),
            content: (
              <Space direction="vertical" size="large" style={{ width: '100%', margin: '20px 0' }}>
                <Button
                  type="primary"
                  block
                  onClick={() => {
                    setDataModelDescType('STREAM')
                    resolve('STREAM')
                    Modal.destroyAll()
                  }}
                >
                  ER模型
                </Button>
                <Button
                  type="primary"
                  block
                  onClick={() => {
                    setDataModelDescType('STREAM_BATCH')
                    resolve('STREAM_BATCH')
                    Modal.destroyAll()
                  }}
                >
                  流批一体表
                </Button>
              </Space>
            ),
          })
        }
      } else {
        // 主表为批表时，副表不能是流表
        if (dataModelDescType === 'BATCH' && computeType === 'STREAM') {
          Modal.warning({
            title: '提示',
            content: '当前不支持批式表作为主表，流式表作为副表的场景，请重新选择',
          })
          resolve(false)
        }
        // 如果构建 流批一体表，则只能有且只有一个副表，副表只能是批表
        if (dataModelDescType === 'STREAM_BATCH') {
          if (nodes.length === 1 && computeType === 'STREAM') {
            Modal.warning({
              title: '提示',
              content: '当前为流批一体表，在主表为流表的情况下，副表不能为流表，请重新选择',
            })
            resolve(false)
          }
          if (nodes.length > 1) {
            Modal.warning({
              title: '提示',
              content: '当前为流批一体表，副表有且只有一张，不可多选副表',
            })
            resolve(false)
          }
          // 添加批表后开始检查数据一致性
          if (nodes.length === 1 && computeType === 'BATCH') {
            void message.loading({
              type: 'loading',
              content: '正在检验两张表schema是否一致',
              duration: 0,
            })

            const isPassed = isSchemaEqual(info?.data?.columns, nodes[0].data.columns)
            message.destroy()
            if (!isPassed) {
              Modal.warning({
                title: '提示',
                content: '当前为流批一体表，两张表schema不一致，请重新选择',
              })
              resolve(false)
            }
            resolve(dataModelDescType)
          }
        }
        resolve(dataModelDescType)
      }
    })
  }

  // 初始化画布内容，可用于回显er模型关系。创建则默认为空数组
  const setInitData = useCallback(() => {
    initData({
      nodes: [],
      edges: [],
    })
  }, [initData])

  useEffect(() => {
    setInitData()
  }, [setInitData])

  // 当nodes 和edges 发生变化的时候，及时callback
  useEffect(() => {
    onChange &&
      onChange({
        nodes,
        edges,
        dataModelDescType,
      })
  }, [nodes, edges, dataModelDescType])

  // 程序退出时，重置
  useEffect(() => {
    return () => {
      setHasFact(false)
      setDataModelDescType('')
    }
  }, [])

  return (
    <TableConfigModal
      tableType={dataModelDescType}
      open={tableCfgModelOpen}
      onClose={() => setTableCfgModelOpen(false)}
      data={tableInfo}
      onConfirm={createNewReactErNode}
    />
  )
}
