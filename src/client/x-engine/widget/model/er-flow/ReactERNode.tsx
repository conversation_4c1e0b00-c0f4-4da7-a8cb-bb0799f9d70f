// @ts-nocheck
import React, { useState } from 'react'
import { DeleteOutlined, MinusOutlined, PlusOutlined, UnorderedListOutlined } from '@ant-design/icons'
import { Button, Popconfirm, Space, Tag, Tooltip, Typography } from 'antd'
import { LINE_HEIGHT, NODE_HEADER_HEIGHT, NODE_WIDTH, COLUMN_LIMIT } from './conf/constant'
import { useAtom } from 'jotai'
import { hasFactAtom, dataModelDescType<PERSON>tom } from '@atoms/er'
import clsx from 'clsx'

function Node({
  data,
  onDelete,
  onPortChange,
}: {
  data: any
  onDelete?: (data: any) => void
  onPortChange?: (limit: number) => void
}) {
  const max = data?.columns.length

  const isToggle = max - COLUMN_LIMIT > 0

  const [limit, setLimit] = useState<number>(COLUMN_LIMIT)

  const toggleHandle = () => {
    const _limit = max > limit ? max : COLUMN_LIMIT
    setLimit(_limit)
    onPortChange && onPortChange(_limit)
  }

  return (
    <div className={`react-er-node rounded-[4px] border-[1px] border-[#e8e8e8]`} style={{ width: NODE_WIDTH }}>
      <div className="node-header border-b-[1px] border-[#e8e8e8]" style={{ height: NODE_HEADER_HEIGHT }}>
        <div className="flex items-center gap-[8px]">
          <UnorderedListOutlined />
          <Typography.Text className="node-table w-full" ellipsis={{ tooltip: `${data.catalogName}.${data.name}` }}>
            {data.catalogName}.{data.name}
          </Typography.Text>
          <Tooltip title="删除">
            <Popconfirm
              title={'确认删除吗'}
              description={data.isFact ? '删除主表后，画布中其他的副表也会被一并清除' : ''}
              okText="确定"
              cancelText="取消"
              onConfirm={() => onDelete(data)}
            >
              <DeleteOutlined />
            </Popconfirm>
          </Tooltip>
        </div>
        <div
          className={clsx(
            'mt-[6px] w-fit rounded-[2px] px-[4px] py-[2px] text-[12px]',
            data.isFact ? 'bg-[#F3E6F6]' : 'bg-[#EBF9F6]',
          )}
        >
          {data.isFact ? '主表' : '副表'}
        </div>
      </div>
      <div className="columns-list">
        {data.columns.slice(0, limit).map((item: any, index: number) => (
          <div
            className={`columns-item align-center flex border-b-[1px] border-[#e8e8e8] p-[6px] pl-[8px] bg-dimension-${item.isDimension} bg-metrics-${item.isMetrics}`}
            key={index}
            style={{ height: LINE_HEIGHT }}
          >
            <div className={`columns-item-title`}>
              <Typography.Text style={{ fontSize: 16 }} color="#333" ellipsis>
                {item.name}
              </Typography.Text>
            </div>
            <div style={{ fontSize: 12, marginLeft: 5 }}>
              <Typography.Text type="secondary" italic>
                {item.columnType}
              </Typography.Text>
              {(item.pk.length > 0 || item.fk.length > 0) && (
                <Tag bordered={false} color="purple" style={{ margin: '0 0 0 5px' }}>
                  {keyName(item.pk, item.fk)}
                </Tag>
                // <Typography.Text type='secondary' italic style={{ marginLeft: 5 }}>
                //     {keyName(item.pk, item.fk)}
                // </Typography.Text>
              )}
            </div>
          </div>
        ))}
      </div>
      {isToggle && (
        <Button
          className="rounded-[0px]"
          type="text"
          block
          onClick={toggleHandle}
          icon={max > limit ? <PlusOutlined /> : <MinusOutlined />}
        >
          {max > limit ? '展开' : '收起'}
        </Button>
      )}
    </div>
  )
}

const keyName = (pk: string[], fk: string[]) => {
  const word1 = pk.length > 0 ? 'pk' : ''
  const word2 = fk.length > 0 ? 'fk' : ''
  return [word1, word2].filter((item) => item).join('/')
}

const ReactERNode = ({ node, graph }: { node?: any; graph?: any }) => {
  const data = node.prop('data')

  const [, setHasFact] = useAtom(hasFactAtom)
  const [dataModelDescType, setDataModelDescType] = useAtom(dataModelDescTypeAtom)

  const onPortChange = (limit: number) => {
    const ports = node.getPorts()
    if (ports.length > 0 && dataModelDescType !== 'STREAM_BATCH') {
      const columnsLength = ports.length / 2
      ports.forEach((port: any, index: number) => {
        const anchor = Math.floor(index / 2)
        if (anchor >= COLUMN_LIMIT) {
          const y =
            LINE_HEIGHT * (limit === columnsLength ? anchor : COLUMN_LIMIT) + NODE_HEADER_HEIGHT + LINE_HEIGHT / 2
          node.portProp(port.id!, 'args/y', y)
          node.portProp(port.id!, 'attrs/circle/magnet', limit === columnsLength)
        }
      })
    }
  }

  const deleteHandle = (info?: any) => {
    if (info) {
      if (info.isFact) {
        graph.clearCells()
        setDataModelDescType('')
        setHasFact(false)
      } else {
        graph.removeNode(info.id)
      }
    }
  }
  if (!data) return null
  return <Node data={data} onDelete={deleteHandle} onPortChange={onPortChange} />
}

export default ReactERNode
