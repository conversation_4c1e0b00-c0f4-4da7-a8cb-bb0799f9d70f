import React, { useContext, useEffect, useState } from 'react'
import { Button, Form, FormInstance, InputNumber, Modal, Select } from 'antd'
import type { SelectProps } from 'antd/lib'
import './TimeLimitModal.scss'
import clsx from 'clsx'
import { PlusOutlined, MinusOutlined } from '@ant-design/icons'

const TimeLimitTimeOffsetOptions: { value: TimeLimitTimeOffsetProps['status']; label: string }[] = [
  { value: 'NONE', label: '-' },
  { value: 'PLUS', label: '加' },
  { value: 'MINUS', label: '减' },
]

const TimeLimitTimeOffsetTimeOptions = [
  { value: 'HOURS', label: '小时' },
  { value: 'MINUTES', label: '分钟' },
  { value: 'SECONDS', label: '秒' },
]

const LogicCompareOptions = [
  { value: 'GT', label: '大于' },
  { value: 'LT', label: '小于' },
  { value: 'GE', label: '大于等于' },
  { value: 'LE', label: '小于等于' },
  { value: 'EQ', label: '等于' },
]

const TimeLimitContext = React.createContext<TimeLimitContextValue | null>(null)

const initialValues = {
  [`time-limit1-input1-offset-select`]: TimeLimitTimeOffsetOptions[0].value,
  [`time-limit1-input2-offset-select`]: TimeLimitTimeOffsetOptions[0].value,
  [`time-limit2-input1-offset-select`]: TimeLimitTimeOffsetOptions[0].value,
  [`time-limit2-input2-offset-select`]: TimeLimitTimeOffsetOptions[0].value,

  [`time-limit1-input1-time-num`]: 0,
  [`time-limit1-input2-time-num`]: 0,
  [`time-limit2-input1-time-num`]: 0,
  [`time-limit2-input2-time-num`]: 0,

  [`time-limit1-input1-time-select`]: TimeLimitTimeOffsetTimeOptions[2].value,
  [`time-limit1-input2-time-select`]: TimeLimitTimeOffsetTimeOptions[2].value,
  [`time-limit2-input1-time-select`]: TimeLimitTimeOffsetTimeOptions[2].value,
  [`time-limit2-input2-time-select`]: TimeLimitTimeOffsetTimeOptions[2].value,

  [`time-limit1-logic-compare`]: LogicCompareOptions[0].value,
  [`time-limit2-logic-compare`]: LogicCompareOptions[0].value,

  'need-time-limit': false,
  'need-add': false,
}

interface TimeLimitTimeOffsetProps {
  status?: 'PLUS' | 'MINUS' | 'NONE'
  unit?: 'h' | 'm' | 's'
  value?: number
  onChange?: (val: number) => void
  prefix?: string
}

function TimeLimitTimeOffset(props: TimeLimitTimeOffsetProps) {
  const { prefix = '', status: _status } = props
  const { namePrefix } = useContext(TimeLimitContext)!
  let status = Form.useWatch(`${namePrefix}-${prefix}-offset-select`, Form.useFormInstance())
  if (_status) status = _status
  return (
    <>
      <Form.Item className="mb-0 ml-[-1px] w-[60px] shrink-0" name={`${namePrefix}-${prefix}-offset-select`}>
        <Select className="time-limit-modal-offset-select" options={TimeLimitTimeOffsetOptions} />
      </Form.Item>
      {status !== TimeLimitTimeOffsetOptions[0].value && (
        <>
          <Form.Item className="mb-0 ml-[-1px] w-[80px] shrink-0" name={`${namePrefix}-${prefix}-time-num`}>
            <InputNumber precision={0} className={clsx('time-limit-modal-time-num w-full rounded-[0px]')} />
          </Form.Item>
          <Form.Item className="mb-0 ml-[-1px] w-[80px] shrink-0" name={`${namePrefix}-${prefix}-time-select`}>
            <Select className="time-limit-modal-time-select" options={TimeLimitTimeOffsetTimeOptions} />
          </Form.Item>
        </>
      )}
    </>
  )
}

interface TimeLimitInputProps {
  prefix?: string
  selectProps?: Partial<SelectProps>
  timeOffsetProps?: Partial<TimeLimitTimeOffsetProps>
}

function TimeLimitInput(props: TimeLimitInputProps) {
  const { prefix } = props
  const { namePrefix } = useContext(TimeLimitContext)!
  return (
    <div className="time-limit-modal-time-input flex w-full">
      <Form.Item className="mb-0 ml-[-1px] w-full" name={`${namePrefix}-${prefix}-time-limit-select`}>
        <Select
          disabled
          {...props.selectProps}
          className={clsx('time-limit-modal-time-limit-select', props.selectProps?.className)}
        />
      </Form.Item>
      <TimeLimitTimeOffset prefix={props.prefix} {...props.timeOffsetProps} />
    </div>
  )
}

function LogicCompare() {
  const { namePrefix } = useContext(TimeLimitContext)!
  return (
    <Form.Item className="mb-0 w-[112px]" name={`${namePrefix}-logic-compare`}>
      <Select options={LogicCompareOptions} />
    </Form.Item>
  )
}

interface TimeLimitContextValue {
  namePrefix: string
}

interface TimeLimitModalProps {
  form: FormInstance<any>
  originData: any
  visible: boolean
  setVisible: (v: boolean) => void
}

export function TimeLimitModal(props: TimeLimitModalProps) {
  const { visible, setVisible, originData, form } = props
  const nodes = originData?.nodes ?? []
  const selectOptions = [
    {
      label: `主表时间字段 ${nodes[0]?.data.timeColumn ?? 'Unknown'}`,
      value: `${nodes[0]?.data.catalogName}.${nodes[0]?.data.databaseName}.${nodes[0]?.data.name}.${nodes[0]?.data.timeColumn ?? 'Unknown1'}~${nodes[0]?.data.id}`,
    },
    {
      label: `副表时间字段 ${nodes[1]?.data.timeColumn ?? 'Unknown'}`,
      value: `${nodes[1]?.data.catalogName}.${nodes[1]?.data.databaseName}.${nodes[1]?.data.name}.${nodes[1]?.data.timeColumn ?? 'Unknown2'}~${nodes[1]?.data.id}`,
    },
  ]
  useEffect(() => {
    // console.log('OriginData', originData, selectOptions)
    form.setFieldsValue({
      [`time-limit1-input1-time-limit-select`]: selectOptions[0].value,
      [`time-limit1-input2-time-limit-select`]: selectOptions[1].value,
      [`time-limit2-input1-time-limit-select`]: selectOptions[0].value,
      [`time-limit2-input2-time-limit-select`]: selectOptions[1].value,
    })
  }, [originData])
  const timeLimit = (
    <>
      <TimeLimitInput
        prefix="input1"
        selectProps={{
          options: selectOptions,
        }}
      />
      <LogicCompare />
      <TimeLimitInput
        prefix="input2"
        selectProps={{
          options: selectOptions,
        }}
      />
    </>
  )
  const [currentFormValues, setCurrentFormValues] = useState({})
  useEffect(() => {
    if (visible) {
      setCurrentFormValues(form.getFieldsValue())
    }
  }, [visible])

  const needAdd = Form.useWatch('need-add', form)
  return (
    <Modal
      className="time-limit-modal"
      title="时间约束条件设置"
      open={visible}
      onOk={() => setVisible(false)}
      onCancel={() => {
        setVisible(false)
        form.setFieldsValue(currentFormValues)
      }}
      footer={(originNode) => {
        return (
          <div className="flex justify-between">
            <div>
              <Button
                onClick={() => {
                  form.setFieldsValue(initialValues)
                }}
              >
                清空
              </Button>
            </div>
            <div className="flex gap-[4px]">{originNode}</div>
          </div>
        )
      }}
    >
      <Form
        form={form}
        initialValues={initialValues}
        onValuesChange={(_) => {
          // console.log('value changed', o)
          form.setFieldValue('need-time-limit', true)
        }}
      >
        <div className="flex flex-col gap-4">
          <TimeLimitContext.Provider value={{ namePrefix: 'time-limit1' }}>{timeLimit}</TimeLimitContext.Provider>
          {needAdd && (
            <>
              <div>AND</div>
              <TimeLimitContext.Provider value={{ namePrefix: 'time-limit2' }}>{timeLimit}</TimeLimitContext.Provider>
            </>
          )}
          <a
            className="w-fit"
            type="link"
            onClick={() => {
              form.setFieldValue('need-add', !needAdd)
            }}
          >
            {needAdd ? (
              <>
                <MinusOutlined />
                删除AND条件
              </>
            ) : (
              <>
                <PlusOutlined />
                配置AND条件
              </>
            )}
          </a>
          <Form.Item name="need-add" className="hidden"></Form.Item>
          <Form.Item name="need-time-limit" className="hidden"></Form.Item>
        </div>
      </Form>
    </Modal>
  )
}
