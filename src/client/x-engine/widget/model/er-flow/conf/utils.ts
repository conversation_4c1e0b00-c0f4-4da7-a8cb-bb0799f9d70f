import { LINE_HEIGHT, NODE_HEADER_HEIGHT, NODE_WIDTH, COLUMN_LIMIT, RADIUS } from './constant'

export const getPortsData = (info: any) => {
    return info.columns.map((item: any, index: number) => {
        const id = [
            info.catalogName,
            info.databaseName,
            info.id,
            info.isFact,
            item.name,
            info.parent || '0',
            info.joinType || '0',
        ].join('~')
        return generatePorts(id, {
            y: LINE_HEIGHT * Math.min(index, COLUMN_LIMIT) + NODE_HEADER_HEIGHT + LINE_HEIGHT / 2,
            magnet: index < COLUMN_LIMIT,
        })
    })
}

export const generatePorts = (id: string, attrs: Record<string, any>) => {
    return [generateCirclePort(id, attrs, 0, 'left'), generateCirclePort(id, attrs, NODE_WIDTH, 'right')]
}

// function generateReactPort(id: string, attrs: Record<string, any>, x: number, position?: string, color?: string) {
//     return {
//         id: id + '~' + position,
//         markup: [
//             {
//                 tagName: 'rect',
//                 selector: 'portBody',
//             },
//         ],
//         args: {
//             position: position || 'top',
//         },
//         highlighting: false,
//         selected: false,
//         attrs: {
//             portBody: {
//                 width: PORT_WIDTH, //需要跟自定义节点每一行设置的一致
//                 height: LINE_HEIGHT, //需要跟自定义节点每一行设置的一致
//                 fill: color || 'rgba(255, 255, 255, 0)',
//                 magnet: true,
//                 transform: 'matrix(1,0,0,1,0,0)',
//                 x,
//                 ...attrs,
//             },
//         },
//         // zIndex: 0, //隐藏在节点层级之后
//     }
// }

function generateCirclePort(id: string, attrs: Record<string, any>, x: number, position?: string, color?: string) {
    return {
        id: id + '~' + position,
        group: 'group1',
        args: {
            x,
            y: attrs.y,
        },
        attrs: {
            circle: {
                ...attrs,
                r: RADIUS,
                strokeWidth: 2,
                stroke: color,
            },
        },
        // zIndex: 0
    }
}

// 判断两张表的schema是否一致
export const isSchemaEqual = (schema1: any, schema2: any) => {
    if (schema1.length !== schema2.length) {
        return false
    }

    for (let i = 0; i < schema1.length; i++) {
        const schema1ColumnType = getFormatColumnType(schema1[i].columnType)
        const schema2ColumnType = getFormatColumnType(schema2[i].columnType)
        if (schema1[i].name !== schema2[i].name || schema1ColumnType !== schema2ColumnType) {
            return false
        }
    }
    return true
}

// 遇到 TIMESTAMP 的类型 暂时 当成 VARCHAR 处理
const getFormatColumnType = (columnType: string) => {
    if (columnType === 'TIMESTAMP') return 'VARCHAR'
    return columnType
}
