import { Popover, theme, Tree, Typography, type TreeDataNode, Input, Tooltip } from 'antd'
import React, { useState, useMemo } from 'react'
import { Api } from '@api'
import { useRequest } from 'ahooks'
import { HolderOutlined, InfoCircleOutlined } from '@ant-design/icons'
import { useDnd } from '@antv/xflow'
import { getUnitId, streamTableCatalogType } from '@libs/util'
import TextHighlight from 'src/client/components/TextHighlight'
import { debounce, omit } from 'lodash-es'

type TreeNodeType = TreeDataNode & {
  name: string
  _type: string
  children: TreeNodeType[] | null
}

const TreeNode = () => {
  const { token } = theme.useToken()
  const { startDrag } = useDnd()
  const [treeData, setTreeData] = useState<any[]>([])
  const [searchValue, setSearchValue] = useState('')

  const handleMouseDown = (e: React.MouseEvent<Element, MouseEvent>, item: any) => {
    startDrag(
      {
        id: 'placeholder',
        shape: 'rect',
        width: 150,
        height: 32,
        attrs: {
          body: {
            stroke: '#D9DADD',
            strokeWidth: 1,
          },
        },
        data: item,
        label: item.name,
      },
      e,
    )
  }

  function WrapTreeTitle({
    type,
    item,
    searchHightLight,
  }: {
    type: string
    item: { name: string }
    searchHightLight?: string
  }) {
    switch (type) {
      case 'catalog':
      case 'database': {
        return <TextHighlight text={item.name} highlight={searchHightLight} />
      }
      case 'table': {
        return (
          <div
            style={{ width: '100%', display: 'flex', alignItems: 'center', overflow: 'hidden' }}
            onMouseDown={(e) => handleMouseDown(e, item)}
          >
            <HolderOutlined color={token.colorPrimary} />
            <div style={{ flex: 1, overflow: 'hidden', width: 0, marginLeft: 8 }}>
              <Popover content={item.name}>
                <Typography.Text ellipsis>
                  <TextHighlight text={item.name} highlight={searchHightLight} />
                </Typography.Text>
              </Popover>
            </div>
          </div>
        )
      }
      default: {
        return item.name
      }
    }
  }

  useRequest(() => Api.apiEngineV1CatalogListGet({ current: 1, pageSize: -1 }), {
    onSuccess(res) {
      if (res) {
        setTreeData(
          res
            .filter((item: any) => item.type === 'INTERNAL' || streamTableCatalogType.includes(item.type.toLowerCase()))
            .map((item, index) => ({
              title: <WrapTreeTitle type="catalog" item={item} searchHightLight={searchValue} />,
              key: 'catalog_' + item.id + '_' + getUnitId(),
              isLeaf: false,
              ...item,
              _type: 'catalog',
              catalogIndex: index,
            })),
        )
      }
    },
  })

  const { runAsync: getDbList } = useRequest(
    (args) =>
      Api.apiEngineV1DatabaseListGet({
        current: 1,
        pageSize: -1,
        catalog: args.catalog,
      }),
    {
      manual: true,
      onSuccess: (data, params) => {
        const _treeData = [...treeData]
        const index = params[0].catalogIndex
        if (data.length > 0) {
          _treeData[index].children = data.map((item, i) => ({
            title: <WrapTreeTitle type="database" item={item} searchHightLight={searchValue} />,
            key: `db_${item.catalogName}_${item.id}`,
            databaseIndex: i,
            isLeaf: false,
            catalogIndex: index,
            ...item,
            _type: 'database',
          }))
        } else {
          _treeData[index].children = []
        }
        setTreeData(_treeData)
      },
    },
  )

  const { runAsync: getTableList } = useRequest(
    async (args) => {
      if (
        streamTableCatalogType.find((i) => {
          return new RegExp(i, 'i').test(args.catalogType)
        })
      ) {
        return Api.apiEngineV1PtableListGet({
          current: '1',
          pageSize: '-1',
          catalog: args.catalog,
          database: args.database,
        })
      } else {
        return Api.apiEngineV1VtableSearchGet({
          current: 1,
          pageSize: -1,
          catalog: args.catalog,
          database: args.database,
        })
      }
    },
    {
      manual: true,
      onSuccess: (data, params) => {
        const _treeData = [...treeData]
        const catalogIndex = params[0].catalogIndex
        const databaseIndex = params[0].databaseIndex
        if (data?.list.length > 0) {
          _treeData[catalogIndex].children[databaseIndex].children = data?.list.map((item, i) => ({
            title: <WrapTreeTitle type="table" item={item} searchHightLight={searchValue} />,
            key: `table_${item.catalogName}_${item.databaseName}_${item.id}`,
            databaseIndex,
            catalogIndex,
            tableIndex: i,
            ...item,
            _type: 'table',
            isLeaf: true,
          }))
        } else {
          _treeData[catalogIndex].children[databaseIndex].children = []
        }
        setTreeData(_treeData)
      },
    },
  )

  const _loadData = (node: any) => {
    const { _type, children, name, catalogName, catalogIndex, databaseIndex, catalogType } = node
    if (children) {
      return Promise.resolve()
    }
    switch (_type) {
      case 'catalog':
        return getDbList({ catalog: name, catalogIndex })
      case 'database':
        return getTableList({ catalog: catalogName, database: name, catalogIndex, databaseIndex, catalogType })
      default:
        return Promise.resolve()
    }
  }

  const loadData = async (node: any) => {
    const res = await _loadData(node)
    return res
  }

  const showTreeData = useMemo(() => {
    function generateTreeData(data: TreeNodeType[]) {
      const regExp = new RegExp(searchValue || '', 'i')
      const newTreeData: TreeNodeType[] = []
      data.forEach((tree) => {
        if (tree) {
          const isMatch = regExp.test(tree.name as string)
          const children = tree.children ? generateTreeData(tree.children) : null
          if (isMatch || children?.length) {
            newTreeData.push({
              ...tree,
              children: children as TreeNodeType[],
              title: (
                <WrapTreeTitle
                  type={tree._type || ''}
                  item={
                    omit(tree, [
                      'key',
                      'isLeaf',
                      'title',
                      'children',
                      'databaseIndex',
                      'catalogIndex',
                      'tableIndex',
                    ]) as { name: string }
                  }
                  searchHightLight={searchValue}
                />
              ),
            })
          }
        }
      })
      return newTreeData
    }
    return generateTreeData(treeData)
  }, [treeData, searchValue])

  return (
    <div className="mr-2">
      <Input.Search
        onChange={debounce((e: React.ChangeEvent<HTMLInputElement>) => {
          const value = e.target.value
          setSearchValue(value)
        }, 400)}
        allowClear
        className="mb-2"
        placeholder="请输入搜索"
        suffix={
          <Tooltip title="数据初次点击时加载，当前搜索只会搜索已加载的数据">
            <InfoCircleOutlined className='cursor-pointer text-["rgba(0,0,0,.45)"]' />
          </Tooltip>
        }
      />
      <div>
        <Tree showLine blockNode checkable={false} loadData={loadData} treeData={showTreeData} />
      </div>
    </div>
  )
}

export default TreeNode
