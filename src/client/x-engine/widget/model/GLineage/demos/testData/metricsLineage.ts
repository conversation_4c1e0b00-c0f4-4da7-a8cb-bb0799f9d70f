// todo: 显示太丑了，得看一下，怎么兼容展示
const data = {
    edges: [
        {
            source: {
                table: true,
                name: 'table5',
                columnExpression: 'column8',
            },
            target: {
                table: true,
                name: 'table4',
                columnExpression: 'column7',
            },
        },
        {
            source: {
                table: true,
                name: 'table6',
                columnExpression: 'column9',
            },
            target: {
                table: true,
                name: 'table4',
                columnExpression: 'column7',
            },
        },
        {
            source: {
                table: true,
                name: 'table7',
                columnExpression: 'column10',
            },
            target: {
                table: true,
                name: 'table4',
                columnExpression: 'column7',
            },
        },
        {
            source: {
                table: true,
                name: 'table2',
                columnExpression: 'column5',
            },
            target: {
                table: true,
                name: 'table1',
                columnExpression: 'column4',
            },
        },
        {
            source: {
                table: true,
                name: 'table3',
                columnExpression: 'column6',
            },
            target: {
                table: true,
                name: 'table1',
                columnExpression: 'column4',
            },
        },
        {
            source: {
                table: true,
                name: 'table1',
                columnExpression: 'column4',
            },
            target: {
                table: false,
                name: 'metrics-3',
                columnExpression: 'column3',
            },
        },
        {
            source: {
                table: true,
                name: 'table4',
                columnExpression: 'column4',
            },
            target: {
                table: false,
                name: 'metrics-2',
                columnExpression: 'column2',
            },
        },
        {
            source: {
                table: false,
                name: 'metrics-2',
                columnExpression: 'column2',
            },
            target: {
                table: false,
                name: 'metrics-1',
                columnExpression: 'column1',
            },
        },
        {
            source: {
                table: false,
                name: 'metrics-3',
                columnExpression: 'column3',
            },
            target: {
                table: false,
                name: 'metrics-1',
                columnExpression: 'column1',
            },
        },
    ],
    nodes: [
        {
            table: false,
            metricsType: 'atomic',
            name: 'metrics-1',
            columnExpression: 'column1',
        },
        {
            table: false,
            metricsType: 'composite',
            name: 'metrics-2',
            columnExpression: 'column2',
        },
        {
            table: false,
            metricsType: 'derived',
            name: 'metrics-3',
            columnExpression: 'column3',
        },
        {
            table: true,
            metricsType: '',
            name: 'table1',
            columnExpression: 'column4',
        },
        {
            table: true,
            metricsType: '',
            name: 'table2',
            columnExpression: 'column5',
        },
        {
            table: true,
            metricsType: '',
            name: 'table3',
            columnExpression: 'column6',
        },
        {
            table: true,
            metricsType: '',
            name: 'table4',
            columnExpression: 'column7',
        },
        {
            table: true,
            metricsType: '',
            name: 'table5',
            columnExpression: 'column8',
        },
        {
            table: true,
            metricsType: '',
            name: 'table6',
            columnExpression: 'column9',
        },
        {
            table: true,
            metricsType: '',
            name: 'table7',
            columnExpression: 'column10',
        },
    ],
}


const spellId = (tar: Record<string, any>) => {
    const { table, name } = tar
    const tableChar = 'T'
    return table === false ? name : `${tableChar}-${name}`
}
function formatFn(data) {
    const { edges = [], nodes = [] } = data
    if (!Array.isArray(nodes) || !Array.isArray(edges)) {
        return {
            nodes: [],
            edges: []
        }
    }
    const ansNodes = nodes.map((n) => {
        const id = spellId(n)
        const metricsType = n.metricsType ? n.metricsType : 'metricsTable'
        return {
            ...n,
            id: id,
            metricsType
        }
    })
    const ansEdges = edges.map((e) => {
        const { source, target } = e
        const sourceId = spellId(source)
        const targetId = spellId(target)
        return {
            source: sourceId,
            target: targetId,
            sourceKey: source.columnExpression,
            targetKey: target.columnExpression
        }
    })
    return {
        nodes: ansNodes,
        edges: ansEdges
    }
}
export const metricsLineageData = formatFn(data)