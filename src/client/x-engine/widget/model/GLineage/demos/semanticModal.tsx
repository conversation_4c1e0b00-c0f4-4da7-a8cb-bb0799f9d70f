import GLineage from '@model/GLineage'
const semanticModal = {
  name: 'a8',
  model: "ref('a8')",
  defaults: {
    agg_time_dimension: 'dt',
  },
  description: null,
  dimensions: [
    {
      name: 'p_end_date',
      type: 'categorical',
      expr: null,
      description: null,
      type_params: null,
    },
    {
      name: 'dt',
      type: 'time',
      expr: null,
      description: null,
      type_params: {
        time_granularity: 'day',
      },
    },
    {
      name: 'p_item_sk',
      type: 'categorical',
      expr: null,
      description: null,
      type_params: null,
    },
  ],
  measures: [
    {
      name: 'measure6',
      agg: 'max',
      expr: null,
      description: 'aaa',
    },
  ],
  entities: [
    {
      name: 'p_channel_dmail',
      type: 'primary',
    },
  ],
}

function formatSemanticModal(data) {
  const { name, dimensions, measures, entities } = data || {}
  let isIncludeEntity = false
  const semanticNode = {
    table: name || '',
    type: 'semanticModal',
    id: '123',
    dimensionsColumns: (dimensions || []).map((dim) => {
      const entity = entities.find((i) => i.name === dim.name)
      const subInfo = []
      if (entity) {
        isIncludeEntity = true
        subInfo.push('pk')
      }
      if (dim.description && typeof dim.description === 'string') {
        subInfo.push(dim.description)
      }
      return {
        name: dim.name,
        subInfo: (dim.type === 'time' ? ['时间度量列'] : []).concat(subInfo),
      }
    }),
    metricsColumns: (measures || []).map((mea) => {
      const entity = entities.find((i) => i.name === mea.name)
      const subInfo = []
      if (entity) {
        isIncludeEntity = true
        subInfo.push('pk')
      }
      if (mea.description && typeof mea.description === 'string') {
        subInfo.push(mea.description)
      }
      return {
        name: mea.name,
        subInfo: subInfo,
      }
    }),
    commonColumns: isIncludeEntity
      ? []
      : (entities || []).map((ent) => ({
          name: ent.name,
          subInfo: ['pk'],
        })),
  }
  const ans = {
    nodes: [semanticNode],
    edges: [],
  }

  console.log('and__________', ans)
  return ans
}
export default function Page() {
  return (
    <div
      style={{
        height: '500px',
        // background: 'pink'
      }}
    >
      <GLineage data={formatSemanticModal(semanticModal)} typeKey="type" />
    </div>
  )
}
