import React from 'react'
// 记得把 MyLineage 在 src/index.ts 暴露出来
import GLineage from '@model/GLineage'

const data = {
  nodes: [
    {
      catalogName: 'dipeak',
      databaseName: 'etl',
      tableName: 'etf',
      type: 'asVirtualTable',
      columnNames: [
        'b_info_holder',
        'wind_fundcode',
        'fundname',
        'fundamount',
        'last_fundamount',
        'fundamount_incr',
        'etf_fundamount',
        's_dq_1day_amount',
        's_dq_30day_avg_amountsjalsdkjaslkdjsaldkjsadlkjaslda',
        's_dq_60day_avg_amount',
        'org_fundamount_rate',
        'feeder_fund_code',
        'feeder_fundamount',
        'feeder_fundamount_rate',
        'belong_comp',
        'first_track',
        'second_track',
        'trackingindex_code',
        'trackingindex_name',
        'group_code',
        'capital_type',
        'org_short_name',
        'last_partition',
        'prev_partition',
        'avg_org_fundrate',
        'avg_s_dq_1day_amount',
        'max_s_dq_1day_amount',
        'min_s_dq_1day_amount',
        'avg_etf_fundamount',
        'upd_tm',
        'dt',
      ],
      metrics: false,
    },
    {
      catalogName: 'dipeak',
      databaseName: 'etl',
      tableName: 'vt_wind_etf_flat_table',
      type: 'asVirtualTable',
      columnNames: [
        'b_info_holder',
        'wind_fundcode',
        'fundamount',
        'last_fundamount',
        'fundamount_incr',
        'upd_tm',
        'fundcode',
        'etf_fundamount',
        's_dq_1day_amount',
        's_dq_30day_avg_amount',
        's_dq_60day_avg_amount',
        's_dq_1hour_avg_amount',
        's_dq_6hour_avg_amount',
        's_dq_12hour_avg_amount',
        'org_fundamount_rate',
        'feeder_fund_code',
        'feeder_fundamount',
        'feeder_fundamount_rate',
        'fundname',
        'belong_comp',
        'first_track',
        'second_track',
        'trackingindex_code',
        'trackingindex_name',
        'group_code',
        'f_nav_unit',
        'f_nav_adjusted',
        'f_info_listdate',
        'f_info_maturitydate',
        'capital_type',
        'org_short_name',
        'first_type_holder',
        'second_type_holder',
        'third_type_holder',
        'b_issuer_sharecategory',
        'dt',
        'last_partition',
        'prev_partition',
        'max_org_fundrate',
        'avg_org_fundrate',
        'avg_s_dq_1day_amount',
        'max_s_dq_1day_amount',
        'min_s_dq_1day_amount',
        'avg_etf_fundamount',
      ],
      metrics: false,
    },
    {
      catalogName: 'dipeak',
      databaseName: 'etl',
      tableName: 'vt_wind_etf_b10_holding_info',
      type: 'likeVirtualTable',
      columnNames: [
        'wind_fundcode',
        'b_info_holder',
        'fundamount',
        'last_fundamount',
        'fundamount_incr',
        'upd_tm',
        'dt',
      ],
      metrics: false,
    },
    {
      catalogName: 'dipeak',
      databaseName: 'etl',
      tableName: 'wind_etf_b10_holding_info',
      type: null,
      columnNames: [
        'wind_fundcode',
        'b_info_holder',
        'fundamount',
        'last_fundamount',
        'fundamount_incr',
        'upd_tm',
        'dt',
      ],
      metrics: false,
    },
    {
      catalogName: 'dipeak',
      databaseName: 'etl',
      tableName: 'vt_wind_etf_b10_fund_info',
      type: 'likeVirtualTable',
      columnNames: [
        'fundcode',
        'wind_fundcode',
        'fundname',
        'belong_comp',
        'first_track',
        'second_track',
        'trackingindex_code',
        'trackingindex_name',
        'group_code',
        'f_nav_unit',
        'f_nav_adjusted',
        'f_info_listdate',
        'F_INFO_MATURITYDATE',
        'dt',
      ],
      metrics: false,
    },
    {
      catalogName: 'dipeak',
      databaseName: 'etl',
      tableName: 'wind_etf_b10_fund_info',
      type: null,
      columnNames: [
        'fundcode',
        'wind_fundcode',
        'fundname',
        'belong_comp',
        'first_track',
        'second_track',
        'trackingindex_code',
        'trackingindex_name',
        'group_code',
        'f_nav_unit',
        'f_nav_adjusted',
        'f_info_listdate',
        'F_INFO_MATURITYDATE',
        'dt',
      ],
      metrics: false,
    },
    {
      catalogName: 'dipeak',
      databaseName: 'etl',
      tableName: 'vt_wind_etf_b10_fund_scale_info',
      type: 'likeVirtualTable',
      columnNames: [
        'wind_fundcode',
        'etf_fundamount',
        's_dq_1day_amount',
        's_dq_30day_avg_amount',
        's_dq_60day_avg_amount',
        's_dq_1hour_avg_amount',
        's_dq_6hour_avg_amount',
        's_dq_12hour_avg_amount',
        'org_fundamount_rate',
        'feeder_fund_code',
        'feeder_fundamount',
        'feeder_fundamount_rate',
        'dt',
      ],
      metrics: false,
    },
    {
      catalogName: 'dipeak',
      databaseName: 'etl',
      tableName: 'wind_etf_b10_fund_scale_info',
      type: null,
      columnNames: [
        'wind_fundcode',
        'etf_fundamount',
        's_dq_1day_amount',
        's_dq_30day_avg_amount',
        's_dq_60day_avg_amount',
        's_dq_1hour_avg_amount',
        's_dq_6hour_avg_amount',
        's_dq_12hour_avg_amount',
        'org_fundamount_rate',
        'feeder_fund_code',
        'feeder_fundamount',
        'feeder_fundamount_rate',
        'dt',
      ],
      metrics: false,
    },
    {
      catalogName: 'dipeak',
      databaseName: 'etl',
      tableName: 'vt_wind_etf_b10_cust_info',
      type: 'likeVirtualTable',
      columnNames: [
        'capital_type',
        'org_short_name',
        'holder_name',
        'first_type_holder',
        'second_type_holder',
        'third_type_holder',
        'b_issuer_sharecategory',
        'dt',
      ],
      metrics: false,
    },
    {
      catalogName: 'dipeak',
      databaseName: 'etl',
      tableName: 'wind_etf_b10_cust_info',
      type: null,
      columnNames: [
        'capital_type',
        'org_short_name',
        'holder_name',
        'first_type_holder',
        'second_type_holder',
        'third_type_holder',
        'b_issuer_sharecategory',
        'dt',
      ],
      metrics: false,
    },
  ],
  edges: [
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'b_info_holder',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'b_info_holder',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'b_info_holder',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_holding_info',
        columnName: 'b_info_holder',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_holding_info',
        columnName: 'b_info_holder',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_holding_info',
        columnName: 'b_info_holder',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'wind_fundcode',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'wind_fundcode',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'wind_fundcode',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_holding_info',
        columnName: 'wind_fundcode',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_holding_info',
        columnName: 'wind_fundcode',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_holding_info',
        columnName: 'wind_fundcode',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'fundname',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'fundname',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'fundname',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_info',
        columnName: 'fundname',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_info',
        columnName: 'fundname',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_fund_info',
        columnName: 'fundname',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'fundamount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'fundamount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'fundamount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_holding_info',
        columnName: 'fundamount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_holding_info',
        columnName: 'fundamount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_holding_info',
        columnName: 'fundamount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'last_fundamount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'last_fundamount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'last_fundamount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_holding_info',
        columnName: 'last_fundamount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_holding_info',
        columnName: 'last_fundamount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_holding_info',
        columnName: 'last_fundamount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'fundamount_incr',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'fundamount_incr',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'fundamount_incr',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_holding_info',
        columnName: 'fundamount_incr',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_holding_info',
        columnName: 'fundamount_incr',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_holding_info',
        columnName: 'fundamount_incr',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'etf_fundamount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'etf_fundamount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'etf_fundamount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 'etf_fundamount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 'etf_fundamount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_fund_scale_info',
        columnName: 'etf_fundamount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 's_dq_1day_amount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 's_dq_1day_amount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 's_dq_1day_amount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 's_dq_1day_amount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 's_dq_1day_amount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_fund_scale_info',
        columnName: 's_dq_1day_amount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 's_dq_30day_avg_amount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 's_dq_30day_avg_amount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 's_dq_30day_avg_amount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 's_dq_30day_avg_amount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 's_dq_30day_avg_amount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_fund_scale_info',
        columnName: 's_dq_30day_avg_amount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 's_dq_60day_avg_amount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 's_dq_60day_avg_amount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 's_dq_60day_avg_amount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 's_dq_60day_avg_amount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 's_dq_60day_avg_amount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_fund_scale_info',
        columnName: 's_dq_60day_avg_amount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'org_fundamount_rate',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'org_fundamount_rate',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'org_fundamount_rate',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 'org_fundamount_rate',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 'org_fundamount_rate',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_fund_scale_info',
        columnName: 'org_fundamount_rate',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'feeder_fund_code',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'feeder_fund_code',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'feeder_fund_code',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 'feeder_fund_code',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 'feeder_fund_code',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_fund_scale_info',
        columnName: 'feeder_fund_code',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'feeder_fundamount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'feeder_fundamount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'feeder_fundamount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 'feeder_fundamount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 'feeder_fundamount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_fund_scale_info',
        columnName: 'feeder_fundamount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'feeder_fundamount_rate',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'feeder_fundamount_rate',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'feeder_fundamount_rate',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 'feeder_fundamount_rate',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 'feeder_fundamount_rate',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_fund_scale_info',
        columnName: 'feeder_fundamount_rate',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'belong_comp',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'belong_comp',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'belong_comp',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_info',
        columnName: 'belong_comp',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_info',
        columnName: 'belong_comp',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_fund_info',
        columnName: 'belong_comp',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'first_track',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'first_track',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'first_track',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_info',
        columnName: 'first_track',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_info',
        columnName: 'first_track',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_fund_info',
        columnName: 'first_track',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'second_track',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'second_track',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'second_track',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_info',
        columnName: 'second_track',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_info',
        columnName: 'second_track',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_fund_info',
        columnName: 'second_track',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'trackingindex_code',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'trackingindex_code',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'trackingindex_code',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_info',
        columnName: 'trackingindex_code',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_info',
        columnName: 'trackingindex_code',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_fund_info',
        columnName: 'trackingindex_code',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'trackingindex_name',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'trackingindex_name',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'trackingindex_name',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_info',
        columnName: 'trackingindex_name',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_info',
        columnName: 'trackingindex_name',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_fund_info',
        columnName: 'trackingindex_name',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'group_code',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'group_code',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'group_code',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_info',
        columnName: 'group_code',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_info',
        columnName: 'group_code',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_fund_info',
        columnName: 'group_code',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'capital_type',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'capital_type',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'capital_type',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_cust_info',
        columnName: 'capital_type',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_cust_info',
        columnName: 'capital_type',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_cust_info',
        columnName: 'capital_type',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'org_short_name',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'org_short_name',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'org_short_name',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_cust_info',
        columnName: 'org_short_name',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_cust_info',
        columnName: 'org_short_name',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_cust_info',
        columnName: 'org_short_name',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'last_partition',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'last_partition',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'last_partition',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 'dt',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 'dt',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_fund_scale_info',
        columnName: 'dt',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'prev_partition',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'prev_partition',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'prev_partition',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 'dt',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'avg_org_fundrate',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'avg_org_fundrate',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'avg_org_fundrate',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 'org_fundamount_rate',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'avg_s_dq_1day_amount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'avg_s_dq_1day_amount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'avg_s_dq_1day_amount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 's_dq_1day_amount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'max_s_dq_1day_amount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'max_s_dq_1day_amount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'max_s_dq_1day_amount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 's_dq_1day_amount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'min_s_dq_1day_amount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'min_s_dq_1day_amount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'min_s_dq_1day_amount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 's_dq_1day_amount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'avg_etf_fundamount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'avg_etf_fundamount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'avg_etf_fundamount',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_fund_scale_info',
        columnName: 'etf_fundamount',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'upd_tm',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'upd_tm',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'upd_tm',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_holding_info',
        columnName: 'upd_tm',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_holding_info',
        columnName: 'upd_tm',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_holding_info',
        columnName: 'upd_tm',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'etf',
        columnName: 'dt',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'dt',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_flat_table',
        columnName: 'dt',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_holding_info',
        columnName: 'dt',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'vt_wind_etf_b10_holding_info',
        columnName: 'dt',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'etl',
        tableName: 'wind_etf_b10_holding_info',
        columnName: 'dt',
      },
    },
  ],
}

const data1 = {
  nodes: [
    {
      catalogName: 'dipeak',
      databaseName: 'tpcds',
      tableName: 'whc_tpcds_test',
      type: 'asVirtualTable',
      columnNames: [
        'cs_ext_tax',
        'cs_ext_wholesale_cost',
        'cs_ext_sales_price',
        'cs_ext_ship_cost',
        'c_birth_month',
        'c_birth_year',
        'c_birth_day',
        'dt',
        'cs_ship_mode_sk',
        'cs_bill_addr_sk',
        'cs_sold_time_sk',
      ],
      metrics: false,
    },
    {
      catalogName: 'dipeak',
      databaseName: 'tpcds',
      tableName: 'vt_dwd_catalog_sales',
      type: 'likeVirtualTable',
      columnNames: [
        'cs_sold_date_sk',
        'cs_sold_time_sk',
        'cs_ship_date_sk',
        'cs_bill_customer_sk',
        'cs_bill_cdemo_sk',
        'cs_bill_hdemo_sk',
        'cs_bill_addr_sk',
        'cs_ship_customer_sk',
        'cs_ship_cdemo_sk',
        'cs_ship_hdemo_sk',
        'cs_ship_addr_sk',
        'cs_call_center_sk',
        'cs_catalog_page_sk',
        'cs_ship_mode_sk',
        'cs_warehouse_sk',
        'cs_item_sk',
        'cs_promo_sk',
        'cs_order_number',
        'cs_quantity',
        'cs_wholesale_cost',
        'cs_list_price',
        'cs_sales_price',
        'cs_ext_discount_amt',
        'cs_ext_sales_price',
        'cs_ext_wholesale_cost',
        'cs_ext_list_price',
        'cs_ext_tax',
        'cs_coupon_amt',
        'cs_ext_ship_cost',
        'cs_net_paid',
        'cs_net_paid_inc_tax',
        'cs_net_paid_inc_ship',
        'cs_net_paid_inc_ship_tax',
        'cs_net_profit',
        'dt',
      ],
      metrics: false,
    },
    {
      catalogName: 'hive',
      databaseName: 'my_tpcds',
      tableName: 'dwd_catalog_sales',
      type: 'externalTable',
      columnNames: [
        'cs_sold_date_sk',
        'cs_sold_time_sk',
        'cs_ship_date_sk',
        'cs_bill_customer_sk',
        'cs_bill_cdemo_sk',
        'cs_bill_hdemo_sk',
        'cs_bill_addr_sk',
        'cs_ship_customer_sk',
        'cs_ship_cdemo_sk',
        'cs_ship_hdemo_sk',
        'cs_ship_addr_sk',
        'cs_call_center_sk',
        'cs_catalog_page_sk',
        'cs_ship_mode_sk',
        'cs_warehouse_sk',
        'cs_item_sk',
        'cs_promo_sk',
        'cs_order_number',
        'cs_quantity',
        'cs_wholesale_cost',
        'cs_list_price',
        'cs_sales_price',
        'cs_ext_discount_amt',
        'cs_ext_sales_price',
        'cs_ext_wholesale_cost',
        'cs_ext_list_price',
        'cs_ext_tax',
        'cs_coupon_amt',
        'cs_ext_ship_cost',
        'cs_net_paid',
        'cs_net_paid_inc_tax',
        'cs_net_paid_inc_ship',
        'cs_net_paid_inc_ship_tax',
        'cs_net_profit',
        'dt',
      ],
      metrics: false,
    },
    {
      catalogName: 'dipeak',
      databaseName: 'tpcds',
      tableName: 'vt_dim_customer',
      type: 'likeVirtualTable',
      columnNames: [
        'c_customer_sk',
        'c_customer_id',
        'c_current_cdemo_sk',
        'c_current_hdemo_sk',
        'c_current_addr_sk',
        'c_first_shipto_date',
        'c_first_sales_date',
        'c_salutation',
        'c_first_name',
        'c_last_name',
        'c_preferred_cust_flag',
        'c_birth_day',
        'c_birth_month',
        'c_birth_year',
        'c_birth_country',
        'c_login',
        'c_email_address',
        'c_last_review_date',
        'dt',
      ],
      metrics: false,
    },
    {
      catalogName: 'hive',
      databaseName: 'my_tpcds',
      tableName: 'dim_customer',
      type: 'externalTable',
      columnNames: [
        'c_customer_sk',
        'c_customer_id',
        'c_current_cdemo_sk',
        'c_current_hdemo_sk',
        'c_current_addr_sk',
        'c_first_shipto_date',
        'c_first_sales_date',
        'c_salutation',
        'c_first_name',
        'c_last_name',
        'c_preferred_cust_flag',
        'c_birth_day',
        'c_birth_month',
        'c_birth_year',
        'c_birth_country',
        'c_login',
        'c_email_address',
        'c_last_review_date',
        'dt',
      ],
      metrics: false,
    },
    {
      catalogName: 'dipeak',
      databaseName: 'tpcds',
      tableName: 'whc_test_1',
      type: 'atomic',
      columnNames: ['dt', 'whc_test_1'],
      metrics: true,
    },
  ],
  edges: [
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'whc_tpcds_test',
        columnName: 'cs_ext_tax',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dwd_catalog_sales',
        columnName: 'cs_ext_tax',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dwd_catalog_sales',
        columnName: 'cs_ext_tax',
      },
      target: {
        catalogName: 'hive',
        databaseName: 'my_tpcds',
        tableName: 'dwd_catalog_sales',
        columnName: 'cs_ext_tax',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'whc_tpcds_test',
        columnName: 'cs_ext_wholesale_cost',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dwd_catalog_sales',
        columnName: 'cs_ext_wholesale_cost',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dwd_catalog_sales',
        columnName: 'cs_ext_wholesale_cost',
      },
      target: {
        catalogName: 'hive',
        databaseName: 'my_tpcds',
        tableName: 'dwd_catalog_sales',
        columnName: 'cs_ext_wholesale_cost',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'whc_tpcds_test',
        columnName: 'cs_ext_sales_price',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dwd_catalog_sales',
        columnName: 'cs_ext_sales_price',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dwd_catalog_sales',
        columnName: 'cs_ext_sales_price',
      },
      target: {
        catalogName: 'hive',
        databaseName: 'my_tpcds',
        tableName: 'dwd_catalog_sales',
        columnName: 'cs_ext_sales_price',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'whc_tpcds_test',
        columnName: 'cs_ext_ship_cost',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dwd_catalog_sales',
        columnName: 'cs_ext_ship_cost',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dwd_catalog_sales',
        columnName: 'cs_ext_ship_cost',
      },
      target: {
        catalogName: 'hive',
        databaseName: 'my_tpcds',
        tableName: 'dwd_catalog_sales',
        columnName: 'cs_ext_ship_cost',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'whc_tpcds_test',
        columnName: 'c_birth_month',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dim_customer',
        columnName: 'c_birth_month',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dim_customer',
        columnName: 'c_birth_month',
      },
      target: {
        catalogName: 'hive',
        databaseName: 'my_tpcds',
        tableName: 'dim_customer',
        columnName: 'c_birth_month',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'whc_tpcds_test',
        columnName: 'c_birth_year',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dim_customer',
        columnName: 'c_birth_year',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dim_customer',
        columnName: 'c_birth_year',
      },
      target: {
        catalogName: 'hive',
        databaseName: 'my_tpcds',
        tableName: 'dim_customer',
        columnName: 'c_birth_year',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'whc_tpcds_test',
        columnName: 'c_birth_day',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dim_customer',
        columnName: 'c_birth_day',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dim_customer',
        columnName: 'c_birth_day',
      },
      target: {
        catalogName: 'hive',
        databaseName: 'my_tpcds',
        tableName: 'dim_customer',
        columnName: 'c_birth_day',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'whc_tpcds_test',
        columnName: 'dt',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dwd_catalog_sales',
        columnName: 'dt',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dwd_catalog_sales',
        columnName: 'dt',
      },
      target: {
        catalogName: 'hive',
        databaseName: 'my_tpcds',
        tableName: 'dwd_catalog_sales',
        columnName: 'dt',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'whc_tpcds_test',
        columnName: 'cs_ship_mode_sk',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dwd_catalog_sales',
        columnName: 'cs_ship_mode_sk',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dwd_catalog_sales',
        columnName: 'cs_ship_mode_sk',
      },
      target: {
        catalogName: 'hive',
        databaseName: 'my_tpcds',
        tableName: 'dwd_catalog_sales',
        columnName: 'cs_ship_mode_sk',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'whc_tpcds_test',
        columnName: 'cs_bill_addr_sk',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dwd_catalog_sales',
        columnName: 'cs_bill_addr_sk',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dwd_catalog_sales',
        columnName: 'cs_bill_addr_sk',
      },
      target: {
        catalogName: 'hive',
        databaseName: 'my_tpcds',
        tableName: 'dwd_catalog_sales',
        columnName: 'cs_bill_addr_sk',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'whc_tpcds_test',
        columnName: 'cs_sold_time_sk',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dwd_catalog_sales',
        columnName: 'cs_sold_time_sk',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'vt_dwd_catalog_sales',
        columnName: 'cs_sold_time_sk',
      },
      target: {
        catalogName: 'hive',
        databaseName: 'my_tpcds',
        tableName: 'dwd_catalog_sales',
        columnName: 'cs_sold_time_sk',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'whc_test_1',
        columnName: 'dt',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'whc_tpcds_test',
        columnName: 'dt',
      },
    },
    {
      source: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'whc_test_1',
        columnName: 'whc_test_1',
      },
      target: {
        catalogName: 'dipeak',
        databaseName: 'tpcds',
        tableName: 'whc_tpcds_test',
        columnName: 'cs_ext_sales_price',
      },
    },
  ],
  available: true,
  msg: null,
}
function formatVTLineageData(data) {
  const spellId = (n: any) => {
    const { catalogName, databaseName, tableName } = n
    const name = `${catalogName}.${databaseName}.${tableName}`
    return name
  }
  const { edges = [], nodes = [] } = data || {}
  if (!Array.isArray(nodes) || !Array.isArray(edges)) {
    return {
      nodes: [],
      edges: [],
    }
  }
  const ansNodes = nodes.map((n) => {
    const id = spellId(n)
    return {
      ...n,
      id: id,
    }
  })
  const ansEdges = edges.map((e) => {
    const { source, target } = e
    const sourceId = spellId(source)
    const targetId = spellId(target)
    return {
      source: sourceId,
      target: targetId,
      sourceKey: source.columnName,
      targetKey: target.columnName,
    }
  })
  return {
    nodes: ansNodes,
    edges: ansEdges,
  }
}

export default function Page() {
  return (
    <GLineage
      data={formatVTLineageData(data)}
      typeKey="type"
      menuProps={{
        content: '<div style="cursor:pointer;">跳转到物化视图</div>',
        handleClickMenu(...args) {
          console.log('args_____________', args)
        },
      }}
    />
  )
}
