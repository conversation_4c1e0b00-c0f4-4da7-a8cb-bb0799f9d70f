// @ts-nocheck
import G6 from '@antv/g6'
const { Util, registerBehavior, registerEdge, registerNode } = G6
import editSVG from '../widget/edit.svg'
import { isInBBox, fittingString } from '../widget/utils'
import { getTitleAndMulItemsShapeInfo } from './shapeInit'

const edgeColor = '#A8AEB8'

export function diceErScrollRegisterBehavior(graphRef, config) {
  const { nodeShapeCfg } = config || {}
  const { itemHeight, itemCount } = getTitleAndMulItemsShapeInfo(nodeShapeCfg)
  registerBehavior('dice-er-scroll', {
    getDefaultCfg() {
      return {
        multiple: true,
      }
    },
    getEvents() {
      return {
        itemHeight,
        wheel: 'scorll',
        click: 'click',
      }
    },
    scorll(e) {
      e.preventDefault()
      const graph = graphRef.current
      if (!graph) {
        return
      }
      const nodes = graph.getNodes().filter((n) => {
        const bbox = n.getBBox()

        return isInBBox(graph.getPointByClient(e.clientX, e.clientY), bbox)
      })

      const x = e.deltaX || e.movementX
      let y = e.deltaY || e.movementY
      if (!y && navigator.userAgent.indexOf('Firefox') > -1) y = (-e.wheelDelta * 125) / 3

      if (nodes) {
        const edgesToUpdate = new Set()
        nodes.forEach((node) => {
          const model = node.getModel()
          const length = model.items.length
          if (length < itemCount) {
            return
          }
          const idx = model.startIndex || 0
          let startX = model.startX || 0.5
          let startIndex = idx + y * 0.02
          startX -= x
          if (startIndex < 0) {
            startIndex = 0
          }
          if (startX > 0) {
            startX = 0
          }

          if (startIndex > length - itemCount) {
            startIndex = length - itemCount
          }
          graph.updateItem(node, {
            startIndex,
            startX,
          })
          node.getEdges().forEach((edge) => edgesToUpdate.add(edge))
        })
        // G6 update the related edges when graph.updateItem with a node according to the new properties
        // here you need to update the related edges manualy since the new properties { startIndex, startX } for the nodes are custom, and cannot be recognized by G6
        edgesToUpdate.forEach((edge) => edge.refresh())
      }
    },
    click(e) {
      const graph = graphRef.current
      const item = e.item
      const shape = e.shape
      if (!item) {
        return
      }
      if (shape.get('name') === 'collapse') {
        graph.updateItem(item, {
          collapsed: true,
          size: [300, 50],
        })
      } else if (shape.get('name') === 'expand') {
        graph.updateItem(item, {
          collapsed: false,
          size: [300, 80],
        })
      }
      if (shape.get('name') === 'edit') {
        const { handleClickRightTopIcon } = config || {}
        if (typeof handleClickRightTopIcon === 'function') {
          handleClickRightTopIcon(item.getModel())
        }
      }
    },
  })
}

export function diceErEdgeRegisterEdge(graphRef, config) {
  const { nodeShapeCfg } = config || {}
  const { itemHeight, titleHeight, itemCount } = getTitleAndMulItemsShapeInfo(nodeShapeCfg)
  registerEdge('dice-er-edge', {
    draw(cfg, group) {
      const maxHeight = titleHeight + itemHeight * itemCount + 8

      // 利用source和targte进行node.id的定位获取node
      const edge = group.cfg.item
      const sourceNode = edge?.getSource()?.getModel()
      const targetNode = edge?.getTarget()?.getModel()
      if (!sourceNode || !targetNode) {
        console.warn('edge的格式不符')
        return null
      }

      // 居然是依赖node找到key
      const sourceIndex = sourceNode.items.findIndex((e) => e.key === cfg.sourceKey)
      const sourceStartIndex = sourceNode.startIndex || 0

      let sourceY = 15

      if (!sourceNode.collapsed && sourceIndex > sourceStartIndex - 1) {
        sourceY = titleHeight + (sourceIndex - sourceStartIndex + 0.5) * itemHeight
        sourceY = Math.min(sourceY, maxHeight)
      }

      const targetIndex = targetNode.items.findIndex((e) => e.key === cfg.targetKey)

      const targetStartIndex = targetNode.startIndex || 0

      let targetY = 15

      if (!targetNode.collapsed && targetIndex > targetStartIndex - 1) {
        targetY = (targetIndex - targetStartIndex + 0.5) * itemHeight + titleHeight
        targetY = Math.min(targetY, maxHeight)
      }

      const startPoint = {
        ...cfg.startPoint,
      }
      const endPoint = {
        ...cfg.endPoint,
      }

      startPoint.y = startPoint.y + sourceY
      endPoint.y = endPoint.y + targetY

      let shape
      if (sourceNode.id !== targetNode.id) {
        shape = group.addShape('path', {
          attrs: {
            stroke: edgeColor,
            path: [
              ['M', startPoint.x, startPoint.y],
              [
                'C',
                endPoint.x / 3 + (2 / 3) * startPoint.x,
                startPoint.y,
                endPoint.x / 3 + (2 / 3) * startPoint.x,
                endPoint.y,
                endPoint.x,
                endPoint.y,
              ],
            ],
            endArrow: true,
          },
          name: 'path-shape',
        })
      } else if (!sourceNode.collapsed) {
        let gap = Math.abs((startPoint.y - endPoint.y) / 3)
        if (startPoint['index'] === 1) {
          gap = -gap
        }
        shape = group.addShape('path', {
          attrs: {
            stroke: edgeColor,
            path: [
              ['M', startPoint.x, startPoint.y],
              ['C', startPoint.x - gap, startPoint.y, startPoint.x - gap, endPoint.y, startPoint.x, endPoint.y],
            ],
            endArrow: true,
          },
          name: 'path-shape',
        })
      }

      return shape
    },
    afterDraw(cfg, group) {
      const labelCfg = cfg.labelCfg || {}
      const edge = group.cfg.item
      const sourceNode = edge.getSource().getModel()
      const targetNode = edge.getTarget().getModel()
      if (sourceNode.collapsed && targetNode.collapsed) {
        return
      }
      const path = group.find((element) => element.get('name') === 'path-shape')

      const labelStyle = Util.getLabelPosition(path, 0.5, 0, 0, true)
      const label = group.addShape('text', {
        attrs: {
          ...labelStyle,
          text: cfg.label || '',
          fill: '#000',
          textAlign: 'center',
          stroke: '#fff',
          lineWidth: 1,
        },
      })
      label.rotateAtStart(labelStyle.rotate)
    },
  })
}

export function diceErBoxRegisterNode(graphRef, config) {
  const { rightTopIconType, nodeShapeCfg } = config || {}
  const {
    width,
    itemHeight,
    titleHeight,
    borderWidth,
    collapseHeight,
    itemCount,
    titleFontSize,
    textFontSize,
    textPadding,
    itemNameOccupyWidth,
    itemSubInfoOccupyWidth,
    itemSubInfoTextStart,
  } = getTitleAndMulItemsShapeInfo(nodeShapeCfg)
  registerNode('dice-er-box', {
    draw(cfg, group) {
      // config
      const {
        title,
        items = [],
        isEdit,
        startIndex = 0,
        selectedIndex,
        collapsed,
      } = cfg as { startIndex: number; [key: string]: any }

      const height =
        titleHeight +
        (items.length < itemCount ? items.length : itemCount) * itemHeight +
        (items.length > 0 ? collapseHeight : 0)

      const nBoxStyle = cfg.title.styleCfg
      const boxStyle = {
        stroke: nBoxStyle.fill,
        radius: 4,
      }

      const list = items
      // afterList 展示的list
      const afterList = list.slice(Math.floor(startIndex), Math.floor(startIndex + itemCount + 1))
      const offsetY = -(startIndex % 1) * itemHeight + titleHeight

      // title图形
      const titleTextOffesetY = Math.ceil((titleHeight - titleFontSize) / 2) + titleFontSize
      group.addShape('rect', {
        attrs: {
          x: 0,
          y: 0,
          fill: boxStyle.stroke,
          height: titleHeight,
          width,
          radius: items.length > 0 ? [boxStyle.radius, boxStyle.radius, 0, 0] : boxStyle.radius,
        },
        draggable: true,
        name: 'title-box',
      })

      // edit图标
      ;(isEdit || rightTopIconType === 'edit') &&
        group.addShape('image', {
          attrs: {
            x: width - textPadding - 14,
            y: titleTextOffesetY - titleFontSize,
            height: 16,
            width: 16,
            cursor: 'pointer',
            img: editSVG,
          },
          name: 'edit',
        })

      const titleWidth = width - textPadding * 2 - (isEdit || rightTopIconType === 'edit' ? 18 : 0)
      const { text: titleText } = fittingString(title.text, titleWidth, titleFontSize)
      // title字体
      group.addShape('text', {
        attrs: {
          y: titleTextOffesetY,
          x: textPadding,
          fill: '#fff',
          text: titleText,
          fontSize: titleFontSize,
          fontWeight: 500,
          full: title,
          cursor: 'pointer',
        },
        name: 'title-text',
      })
      if (items.length > 0) {
        // 收缩展开框
        group.addShape('rect', {
          attrs: {
            x: 0,
            y: collapsed ? titleHeight : height - collapseHeight,
            height: collapseHeight,
            width,
            fill: boxStyle.stroke,
            radius: [0, 0, boxStyle.radius, boxStyle.radius],
            cursor: 'pointer',
          },
          name: collapsed ? 'expand' : 'collapse',
        })
        // 收缩展开text
        group.addShape('text', {
          attrs: {
            x: width / 2 - 6,
            y: (collapsed ? titleHeight : height - collapseHeight) + 13,
            fontSize: textFontSize,
            text: collapsed ? '+' : '-',
            width,
            fill: '#000',
            radius: [0, 0, boxStyle.radius, boxStyle.radius],
            cursor: 'pointer',
          },
          name: collapsed ? 'expand' : 'collapse',
        })
      }
      // 整个框选的主图形
      const keyshape = group.addShape('rect', {
        attrs: {
          x: 0,
          y: 0,
          width,
          height: collapsed ? 45 : height,
          ...boxStyle,
          stroke: boxStyle.stroke,
        },
        draggable: true,
        name: 'main-box',
      })

      if (collapsed) {
        return keyshape
      }

      // 不收缩的情况下，增加Group
      const listContainer = group.addGroup({
        id: 'scrollItemsGroup',
      })

      // 滚动条
      if (list.length > itemCount) {
        // 滚动优化样式
        listContainer.setClip({
          type: 'rect',
          attrs: {
            x: -8,
            y: 30,
            width: width + 16,
            height: height - titleHeight - 16,
          },
        })
        listContainer.addShape({
          type: 'rect',
          attrs: {
            x: 1,
            y: 30,
            width: width - 2,
            height: height - titleHeight - 16,
            fill: 'rgba(255,255,255, 0.8)',
          },
          draggable: true,
        })
        const contentHeight = height - titleHeight - collapseHeight
        const barStyle = {
          width: 4,
          padding: 0,
          boxStyle: {
            stroke: '#00000022',
          },
          innerStyle: {
            fill: '#00000022',
          },
        }

        listContainer.addShape('rect', {
          attrs: {
            y: 30,
            x: width - barStyle.padding - barStyle.width,
            width: barStyle.width,
            height: contentHeight,
            ...barStyle.boxStyle,
          },
        })

        const indexHeight = afterList.length > itemCount - 1 ? (afterList.length / list.length) * contentHeight : 10

        listContainer.addShape('rect', {
          attrs: {
            y: 30 + barStyle.padding + (startIndex / list.length) * contentHeight,
            x: width - barStyle.padding - barStyle.width,
            width: barStyle.width,
            height: Math.min(height, indexHeight),
            ...barStyle.innerStyle,
          },
        })
      }

      // items项
      if (afterList) {
        afterList.forEach((e, i) => {
          const isSelected = Math.floor(startIndex) + i === Number(selectedIndex)
          const { name, subInfo = [] } = e
          // todo: 可以预先计算好，进行个数比对
          // const label = key.length > 26 ? key.slice(0, 24) + "..." : key;
          const { text: label } = fittingString(name.text, itemNameOccupyWidth, textFontSize)
          const nameStyleCfg = name.styleCfg || {}
          const hasBar = list.length > itemCount
          listContainer.addShape('rect', {
            attrs: {
              x: borderWidth,
              y: i * itemHeight + offsetY,
              width: width - borderWidth * 2 - (hasBar ? 4 : 0),
              height: itemHeight,
              radius: 0,
              lineWidth: 1,
              cursor: 'pointer',
              ...nameStyleCfg,
            },
            name: `item-${Math.floor(startIndex) + i}-content`,
            draggable: true,
          })

          listContainer.addShape('text', {
            attrs: {
              x: textPadding,
              y: i * itemHeight + offsetY + textFontSize + Math.ceil((itemHeight - textFontSize) / 2),
              text: `${label}`,
              fontSize: textFontSize,
              fill: '#000',
              fontFamily:
                'Avenir,-apple-system,BlinkMacSystemFont,Segoe UI,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol',
              full: e,
              fontWeight: isSelected ? 500 : 100,
              cursor: 'pointer',
            },
            name: `item-${Math.floor(startIndex) + i}`,
          })

          subInfo.sort((a, b) => {
            a = a.isEllipsis === false ? 1 : 0
            b = b.isEllipsis === false ? 1 : 0
            return b - a
          })
          let rightWidth = itemSubInfoOccupyWidth
          let subInfoStartTextX = Math.ceil(itemSubInfoTextStart + itemSubInfoOccupyWidth)
          const ellipsisLength = 10
          const textSpace = 6
          const resolvedSubInfo = subInfo.reduce((preSubInfo, subInfoItem) => {
            const { text = '', styleCfg = {} } = subInfoItem
            if (!text || rightWidth < ellipsisLength) {
              return preSubInfo
            }
            const { text: textLabel, textWidth } = fittingString(text, rightWidth, textFontSize)
            const startX = Math.ceil(subInfoStartTextX - textWidth)
            subInfoStartTextX = startX - textSpace
            rightWidth -= textWidth + textSpace
            const item = { text: textLabel, styleCfg, startX }
            preSubInfo.push(item)
            return preSubInfo
          }, [])
          resolvedSubInfo.forEach((subItem) => {
            const { text, styleCfg, startX } = subItem
            listContainer.addShape('text', {
              attrs: {
                x: startX,
                y: i * itemHeight + offsetY + textFontSize + Math.ceil((itemHeight - textFontSize) / 2),
                text: text,
                fontSize: textFontSize,
                fill: '#333',
                fontFamily:
                  'Avenir,-apple-system,BlinkMacSystemFont,Segoe UI,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol',
                full: e,
                fontWeight: isSelected ? 500 : 100,
                cursor: 'pointer',
                ...styleCfg,
              },
              name: `item-text-${Math.floor(startIndex) + i}`,
            })
          })
        })
      }

      return keyshape
    },
    getAnchorPoints() {
      return [
        [0, 0],
        [1, 0],
      ]
    },
  })
}
