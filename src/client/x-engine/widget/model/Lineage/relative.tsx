import { Graph } from '@antv/x6'
import React, { useEffect, useState, useRef } from 'react'
import { registerGraphConf, switchLayout } from './dataConf'
import { type RelativePropsType } from './interface'
import { debounce } from 'lodash-es'
import {
	addMiniMapTool,
	addHightLightFeatureTool,
	addPoppoverTipTool,
	addCollapseTool,
	turnToAllNodesCollapse,
	turnToAllNodesUnCollapse,
	turnFoldRelativeData,
} from './tools'
import { Tag, Space, Button, Tooltip } from 'antd'
import {
	ZoomInOutlined,
	ZoomOutOutlined,
	RedoOutlined,
	LineOutlined,
	MenuOutlined,
	SettingOutlined,
	QuestionOutlined,
} from '@ant-design/icons'
import { RelativeDataFormat } from './shapes'
import cs from './relative.module.scss'
const ScaleStep = 0.1

// fetch Graph
function fetchGraph(graph: Graph) {
	graph.centerContent()
	graph.zoomToFit({ padding: 30, maxScale: 1 })
}

// Ralative Component
const Relative: React.FC<RelativePropsType> = function (props: RelativePropsType) {
	const {
		data,
		legend,
		popoverTipParams,
		showMiniMap = true,
		typeKey,
		attrsSetting,
		layout,
		layoutConf,
		showArrow,
		miniMapSize,
		defaultType,
		afterInitGraph,
		isUseHightlightTool,
		isUseCollapseTool,
		portItemProps,
		legendSetting = {},
	} = props
	// register node type config
	registerGraphConf()
	const [container, setContainer] = useState<HTMLElement>(null)
	const [minimapContainer, setMinimapContainer] = useState<HTMLElement>(null)
	const [isMenuOpen, setIsMenuOpen] = useState(false)
	const graph = useRef<Graph>(null)
	const [collapseToolInitData, setCollapseToolInitData] = useState<Map<string, unknown>>()
	const [isShowLegend, setIsShowLegend] = useState(legendSetting.defaultLegendShow)

	const { data: RelativeData, legendInfo } = RelativeDataFormat({
		data,
		typeKey,
		attrsSetting,
		showArrow,
		defaultType,
		isUseCollapseTool,
		isUseHightlightTool,
		portItemProps
	})
	// todo: 没有启动isUseCollapseTool参数，使用此工具不进行数据转换
	const foldRelativeData = isUseCollapseTool ? turnFoldRelativeData(RelativeData) : {}

	// layout
	const dagreLayout = switchLayout(layout, layoutConf)

	const model = dagreLayout.layout(RelativeData as never)
	const model2 = dagreLayout.layout(foldRelativeData as never)

	const refContainer = (container: HTMLDivElement) => {
		if (container) {
			setContainer(container)
		}
	}
	const refMiniMapContainer = (container: HTMLDivElement) => {
		if (container) {
			setMinimapContainer(container)
		}
	}

	// 工具箱
	const toolList = [
		{
			icon: ZoomInOutlined,
			tooltip: '放大',
			onClick: () => {
				debounce(() => {
					const zoom = graph.current.zoom()
					const newZoom = zoom + ScaleStep
					graph.current.zoomTo(newZoom)
				}, 300)()
			},
		},
		{
			icon: ZoomOutOutlined,
			tooltip: '缩小',
			onClick: () => {
				debounce(() => {
					const zoom = graph.current.zoom()
					if (zoom < ScaleStep) {
						return
					}
					const newZoom = zoom - ScaleStep
					graph.current.zoomTo(newZoom)
				}, 300)()
			},
		},
		{
			icon: RedoOutlined,
			tooltip: '重置大小',
			onClick: () => {
				debounce(() => {
					fetchGraph(graph.current)
				}, 300)()
			},
		},
		{
			icon: QuestionOutlined,
			tooltip: '图标展示隐藏',
			onClick: () => {
				debounce(() => {
					setIsShowLegend((isShow) => !isShow)
				}, 300)()
			},
		},
		{
			icon: LineOutlined,
			tooltip: '全部节点折叠',
			onClick: () => {
				debounce(() => {
					graph.current.fromJSON(model2)
					fetchGraph(graph.current)
					turnToAllNodesCollapse(graph.current, collapseToolInitData)
				}, 1000)()
			},
			isShow: () => isUseCollapseTool,
		},
		{
			icon: MenuOutlined,
			tooltip: '全部节点展开',
			onClick: () => {
				debounce(() => {
					graph.current.fromJSON(model)
					fetchGraph(graph.current)
					turnToAllNodesUnCollapse(graph.current, collapseToolInitData)
				}, 1000)()
			},
			isShow: () => isUseCollapseTool,
		},
	]

	useEffect(() => {
		if (!container) {
			return
		}
		let graphInstance = new Graph({
			container: container,
			interacting: {
				nodeMovable: true,
				magnetConnectable: false,
				edgeMovable: false,
				edgeLabelMovable: false,
				arrowheadMovable: false,
				vertexMovable: false,
				vertexAddable: false,
				vertexDeletable: false
			},
			autoResize: true,
			panning: true,
			mousewheel: false,
			virtual: false,
			async: false,
			background: {
				color: '#f7f7ff',
			},
			grid: {
				size: 20,
			},

			// 连接点
			connecting: {
				anchor: {
					name: 'midSide',
					args: {
						direction: 'H',
					},
				},
				// connectionPoint: {
				// name: 'anchor',
				// },
			},
		})
		graphInstance.fromJSON(model)

		// add miniMap tool
		showMiniMap && minimapContainer && addMiniMapTool(graphInstance, minimapContainer, miniMapSize)
		// add poppoverTip tool
		if (popoverTipParams && Object.keys(popoverTipParams).length !== 0) {
			addPoppoverTipTool(graphInstance, popoverTipParams)
		}
		// add hightlight tool
		if (isUseHightlightTool) {
			addHightLightFeatureTool(graphInstance)
		}
		// add collapse tool
		if (isUseCollapseTool) {
			addCollapseTool(graphInstance, (initDataMap: Map<string, unknown>) => {
				setCollapseToolInitData(initDataMap)
			})
		}

		fetchGraph(graphInstance)
		graph.current = graphInstance
		if (typeof afterInitGraph === 'function') {
			afterInitGraph(graphInstance)
		}
		return () => {
			graph.current = null
			graphInstance = null
			container.innerHTML = ''
		}
	}, [container, minimapContainer, data])

	return (
		<div
			style={{
				minHeight: '420px',
				height: '100%',
				width: '100%',
				position: 'relative',
				boxSizing: 'border-box',
				display: 'flex',
				flexDirection: 'column',
				transition: 'all 0.5s ease-in-out',
				fontFamily:
					'"Helvetica Neue", helvetica, arial, "Heti Hei", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',
			}}
		>
			<div
				style={{
					flex: 1,
				}}
				ref={refContainer}
			></div>



			{legend && legendInfo && legendInfo.length > 0 && (
				<div
					className={cs.lengendWrap}
				>
					{showMiniMap && (
						<div
							className={cs.relativeMiniMap}

						>
							<div ref={refMiniMapContainer}></div>
						</div>
					)}
					<div
						className={cs.legendContent}
						style={{
							maxHeight: isShowLegend ? '200px' : '0px',
						}}
					>

						<span
							style={{
								flexShrink: 0,
							}}
						>
							图例说明：
						</span>

						<div className="legendCtx">
							{legendInfo.map((legendItem, index) => {
								return (
									<Tag
										color={legendItem.color}
										style={{
											color: '#fff',
											flexShrink: 0,
										}}
										key={index}
									>
										{legendItem.label}
									</Tag>
								)
							})}
						</div>

					</div>
				</div>
			)}

			<div className={cs.relativeToolBar}>
				<Space direction='vertical'>
					<Tooltip title='工具箱'>
						<Button
							shape='circle'
							type='primary'
							icon={<SettingOutlined />}
							onClick={() => {
								setIsMenuOpen((isMenuOpen) => !isMenuOpen)
							}}
						/>
					</Tooltip>
					{isMenuOpen && (
						<>
							{toolList.map((Tool, index) => {
								return (
									<>
										{(Tool.isShow === undefined || Tool.isShow()) && (
											<Tooltip title={Tool.tooltip} key={index}>
												<Button
													shape='circle'
													icon={<Tool.icon />}
													onClick={() => {
														Tool.onClick()
													}}
												/>
											</Tooltip>
										)}
									</>
								)
							})}
						</>
					)}
				</Space>
			</div>
		</div>
	)
}
export default Relative
