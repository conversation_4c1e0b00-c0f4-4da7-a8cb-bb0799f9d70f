import React from 'react'
// 记得把 Lineage 在 src/index.ts 暴露出来
import Lineage from '@model/Lineage'
import { useState } from 'react'
import { Modal } from 'antd'

const joinDiagram = {
  nodes: [
    {
      vid: '0',
      table: 'DEFAULT.KYLIN_SALES',
      Kind: 'FACT',
      alias: '',
      dimensions_columns: [
        {
          name: 'dimensions_columns__1',
          alias: '',
          tableKey: 'pk',
        },
        {
          name: 'dimensions_columns__2',
          alias: '',
        },
      ],
      metrics_columns: [
        {
          name: 'metrics_columns__1',
          alias: '',
        },
        {
          name: 'metrics_columns__2',
          alias: '',
        },
      ],
      common_columns: [
        {
          name: 'metrics_columns__111',
          alias: '',
        },
        {
          name: 'metrics_columns__2111',
          alias: '',
        },
      ],
    },
    {
      vid: '1',
      table: 'DEFAULT.KYLIN_CAL_DT',
      Kind: 'DIM',
      alias: '',
      dimensions_columns: [
        {
          name: 'dimensions_columns__3',
          alias: '',
        },
        {
          name: 'dimensions_columns__4',
          alias: '',
        },
        {
          name: 'dimensions_columns__5',
          alias: '',
        },
        {
          name: 'dimensions_columns__6',
          alias: '',
        },
      ],
      metrics_columns: [
        {
          name: 'metrics_columns__3',
          alias: '',
        },
        {
          name: 'metrics_columns__4',
          alias: '',
        },
        {
          name: 'metrics_columns__5',
          alias: '',
        },
        {
          name: 'metrics_columns__6',
          alias: '',
        },
        {
          name: 'metrics_columns__7',
          alias: '',
        },
        {
          name: 'metrics_columns__8',
          alias: '',
        },
        {
          name: 'metrics_columns__9',
          alias: '',
        },
        {
          name: 'metrics_columns__10',
          alias: '',
        },
      ],
    },
    {
      vid: '2',
      table: 'DEFAULT.KYLIN_CAL_DT_2',
      Kind: 'DIM',
      alias: '',
      dimensions_columns: [
        {
          name: 'dimensions_columns__7',
          alias: '',
        },
        {
          name: 'dimensions_columns__8',
          alias: '',
        },
      ],
      metrics_columns: [
        {
          name: 'metrics_columns__11',
          alias: '',
        },
        {
          name: 'metrics_columns__12',
          alias: '',
        },
      ],
    },
  ],
  edges: [
    {
      eid: '0',
      from: '0',
      to: '1',
      join_type: 'left',
      primary_key: ['KYLIN_CAL_DT.DT'],
      foreign_key: ['KYLIN_SALES.DT'],
    },
    {
      eid: '1',
      from: '1',
      to: '2',
      join_type: 'left',
      primary_key: ['KYLIN_SALES.DT_A'],
      foreign_key: ['KYLIN_CAL_DT.CAL_DT'],
    },
    // {
    //     "eid": 2,
    //     "from": 0,
    //     "to": 3,
    //     "join_type": "left",
    //     "primary_key": [
    //         "KYLIN_CAL_DT_3.CAL_DT",
    //         "KYLIN_CAL_DT_3.DT"
    //     ],
    //     "foreign_key": [
    //         "KYLIN_SALES.PART_DT",
    //         "KYLIN_SALES.DT"
    //     ]
    // }
  ],
}

const d = {
  vertices: [
    {
      id: 'dipeak.vtpcds.vt_dim_catalog_page',
      table: 'vtpcds.vt_dim_catalog_page',
      kind: 'FACT',
      dimensionsColumns: [
        {
          name: 'cp_start_date',
        },
      ],
      metricsColumns: [
        {
          name: 'cp_end_date',
        },
      ],
      primaryKeys: [],
      foreignKeys: ['cp_catalog_page_id', 'cp_department'],
    },
    {
      id: 'dipeak.vtpcds.vt_dim_income_band',
      table: 'vtpcds.vt_dim_income_band',
      kind: 'DIM',
      dimensionsColumns: [],
      metricsColumns: [],
      primaryKeys: ['ib_income_band_sk'],
      foreignKeys: [],
    },
  ],
  edges: [
    {
      id: 'era0857jotao',
      from: 'dipeak.vtpcds.vt_dim_catalog_page',
      to: 'dipeak.vtpcds.vt_dim_income_band',
      joinType: 'LEFT',
      primaryKeys: [
        {
          name: 'vtpcds.vt_dim_income_band.ib_income_band_sk',
        },
      ],
      foreignKeys: [
        {
          name: 'vtpcds.vt_dim_catalog_page.cp_catalog_page_id',
        },
      ],
    },
  ],
}

function formatData(data) {
  const { nodes, edges } = data
  if (!Array.isArray(nodes) || !Array.isArray(edges)) {
    return data
  }
  const nodeJoinKeyMap = {}

  const ansEdges = edges.reduce((pre, e) => {
    const primaryKeys = e.primary_key
    const foreignKeys = e.foreign_key
    const from = e.from
    const to = e.to
    if (nodeJoinKeyMap[to] === undefined) {
      nodeJoinKeyMap[to] = new Set()
    }
    if (nodeJoinKeyMap[from] === undefined) {
      nodeJoinKeyMap[from] = new Set()
    }

    const es =
      primaryKeys.map((fromCol, index) => {
        const source = { id: e.from, column: fromCol }
        const target = { id: e.to, column: foreignKeys[index] }
        nodeJoinKeyMap[from].add(fromCol)
        nodeJoinKeyMap[to].add(foreignKeys[index])
        return {
          source,
          target,
        }
      }) || []
    pre.push(...es)
    return pre
  }, [])

  const ansNodes = nodes.map((n) => {
    // 校验数据
    if (n.vid === undefined || n.vid === null) {
      return n
    }
    // 操作数据
    const dimensionsColumns = (n.dimensions_columns || []).map((col) => {
      return {
        portText: col.name,
        portExtraText: 'pk',
      }
    })
    const metricsColumns = (n.metrics_columns || []).map((col) => {
      return col.name
    })

    const commonColumns = (n.common_columns || []).map((col) => {
      return col.name
    })
    const joinKeyColumns = nodeJoinKeyMap[n.vid] ? [...nodeJoinKeyMap[n.vid]] : []
    return {
      id: n.vid,
      table: n.table,
      type: n.Kind,
      dimensionsColumns,
      metricsColumns,
      joinKeyColumns,
      commonColumns,
    }
  })
  return {
    nodes: ansNodes,
    edges: ansEdges,
  }
}
const initData = formatData(joinDiagram)

function convertSvgToMarkUp(style: Record<string, string>, svgAttrs: Record<string, string>[]) {
  return [
    {
      tagName: 'foreignObject',
      selector: 'svg-wrapper',
      attrs: {
        // Configuring svg location
        width: 20,
        height: 20,
        x: -12,
        y: -10,
      },
      style: style,
      children: [
        {
          tagName: 'svg',
          attrs: {
            viewBox: '0 0 1024 1024',
            version: '1.1',
            xmlns: 'http://www.w3.org/2000/svg',
            class: 'icon',
            width: '100%',
            height: '100%',
          },
          children: svgAttrs.map((attr) => ({
            tagName: 'path',
            attrs: attr,
          })),
        },
      ],
    },
  ]
}
const editIconMarkup = convertSvgToMarkUp(
  {
    cursor: 'pointer',
    zIndex: '2',
  },
  [
    {
      d: 'M257.7 752c2 0 4-0.2 6-0.5L431.9 722c2-0.4 3.9-1.3 5.3-2.8l423.9-423.9c3.9-3.9 3.9-10.2 0-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2c-1.9 11.1 1.5 21.9 9.4 29.8 6.6 6.4 14.9 9.9 23.8 9.9z m67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z',
      'p-id': '2759',
      // fill: '#6493EA',
      fill: 'red',
    },
  ],
)
function formatERData(data) {
  if (!data) {
    return {
      nodes: [],
      edges: [],
    }
  }

  const { vertices: nodes, edges } = data
  if (!Array.isArray(nodes) || !Array.isArray(edges)) {
    return { nodes: [], edges: [] }
  }
  const nodeJoinKeyMap = {}

  const ansEdges = edges.reduce((pre, e) => {
    const primaryKeys = e.primaryKeys || []
    const foreignKeys = e.foreignKeys || []
    const from = e.from
    const to = e.to

    if (nodeJoinKeyMap[to] === undefined) {
      nodeJoinKeyMap[to] = {
        pks: new Set(),
        fks: new Set(),
      }
    }
    if (nodeJoinKeyMap[from] === undefined) {
      nodeJoinKeyMap[from] = {
        pks: new Set(),
        fks: new Set(),
      }
    }

    const es =
      primaryKeys.map((pkItem, index) => {
        const toColStrs = (pkItem.name || '').split('.')
        const fromColStrs = (foreignKeys[index].name || '').split('.')

        const fk = fromColStrs[fromColStrs.length - 1]
        const pk = toColStrs[toColStrs.length - 1]
        nodeJoinKeyMap[to].pks.add(pk)
        nodeJoinKeyMap[from].fks.add(fk)

        const source = { id: e.from, column: fk }
        const target = { id: e.to, column: pk }
        return {
          source,
          target,
        }
      }) || []
    pre.push(...es)
    return pre
  }, [])

  const ansNodes = nodes.map((n) => {
    // 校验数据
    if (n.id === undefined || n.id === null) {
      return n
    }
    // primaryKey
    const primaryKeys = nodeJoinKeyMap[n.id] ? nodeJoinKeyMap[n.id].pks : new Set()

    // foreignKeys
    const foreignKeys = nodeJoinKeyMap[n.id] ? nodeJoinKeyMap[n.id].fks : new Set()

    const primaryKeysLength = primaryKeys.length

    const commondColumnsSetArr = [...new Set([...primaryKeys, ...foreignKeys])]

    // 操作数据
    const dimensionsColumns = (n.dimensionsColumns || []).map((col) => {
      const colNameStrs = (col.name || '').split('.')
      const colName = colNameStrs[colNameStrs.length - 1]

      const isPk = primaryKeys.has(colName)
      const isFk = foreignKeys.has(colName)
      const portExtraText = isPk ? 'pk' : isFk ? 'fk' : ''
      return { portText: colName, portExtraText }
    })

    const metricsColumns = (n.metricsColumns || []).map((col) => {
      const colNameStrs = (col.name || '').split('.')
      const colName = colNameStrs[colNameStrs.length - 1]

      const isPk = primaryKeys.has(colName)
      const isFk = foreignKeys.has(colName)
      const portExtraText = isPk ? 'pk' : isFk ? 'fk' : ''
      return { portText: colName, portExtraText }
    })

    const commonColumns = commondColumnsSetArr
      .filter((col) => {
        const isDimensions = dimensionsColumns.find((dim) => dim.portText === col)
        const isMetrics = metricsColumns.find((met) => met.portText === col)
        return !isDimensions && !isMetrics
      })
      .map((col, index) => ({
        portText: { portText: col || '', portExtraText: index < primaryKeysLength ? 'pk' : 'fk' },
      }))

    const _pks = Array.from(primaryKeys)
    const _fks = Array.from(foreignKeys)
    return {
      id: n.id,
      table: n.table,
      type: n.kind,
      dimensionsColumns,
      metricsColumns,
      commonColumns,
      pks: _pks,
      fks: _fks?.filter((k) => !_pks.includes(k)),
    }
  })
  return {
    nodes: ansNodes,
    edges: ansEdges,
  }
}
const erdata = {
  vertices: [
    {
      id: 'dipeak.vtpcds.vt_dwd_catalog_sales',
      table: 'vtpcds.vt_dwd_catalog_sales',
      kind: 'FACT',
      dummy: false,
      dimensionsColumns: [
        {
          name: 'vtpcds.vt_dwd_catalog_sales.dt',
          alias: null,
        },
      ],
      metricsColumns: [
        {
          name: 'vtpcds.vt_dwd_catalog_sales.cs_wholesale_cost',
          alias: null,
        },
        {
          name: 'vtpcds.vt_dwd_catalog_sales.cs_list_price',
          alias: null,
        },
        {
          name: 'vtpcds.vt_dwd_catalog_sales.cs_sales_price',
          alias: null,
        },
        {
          name: 'vtpcds.vt_dwd_catalog_sales.cs_ext_discount_amt',
          alias: null,
        },
        {
          name: 'vtpcds.vt_dwd_catalog_sales.cs_ext_sales_price',
          alias: null,
        },
        {
          name: 'vtpcds.vt_dwd_catalog_sales.cs_item_sk',
          alias: null,
        },
      ],
    },
    {
      id: 'dipeak.vtpcds.vt_dim_item',
      table: 'vtpcds.vt_dim_item',
      kind: 'DIM',
      dummy: false,
      dimensionsColumns: [
        {
          name: 'vtpcds.vt_dim_item.i_item_sk',
          alias: null,
        },
        {
          name: 'vtpcds.vt_dim_item.i_brand',
          alias: null,
        },
        {
          name: 'vtpcds.vt_dim_item.i_class',
          alias: null,
        },
        {
          name: 'vtpcds.vt_dim_item.i_category',
          alias: null,
        },
        {
          name: 'vtpcds.vt_dim_item.i_manufact',
          alias: null,
        },
      ],
      metricsColumns: [],
    },
  ],
  edges: [
    {
      id: 'e3n6d068l4co',
      from: 'dipeak.vtpcds.vt_dwd_catalog_sales',
      to: 'dipeak.vtpcds.vt_dim_item',
      joinType: 'LEFT',
      primaryKeys: [
        {
          name: 'vtpcds.vt_dim_item.i_item_sk',
          alias: null,
        },
        {
          name: 'vtpcds.vt_dim_item.dt',
          alias: null,
        },
      ],
      foreignKeys: [
        {
          name: 'vtpcds.vt_dwd_catalog_sales.cs_item_sk',
          alias: null,
        },
        {
          name: 'vtpcds.vt_dwd_catalog_sales.dt',
          alias: null,
        },
      ],
      properties: {},
    },
  ],
}
export default function Page() {
  const [modalOpen, setModalOpen] = useState(false)
  const [data, setData] = useState(initData)
  return (
    <>
      <Modal
        title="模型"
        open={modalOpen}
        onCancel={() => {
          setModalOpen(false)
        }}
      >
        展开模型
      </Modal>
      <div
        style={{
          width: '100%',
          height: '520px',
        }}
      >
        <Lineage
          miniMapSize={{ height: 100, width: 120 }}
          data={formatERData(erdata)}
          typeKey="type"
          layout="dagre"
          layoutConf={{
            type: 'dagre',
            rankdir: 'LR',
            align: 'UL',
            begin: [0, 0],
            nodesepFunc: (node: Record<string, any>) => {
              return node.height * 0.6
            },
            ranksepFunc: (node: Record<string, any>) => {
              return node.width * 0.4
            },
          }}
          isUseHightlightTool={false}
          isUseCollapseTool={false}
          showArrow={false}
          legend={true}
          legendSetting={{
            defaultLegendShow: true,
          }}
          portItemProps={{
            sort: 'asc',
          }}
          afterInitGraph={(graph) => {
            graph.on('node:mouseenter', ({ node }) => {
              node.addTools({
                name: 'button',
                args: {
                  markup: editIconMarkup,
                  x: '100%',
                  // y: '100%',
                  offset: { x: -13, y: 12 },
                  // offset: { x: -10, y: -50 },
                  onClick({ cell }: { cell: Node }) {
                    setModalOpen(true)
                    // console.log('cell_____', cell)
                    setData((data) => ({
                      nodes: data.nodes,
                      edges: [
                        ...data.edges,
                        {
                          source: { id: '1', column: 'metrics_columns__3' },
                          target: { id: '2', column: 'metrics_columns__11' },
                        },
                      ],
                    }))
                    console.log('data___________', data)
                    //获取到当前这个节点的ID,也是tableID
                    // selectedNodeId.current = cell.id
                    // setNodeModal(true)
                  },
                },
              })
            })
            graph.on('node:mouseleave', ({ node }) => {
              node.removeTools()
            })
          }}
          attrsSetting={{
            bodyRect: {
              width: '380',
            },
            titleRect: {
              width: '380',
            },
          }}
        />
      </div>
    </>
  )
}
