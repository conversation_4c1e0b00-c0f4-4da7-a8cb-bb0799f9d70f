import { Graph, type Node } from '@antv/x6'
import { MiniMap } from '@antv/x6-plugin-minimap'
import { type EdgeType, type RelativeDataType } from './interface'
import { ConstantInfo } from './shapes'
import { moreSVG } from './svg'
import { set, throttle, omit, cloneDeep } from 'lodash-es'
// import * as _ from 'lodash-es'
import { type NodeRegisterMapType } from './dataConf'

/**
 * add miniMap tool to graph
 * @param graph
 * @param el
 * @param miniMapSize - miniMap size setting
 */
export function addMiniMapTool(graph: Graph, el: HTMLElement, miniMapSize?: { width: number; height: number }) {
	el.innerHTML = ''
	graph.use(
		new MiniMap({
			container: el,
			width: miniMapSize?.width ?? 120,
			height: miniMapSize?.height ?? 100,
			// todo: 这个设置，会爆Typeerror错误
			graphOptions: {
				async: true,
			},
		}),
	)
}

/**
 * add poppoverTip tool to graph
 * @param graph
 * @param popoverTipParams - {[apiKey]: [apiKeyValue displayName in poopoverTip ]}
 */
export function addPoppoverTipTool(graph: Graph, popoverTipParams: Record<string, string>) {
	graph.on('node:click', ({ e, x, y, node }) => {
		e.stopPropagation()
		const attrs = node.getAttrs()
		if (!attrs) return
		const { name: nameAttr } = attrs
		if (!nameAttr) return

		const { needOpen, isOpen } = nameAttr
		if (!needOpen || isOpen) return
		const { data: nodeData } = node.store
		if (!data) return

		graph.addNode({
			shape: 'popoverTip',
			x,
			y,
			data: {
				detail: Object.entries(popoverTipParams).map(([key, rename]) => {
					return [rename, nodeData[key]]
				}),
				parent: node,
			},
		})
		node.setAttrs({
			name: { isOpen: true },
		})
	})
}

type NodeType = {
	cell: string
	port?: string
}
// 在有限集中，控制好其他集合即可保持状态；用来比对差别进行设值，减少不必要的渲染
const nodeGreyColor = '#b2b2b2'
const edgeGreyColor = '#b2b2b2'
const hightlightStyleSetting = {
	fillNodeColor: '#ac8af8',
	fillEdgeColor: '#6f00ff',
	strokeWidth: 1,
	strokeColor: '#ac8af8',
}

let lastGreyNodes: Set<string> = new Set()
let lastHightlightNodes: NodeType[] = []
let lastHighlightEdges: string[] = []
let foldNodeIds = new Set()

/**
 * find node upstream and downstream relationship
 * @param edges
 * @param sourceNodes
 * @param targetNodes
 * @returns
 */
function findRelatiionshipTraverse(
	edges: EdgeType[],
	sourceNodes: { cell: string; port?: string }[] | [],
	targetNodes: { cell: string; port?: string }[] | [],
): { nodeIds: { cell: string; port?: string }[] | []; edgeIds: string[] | []; highlightNodes: Set<string> } {
	const nodeIds: { cell: string; port?: string }[] = []
	const edgeIds: string[] = []
	const highlightNodes: Set<string> = new Set()
	if (sourceNodes.length === 0 || targetNodes.length === 0 || !edges?.length) {
		return {
			nodeIds,
			edgeIds,
			highlightNodes,
		}
	}
	nodeIds.push(sourceNodes[0])
	highlightNodes.add(sourceNodes[0].cell)
	const edgeLen = edges.length
	while (sourceNodes.length !== 0 || targetNodes.length !== 0) {
		const source = sourceNodes.shift()
		const target = targetNodes.shift()

		for (let i = 0; i < edgeLen; ++i) {
			const edge = edges[i]
			if (!edge) {
				continue
			}
			const { source: sou, target: tar } = edge
			if (source && sou.cell === source.cell && (!source.port || source.port === sou.port)) {
				sourceNodes.push(tar)
				const cur = { cell: tar.cell as string }
				if (Object.hasOwnProperty.call(tar, 'port')) {
					cur.port = tar.port
				}
				delete edges[i]
				nodeIds.push(cur)
				edgeIds.push(edge.id)
				highlightNodes.add(cur.cell)
				continue
			}
			if (target && tar.cell === target.cell && (!target.port || target.port === tar.port)) {
				targetNodes.push(sou)
				const cur = { cell: sou.cell }
				if (Object.hasOwnProperty.call(sou, 'port')) {
					cur.port = sou.port
				}
				delete edges[i]
				nodeIds.push(cur)
				edgeIds.push(edge.id)
				highlightNodes.add(cur.cell)
				continue
			}
		}
	}

	return {
		nodeIds,
		edgeIds,
		highlightNodes,
	}
}

/**
 * restore nodes to its original color
 * @param graph
 */
function resetColor(graph: Graph) {
	graph.startBatch('resetColor')
	lastHighlightEdges.forEach((e) => {
		const edge = graph.getCellById(e)
		if (!edge) {
			return
		}
		edge.setAttrs(
			{
				line: { stroke: edgeGreyColor, strokeWidth: 2 },
			},
			{ deep: true },
		)
		edge.removeZIndex()
	})
	lastHightlightNodes.forEach((n) => {
		const { cell, port } = n
		const node = graph.getCellById(cell) as Node
		if (node && port && node.hasPort(port)) {
			node.setPortProp(port, ['attrs', 'portBody', 'fill'], '#fff')
		}
	})
	lastGreyNodes.forEach((n) => {
		const node = graph.getCellById(n) as Node
		if (!node) {
			return
		}
		const nodeColor = node.getData()?.nodeColor
		node.setAttrs(
			{
				titleRect: {
					fill: nodeColor,
				},
				bodyRect: {
					stroke: nodeColor,
				},
				titleMask: {
					fill: nodeColor,
				},
			},
			{
				deep: true,
			},
		)
	})
	graph.stopBatch('resetColor')
	lastGreyNodes = new Set()
	lastHighlightEdges = []
	lastHightlightNodes = []
}
/**
 * highlight nodes when click node
 * @param params
 * @param graph
 * @returns
 */
function handleHighlightClick(params: unknown, graph: Graph) {
	graph.startBatch('handleHighlightClick')
	const { e, node } = params
	const { id } = node
	const startNode: NodeType = { cell: id }
	let isClickPortFlag = true

	// 判断是否点到字段级别下的表头
	if (node?.port?.ports?.length !== 0) {
		const nodeData = node.store.data
		if (!Object.hasOwnProperty.call(nodeData, 'name')) {
			let { target: targetEl } = e
			if (targetEl.tagName === 'rect') {
				while (targetEl.tagName !== 'text') {
					targetEl = targetEl.nextSibling
					if (!targetEl) {
						isClickPortFlag = false
						break
					}
				}
			}
			const portText = targetEl.textContent

			if (portText === id || !portText || !isClickPortFlag) {
				return
			}
			startNode.port = portText
		}
	}
	// 处理表级别下点表、字段级别下点字段
	const initEdgesData = graph.getEdges() as EdgeType[]

	const nodeRelationShip = findRelatiionshipTraverse(initEdgesData, [startNode], [startNode])

	const { nodeIds, edgeIds, highlightNodes } = nodeRelationShip
	const allNodes = graph.getNodes()
	const greyNodes = allNodes
		.filter((node) => {
			return !highlightNodes.has(node.id)
		})
		.map((node) => node.id)

	const toSetGreyNodes = []
	const toSetHighlightNodes = []
	const toSetHighlightNodePorts = []
	const toSetInitNodePorts = []

	const toSetGreyEdges = []
	const toSetHighlightEdges = []

	// 点击节点，将全集分为greyNodes和nodeIds，
	const greyNodesLen = greyNodes?.length
	const nodeIdsLen = nodeIds.length
	const lastHighlightNodesLen = lastHightlightNodes.length
	const edgeIdsLen = edgeIds.length
	const lastHighlightEdgesLen = lastHighlightEdges.length

	for (let i = 0; i < greyNodesLen; ++i) {
		const cell = greyNodes[i]
		// 比对找出变化去设置值
		if (lastGreyNodes.has(cell)) {
			continue
		}
		let isLastHighlightHasGreyNode = false
		for (let k = 0; k < lastHighlightNodesLen; ++k) {
			const lastHighlightNode = lastHightlightNodes[k]
			if (!lastHighlightNode) {
				continue
			}
			const { cell: lastCell, port: lastPort } = lastHighlightNode
			if (lastCell === cell) {
				isLastHighlightHasGreyNode = true
				toSetGreyNodes.push(lastHighlightNode)
				if (lastPort) {
					toSetInitNodePorts.push(lastHighlightNode)
				}
				delete lastHightlightNodes[k]
				continue
			}
		}
		if (!isLastHighlightHasGreyNode) {
			toSetGreyNodes.push(cell)
		}
	}

	for (let i = 0; i < nodeIdsLen; ++i) {
		const nodeItem = nodeIds[i]
		const { cell, port } = nodeItem
		// 比对找出变化去设置值
		if (lastGreyNodes.has(cell)) {
			toSetHighlightNodes.push(nodeItem)
			continue
		}
		let isHighLightFlag = false
		let isNodeItemToSetHighlightNodePorts = true
		for (let k = 0; k < lastHighlightNodesLen; ++k) {
			const lastHighlightNode = lastHightlightNodes[k]
			if (!lastHighlightNode) {
				continue
			}
			const { port: lastPort, cell: lastCell } = lastHighlightNode
			if (lastCell === cell) {
				isHighLightFlag = true
				if (port === lastPort) {
					delete lastHightlightNodes[k]
					isNodeItemToSetHighlightNodePorts = false
					continue
				}
				if (port && lastPort === undefined) {
					delete lastHightlightNodes[k]
					isNodeItemToSetHighlightNodePorts = false
					toSetHighlightNodePorts.push(nodeItem)
					continue
				}
				if (lastPort && port === undefined) {
					delete lastHightlightNodes[k]
					toSetInitNodePorts.push(lastHighlightNode)
					continue
				}
				const lastHighlightNodeIsInNodeIds = nodeIds.find(({ cell, port }) => {
					return cell === lastCell && port === lastPort
				})
				if (!lastHighlightNodeIsInNodeIds) {
					toSetInitNodePorts.push(lastHighlightNode)
				}
			}
		}
		if (!isHighLightFlag) {
			toSetHighlightNodes.push(nodeItem)
		}
		if (isNodeItemToSetHighlightNodePorts) {
			toSetHighlightNodePorts.push(nodeItem)
		}
	}

	if (edgeIdsLen === 0) {
		for (let j = 0; j < lastHighlightEdgesLen; ++j) {
			const lastEdge = lastHighlightEdges[j]
			toSetGreyEdges.push(lastEdge)
		}
	} else {
		for (let i = 0; i < edgeIdsLen; ++i) {
			let hasFlag = false
			const edgeItem = edgeIds[i]
			for (let j = 0; j < lastHighlightEdgesLen; ++j) {
				const lastEdge = lastHighlightEdges[j]
				if (!lastEdge) {
					continue
				}
				if (edgeItem === lastEdge) {
					delete lastHighlightEdges[j]
					hasFlag = true
				}
			}
			if (!hasFlag) {
				toSetHighlightEdges.push(edgeItem)
			}
		}
		for (let i = 0; i < lastHighlightEdgesLen; ++i) {
			const lastEdge = lastHighlightEdges[i]
			if (lastEdge) {
				toSetGreyEdges.push(lastEdge)
			}
		}
	}

	// 设置颜色
	toSetGreyNodes.forEach((n) => {
		const cell = typeof n === 'string' ? n : n.cell
		const port = (n as NodeType).port
		const node = graph.getCellById(cell as string) as Node
		if (node) {
			node.setAttrs(
				{
					titleRect: {
						fill: nodeGreyColor,
					},
					bodyRect: {
						stroke: nodeGreyColor,
					},
					titleMask: {
						fill: nodeGreyColor,
					},
				},
				{
					deep: true,
				},
			)
			if (port && node.hasPort(port)) {
				node.setPortProp(port, ['attrs', 'portBody', 'fill'], '#fff')
			}
		}
	})
	toSetHighlightNodes.forEach((n) => {
		const { cell, port } = n
		const node = graph.getCellById(cell) as Node
		if (node) {
			const nodeColor = node.getData()?.nodeColor
			node.setAttrs(
				{
					titleRect: {
						fill: nodeColor,
					},
					bodyRect: {
						stroke: nodeColor,
					},
					titleMask: {
						fill: nodeColor,
					},
				},
				{
					deep: true,
				},
			)
			if (port && node.hasPort(port)) {
				node.setPortProp(port, ['attrs', 'portBody', 'fill'], hightlightStyleSetting.fillNodeColor)
			}
		}
	})
	toSetInitNodePorts.forEach((n) => {
		const { cell, port } = n
		const node = graph.getCellById(cell) as Node
		if (node && port && node.hasPort(port)) {
			node.setPortProp(port, ['attrs', 'portBody', 'fill'], '#fff')
		}
	})
	toSetHighlightNodePorts.forEach((n) => {
		const { cell, port } = n
		const node = graph.getCellById(cell) as Node
		if (node && port && node.hasPort(port)) {
			node.setPortProp(port, ['attrs', 'portBody', 'fill'], hightlightStyleSetting.fillNodeColor)
		}
	})

	toSetGreyEdges.forEach((e) => {
		const edge = graph.getCellById(e)
		if (edge) {
			edge.setAttrs(
				{
					line: {
						stroke: edgeGreyColor,
						strokeWidth: 2,
					},
				},
				{
					deep: true,
				},
			)
			edge.removeZIndex()
		}
	})

	toSetHighlightEdges.forEach((e) => {
		const edge = graph.getCellById(e)
		if (edge) {
			edge.setAttrs(
				{
					line: {
						stroke: hightlightStyleSetting.fillEdgeColor,
						strokeWidth: 2,
					},
				},
				{
					deep: true,
				},
			)
			edge.setZIndex(3)
		}
	})
	graph.stopBatch('handleHighlightClick')
	lastGreyNodes = new Set(greyNodes)
	lastHightlightNodes = nodeIds
	lastHighlightEdges = edgeIds
}
const resetColorThrotteFn = throttle(resetColor, 1000)
const handleHighlightClickThrotteFn = throttle(handleHighlightClick, 1000)

/**
 * init hightlight
 */
function initHightLight() {
	lastGreyNodes = new Set()
	lastHighlightEdges = []
	lastHightlightNodes = []
}

/**
 * add hightlight feature tool to graph
 * @param graph
 * @param relativeData
 */
export function addHightLightFeatureTool(graph: Graph) {
	// save node original color
	const nodes = graph.getNodes()
	initHightLight()
	nodes.forEach((node) => {
		const attrs = node.getAttrs()
		const nodeColor = attrs.titleRect?.fill
		node.setData({ nodeColor }, { silent: true, deep: false })
	})
	// handle click event
	graph.on('node:click', (params) => {
		handleHighlightClickThrotteFn(params, graph)
	})

	graph.on('blank:click', () => {
		resetColorThrotteFn(graph)
	})
}

/**
 * add collapse tool to graph
 * @param graph
 */
const foldThrotteFn = throttle(fold, 1000)
const unfoldThrotteFn = throttle(unfold, 1000)

export function addCollapseTool(graph: Graph, onInitDataFinish?: (initDataMap: Map<string, unknown>) => unknown) {
	const initDataMap = new Map()
	initCollapseNode(graph, initDataMap)
	onInitDataFinish(initDataMap)
	graph.on('node:collapse', ({ node, e }: { node: Node; e: Event }) => {
		e.stopPropagation()
		const isCollapse = node.getData()?.isCollapse as boolean
		if (isCollapse) {
			unfoldThrotteFn(node, graph)
		} else {
			foldThrotteFn(node, graph)
		}
	})
}

/**
 *
 * @param relativeData
 * @returns 转变为折叠数据
 */

export function turnFoldRelativeData(relativeData: RelativeDataType): RelativeDataType {
	const newNodes = []
	const newEdges = []
	const { nodes = [], edges = [] } = relativeData
	const isEdgeExist = new Set()
	for (let i = 0; i < edges.length; ++i) {
		const curEdge = edges[i]
		const { source, target } = curEdge
		const id = [source.cell, target.cell].join('->')
		if (isEdgeExist.has(id)) {
			continue
		}
		isEdgeExist.add(id)
		newEdges.push(curEdge)
	}
	for (let i = 0; i < nodes.length; ++i) {
		const n = cloneDeep(omit(nodes[i], ['ports']))
		const newNode = Object.assign(n, {
			attrs: Object.assign(n?.attrs, {
				bodyRect: Object.assign(n?.attrs?.bodyRect, { height: ConstantInfo.TITLE_LINEHEIGHT }),
			}),
		})
		newNodes.push(newNode)
	}
	return {
		edges: newEdges,
		nodes: newNodes,
	}
}

function resolveEdgeSourceAndTargetData(data, options = {}): NodeType | Record<string, unknown> {
	if (!data) return {}
	const resolveKeys = ['cell', 'port']
	const ans = {}
	resolveKeys.forEach((key) => {
		const value = Object.hasOwnProperty.call(options, key)
			? options[key]
			: Object.hasOwnProperty.call(data, key)
				? data[key]
				: null
		if (value !== null) {
			ans[key] = value
		}
	})
	return ans
}

/**
 * init collapse tool
 * @param graph
 */
function initCollapseNode(graph: Graph, initDataMap?: Map<string, unknown>) {
	const nodes = graph.getNodes() || []
	foldNodeIds = new Set()
	nodes.forEach((node) => {
		if (!node) {
			return
		}
		const id = node.id
		const attrs = node.getAttrs()
		const height = attrs.bodyRect?.height
		if (!height) {
			return
		}
		const ports = node.getPortsByGroup('list')
		const edges = graph.getConnectedEdges(node)
		const initEdgesData: {
			shape: string
			source: NodeType
			target: NodeType
		}[] = []
		edges.forEach((e) => {
			const source = e.getSource()
			const target = e.getTarget()
			initEdgesData.push({
				shape: 'defaultEdge',
				source: resolveEdgeSourceAndTargetData(source),
				target: resolveEdgeSourceAndTargetData(target),
			})
		})
		node.setData(
			{ isCollapse: false, unfoldHeight: height, portsData: ports, unfoldEdgesData: initEdgesData },
			{ silent: true, deep: false },
		)
		if (initDataMap && id !== undefined) {
			initDataMap.set(id, {
				unfoldHeight: height,
				portsData: ports,
				unfoldEdgesData: initEdgesData,
			})
		}
	})
}

export function turnToAllNodesCollapse(graph: Graph, initDataMap?: Map<string, unknown>) {
	const nodes = graph.getNodes() || []
	nodes.forEach((node) => {
		const id = node.id
		if (id == undefined) {
			return
		}
		foldNodeIds.add(id)
		const initData = (initDataMap && initDataMap.get(id)) || {}
		node.setData(Object.assign({ isCollapse: true }, initData), { silent: true, deep: false })
		const attrs = node.getAttrs()
		const nodeColor = attrs.titleRect?.fill
		node.setData({ nodeColor }, { silent: true, deep: false })
	})
	lastGreyNodes = new Set()
	lastHightlightNodes = []
	lastHighlightEdges = []
}

export function turnToAllNodesUnCollapse(graph: Graph, initDataMap?: Map<string, unknown>) {
	const nodes = graph.getNodes() || []
	nodes.forEach((node) => {
		const id = node.id
		if (id === undefined) {
			return
		}
		const initData = (initDataMap && initDataMap.get(id)) || {}
		node.setData(Object.assign({ isCollapse: false }, initData), { silent: true, deep: false })
		const attrs = node.getAttrs()
		const nodeColor = attrs.titleRect?.fill
		node.setData({ nodeColor }, { silent: true, deep: false })
	})
	foldNodeIds = new Set()
	lastGreyNodes = new Set()
	lastHightlightNodes = []
	lastHighlightEdges = []
}

/**
 * handle collapse fold operation
 * @param node
 * @param graph
 * @returns
 */
export function fold(node: Node, graph: Graph) {
	graph.startBatch('fold')
	const data = node.getData()
	const isCollapse = data?.isCollapse
	if (isCollapse) {
		return
	}
	const ports = data?.portsData
	const height = data?.unfoldHeight as number
	const edgesData = data?.unfoldEdgesData
	const edgesLen = edgesData.length
	if (!ports || ports.length === 0) return
	const cnt = ports.length
	const h = height - ConstantInfo.PORT_LINEHEIGHT * cnt - ConstantInfo.BODY_STROKE_WIDTH * 2
	const newEdges = []
	const alreadyAddNodeIds = new Set()
	for (let i = 0; i < edgesLen; ++i) {
		const edge = edgesData[i]
		const { source, target } = edge
		const otherSideEdgeNode = source?.cell === node?.id ? target : source
		if (foldNodeIds.has(otherSideEdgeNode?.cell) && alreadyAddNodeIds.has(otherSideEdgeNode?.cell)) {
			continue
		}
		alreadyAddNodeIds.add(otherSideEdgeNode.cell)
		newEdges.push(edge)
	}
	resetColor(graph)
	node.setAttrs(
		{
			bodyRect: { height: h },
		},
		{
			deep: true,
		},
	)

	node.setData({ isCollapse: true }, { silent: true, deep: false })
	graph.removeConnectedEdges(node)
	node.removePorts(null)
	graph.addEdges(newEdges)
	graph.stopBatch('fold')
	foldNodeIds.add(node.id)
}
/**
 * handle collapse unfold operation
 * @param node
 * @param graph
 * @returns
 */
export function unfold(node: Node, graph: Graph) {
	graph.startBatch('unfold')
	const data = node.getData()
	const isCollapse = data?.isCollapse
	if (!isCollapse) {
		return
	}
	const ports = data?.portsData
	const edgesData = data?.unfoldEdgesData
	const height = data?.unfoldHeight as number
	if (!ports || ports.length === 0) return
	resetColor(graph)
	node.setAttrs(
		{
			bodyRect: { height: height },
		},
		{
			deep: true,
		},
	)
	node.setData({ isCollapse: false }, { silent: true, deep: false })

	graph.removeConnectedEdges(node)
	node.addPorts(ports)
	graph.addEdges(edgesData)
	graph.stopBatch('unfold')
	foldNodeIds.delete(node.id)
}

export function handleAddHighlightNode(node: Record<string, any>) {
	set(node, 'attrs.bodyRect.cursor', 'pointer')
	set(node, 'attrs.titleText.cursor', 'pointer')
	const ports = node.ports
	if (Array.isArray(ports) && ports.length !== 0) {
		ports.forEach((_, i) => {
			set(node, `ports[${i}].attrs.portBody.cursor`, 'pointer')
			set(node, `ports[${i}].attrs.portRect.cursor`, 'pointer')
			set(node, `ports[${i}].attrs.portText.cursor`, 'pointer')
		})
	}
}
/**
 *
 * @param inheritShape
 * @param node
 */
export function handleAddCollapseNode(inheritShape: NodeRegisterMapType, node: Record<string, any>) {
	if (Number.isNaN(node.width) || Number.isNaN(node.height)) return
	const collapseAddWidth = 40
	const newRegisterType = `${inheritShape}AddCollapse`
	switch (inheritShape) {
		case 'aPinkSingleRectAndMulPortsShape':
		case 'aGreenSingleRectAndMulPortsShape':
		case 'aOrangeSingleRectAndMulPortsShape':
		case 'aBrownSingleRectAndMulPortsShape':
		case 'aBlueSingleRectAndMulPortsShape':
		case 'aPurpleSingleRectAndMulPortsShape':
			Graph.registerNode(
				newRegisterType,
				{
					inherit: inheritShape,
					markup: [
						{
							tagName: 'rect',
							selector: 'bodyRect',
						},
						{
							tagName: 'rect',
							selector: 'titleRect',
						},
						{
							tagName: 'rect',
							selector: 'titleMask',
						},
						{
							tagName: 'text',
							selector: 'titleText',
						},
						{
							tagName: 'g',
							selector: 'collapseGroup',
							children: [
								{
									selector: 'collapseWrapRect',
									tagName: 'rect',
								},
								{
									selector: 'collapseSVG',
									tagName: 'path',
									attrs: {
										d: moreSVG,
									},
								},
							],
						},
					],
					attrs: {
						collapseGroup: {
							ref: 'titleRect',
							refX: 10,
							xAlign: 'middle',
							yAlign: 'middle',
							refY: 0.5,
							cursor: 'pointer',
							event: 'node:collapse',
						},
						collapseWrapRect: {
							ref: 'titleRect',
							width: 16,
							height: 16,
							stroke: 'none',
							fillOpacity: 0,
						},
						collapseSVG: {
							fill: '#fff',
							ref: 'titleRect',
						},
					},
				},
				true,
			)
	}
	node.shape = newRegisterType
	const w = node.width + collapseAddWidth
	const ports = node.ports
	set(node, 'width', w)
	set(node, 'size', {
		width: w,
		height: node.height,
	})
	set(node, 'attrs.bodyRect.width', w)
	set(node, 'attrs.titleRect.width', w)
	if (Array.isArray(ports) && ports.length !== 0) {
		ports.forEach((_, i) => {
			set(node, `ports[${i}].attrs.portBody.width`, w - ConstantInfo.BODY_STROKE_WIDTH * 2)
		})
	}
}
