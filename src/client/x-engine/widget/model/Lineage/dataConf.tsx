import { Graph, Path, type Node } from '@antv/x6'
import { PopoverTipComponent, singleRectShape, singleRectAndMulPortsShape, ConstantInfo } from './shapes'
import { register } from '@antv/x6-react-shape'

import {
	DagreLayout,
	CircularLayout,
	GridLayout,
	DagreLayoutOptions,
	GridLayoutOptions,
	CircularLayoutOptions,
} from '@antv/layout'
import { LayoutConfType } from './interface'

type AxisType = { x: number; y: number }
type OrientationType = {
	angle: number
	xDirection: boolean
	yDirection: boolean
}
type RectType = { width: number; height: number; x: number; y: number }
type CubicParamsType = [AxisType | null, AxisType | null, AxisType | null, AxisType | null]
const LEGEND_LINE_HEIGHT = 32

/**
 * type - Graph.registerNode - node
 */
export const nodeRegisterMap = {
	aPinkSingleRectAndMulPortsShape: singleRectAndMulPortsShape({
		bodyRect: {
			stroke: '#d63864',
		},
		titleRect: {
			fill: '#d63864',
		},
		titleMask: {
			fill: '#d63864',
		},
	}),
	aGreenSingleRectAndMulPortsShape: singleRectAndMulPortsShape({
		bodyRect: {
			stroke: '#3b8771',
		},
		titleRect: {
			fill: '#3b8771',
		},
		titleMask: {
			fill: '#3b8771',
		},
	}),
	aOrangeSingleRectAndMulPortsShape: singleRectAndMulPortsShape({
		bodyRect: {
			stroke: '#ec9235',
		},
		titleRect: {
			fill: '#ec9235',
		},
		titleMask: {
			fill: '#ec9235',
		},
	}),
	aBrownSingleRectAndMulPortsShape: singleRectAndMulPortsShape({
		bodyRect: {
			stroke: '#74574a',
		},
		titleRect: {
			fill: '#74574a',
		},
		titleMask: {
			fill: '#74574a',
		},
	}),
	aBlueSingleRectAndMulPortsShape: singleRectAndMulPortsShape({
		bodyRect: {
			stroke: '#4359f5',
		},
		titleRect: {
			fill: '#4359f5',
		},
		titleMask: {
			fill: '#4359f5',
		},
	}),
	aPurpleSingleRectAndMulPortsShape: singleRectAndMulPortsShape({
		bodyRect: {
			stroke: '#9c1ef6',
		},
		titleRect: {
			fill: '#9c1ef6',
		},
		titleMask: {
			fill: '#9c1ef6',
		},
	}),
	aGreySingleRectAndMulPortsShape: singleRectAndMulPortsShape({
		bodyRect: {
			stroke: '#424242',
		},
		titleRect: {
			fill: '#424242',
		},
		titleMask: {
			fill: '#424242',
		},
	}),
	aBlueGreySingleRectAndMulPortsShape: singleRectAndMulPortsShape({
		bodyRect: {
			stroke: '#78909c',
		},
		titleRect: {
			fill: '#78909c',
		},
		titleMask: {
			fill: '#78909c',
		},
	}),

	defaultLegend: {
		inherit: 'rect',
		markup: [
			{
				tagName: 'rect',
				selector: 'body',
			},
			{
				tagName: 'text',
				selector: 'label',
			},
		],
		attrs: {
			rect: {
				strokeWidth: 0,
				stroke: '#5F95FF',
				fill: '#5F95FF',
			},
			label: {
				fontWeight: 'bold',
				fill: '#ffffff',
				fontSize: 0,
			},
		},
		ports: {
			groups: {
				list: {
					markup: [
						{
							tagName: 'rect',
							selector: 'item',
						},
						{
							tagName: 'text',
							selector: 'itemText',
						},
						{
							tagName: 'rect',
							selector: 'itemIcon',
						},
					],
					attrs: {
						item: {
							width: 150,
							height: LEGEND_LINE_HEIGHT,
							strokeWidth: 1,
							stroke: '#aaa',
							fill: '#fff',
							magnet: true,
						},
						itemText: {
							ref: 'item',
							refX: 10,
							refY: 0.5,
							fontSize: 16,
							fill: '#232323',
							yAlign: 'middle',
						},
						itemIcon: {
							width: 12,
							height: 12,
							ref: 'item',
							refDx: -24,
							refY: 0.5,
							yAlign: 'middle',
						},
					},
					position: 'legendPortPosition',
				},
			},
		},
	},
	defaultRect: singleRectShape({}),
}
export type NodeRegisterMapType = keyof typeof nodeRegisterMap

/**
 * type - Graph.registerPortLayout - portlayout
 */

export const portLayoutRegisterMap = {
	legendPortPosition: (portsPositionArgs: any[]) => {
		return portsPositionArgs.map((_, index) => {
			return {
				position: {
					x: 0,
					// 这个y是多个port时应该如何进行定位
					y: (index + 1) * LEGEND_LINE_HEIGHT,
				},
				angle: 0,
			}
		})
	},
}

/**
 * type - Graph.registerEdge - edge
 */
export const edgeRegisterMap = {
	defaultEdge: {
		inhert: 'edge',
		// todo: /node_modules/@antv/x6/src/registry/router/manhattan/router.ts
		router: {
			name: 'manhattan',
			args: {
				startDirections: ['right', 'left'],
				endDirections: ['right', 'left'],
				step: 50,
				// 这个很关键啊
				padding: 30,
			},
		},
		connector: {
			name: 'curveConnector',
		},
		attrs: {
			line: {
				stroke: '#aaa',
				strokeWidth: 2,
			},
		},
	},
	noarrowDeaultEdge: {
		inherit: 'defaultEdge',
		attrs: {
			line: {
				sourceMarker: {},
				targetMarker: {},
			},
		},
	},
}

// 连线有没有穿过rect形状
function hasCrosspoint(rect: RectType, cubicParams: CubicParamsType) {
	const errRange = 10
	const start = cubicParams[0] as AxisType
	const end = cubicParams[3] as AxisType
	const { width, height, x, y } = rect
	const cubicRectLeft = start.x < end.x ? start.x : end.x
	const cubicRectRight = start.x >= end.x ? start.x : end.x
	const cubicRectTop = start.y < end.y ? start.y : end.y
	const cubicRectBottom = start.y >= end.y ? start.y : end.y
	// 在cubic的变化矩形中
	if (
		x + errRange > cubicRectRight ||
		x + width - errRange < cubicRectLeft ||
		y + errRange > cubicRectBottom ||
		y + height - errRange < cubicRectTop
	) {
		return false
	}
	return true
}

// 连线有没有穿过node节点
function isLineCrossNode(graph: Graph, cubicParams: CubicParamsType) {
	const nodes = graph.getNodes()
	// node.position() 获得的是节点的左上角位置
	return nodes.some((node: Node) => {
		const position = node.position()
		const size = node.size()
		const rect = {
			...size,
			...position,
		}
		// console.log('rect_____cubicParams_____', cubicParams, rect)
		return hasCrosspoint(rect, cubicParams)
	})
}

// 判断方向
function getDirection(origin: number, target: number) {
	return origin < target ? true : false
}

function getAngle(fromPoint: AxisType, toPoint: AxisType) {
	// 与x轴成的距离
	const PI = Math.PI
	const hgap = Math.abs(fromPoint.x - toPoint.x)
	if (hgap === 0) {
		return PI / 2
	}
	const vgap = Math.abs(fromPoint.y - toPoint.y)
	if (vgap === 0) {
		return 0
	}
	const angle = Math.atan(vgap / hgap)
	// 返回以弧度为单位的返回值
	return angle
}

// 获得夹角、x、y的正反方向
function getOrientation(fromPoint: AxisType, toPoint: AxisType) {
	const angle = getAngle(fromPoint, toPoint)
	const xDirection = getDirection(fromPoint.x, toPoint.x)
	const yDirection = getDirection(fromPoint.y, toPoint.y)
	return {
		angle,
		xDirection,
		yDirection,
	}
}
// 获得控制点
function resolveCubicControlPoints(
	source: AxisType,
	target: AxisType,
	orientation1: OrientationType,
	orientation2: OrientationType,
): { control1: AxisType; control2: AxisType } {
	const PI = Math.PI
	const { x: fromX, y: fromY } = source
	const { x: toX, y: toY } = target
	const hgap = Math.abs(fromX - toX)
	const vgap = Math.abs(fromY - toY)
	const xDirection = getDirection(source.x, target.x)
	const yDirection = getDirection(source.y, target.y)
	const { angle: angle1 } = orientation1

	let movex1 = hgap / 2
	let movex2 = hgap - movex1
	let movey1 = vgap / 2
	let movey2 = vgap - movey1
	// 垂直的情况
	if (angle1 === PI / 2) {
		movex1 = 0
		movex2 = hgap - movex1
		// 平行
	} else if (angle1 === 0) {
		movey1 = 0
		movey2 = vgap - movey1
	} else {
		const tan = Math.tan(angle1)
		const xgapBorder = (2 * hgap) / 3
		const ygapBorder = (2 * vgap) / 3
		let xgap = vgap / tan / 2
		let ygap = (tan * hgap) / 2

		const shrinkRate = 1 / 4
		while (xgap > xgapBorder || ygap > ygapBorder) {
			xgap = xgap * shrinkRate
			ygap = ygap * shrinkRate
		}
		movex1 = xgap
		movex2 = hgap - movex1
		movey1 = ygap
		movey2 = vgap - movey1
	}
	const control1 = {
		x: source.x + (orientation1.xDirection ? movex1 : -movex1),
		y: source.y + (orientation1.yDirection ? movey1 : -movey1),
	}
	const control2 = orientation2
		? resolveCubicControlPoints(target, source, orientation2, null).control1
		: { x: source.x + (xDirection ? movex2 : -movex2), y: source.y + (yDirection ? movey2 : -movey2) }
	return {
		control1,
		control2,
	}
}

// 设置配置
export function registerGraphConf() {
	Graph.registerPortLayout(
		'erPortPosition',
		(portsPositionArgs) => {
			// const h = ConstantInfo.PADDING_TOP + ConstantInfo.TITLE_LINEHEIGHT
			const h = ConstantInfo.TITLE_LINEHEIGHT + ConstantInfo.BODY_STROKE_WIDTH
			return portsPositionArgs.map((_, index) => {
				if (index === 0) {
					return {
						position: {
							x: ConstantInfo.BODY_STROKE_WIDTH,
							y: h,
						},
						angle: 0,
					}
				}
				return {
					position: {
						x: ConstantInfo.BODY_STROKE_WIDTH,
						y: index * ConstantInfo.PORT_LINEHEIGHT + h,
					},
					angle: 0,
				}
			})
		},
		true,
	)
	Graph.registerConnector(
		'curveConnector',
		(sourcePoint, targetPoint, routePoints, _, Edgeview) => {
			const graph = Edgeview.graph
			const isPortType = !!Edgeview.cell.source?.port
			const path = new Path()
			const radius = 40
			const preParallelLong = 6
			const lastParalleLong = 6
			const errRange = 8

			const sourceParallels = 8
			const targetParallels = 8

			// console.log('source____target___soup____tarP', source, target, sourcePoint, targetPoint)
			// console.log('isLineCrossNode____', Edgeview)
			if (!isLineCrossNode(graph, [sourcePoint, undefined, undefined, targetPoint])) {
				const source = {
					x: getDirection(sourcePoint.x, targetPoint.x)
						? sourcePoint.x + sourceParallels
						: sourcePoint.x - sourceParallels,
					y: sourcePoint.y,
				}
				const target = {
					x: getDirection(sourcePoint.x, targetPoint.x)
						? targetPoint.x - targetParallels
						: targetPoint.x + targetParallels,
					y: targetPoint.y,
				}
				path.appendSegment(Path.createSegment('M', sourcePoint.x, sourcePoint.y))

				path.appendSegment(Path.createSegment('L', source.x, source.y))

				// 匹配到了，先解决距离短不好拐弯的问题
				const orientation1 = getOrientation(sourcePoint, source)

				const orientation2 = getOrientation(targetPoint, target)

				const { control1, control2 } = resolveCubicControlPoints(
					sourcePoint,
					targetPoint,
					orientation1,
					orientation2,
				)

				path.appendSegment(
					Path.createSegment('C', control1.x, control1.y, control2.x, control2.y, target.x, target.y),
				)
				path.appendSegment(Path.createSegment('L', targetPoint.x, targetPoint.y))
				return path.serialize()
			}
			let x = sourcePoint.x
			let y = sourcePoint.y

			const sourceNext = Array.isArray(routePoints) && routePoints.length > 1 ? routePoints[0] : targetPoint
			const targetPrev =
				Array.isArray(routePoints) && routePoints.length > 1 ? routePoints[routePoints.length - 1] : sourcePoint

			const source = {
				x: getDirection(sourcePoint.x, sourceNext.x)
					? sourcePoint.x + sourceParallels
					: sourcePoint.x - sourceParallels,
				y: sourcePoint.y,
			}
			const target = {
				x: getDirection(targetPrev.x, targetPoint.x)
					? targetPoint.x - targetParallels
					: targetPoint.x + targetParallels,
				y: targetPoint.y,
			}

			if (isPortType) {
				x += getDirection(x, targetPoint.x) ? ConstantInfo.BODY_STROKE_WIDTH : -ConstantInfo.BODY_STROKE_WIDTH
			}
			path.appendSegment(Path.createSegment('M', x, y))

			path.appendSegment(Path.createSegment('L', source.x, source.y))

			x = source.x
			y = source.y

			if (Math.abs(x - targetPoint.x) < errRange) {
				const { x: nextX, y: nextY } = targetPoint
				const vgap = Math.abs(nextY - y)
				const long = 6
				path.appendSegment(
					Path.createSegment(
						'C',
						x,
						y < nextY ? y + vgap / 2 + long : y - vgap / 2 - long,

						nextX,
						nextY > y ? nextY - vgap / 2 - long : nextY + vgap / 2 + long,
						nextX,
						nextY,
					),
				)
				x = nextX
				y = nextY
			} else {
				const nextsPointers = [...routePoints, target]
				if (routePoints[0] && routePoints[0].y === y && Math.abs(routePoints[0].x - x) > preParallelLong) {
					x += getDirection(x, routePoints[0].x) ? preParallelLong : -preParallelLong
					path.appendSegment(Path.createSegment('L', x, y))
				}
				for (let i = 0; i < nextsPointers.length; ++i) {
					const { y: nextY } = nextsPointers[i]
					let { x: nextX } = nextsPointers[i]

					if (y === nextY) {
						if (Math.abs(nextX - x) > radius) {
							path.appendSegment(
								Path.createSegment(
									'L',
									getDirection(x, nextX) ? nextX - radius : nextX + radius,
									nextY,
								),
							)
							x = getDirection(x, nextX) ? nextX - radius : nextX + radius
						}
						continue
					} else {
						const long = 8
						if (!nextsPointers[i + 1]) {
							const nexttPoint = targetPoint
							const { y: nextYY } = nexttPoint
							let { x: nextXX } = nexttPoint
							if (Math.abs(nextXX - nextX) > lastParalleLong) {
								nextXX += getDirection(nextX, nextXX) ? lastParalleLong : -lastParalleLong
							}
							if (nextYY === nextY) {
								const nextXgap = Math.abs(nextX - nextXX)
								nextX += getDirection(nextX, nextXX) ? nextXgap / 2 : -nextXgap / 2
							}
						}
						const nextnextPoint = nextsPointers[i + 1] || targetPoint
						const { x: nnextX } = nextnextPoint
						const hgap = Math.abs(x - nextX)
						const firstTurningPointDirection = getDirection(x, nextX)
						const secondTurningPointDirection = getDirection(nextX, nnextX)
						path.appendSegment(
							Path.createSegment(
								'C',
								firstTurningPointDirection ? x + hgap / 2 + long : x - hgap / 2 - long,
								y,
								secondTurningPointDirection ? nextX - hgap / 2 - long : nextX + hgap / 2 + long,
								nextY,
								nextX,
								nextY,
							),
						)
						path.appendSegment(Path.createSegment('L', nextX, nextY))
						x = nextX
						y = nextY
					}
				}
			}

			if (x !== targetPoint.x || y !== targetPoint.y) {
				let targetPointX = targetPoint.x
				if (isPortType) {
					targetPointX += getDirection(x, targetPointX)
						? -ConstantInfo.BODY_STROKE_WIDTH
						: ConstantInfo.BODY_STROKE_WIDTH
				}
				path.appendSegment(Path.createSegment('L', targetPointX, targetPoint.y))
			}

			return path.serialize()
		},
		true,
	)
	register({
		shape: 'popoverTip',
		effect: ['data'],
		component: PopoverTipComponent,
	})

	Object.entries(portLayoutRegisterMap).forEach((element) => {
		const [type, portLayout] = element
		Graph.registerPortLayout(type, portLayout, true)
	})

	Object.entries(nodeRegisterMap).forEach((element) => {
		const [type, node] = element
		Graph.registerNode(type, node, true)
	})

	Object.entries(edgeRegisterMap).forEach((element) => {
		const [type, edge] = element
		Graph.registerEdge(type, edge, true)
	})
}

// 布局类型
export function switchLayout(type: string, conf: LayoutConfType) {
	switch (type) {
		case 'dagre':
			// debugger
			return new DagreLayout(conf as DagreLayoutOptions)
		case 'grid':
			return new GridLayout(conf as GridLayoutOptions)
		case 'circular':
			return new CircularLayout(conf as CircularLayoutOptions)
		default:
			return new DagreLayout(conf as DagreLayoutOptions)
	}
}
