// @ts-nocheck
import React from 'react'
import { ModalProps } from 'antd/lib/modal/Modal'
import { IEditor } from './Editor'
import { IQueryResult } from './QueryResult'
import { Modal } from 'antd'
import { EditorWithTreeAndResult } from '@model/sql/EditorWithTreeAndResult'

// eslint-disable-next-line
const empty = () => {}

export const FullEditorInModal: React.FC<
    IEditor & Partial<IQueryResult> & { showQueryResult?: boolean; showSearchTableTree?: boolean } & ModalProps
> = ({
    visible,
    className = '',
    editor,
    setEditor,
    onRunCurrentSQL,
    onRunAllSQL,
    onRunSelectedSQL,
    onSQLUpdate,
    showFormatBtn,
    showRunBtn,
    readOnly = false,
    showQueryResult = false,
    loading = false,
    SQLQueryResult = [],
    height,
    placeholder,
    autoFocus,
    onCancel,
    showSearchTableTree,
    ...res
}) => {
    return (
        <Modal
            title='入指标库'
            width={700}
            open={visible}
            onOk={empty}
            okButtonProps={{}}
            maskClosable={false}
            onCancel={onCancel}
            {...res}
        >
            <EditorWithTreeAndResult
                className={className}
                key={'full-editor-in-modal'}
                onRunCurrentSQL={onRunCurrentSQL}
                onRunAllSQL={onRunAllSQL}
                onRunSelectedSQL={onRunSelectedSQL}
                showSearchTableTree={showSearchTableTree}
                onSQLUpdate={onSQLUpdate}
                readOnly={readOnly}
                editor={editor}
                setEditor={setEditor}
                showFormatBtn={showFormatBtn}
                showRunBtn={showRunBtn}
                loading={loading}
                SQLQueryResult={SQLQueryResult}
                showQueryResult={showQueryResult}
                placeholder={placeholder}
                autoFocus={autoFocus}
                height={height}
            />
        </Modal>
    )
}
