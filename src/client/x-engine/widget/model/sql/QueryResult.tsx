// @ts-nocheck
import React, { useEffect, useRef, useState } from 'react'
import ReactDOM from 'react-dom/client'

import { Button, Col, message, Row, Table, Tabs, Tag, Tooltip } from 'antd'
import type { TabsProps } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import Broadcast from '@libs/broadcast'
import styles from './sql.module.scss'
import { sortMvColumns } from '@libs/util'
import { tableInfoListWithWindowId, activeTabKeyInSearchResultTab } from '@atoms/xEngineAtoms'
import { useAtom } from 'jotai'
import { useGetState, useRafInterval, useFullscreen, useUpdateEffect } from 'ahooks'
import { FullscreenOutlined, FullscreenExitOutlined } from '@ant-design/icons'
import { copyToClipboard } from '@libs'
import { SQL_QUERY_ROW_KEY, TABLE_COMMON_PROPS } from '@constant'

interface DataType {
  key: React.Key
  name: string
  age: number
  address: string
}

const runStatusMap = [
  {
    code: 1,
    text: '运行中',
    color: 'processing',
  },
  {
    code: 2,
    text: '成功',
    color: 'green',
  },
  {
    code: 3,
    text: '失败',
    color: 'red',
  },
  {
    code: 4,
    text: 'KILL',
    color: 'volcano',
  },
]

export interface IQueryResult {
  windowId: string
  SQLQueryResult: {
    sql: string
    queryId: number
    status: {
      /**
       * 1 running，
       * 2 run success，
       * 3 run fail
       */
      code: number
      message: string
    }
    executionTime: number
    rows: number
    columnName: Array<string>
    data: Array<object>
  }[]
  loading: boolean
}

const PositionTable = (props) => {
  const positionRef = useRef(null)
  const positionRefBottom = useRef(null)
  const [scrollProps, setScrollProps, getScrollProps] = useGetState({ x: 0, y: 0 })
  const clearInterval = useRafInterval(() => {
    const container = document.getElementsByClassName('queryPageContentTabContainer')[0]
    const bottomPosition = container.getBoundingClientRect().bottom

    if (positionRef.current && positionRefBottom.current) {
      const offsetWidth = document.body.offsetWidth
      const { left, top } = positionRef.current.getBoundingClientRect()
      const calculatedX = offsetWidth - left - 50
      const calculatedY = bottomPosition - top - (props.subNumber || 0)
      const { x, y } = getScrollProps()
      if (x !== calculatedX || y !== calculatedY) {
        setScrollProps({ x: calculatedX, y: calculatedY })
      }
    }
  }, 50)

  useEffect(() => {
    return () => clearInterval()
  }, [])
  return (
    <>
      <div ref={positionRef}></div>
      <Table className={'pointer'} {...props} rowKey={SQL_QUERY_ROW_KEY} scroll={scrollProps} />
      <div ref={positionRefBottom}></div>
    </>
  )
}

const useAddClass = (className: string, selector: string) => {
  useEffect(() => {
    const elements = document.querySelectorAll(selector)
    ;[...elements].forEach((i) => {
      return i.classList.add(className)
    })
    return () =>
      [...elements].forEach((i) => {
        i.classList.remove(className)
      })
  }, [])
}

const getKey = (tableData) => {
  return `${tableData.catalogName}->${tableData.databaseName}->${tableData.name}`
}
export const QueryResult: React.FC<IQueryResult> = ({ windowId, SQLQueryResult, loading }) => {
  // tableInfoListWithWindowId only for one time component need save by themselves
  const [_tableInfoListWithWindowId] = useAtom(tableInfoListWithWindowId)
  const [_activeTabKeyInSearchResultTab, setActiveTabKeyInSearchResultTab] = useAtom(activeTabKeyInSearchResultTab)
  const [tableTabDataList, setTableTabDataList] = useState([])
  const [activeKey, setActiveKey] = useState<string>('dataPreview')
  useUpdateEffect(() => {
    // chosen table
    const item = _tableInfoListWithWindowId[0]
    if (item && item.windowId === windowId) {
      const t = tableTabDataList.find((i) => {
        return (
          i.tableData.catalogName === item.tableData.catalogName &&
          i.tableData.databaseName === item.tableData.databaseName &&
          i.tableData.name === item.tableData.name
        )
      })

      if (!t) {
        setTableTabDataList((pre) => [...pre, item])
      }
      setActiveKey(getKey(item.tableData))
    }
  }, [_tableInfoListWithWindowId, setActiveKey])

  useEffect(() => {
    if (windowId in _activeTabKeyInSearchResultTab) {
      setActiveKey(_activeTabKeyInSearchResultTab[windowId])
    }
  }, [_activeTabKeyInSearchResultTab, setActiveKey])
  const columns: ColumnsType<DataType & { queryEngine: string }> = [
    {
      title: '状态',
      width: 100,
      dataIndex: 'status',
      // Data is only available when it is success
      render: (_, record) => {
        const status = runStatusMap.find((s) => s.code === record?.status?.code)
        return (
          <Tag
            color={status?.color}
            onClick={() => {
              setResultList((pre) => {
                if (pre.some((i) => i.queryId === record.queryId)) {
                  return pre // highlight
                } else {
                  return [...pre, record]
                }
              })
              setActiveKey(record.queryId)
            }}
          >
            {status?.text || '无状态'}
          </Tag>
        )
      },
    },
    {
      title: '查询ID',
      width: 330,
      dataIndex: 'queryId',
      ellipsis: { showTitle: true },
      render: (value, record) => {
        const canGetDetail = record.queryEngine === 'XEngine'
        return (
          <Button
            style={{ background: 'transparent', cursor: canGetDetail ? 'pointer' : 'unset' }}
            size={'small'}
            type={canGetDetail ? 'link' : 'text'}
            onClick={(e) => {
              if (canGetDetail) {
                Broadcast.trigger('OPERATE_SQL_QUERY_DETAIL', {
                  operate: 'open',
                  queryId: value,
                })
                e.stopPropagation()
              }
            }}
          >
            {value}
          </Button>
        )
      },
    },
    {
      title: '行数',
      key: 'rows',
      dataIndex: 'rows',
      width: 100,
    },
    {
      title: 'SQL',
      dataIndex: 'sql',
      ellipsis: { showTitle: true },
      key: 'sql',
      render: (text) => {
        return (
          <span
            className={'pointer'}
            onClick={() => {
              copyToClipboard(text)
                .then(() => {
                  message.success('已复制')
                })
                .catch(() => {
                  message.error('浏览器不支持复制')
                })
            }}
          >
            {text}
          </span>
        )
      },
    },
    {
      title: '详情',
      width: 100,
      dataIndex: 'status',
      // Data is only available when it is success
      render: (_, record) => (
        <Button
          className={'padding0'}
          size={'small'}
          onClick={() => {
            setResultList((pre) => {
              if (pre.some((i) => i.queryId === record.queryId)) {
                return pre // highlight
              } else {
                return [...pre, record]
              }
            })
            setActiveKey(record.queryId)
          }}
          type={'link'}
        >
          {'详情'}
        </Button>
      ),
    },
  ]

  const yRef = useRef<HTMLDivElement>()

  const [resultList, setResultList] = useState([])
  const resultListToTab = (result) => {
    return result.map((item) => {
      return {
        label: item.sql.length > 18 ? item.sql.slice(0, 18) + '...' : item.sql,
        key: item.queryId,
        children: item.columnName?.length ? (
          <div className={'pointer'}>
            <Row className={styles.queryDetail} ref={yRef} wrap={false} justify={'space-between'}>
              <Col className={styles.queryDetailSQL}>
                <Tooltip title={item?.sql} mouseEnterDelay={1} placement="bottom">
                  SQL: {item?.sql}
                </Tooltip>
              </Col>
            </Row>
            <PositionTable
              subNumber={100}
              bordered={true}
              virtual
              key={item.sql}
              {...TABLE_COMMON_PROPS}
              columns={
                sortMvColumns(item.columnName)?.map((item) => {
                  return {
                    title: item,
                    dataIndex: item,
                    key: item,
                    width: 230,
                    ellipsis: { showTitle: true },
                    onCell: () => {
                      let tempText
                      return {
                        onClick: (event) => {
                          copyToClipboard(event.target.textContent)
                            .then(() => {
                              message.success('已复制')
                            })
                            .catch(() => {
                              message.error('浏览器不支持复制')
                            })
                        }, // 点击行
                        onMouseEnter: (event) => {
                          tempText = event.target.textContent
                          ReactDOM.createRoot(event.target).render(
                            <Tooltip
                              zIndex={99999999}
                              className={'black'}
                              color={'#f7f7f7'}
                              placement={'topLeft'}
                              title={<span className={'text-black'}>{tempText}</span>}
                              mouseEnterDelay={0.1}
                            >
                              {event.target.textContent}
                            </Tooltip>,
                          )
                        }, // 鼠标移入行
                        onMouseLeave: (event) => {
                          event.target.innerHtml = event.target.textContent
                        },
                      }
                    },
                    render: (text) => <span className={'pointer'}>{text}</span>,
                  }
                }) ?? []
              }
              rowKey="id"
              dataSource={item.data}
              pagination={{ defaultPageSize: 100, size: 'small', style: { marginRight: 15 } }}
              className="tables"
              size="small"
            />
          </div>
        ) : (
          false
        ),
      }
    })
  }

  const tabItems: TabsProps['items'] = [
    {
      label: '执行结果',
      key: 'dataPreview',
      closable: false,
      children: (
        <Table
          {...TABLE_COMMON_PROPS}
          pagination={false}
          size="small"
          loading={loading}
          columns={columns}
          dataSource={
            SQLQueryResult?.map((i) => ({
              ...i,
              key: i.queryId,
            })) ?? []
          }
        />
      ),
    },
    // sql query result tab
    ...resultListToTab(resultList),
    //  table data  click table item will show
    ...tableTabDataList.map((i) => {
      return {
        key: getKey(i.tableData),
        label: i.tableData.name,
        children: (
          <div>
            <Row className={styles.queryDetail} ref={yRef} wrap={false} justify={'space-between'}>
              <Col className={styles.queryDetailSQL}>
                <Tooltip title={i.tableData.name} mouseEnterDelay={1} placement="bottom">
                  table: {i.tableData.name}
                </Tooltip>
              </Col>
            </Row>
            <PositionTable
              subNumber={70}
              testKey={i.tableData.name}
              {...TABLE_COMMON_PROPS}
              bordered={true}
              virtual
              columns={[
                {
                  title: 'name',
                  dataIndex: 'name',
                },
                {
                  title: 'columnType',
                  dataIndex: 'columnType',
                },
                {
                  title: 'comment',
                  dataIndex: 'comment',
                },
              ]}
              rowKey="id"
              dataSource={i.tableData.columns}
              pagination={false}
              className="tables"
              size="small"
            />
          </div>
        ),
      }
    }),
  ]
  useAddClass(styles.scrollXNone, 'body')

  const ref = useRef(null)
  const [isFullscreen, { toggleFullscreen }] = useFullscreen(ref, {
    pageFullscreen: {
      zIndex: 1000,
    },
  })

  const onEdit = (targetKey: string, action: 'add' | 'remove') => {
    if (action === 'remove') {
      let newActiveKey = activeKey
      let lastIndex = -1
      tabItems.forEach((item, i) => {
        if (item.key === targetKey) {
          lastIndex = i - 1
        }
      })
      const newPanes = tabItems.filter((item) => item.key !== targetKey)
      if (newPanes.length && newActiveKey === targetKey) {
        if (lastIndex >= 0) {
          newActiveKey = newPanes[lastIndex].key
        } else {
          newActiveKey = newPanes[0].key
        }
      }
      setResultList((pre) => {
        return pre.filter((i) => i.queryId !== targetKey)
      })
      setTableTabDataList((pre) => {
        return pre.filter((i) => getKey(i.tableData) !== targetKey)
      })
      setActiveKey(newActiveKey)
    }
  }
  return (
    <div ref={ref} style={{ background: 'white' }} className={styles.queryContainer}>
      <Tabs
        type="editable-card"
        hideAdd={true}
        activeKey={activeKey}
        defaultActiveKey={'dataPreview'}
        onEdit={onEdit}
        size="small"
        items={tabItems}
        tabBarExtraContent={{
          left: (
            <div>
              <Button type={'text'} onClick={toggleFullscreen}>
                {isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
              </Button>
            </div>
          ),
        }}
        onChange={(key) =>
          setActiveTabKeyInSearchResultTab((pre) => ({
            ...(pre || {}),
            [windowId]: key,
          }))
        }
      />
    </div>
  )
}
