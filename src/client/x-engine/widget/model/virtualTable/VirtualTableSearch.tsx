import { Form, Button, Col, Row, Input, Select } from 'antd'
import DBSelect from '../DBSelect'
import { virtualTableTypeList, computeTypeList } from '@pages/data-model/forms-conf/constant-conf'
import cs from './VirtualTableSearch.module.scss'

export interface SearchParamType {
    catalog: string
    database: string
    name?: string
    virtualTableType?: string
    computeType?: string
    creator?: string
    hot?: number // not supported now
}

const VirtualTableSearch = (props: { submit: (data: SearchParamType) => void }) => {
    const { submit } = props
    const [form] = Form.useForm()
    const onReset = () => {
        form.resetFields()
    }
    const onSubmit = (data: SearchParamType) => {
        submit(data)
    }
    const colCount = 24 / 4
    return (
        <>
            <Form layout='vertical' form={form} autoComplete='off' onFinish={onSubmit} className={cs.searchForm}>
                <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <DBSelect setFieldValue={form.setFieldValue} span={colCount} />
                    <Col span={colCount}>
                        <Form.Item
                            style={{ marginBottom: '12px' }}
                            name='name'
                            label='表名称'
                            tooltip={{ title: '虚拟表名称，小写字母开头，只支持小写字母、数字和下划线' }}
                            rules={[{ required: false, message: '请输入表名称' }]}
                        >
                            <Input placeholder='请输入表名称' />
                        </Form.Item>
                    </Col>
                    <Col span={colCount}>
                        <Form.Item
                            name='virtualTableType'
                            label='表类型'
                            style={{ marginBottom: '12px' }}
                            tooltip={{
                                title: '贴源虚拟表为关联数据源的底层表，业务虚拟表为基于贴源虚拟表建模的上层表',
                            }}
                            rules={[{ required: false, message: '请选择表类型' }]}
                        >
                            <Select
                                placeholder='请选择表类型'
                                options={virtualTableTypeList.map((e) => ({
                                    label: e.desc,
                                    value: e.value,
                                }))}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={colCount}>
                        <Form.Item
                            name='computeType'
                            label='计算类型'
                            style={{ marginBottom: '12px' }}
                            rules={[{ required: false, message: '请选择计算类型' }]}
                        >
                            <Select
                                placeholder='请选择计算类型'
                                options={computeTypeList.map((e) => ({
                                    label: e.desc,
                                    value: e.value,
                                }))}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={colCount} className={cs.searchBtnCol} push={12}>
                        <div className={cs.searchBtnWrapper}>
                            <Button htmlType='button' onClick={onReset}>
                                重置
                            </Button>
                            <Button htmlType='submit' type='primary'>
                                搜索
                            </Button>
                        </div>
                    </Col>
                    {/* <Col span={colCount}>
                        <Form.Item
                            name='creator'
                            label='创建人'
                            style={{ marginBottom: '12px' }}
                            rules={[{ required: false, message: '请选择创建人' }]}
                        >
                            <Select placeholder='请选择创建人' />
                        </Form.Item>
                    </Col> */}
                </Row>
            </Form>
        </>
    )
}

export { VirtualTableSearch }
