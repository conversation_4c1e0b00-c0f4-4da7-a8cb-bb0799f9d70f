import React, { useEffect } from 'react'
import clsx from 'clsx'
import { ConfigProvider, App } from 'antd'
import { Outlet, useLocation } from 'react-router-dom'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import zhCN from 'antd/lib/locale/zh_CN'
import qs from 'query-string'
import VConsole from 'vconsole'
import { BASE_URL, CompanyName, IS_CHROME_EXTENSION, IS_H5, RecordNumber } from '@shared/constants'
import {
  currentDatasetAtom,
  requestDefaultDatasetAtom,
  requestParamsExtractUrlListAtom,
  themeAtom,
} from './pages/AskBI/askBIAtoms'
import LayoutHeader from './LayoutHeader'
import { getAntdConfigProviderTheme } from './utils'
import { usePageHeight } from './hooks/usePageHeightHook'
import Drawers from './components/Drawers'

export function AskBILayout() {
  const location = useLocation()
  const [theme, _setTheme] = useAtom(themeAtom)
  const requestDefaultDataset = useSetAtom(requestDefaultDatasetAtom)
  const requestParamsExtractUrl = useSetAtom(requestParamsExtractUrlListAtom)
  const currentDataset = useAtomValue(currentDatasetAtom)
  const [pageHeight] = usePageHeight()

  useEffect(() => {
    const search = qs.parse(location.search)
    // if (IS_H5 && search.isDebug === '1') {
    if (IS_H5 && search.isDebug === '1') {
      new VConsole()
    }
  }, [location.search])

  // 聊天页面不需要 footer
  const isDetailPage = location.pathname.startsWith(BASE_URL + '/chat/')

  // 加载默认的 dataset以及文件列表数据
  useEffect(() => {
    // 先判断 currentDatasetAtom 是否为空，当为空的时候才去请求
    if (currentDataset == null) {
      requestDefaultDataset()
      requestParamsExtractUrl()
    }
  }, [currentDataset, requestDefaultDataset, requestParamsExtractUrl])

  return (
    <ConfigProvider locale={zhCN} theme={getAntdConfigProviderTheme(theme)}>
      <App>
        <div
          className={clsx(
            'layout-root relative flex flex-col overflow-hidden text-black dark:bg-slate-900 dark:text-slate-100 md:overflow-visible',
            isDetailPage ? 'h-screen' : 'min-h-screen',
            IS_CHROME_EXTENSION && 'chrome-extension',
          )}
          // innerHeight可能大于100vh, 所以高度取最小值
          style={IS_H5 ? { height: `min(${pageHeight}, 100vh)` } : {}}
        >
          {!IS_H5 && <LayoutHeader isFullWidth={false} showMenu={true} />}
          <div className="bg-img absolute top-0 -z-10 flex h-full w-full place-items-center overflow-hidden bg-[#F5F5F7] md:bg-[#F6F5FA]" />
          <Outlet />

          <footer
            className={clsx('my-4 text-center text-xs text-slate-400 dark:text-slate-500', isDetailPage && 'hidden')}
          >
            <p className="block md:hidden">{CompanyName}</p>
            <p className="block md:hidden">{RecordNumber}</p>
            <p className="hidden md:block">{`${CompanyName}｜${RecordNumber}`}</p>
          </footer>
        </div>
        <Drawers />
      </App>
    </ConfigProvider>
  )
}
