import React, { useState } from 'react'
import { Button, Table, App, Modal, Space, Result, Dropdown } from 'antd'
import { ColumnsType } from 'antd/es/table'
import axios from 'axios'
import { useRequest } from 'ahooks'
import Search from 'antd/es/input/Search'
import { MoreOutlined } from '@ant-design/icons'
import { nanoid } from 'nanoid'
import { ResponseErrorType, Scenario, TableStructureResponse } from 'src/shared/metric-types'
import { askBIApiUrls } from 'src/shared/url-map'
import { APIResponse } from 'src/shared/common-types'
import TextHighlight from 'src/client/components/TextHighlight'
import PageHeader from 'src/client/components/PageHeader'
import AdminPage from 'src/client/components/AdminPage'
import TableLineageDrawer from './DataSceneTableLineageDrawer'

type IDataSceneListItem = {
  createTime: number
  creator: string
  modelName: string
  projectName: string
  scenarioName: string
  scenarioType: string
}

type IGetSceneListParams = {
  current?: number
  name?: string
  pageSize?: number
  projectName?: string
  scenarioType?: string
  [property: string]: any
}

const DataScene = () => {
  const { message: antdMessage } = App.useApp()
  const [openDeleteScenarioModal, setOpenDeleteScenarioModal] = useState(false)
  const [deletingScenario, _setDeletingScenario] = useState<Scenario>()
  const [searchValue, setSearchValue] = useState({
    projectName: '',
    scenarioType: '',
    name: '',
  })

  const [scenarioListTotal, setScenarioListTotal] = useState(0)
  const [tableLineageDrawerProps, setTableLineageDrawerProps] = useState({
    open: false,
    modelName: '',
    title: '',
  })
  const {
    run: getDataScenario,
    refresh: refreshDataScenario,
    data: scenarioConfig,
    loading: isScenarioListLoading,
    error: scenarioListError,
  } = useRequest(
    async ({ name, projectName, scenarioType }: IGetSceneListParams = {}) => {
      const requestParams: IGetSceneListParams = { current: 1, pageSize: -1, name, projectName, scenarioType }
      const response = await axios.get<APIResponse<TableStructureResponse<IDataSceneListItem>>>(
        askBIApiUrls.xengine.scenario.list,
        {
          params: requestParams,
        },
      )
      setScenarioListTotal(response.data?.data?.total || 0)
      if (!response.data.data) {
        return []
      }
      return response.data.data.list
    },
    {
      onError: (error) => {
        antdMessage.error('获取数据场景信息失败')
        console.error('Load data scenario list with error', error)
      },
    },
  )

  const { run: handleDelete, loading: isDeleteLoading } = useRequest(
    (id) => {
      return axios.delete(askBIApiUrls.auth.scene.rest, { data: { id } })
    },
    {
      manual: true,
      onSuccess: () => {
        antdMessage.success('删除成功')
        setOpenDeleteScenarioModal(false)
        refreshDataScenario()
      },
      onError: (error: Error & ResponseErrorType) => {
        antdMessage.error('删除数据场景失败：' + error.msg || error.error || '未知原因')
        console.error('deleteDataScenario error =', error)
      },
    },
  )

  const scenarioColumn: ColumnsType<IDataSceneListItem> = [
    {
      title: '数据场景名称',
      dataIndex: 'scenarioName',
      width: 200,
      render(value) {
        return <TextHighlight text={value} highlight={searchValue.name} />
      },
    },
    {
      title: '数据场景类型',
      dataIndex: 'scenarioType',
      ellipsis: true,
      render(value) {
        return <TextHighlight text={value} highlight={searchValue.scenarioType} />
      },
    },
    { title: '创建人', dataIndex: 'creator', width: 180 },
    {
      title: '所属项目',
      dataIndex: 'projectName',
      width: 220,
      render(value) {
        return <TextHighlight text={value} highlight={searchValue.projectName} />
      },
    },
    {
      title: '操作',
      dataIndex: 'id',
      width: 80,
      render(_value, record: IDataSceneListItem) {
        return (
          <Space>
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'detail',
                    label: (
                      <Button
                        type="link"
                        onClick={() => {
                          setTableLineageDrawerProps({
                            open: true,
                            modelName: record?.modelName,
                            title: record?.scenarioName,
                          })
                        }}
                      >
                        详情
                      </Button>
                    ),
                  },
                  // { 后端暂时不支持
                  //   key: 'delete',
                  //   label: (
                  //     <Button
                  //       type="link"
                  //       danger
                  //       onClick={() => {
                  //         setOpenDeleteScenarioModal(true)
                  //         setDeletingScenario(record)
                  //       }}
                  //     >
                  //       删除
                  //     </Button>
                  //   ),
                  // },
                ],
              }}
              placement="bottomRight"
              arrow={{ pointAtCenter: true }}
            >
              <Button type="link">
                <MoreOutlined />
              </Button>
            </Dropdown>
          </Space>
        )
      },
    },
  ]

  const handleSearch = (value: string, type: 'projectName' | 'scenarioType' | 'name') => {
    setSearchValue((pre) => {
      return { ...pre, [type]: value }
    })
    getDataScenario({ ...(searchValue || {}), [type]: value })
  }

  return (
    <AdminPage title={<PageHeader title={<>数据场景管理</>} />}>
      <div>
        <div className="flex justify-between pb-3 pt-2">
          <div>{`共${scenarioListTotal || 0}个`}</div>
          <div className="flex gap-3">
            <Search placeholder="搜索项目名" allowClear onSearch={(val) => handleSearch(val, 'projectName')} />
            <Search placeholder="搜索场景类型" allowClear onSearch={(val) => handleSearch(val, 'scenarioType')} />
            <Search placeholder="搜索数据场景名" allowClear onSearch={(val) => handleSearch(val, 'name')} />
          </div>
        </div>

        {scenarioListError != null ? (
          <Result status="error" title="获取数据场景列表失败" subTitle="请稍后再试" />
        ) : (
          <Table
            columns={scenarioColumn}
            loading={isScenarioListLoading}
            rowKey={() => nanoid()}
            dataSource={scenarioConfig}
            pagination={{
              hideOnSinglePage: true,
              showSizeChanger: true,
            }}
          />
        )}
      </div>

      <Modal
        destroyOnClose
        title={`确认删除${deletingScenario?.label}`}
        open={openDeleteScenarioModal}
        onOk={() => handleDelete(deletingScenario?.id)}
        okButtonProps={{ loading: isDeleteLoading }}
        onCancel={() => setOpenDeleteScenarioModal(false)}
      >
        该数据场景将被删除，请确认
      </Modal>
      <TableLineageDrawer
        {...tableLineageDrawerProps}
        onClose={() => {
          setTableLineageDrawerProps({
            open: false,
            modelName: '',
            title: '',
          })
        }}
      />
    </AdminPage>
  )
}

export default DataScene
