import React from 'react'
import { <PERSON><PERSON>eader } from '@ant-design/pro-layout'
import { Descriptions, Tag, message } from 'antd'
import { get, pick } from 'lodash-es'
import { useRequest } from 'ahooks'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { CopyOutlined } from '@ant-design/icons'
import axios from 'axios'
import columns from '@ui/table/Columns'
import LayoutCard from '@ui/layoutCard/LayoutCard'
import MetricsDetailPanel from '@model/metrics/MetricsMvExplain'
import { assertExhaustive } from '@shared/common-utils'
import { askBIApiUrls, askBIPageUrls } from 'src/shared/url-map'
import { copyToClipboard } from '@libs'

type OutputDescType = {
  brokers?: string
  catalog?: string
  database?: string
  outputTarget: 'ASKDI' | 'KAFKA'
  table?: string
  topic?: string
}
const formattedOutputDesc = (outputDesc: OutputDescType) => {
  const type = outputDesc?.outputTarget
  switch (type) {
    case 'ASKDI': {
      return pick(outputDesc, ['outputTarget', 'catalog', 'database', 'table'])
    }
    case 'KAFKA': {
      return pick(outputDesc, ['outputTarget', 'brokers', 'topic'])
    }
    default:
      assertExhaustive(type)
  }
}

function replaceObjEmptyValue(obj: Record<string, any>, replaceValue?: string) {
  if (!obj) {
    return '-'
  }
  const replaceHolder = replaceValue ?? '-'
  return Object.keys(obj).reduce(
    (acc, key) => {
      const value = obj[key]
      if (value === undefined || value === null || value === '') {
        acc[key] = replaceHolder
      } else {
        acc[key] = value
      }
      return acc
    },
    {} as Record<string, any>,
  )
}
const Main: React.FC = function () {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const table = searchParams.get('name')
  const catalog = searchParams.get('catalog')
  const database = searchParams.get('database')
  const { data: modelData, loading: modelDataLoading } = useRequest(async () => {
    const modelData = await axios
      .get(askBIApiUrls.xengineVTable.getStreamVtableModel, {
        params: {
          catalog,
          database,
          table,
        },
      })
      .then((res) => res.data?.data)
    return modelData
  })
  const descItemsForModelInfo = (data: any) => {
    if (!data) return []
    return [
      {
        label: '创建人',
        key: 'creator',
        render: () => {
          return <>{get(data, 'creator') || '-'}</>
        },
      },
      {
        label: '创建时间',
        key: 'createTime',
        render: () => columns({ type: 'time' })?.(data?.createTime as never, data),
      },
      {
        label: '模型类型',
        key: 'metricModelType',
        render: () => columns({ type: 'metricmodeltype' })?.(data?.metricModelType as never, data),
      },
      {
        label: '计算类型',
        key: 'computeType',
        render: () => columns({ type: 'computetype' })?.(data?.computeType as never, data),
      },
      {
        label: '关联虚拟表',
        key: 'dataModelDescJoinDagVertices',
        render: () => {
          return (
            <div className="grid max-h-[40px] gap-1 overflow-auto scroll-auto">
              {get(data, 'dataModelDesc.joinDag.vertices', []).map(({ table }: { table: string }) => {
                return <Tag key={table}>{table}</Tag>
              })}
            </div>
          )
        },
      },
      {
        label: '起始时间策略',
        key: 'dataModelDesc.streamingDesc',
        render: () => columns({ type: 'timestrategy' })?.(data?.dataModelDesc?.streamingDesc as never, data),
      },
      {
        label: '最小查询粒度',
        key: 'dataModelDesc.windowDesc',
        render: () => columns({ type: 'windowsize' })?.(data?.dataModelDesc?.windowDesc as never, data),
      },
      {
        label: '输出配置',
        key: 'dataModelDesc.outputDesc',
        render: () => {
          replaceObjEmptyValue(formattedOutputDesc(data?.dataModelDesc?.outputDesc) as Record<string, any>)
          if (!data?.dataModelDesc?.outputDesc) {
            return '-'
          }
          const valuesObj = replaceObjEmptyValue(
            formattedOutputDesc(data?.dataModelDesc?.outputDesc) as Record<string, any>,
          ) as Record<string, any>

          const str =
            valuesObj.outputTarget === 'ASKDI'
              ? `${valuesObj.catalog}.${valuesObj.database}.${valuesObj.table}`
              : `${valuesObj.broker}.${valuesObj.topic}`
          function copy() {
            copyToClipboard(str)
              .then(() => {
                message.success('已复制')
              })
              .catch(() => {
                message.error('浏览器不支持复制')
              })
          }
          return (
            <Tag className="cursor-pointer" onClick={copy}>
              <CopyOutlined className="mr-1" />
              {`${valuesObj.outputTarget}: ${str}`}
            </Tag>
          )
        },
      },
      {
        label: '模型说明',
        key: 'dataModelDesc.comment',
        render: () => {
          return <>{get(data, 'dataModelDesc.comment') || '-'}</>
        },
      },
    ]
  }
  return (
    <>
      <PageHeader
        title="模型详情"
        onBack={() =>
          navigate(`${askBIPageUrls.manage.xengine.businessDataModel}?catalog=${catalog}&database=${database}`)
        }
      />
      <LayoutCard loading={modelDataLoading}>
        <Descriptions bordered title={get(modelData, 'name')}>
          {descItemsForModelInfo(modelData).map((item, index) => {
            return (
              <Descriptions.Item label={item.label} key={index}>
                {item.render()}
              </Descriptions.Item>
            )
          })}
        </Descriptions>
      </LayoutCard>

      <MetricsDetailPanel data={modelData} />
    </>
  )
}
export default Main
