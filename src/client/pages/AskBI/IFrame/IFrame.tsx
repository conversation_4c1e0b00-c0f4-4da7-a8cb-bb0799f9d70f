import { useAtom } from 'jotai'
import React, { useRef, useEffect, Suspense } from 'react'
import { useLocation } from 'react-router-dom'
import { Spin } from 'antd'
import { envAtom } from '../askBIAtoms'

const IFrame = (props: { url: string }) => {
  const iframeRef = useRef<HTMLIFrameElement | null>(null)
  const location = useLocation()
  const [env] = useAtom(envAtom)
  useEffect(() => {
    const handleReceiveMessage = (event: { origin: string; data: any }) => {
      console.info('Message from child:', event.data)
    }

    window.addEventListener('message', handleReceiveMessage)

    // Cleanup listener on component unmount
    return () => {
      window.removeEventListener('message', handleReceiveMessage)
    }
  }, [])
  const iframeUrl = `${env?.VITE_XENGINE_ORIGIN}${props.url}${location.search || ''}`

  return (
    <div className="relative h-full">
      <Suspense fallback={<Spin size="large" className="absolute inset-0 flex items-center justify-center" />}>
        <iframe
          allow="*"
          className="absolute inset-0 h-full w-full border-none"
          ref={iframeRef}
          src={iframeUrl}
          style={{ width: '100%' }}
          title="Child Frame"
        />
      </Suspense>
    </div>
  )
}

export default IFrame
