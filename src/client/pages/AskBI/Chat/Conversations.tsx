import React, { useEffect, useRef, useState } from 'react'
import { useAtom, useAtomValue, useSet<PERSON>tom } from 'jotai'
import { ConverChat } from '@prisma/client'
import { useRequest } from 'ahooks'
import type { InputRef } from 'antd'
import { App, Input, Popconfirm } from 'antd'
import clsx from 'clsx'
import axios from 'axios'
import dayjs from 'dayjs'
import { SvgIcon, closeIcon, editIcon } from 'src/client/components/SvgIcon'
import {
  APIResponse,
  AnsChatItem,
  Chat,
  ChatResponse,
  ChatStatus,
  ConverWithDataset,
  DatasetDatum,
  DocResultType,
  LlmType,
} from 'src/shared/common-types'
import { askBIApiUrls } from 'src/shared/url-map'
import {
  cancelTokenSourceAtom,
  chatsAtom,
  conversationIdAtom,
  currentDatasetAtom,
  initChatsAtom,
  isSubmittingAtom,
  llmType<PERSON>tom,
  showChatHistoryListAtom,
} from '../askBIAtoms'
import { readyResponseToChatAns, unreadyResponseToChatAns } from './utils'

// conver接口返回的数据结构 [今天/30天内]的conver
interface ConversationTimeSlices {
  todayConversationListWithDataset: ConverWithDataset[]
  thirtyDaysAgoConversationListWithDataset: ConverWithDataset[]
}

const Conversations = () => {
  const { message } = App.useApp()
  const [showChatHistory, setShowChatHistory] = useAtom(showChatHistoryListAtom)
  const [currentConversationId, setConversationId] = useAtom(conversationIdAtom)
  const setChats = useSetAtom(chatsAtom)
  const setCurrentDataset = useSetAtom(currentDatasetAtom)
  const setLlmType = useSetAtom(llmTypeAtom)
  const setIsSubmitting = useSetAtom(isSubmittingAtom)
  const setCancelTokenSource = useSetAtom(cancelTokenSourceAtom)
  const initChats = useSetAtom(initChatsAtom)

  const {
    run: loadConversationList,
    data: conversations,
    error: isError,
    loading: isLoading,
  } = useRequest<ConversationTimeSlices | undefined, unknown[]>(
    async () => {
      try {
        const response = await axios.get<APIResponse<ConversationTimeSlices>>(askBIApiUrls.convers.list)
        return response.data.data
      } catch (error: any) {
        message.error(`获取历史会话列表失败：${error?.message}`, 0.5)
        console.error('Conversations error =', error)
      }
    },
    { manual: true },
  )

  useEffect(() => {
    if (showChatHistory) {
      console.info('Loading chat history list...')
      loadConversationList()
    }
  }, [loadConversationList, showChatHistory])

  /**
   * 用户切换conversation 将conversationId chats llmType currentDataset 设置成历史会话的默认值
   * 复位 cancelTokenSource isSubmitting
   */
  const handelChangeConversation = async (conversationId: string, llmType: LlmType, dataset: DatasetDatum) => {
    const converChatsResponse = await axios.get<APIResponse<ConverChat[]>>(
      askBIApiUrls.converChats.listByConversationId(conversationId),
    )
    const conversations = converChatsResponse.data.data
    const chats = conversationChatsToChats(conversations || [])
    setConversationId(conversationId)
    setLlmType(llmType)
    setCurrentDataset(dataset)
    setChats(chats)
    setIsSubmitting(false)
    setCancelTokenSource(new AbortController())
    setShowChatHistory(false)
  }

  const handleDeleteConversation = (conversationId: string) => {
    message.loading({
      content: `正在删除图表，请稍候……`,
      key: conversationId,
    })
    axios
      .delete(askBIApiUrls.convers.delete(conversationId))
      .then(() => {
        message.success({
          content: `会话删除成功`,
          key: conversationId,
          duration: 0.5,
        })
      })
      .catch((error) => {
        message.error({
          content: `删除失败，请联系管理员处理`,
          key: conversationId,
          duration: 0.5,
        })
        console.error('删除会话遇到问题，错误信息为：', error)
      })
      .finally(() => {
        // 删除会话 => 加载列表 => 如果删除了当前正在
        loadConversationList()
        if (conversationId === currentConversationId) {
          setConversationId(null)
          initChats()
        }
      })
  }

  if (isError) {
    return <div>获取会话历史列表失败，请联系管理员处理！</div>
  }

  if (isLoading) {
    return <div>Loading...</div>
  }

  return (
    <div className="flex flex-col gap-2">
      <RenderConversationList
        conversations={conversations?.todayConversationListWithDataset || []}
        onChange={handelChangeConversation}
        onDelete={handleDeleteConversation}
        title="今天"
      />
      <RenderConversationList
        conversations={conversations?.thirtyDaysAgoConversationListWithDataset || []}
        onChange={handelChangeConversation}
        onDelete={handleDeleteConversation}
        title="30天内"
      />
    </div>
  )
}

/** 渲染conversation list */
function RenderConversationList(props: {
  conversations: ConverWithDataset[]
  onChange: (conversationId: string, llmType: LlmType, dataset: DatasetDatum) => void
  onDelete: (conversationId: string) => void
  title: string
}) {
  const { conversations, onChange, onDelete, title } = props

  if (conversations.length === 0) {
    return
  }

  return (
    <>
      <div>{title}</div>
      {conversations.map((conversation, index) => {
        return (
          <RenderConversationItem key={index} conversation={conversation} onChange={onChange} onDelete={onDelete} />
        )
      })}
    </>
  )
}

/** 渲染conversation item */
function RenderConversationItem(props: {
  conversation: ConverWithDataset
  onChange: (conversationId: string, llmType: LlmType, dataset: DatasetDatum) => void
  onDelete: (conversationId: string) => void
}) {
  const { conversation, onChange, onDelete } = props
  const { message: antdMessage } = App.useApp()

  const currentConversationId = useAtomValue(conversationIdAtom)
  const inputRef = useRef<InputRef>(null)
  const [isEditingTitle, setIsEditingTitle] = useState(false)
  const [title, setTitle] = useState(conversation.title)

  const handleEditTitle = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    event.stopPropagation()
    if (!isEditingTitle) {
      setIsEditingTitle(true)
    }
  }

  // 在下一次渲染中 使input框获取到焦点
  useEffect(() => {
    if (isEditingTitle && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isEditingTitle])

  const handleBlur = async () => {
    try {
      setIsEditingTitle(false)
      await axios.put(askBIApiUrls.convers.update(conversation.id), { title })
    } catch (error) {
      console.error('修改标题失败:', error)
      antdMessage.error('修改标题失败！')
    }
  }

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      event.preventDefault()
      handleBlur()
    }
  }

  return (
    <div
      className={clsx(
        'conversation-item group flex cursor-pointer items-center justify-between rounded-md border p-1',
        {
          'bg-slate-200': conversation.id === currentConversationId,
        },
      )}
    >
      <div onClick={() => onChange(conversation.id, conversation.llmType as LlmType, conversation.dataset)}>
        {isEditingTitle ? (
          <Input
            className="w-60 px-1 py-[2px] dark:border-slate-400 dark:shadow-sm dark:hover:border-slate-300"
            ref={inputRef}
            value={title}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            onClick={(event) => {
              event.stopPropagation()
            }}
            onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
              setTitle(event.target.value)
            }}
          />
        ) : (
          <div className="px-1 py-[3px] leading-[22px]">{title}</div>
        )}
        <div className="ml-1">{dayjs(conversation.updatedAt).format('YYYY-MM-DD HH:mm:ss')}</div>
      </div>
      <div
        className="conver-icon-list mr-2 hidden gap-1 group-hover:flex"
        onClick={(event) => {
          event.stopPropagation()
        }}
      >
        <div onClick={handleEditTitle}>
          <SvgIcon icon={editIcon} className="h-5 w-5 cursor-pointer" />
        </div>
        <Popconfirm
          title="删除历史会话"
          description={`确认删除 ${title.length > 10 ? title.slice(0, 10) + '...' : title} 会话吗？`}
          onConfirm={() => {
            onDelete(conversation.id)
          }}
          okText="确认"
          cancelText="取消"
        >
          <SvgIcon icon={closeIcon} className="h-5 w-5 cursor-pointer" />
        </Popconfirm>
      </div>
    </div>
  )
}

/**
 * 将conver_chat转化为前端的chat数组
 * 与chatBox的onSubmit共用数据转换方法
 */
function conversationChatsToChats(conversationChats: ConverChat[]): Chat[] {
  const chatsArray: Chat[] = []

  conversationChats.forEach((conversationChat) => {
    const chat: Chat = {
      id: conversationChat.id || '',
      ask: {
        role: 'user',
        content: conversationChat.ask,
        jsonContent: conversationChat.ask,
      },
      ans: [
        {
          role: 'assistant',
          content: [],
          sceneId: '',
          status: 'success',
          ansTime: conversationChat.updatedAt,
        },
      ],
      selectedSceneId: '',
      docAns: {
        role: 'assistant',
        content: [],
        status: 'success',
        ansTime: conversationChat.updatedAt,
      },
      askTime: conversationChat.createdAt,
    }

    const response = conversationChat.response as unknown as ChatResponse[]
    const docResponse = conversationChat.docResponse as unknown as ChatResponse[]
    const responseItem = response[0]
    const docResponseItem = docResponse[0] as unknown as DocResultType
    // 分为4种展示方式
    if (conversationChat && docResponseItem) {
      chat.docAns = {
        role: 'assistant',
        content: [{ type: 'doc-result', result: docResponseItem }],
        status: ChatStatus.success,
        ansTime: conversationChat.updatedAt,
      }
    } else {
      let chatAns: AnsChatItem
      if (responseItem.ready === true) {
        chatAns = readyResponseToChatAns(responseItem, '')
      } else if (responseItem.ready === false) {
        chatAns = unreadyResponseToChatAns(responseItem, '')
      } else {
        chatAns = {
          sceneId: '',
          status: 'failure',
          ansTime: conversationChat.updatedAt,
          role: 'assistant',
          content: [
            {
              type: 'text',
              text: '未知的状态：' + JSON.stringify(responseItem),
            },
          ],
        }
      }
      chat.ans = [chatAns]
    }

    chatsArray.push(chat)
  })

  return chatsArray
}

export default Conversations
