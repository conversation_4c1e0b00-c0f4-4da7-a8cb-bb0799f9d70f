/**
 * @description 元数据-数据概览
 */
import React from 'react'
import { App, Divider, Result, Skeleton, Table, Tooltip } from 'antd'
import { useRequest } from 'ahooks'
import axios from 'axios'
import { useAtomValue } from 'jotai'
import { ColumnsType } from 'antd/es/table'
import { nanoid } from 'nanoid'
import { APIResponse } from 'src/shared/common-types'
import { askBIApiUrls } from 'src/shared/url-map'
import MetricStoreOverview from 'src/client/components/MetricStoreOverview'
import { SampleDataResponse } from 'src/shared/metric-types'
import { SvgIcon, overviewFolderIcon, overviewDatasetIcon, overviewTableIcon } from 'src/client/components/SvgIcon'
import {
  brandInfoAtom,
  conversationIdAtom,
  currentDatasetAtom,
  metricConfigAtom,
  semanticProjectInfoAtom,
} from '../../askBIAtoms'

interface Props {
  traceId: string
}

function DataOverview(props: Props) {
  const { traceId } = props
  const { message: antdMessage } = App.useApp()
  const currentDataset = useAtomValue(currentDatasetAtom)
  const metricConfig = useAtomValue(metricConfigAtom)
  const semanticProjectInfo = useAtomValue(semanticProjectInfoAtom)
  const sceneDetail = semanticProjectInfo
    .find((e) => e.id === currentDataset?.projectId)
    ?.scenes.find((i) => i.id === currentDataset?.sceneId)
  const brandInfo = useAtomValue(brandInfoAtom)
  const conversationId = useAtomValue(conversationIdAtom)
  const {
    data: sampleData,
    loading: isSampleDataLoading,
    error: sampleDataError,
  } = useRequest(
    async () => {
      if (!currentDataset) {
        return
      }
      const response = await axios.get<APIResponse<SampleDataResponse>>(
        askBIApiUrls.datasets.sampleData(currentDataset.sceneId),
        {
          params: { conversationId },
          headers: { traceId }, // 在请求头中添加 traceId
        },
      )
      return response.data.data
    },
    {
      onError: (error) => {
        antdMessage.error('获取预览数据失败')
        console.error('Load user list with error', error)
      },
    },
  )

  if (currentDataset == null) {
    return <Result status="error" title="没有可用的场景" />
  }

  const detailMap = [
    {
      key: 'projectName',
      name: '项目',
      value: currentDataset.projectName,
      icon: <SvgIcon icon={overviewFolderIcon} className="h-4 w-4" />,
    },
    {
      key: 'sceneName',
      name: '场景',
      value: sceneDetail?.label,
      icon: <SvgIcon icon={overviewDatasetIcon} className="h-4 w-4" />,
    },
    {
      key: 'tableName',
      name: '表',
      value: sampleData?.tableName,
      icon: <SvgIcon icon={overviewTableIcon} className="h-4 w-4" />,
    },
  ] as const

  const renderSampleData = () => {
    if (sampleDataError) {
      return <Result status="error" title="获取预览失败" subTitle={sampleDataError.message} />
    }

    if (isSampleDataLoading) {
      return <Skeleton active />
    }

    if (sampleData == null) {
      return <Result status="error" title="表数据为空" />
    }

    const columns: ColumnsType<any> = sampleData.columns.map((col, index) => {
      let title = col
      const { columnsTitleList } = sampleData
      if (columnsTitleList) {
        columnsTitleList.forEach((item) => {
          if (item.field === col && item.title?.trim?.()) {
            // 优先显示中文，没有中文显示默认的英文title
            title = item.title || title
          }
        })
      }
      return {
        title: () => {
          return (
            <Tooltip placement="topLeft" title={title}>
              {title}
            </Tooltip>
          )
        },
        dataIndex: col,
        key: index,
        className: 'truncate min-w-20',
      }
    })

    return (
      <Table
        bordered
        size="small"
        rowKey={(record) => record.id || nanoid()} // 确保每一行都有唯一的 key
        dataSource={sampleData.data}
        scroll={{ x: 'max-content', y: 180 }}
        columns={columns}
        pagination={false}
      />
    )
  }

  const renderDetailItem = (key: (typeof detailMap)[number]['key']) => {
    const detailItem = detailMap.find((item) => item.key === key)
    if (!detailItem) return null
    const label = detailItem.name
    const icon = detailItem.icon
    return (
      <div className="flex">
        <span>{label}</span>
        <div className="flex items-center gap-2 font-bold">
          {icon}
          {detailItem.value || '-'}
        </div>
      </div>
    )
  }

  return (
    <div className="card rounded-tremor-default group flex max-w-full flex-col rounded-xl dark:bg-slate-700">
      <h3 className="mb-2 text-xl font-semibold">数据概览</h3>
      <div className="flex flex-wrap justify-start gap-6">
        {renderDetailItem('projectName')}
        {renderDetailItem('sceneName')}
        {brandInfo.brandName !== 'China Telecom' && renderDetailItem('tableName')}
      </div>
      {sceneDetail?.description && (
        <>
          <h3 className="my-2 text-xl font-semibold">场景描述</h3>
          <span>{sceneDetail.description}</span>
        </>
      )}
      <Divider style={{ margin: '16px 0px' }} />
      <MetricStoreOverview metricConfig={metricConfig} />
      <Divider style={{ margin: '16px 0px' }} />
      <h3 className="text-base font-medium">前5条样例数据：</h3>
      <div className="max-w-full shadow-sm">{renderSampleData()}</div>
    </div>
  )
}

export default React.memo(DataOverview)
