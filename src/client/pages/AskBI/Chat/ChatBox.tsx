/**
 * @description Chart 的 ChatBox 组件，用于渲染图表类的聊天组件。包含：问题输入框，会话列表，推荐问题等
 */
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from 'antd'
import { StopIcon } from '@heroicons/react/24/outline'
import clsx from 'clsx'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import { Chat } from '@shared/common-types'
import { IS_H5, messageSafeAreaClass } from 'src/shared/constants'
import MatchedParamDrawer from 'src/client/components/CustomDrawer/MatchedParamDrawer'
import MessageInputH5 from 'src/client/components/MessageInputH5'
import {
  chatsAtom,
  isSubmittingAtom,
  themeAtom,
  cancelTokenSourceAtom,
  currentFollowUpQuestionAtom,
  makeAgentRequestAtom,
  initChatsAtom,
  agentStructuredMessageAtom,
  isShowMatchedParamDrawerAtom,
} from '../askBIAtoms'
import MessageInput from '../../../components/MessageInput'
import { followUpAskIcon, SvgIcon } from '../../../components/SvgIcon'
import ChatHistoryItem from '../../../components/ChatHistoryItem'
import { docPreviewOpenTagAtom } from '../../AskDoc/askDocAtoms'
import ChatBoxWrapper from './ChatBoxWrapper'

interface Props {
  /** 被首页嵌入时为 true，其他情况为 false */
  isMiniMode: boolean
  className?: string
}

function ChatBox(
  props: Props,
  ref: React.Ref<{
    handleSuggestion: (e: React.FormEvent, message: string) => void
  }>,
) {
  const navigate = useNavigate()

  const setMessage = useSetAtom(agentStructuredMessageAtom)
  // 当前正在基于[currentFollowUpQuestion]追问
  const [currentFollowUpQuestion, setCurrentFollowUpQuestion] = useAtom<Chat | null>(currentFollowUpQuestionAtom)
  const initChats = useSetAtom(initChatsAtom)
  const chats = useAtomValue(chatsAtom)
  const theme = useAtomValue(themeAtom)
  // 表示是否正在提交，如果正在提交，置灰提交按钮，禁止下一次提交，但用户仍能输入
  const isSubmitting = useAtomValue(isSubmittingAtom)
  const isShowMatchedParamDrawer = useAtomValue(isShowMatchedParamDrawerAtom)
  const [cancelTokenSource, setCancelTokenSource] = useAtom(cancelTokenSourceAtom)
  const docPreviewOpenTag = useAtomValue<boolean>(docPreviewOpenTagAtom)

  // 提交 agent 融合请求
  const makeAgentRequest = useSetAtom(makeAgentRequestAtom)

  const inputRef = useRef<{ focus: () => void; blur: () => void }>(null)

  useEffect(() => {
    chats.length === 0 && initChats()
  }, [chats, initChats])

  const handleSubmit = useCallback(
    (newMessage?: string) => {
      makeAgentRequest({
        navigate,
        isMiniMode: props.isMiniMode,
        newMessage: newMessage,
      })
      setTimeout(() => {
        // 回车后输入框失焦, 为了隐藏指标弹窗
        // 加了定时器才能清空输入框里的内容并失焦
        inputRef.current?.blur()
      }, 0)
    },
    [makeAgentRequest, navigate, props.isMiniMode],
  )

  const pressEnterToSubmit = useCallback(
    (e: React.KeyboardEvent<Element>) => {
      if (e.key === 'Enter') {
        e.preventDefault()
        handleSubmit()
      }
    },
    [handleSubmit],
  )

  useImperativeHandle(ref, () => ({
    handleSuggestion: (e: React.FormEvent, message: string) => {
      handleSuggestionClick(e, message)
    },
  }))

  const handleSuggestionClick = useCallback(
    (_e: React.FormEvent, message: string) => {
      // inputRef.current?.blur()
      setMessage(message)
      handleSubmit(message)
    },
    [handleSubmit, setMessage],
  )

  const renderFollowUpBottomContent = (
    <div
      className={`${
        currentFollowUpQuestion
          ? '-translate-y-0 transform opacity-100 transition-all duration-500 ease-in'
          : 'translate-y-4 transform opacity-0 transition-all duration-500 ease-out'
      } flex h-10 w-full items-center justify-between bg-primary px-4 text-sm dark:text-slate-400`}
    >
      <div className="mr-2 flex w-11/12 items-center">
        <SvgIcon icon={followUpAskIcon} className="block h-4 w-4 text-white" />
        <p className="ml-2 truncate text-white">
          {currentFollowUpQuestion
            ? `请输入“${currentFollowUpQuestion.ask.content}”的跟进问题：`
            : '请选择要跟进的问题！'}
        </p>
      </div>

      <Button
        onClick={() => {
          setCurrentFollowUpQuestion(null)
        }}
      >
        取消
      </Button>
    </div>
  )

  const InputElement = IS_H5 ? MessageInputH5 : MessageInput

  const renderMessageInput = (
    <div className={clsx('w-full overscroll-contain', docPreviewOpenTag ? 'md:max-w-screen' : 'md:max-w-screen-xl')}>
      <InputElement
        isFirstMessage={chats.length < 2 /** 第一个为打招呼信息，所以忽略掉 */}
        enableLlmToggle={!__DISABLE_LLM_TOGGLE__}
        isMiniMode={props.isMiniMode}
        isSubmitting={isSubmitting}
        pressEnterToSubmit={(e) => {
          pressEnterToSubmit(e)
        }}
        onSubmit={(message?: string | undefined) => {
          handleSubmit(message)
        }}
        ref={inputRef}
      />
    </div>
  )

  const renderChatHistory = (
    <div
      id="chat-history-box"
      className="chat-history z-10 flex w-full flex-1 flex-col gap-4 overflow-y-auto overflow-x-hidden px-1 pb-2 pt-1 md:px-2 md:pb-4"
    >
      {chats.map((chat, index) => (
        <ChatHistoryItem key={index} chat={chat} theme={theme} />
      ))}
    </div>
  )

  const renderStopGenerate = (
    <>
      {isSubmitting && (
        <div className="absolute bottom-full z-10 mb-4 flex w-full justify-center">
          <button
            onClick={() => {
              cancelTokenSource.abort('本次问答已终止。')

              setCancelTokenSource(new AbortController())
              console.info('cancelToken 用户手动取消请求')
            }}
            className="cursor-pointer rounded-md border bg-white p-2 hover:bg-slate-300 dark:border-slate-500 dark:bg-slate-900 dark:hover:bg-slate-600/50"
          >
            <div className="flex items-center justify-center gap-2">
              <div className="h-4 w-4">
                <StopIcon />
              </div>
              停止输出
            </div>
          </button>
        </div>
      )}
    </>
  )

  if (props.isMiniMode) {
    return (
      <div className="chat-box shadow-blue-40 w-screen">
        <ChatBoxWrapper>
          <div id="chat-toolbar" className={clsx('query', messageSafeAreaClass, { 'bg-[#F5F5F7]': IS_H5 })}>
            <div className="flex flex-col items-center justify-center pb-6">{renderMessageInput}</div>
          </div>
        </ChatBoxWrapper>
        <MatchedParamDrawer isMiniMode={props.isMiniMode} key={String(props.isMiniMode)} />
      </div>
    )
  }

  return (
    <div
      className={clsx(
        'chat-box flex h-full flex-col text-sm dark:bg-slate-900',
        IS_H5 ? 'w-screen' : '',
        props.className,
      )}
    >
      {renderChatHistory}
      {currentFollowUpQuestion && renderFollowUpBottomContent}
      <ChatBoxWrapper>
        <div id="chat-toolbar" className={clsx(messageSafeAreaClass, 'relative', { 'bg-[#F5F5F7]': IS_H5 })}>
          {renderStopGenerate}
          <div
            className={clsx(
              'message-input-wrapper pb-5 md:bg-inherit md:pb-1',
              isShowMatchedParamDrawer ? 'bg-white' : 'bg-[#F5F5F7]',
            )}
          >
            {renderMessageInput}
          </div>
        </div>
      </ChatBoxWrapper>
      <MatchedParamDrawer isMiniMode={props.isMiniMode} key={String(props.isMiniMode)} />
    </div>
  )
}

export default forwardRef(ChatBox)
