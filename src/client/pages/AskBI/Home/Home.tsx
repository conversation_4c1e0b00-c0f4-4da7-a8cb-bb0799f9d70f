/**
 * @description Ask BI 的首页
 */
import React, { useEffect, useRef, useState } from 'react'
import { <PERSON>pp, <PERSON><PERSON>, Spin, Tooltip } from 'antd'
import './Home.css'
import clsx from 'clsx'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import { SvgIcon, sendMessageIcon } from 'src/client/components/SvgIcon'
import { CompanyName, IS_H5, RecordNumber } from 'src/shared/constants'
import { askBIPageUrls } from 'src/shared/url-map'
import H5HomeRecentAsks from 'src/client/components/AskList/H5HomeRecentAsks'
import ChatScenePopover from 'src/client/components/ChatScenePopover'
import HomeHotMetrics from 'src/client/components/HomeHotMetrics'
import {
  currentSuggestionAtom,
  isShowMatchedParamDrawerAtom,
  questionnaireAtom,
  requestDefaultDatasetLoadingAtom,
  semanticProjectInfoAtom,
} from '../askBI<PERSON>toms'
import ChatBox from '../Chat/ChatBox'
import HomeBg from '/img/home-config-bg.webp'
import BiIcon from '/img/biIcon.webp'
import BIImage1 from '/img/bi_1.webp'
import BIImage2 from '/img/bi_2.webp'
import BIImage3 from '/img/bi_3.webp'
import BIImage4 from '/img/bi_4.webp'
import BIImage5 from '/img/bi_5.webp'
import BIImage6 from '/img/bi_6.webp'

const biImageList = [BIImage1, BIImage2, BIImage3, BIImage4, BIImage5, BIImage6]

function HomePage() {
  const { message } = App.useApp()
  const chatBoxRef = useRef<{
    handleSuggestion: (e: React.FormEvent, message: string) => void
  }>(null)
  const currentSuggestion = useAtomValue(currentSuggestionAtom)
  // const setAccessPaths = useSetAtom(accessPathsAtom)
  const semanticProjectInfo = useAtomValue(semanticProjectInfoAtom)
  const loading = useAtomValue(requestDefaultDatasetLoadingAtom)
  const [isHovered, setIsHovered] = useState(false)
  // const setMessage = useSetAtom(updateMessageAndHtmlAtom)
  const isShowMatchedParamDrawer = useAtom(isShowMatchedParamDrawerAtom)
  const setQuestionnaire = useSetAtom(questionnaireAtom)
  const searchParams = new URLSearchParams(window.location.search)
  const questionnaire = searchParams.get('questionnaire') === 'true'

  useEffect(() => {
    const hashValue = window.location.hash
    if (hashValue === '#success') {
      message.success('登录成功').then(() => {
        history.pushState({}, document.title, window.location.pathname)
      })
    }
  }, [message])

  useEffect(() => {
    setQuestionnaire(questionnaire)
  }, [questionnaire, setQuestionnaire])

  // 获取页面权限list
  // useRequest(
  //   async () => {
  //     const response = await axios.get(askBIApiUrls.auth.admin.resource.list, {
  //       params: {
  //         type: 'page',
  //       },
  //     })
  //     const res = response.data.data
  //     res &&
  //       setAccessPaths(
  //         res.list
  //           .map((v: XResource) =>
  //             (v.typeData as string[]).map((path) => ({
  //               path,
  //               allowed: true,
  //             })),
  //           )
  //           .flat(),
  //       )
  //   },
  //   {
  //     onError: (error: any) => {
  //       console.error('获取页面权限list =', error)
  //     },
  //   },
  // )

  // 数据洞察和知识洞察 提问
  const handleAskQuestionClick = (e: React.FormEvent, message: string) => {
    if (chatBoxRef.current) {
      chatBoxRef.current.handleSuggestion(e, message)
    }
  }

  // 未配置场景 - 跳转到开始配置页面 点击事件
  const handleGoConfigPageClick = () => {
    window.open(askBIPageUrls.scenarios.create, '_blank')
  }

  const handleMouseEnter = () => {
    setIsHovered(true)
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
  }

  const renderFooterUI = (
    <div className="flex h-10 items-center justify-center">
      <div className="block text-center text-xs text-[7B738C] opacity-70 dark:text-white md:hidden">
        <p>{CompanyName}</p>
        <p>{RecordNumber}</p>
      </div>
      <p className="hidden text-xs text-[7B738C] opacity-70 md:block">{`${CompanyName}｜${RecordNumber}`}</p>
    </div>
  )

  // 渲染推荐问题列表轮播 item
  const renderSuggestListAnimationBox = (
    <div className="animationBox flex" style={{ animationPlayState: isHovered ? 'paused' : 'running' }}>
      {((currentSuggestion && currentSuggestion.data) || []).map((item, index) => (
        <div
          className="suggestionsItem relative mr-9 inline-block h-[300px] w-[222px] flex-1 rounded-3xl md:h-96 md:w-[280px]"
          key={index}
        >
          <img
            src={biImageList[index % biImageList.length]}
            alt=""
            className="absolute h-full w-full rounded-[36px] object-cover shadow-md md:h-96"
          />
          <div className="absolute top-[64px] flex flex-col items-center justify-center md:top-[94px] lg:top-[60px] xl:top-[60px] 2xl:top-[94px]">
            <Tooltip placement="top" title={item}>
              <div className="line-clamp-5 whitespace-break-spaces break-all px-11 text-left text-[18px] font-semibold text-white">
                {item}
              </div>
            </Tooltip>
          </div>
          <div className="absolute bottom-[60px] flex w-full flex-col items-center">
            <Button
              className="suggestionsButton mt-2 flex h-11 w-[105px] items-center justify-center rounded-[37px] text-white"
              icon={<SvgIcon icon={sendMessageIcon} className="h-5 w-5 text-white dark:text-slate-400" />}
              onClick={(e) => {
                handleAskQuestionClick(e, item)
              }}
            >
              提问
            </Button>
          </div>
        </div>
      ))}
    </div>
  )

  // 渲染推荐问题列表轮播
  const renderSuggestList = (
    <div className="sliderPlay w-screen min-w-80">
      <div className="sliderPlayBox flex" onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
        {renderSuggestListAnimationBox}
        {renderSuggestListAnimationBox}
        {currentSuggestion && currentSuggestion?.data.length < 6 && renderSuggestListAnimationBox}
      </div>
    </div>
  )

  // 渲染已配置过的，正常的home首页
  // z-0用于限制内部元素的高层级, 防止外层其他弹窗需要用高层级来防止被覆盖
  const renderConfiguredUI = (
    <div className="home relative z-0 flex h-full flex-col bg-[#F5F5F7] dark:bg-slate-800 md:bg-[#F3F4F7]">
      {IS_H5 ? (
        <div className="home-content-mobile h-full overflow-y-auto">
          <div className="home-title px-5 pb-9 pt-14">
            <div className="mb-4 text-3xl font-bold text-[#1C1C1C]">用AI助力企业经营</div>
            <ChatScenePopover isMetricOverview={true} />
          </div>
          <HomeHotMetrics />
          <H5HomeRecentAsks />
        </div>
      ) : (
        <div className="home-content flex h-full flex-1 flex-col items-center justify-center overflow-y-auto overflow-x-hidden">
          <div className="text-3xl font-bold text-[#1C1C1C] dark:text-white">用AI洞察一切数据</div>
          <div className="mt-4 md:mt-7">{renderSuggestList}</div>
        </div>
      )}
      {/* <ChatBox isMiniMode={true} ref={chatBoxRef} /> */}
    </div>
  )

  // 渲染未配置时的UI
  const renderConfigUI = (
    <div className="home-config flex h-full flex-col overflow-hidden bg-slate-400">
      <div className="relative flex-grow bg-slate-600">
        <img className="absolute h-full w-full" src={HomeBg} alt="" />
        <div className="absolute flex h-full w-full flex-col items-center justify-center">
          <img className="h-[76px] w-[76px]" src={BiIcon} alt="" />
          <p className="mt-8 px-4 text-center text-xl text-[#171717]">
            Hi！我是AskBot，请配置您的第一个场景，进入智能之旅
          </p>
          <div className="hidden md:block">
            <Button
              type="primary"
              className="mt-12 flex h-12 w-[194px] items-center justify-center text-base"
              icon={<SvgIcon icon={sendMessageIcon} className="h-5 w-5 text-white dark:text-slate-400" />}
              onClick={handleGoConfigPageClick}
            >
              开始配置
            </Button>
          </div>
        </div>
      </div>
      <div className="bg-[#E5DDF4]">{renderFooterUI}</div>
    </div>
  )

  return (
    <main
      className={clsx(
        'ask-bi-home-page relative w-full flex-1 bg-[#F5F5F7] dark:bg-slate-800 md:bg-[#F3F4F7]',
        isShowMatchedParamDrawer ? 'overflow-hidden' : '',
      )}
    >
      {loading ? (
        <Spin size="large" className="absolute inset-0 flex items-center justify-center" />
      ) : semanticProjectInfo.length ? (
        renderConfiguredUI
      ) : (
        renderConfigUI
      )}
    </main>
  )
}

export default HomePage
