import React from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { App, Result, Skeleton } from 'antd'
import { ArrowLeftOutlined } from '@ant-design/icons'
import { useAtomValue } from 'jotai'
import { useRequest } from 'ahooks'
import axios from 'axios'
import AdminCard from 'src/client/components/AdminCard'
import ChartWrapper from 'src/client/charts/ChartWrapper'
import IconButtonGroup from 'src/client/components/IconButtonGroup'
import { getVisibleChartIconMap } from 'src/client/charts/Card'
import { askBIApiUrls } from 'src/shared/url-map'
import { APIResponse, AssistantChartChatItem, ChartType } from 'src/shared/common-types'
import { themeAtom } from '../askBIAtoms'

const ChartDetail = () => {
  const { chartId } = useParams()
  const navigate = useNavigate()
  const theme = useAtomValue(themeAtom)
  const { message: antdMessage } = App.useApp()
  const [chartData, setChartData] = React.useState<AssistantChartChatItem | null>(null)
  const [chartType, setChartType] = React.useState<ChartType>('LineChart')

  const { loading: isLoadingChartDetail, error: loadChartDetailError } = useRequest(
    async () => {
      if (chartId != null) {
        const response = await axios.get<APIResponse<AssistantChartChatItem>>(askBIApiUrls.charts.detail(chartId))
        const responseChartData = response.data.data
        if (responseChartData) {
          setChartData(responseChartData)
          setChartType(responseChartData.chartType)
        } else {
          antdMessage.error('加载图表详情数据失败')
        }
      } else {
        antdMessage.error('加载图表详情失败')
      }
    },
    {
      onError: (error) => {
        console.error('Load chart detail with error:', error)
      },
    },
  )

  // 改变图表的chartType
  const handleChartTypeChange = (newValue: ChartType) => {
    setChartType(newValue)
    if (chartData) {
      setChartData({
        ...chartData,
        chartType: newValue,
      })
    }
  }

  if (isLoadingChartDetail) {
    return (
      <div className="mx-auto w-full max-w-screen-xl">
        <Skeleton active />
      </div>
    )
  }

  if (loadChartDetailError) {
    return (
      <div className="mx-auto w-full max-w-screen-xl">
        <Result status="error" title="加载图表详情失败" subTitle={loadChartDetailError.message} />
      </div>
    )
  }

  return (
    <div className="mx-auto flex w-full max-w-screen-xl flex-col gap-2">
      <div
        className="ml-1 flex w-fit cursor-pointer select-none items-center gap-1"
        onClick={() => {
          navigate(-1)
        }}
      >
        <ArrowLeftOutlined />
        <span>返回</span>
      </div>
      {chartData && (
        <AdminCard title={chartData.chartTitle}>
          <ChartWrapper
            data={chartData}
            theme={theme}
            onTableDataChange={(data) => {
              console.info(data)
            }}
          />
          <div className="flex items-center gap-1">
            <span className="font-bold">切换图表：</span>
            <IconButtonGroup<ChartType>
              options={getVisibleChartIconMap(chartData.recommendChartTypes)}
              value={chartType}
              onChange={handleChartTypeChange}
            />
          </div>
        </AdminCard>
      )}
    </div>
  )
}

export default ChartDetail
