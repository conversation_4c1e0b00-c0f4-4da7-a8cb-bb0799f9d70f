/**
 * @description 图表管理界面
 */
import React, { useCallback, useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import axios, { AxiosResponse } from 'axios'
import { useRequest } from 'ahooks'
import { useAtomValue, useSetAtom, useAtom } from 'jotai'
import { Popconfirm, App, Empty, Dropdown, MenuProps, Skeleton } from 'antd'
import dayjs from 'dayjs'
import clsx from 'clsx'
import {
  APIResponse,
  AssistantChartChatItem,
  DatasetDatum,
  OlapRow,
  ReadyChartResponse,
  ThemeType,
} from '@shared/common-types'
import ChartWrapper from '@charts/ChartWrapper'
import Card from 'src/client/charts/Card'
import { askBIApiUrls, askBIPageUrls } from 'src/shared/url-map'
import {
  SvgIcon,
  chartDescriptionIcon,
  deleteIcon,
  duplicateIcon,
  editChartIcon,
  omitIcon,
} from 'src/client/components/SvgIcon'
import ChatScenePopover from 'src/client/components/ChatScenePopover'
import useIsAllowedRanger from 'src/client/hooks/useIsAllowedRanger'
import { allChartsAtom, currentDatasetAtom, metricConfigAtom, themeAtom } from '../askBIAtoms'
import './ChartManage.css'

const buttonClassName = 'flex cursor-pointer select-none gap-2 p-1 whitespace-nowrap'

const ChartManagePage = ({ className }: { className?: string }) => {
  const { message } = App.useApp()

  const theme = useAtomValue(themeAtom)
  const currentDataset = useAtomValue(currentDatasetAtom)

  const [allCharts, setAllCharts] = useAtom(allChartsAtom)

  // 图表管理界面中 dataset的中间变量
  const [tempDataset, setTempDataset] = useState<DatasetDatum | null>(currentDataset)

  const isAllowedRanger = useIsAllowedRanger()

  const handleDatasetChange = (currentDataset: DatasetDatum) => {
    setTempDataset(currentDataset)
    getChartList({ sceneId: currentDataset.sceneId })
  }

  const {
    run: getChartList,
    loading: isLoading,
    error: isError,
  } = useRequest<AxiosResponse<APIResponse<ReadyChartResponse[]>>, { sceneId: string }[]>(
    (data: { sceneId: string }) => {
      return axios.get(askBIApiUrls.charts.list, {
        params: { ...data, ranger: isAllowedRanger || false },
      })
    },
    {
      manual: true,
      onSuccess(resultList) {
        if (resultList.data?.code === 0) {
          setAllCharts(resultList.data?.data as ReadyChartResponse[])
        } else {
          console.error(`加载图表列表出错，错误信息为：${resultList.data.msg}`)
        }
      },
      onError(error) {
        console.error(`加载图表列表出错，错误信息为：${error.message}`)
      },
    },
  )

  useEffect(() => {
    if (currentDataset != null) {
      console.info('CurrentDataset in Chart Manage Page', currentDataset)
      getChartList({
        sceneId: currentDataset.sceneId,
      })
      setTempDataset(currentDataset)
    }
  }, [currentDataset, getChartList])

  const handleRefresh = () => {
    if (currentDataset != null) {
      getChartList({
        sceneId: currentDataset.sceneId,
      })
    }
  }

  const handleDelete = (idToDelete: string) => {
    message.loading({
      content: `正在删除图表，请稍候……`,
      key: idToDelete,
    })
    axios
      .delete(askBIApiUrls.charts.delete(idToDelete))
      .then(() => {
        message.success({
          content: `图表删除成功`,
          key: idToDelete,
          duration: 0.5,
        })
        handleRefresh()
      })
      .catch((error) => {
        message.error({
          content: `图表删除失败，请联系管理员处理`,
          key: idToDelete,
          duration: 0.5,
        })
        console.error('删除图表遇到问题，错误信息为：', error)
      })
  }

  const renderDatasetSelect = (
    <div className="relative w-fit self-end">
      {currentDataset ? (
        <ChatScenePopover
          onCurrentDatasetChange={(tempDataset) => {
            handleDatasetChange(tempDataset)
          }}
        />
      ) : (
        <p>暂无场景</p>
      )}
    </div>
  )

  const handleTableDataChange = useCallback(
    (data: OlapRow[], id: string) => {
      setAllCharts((prevAllCharts) => {
        const indexToUpdate = prevAllCharts.findIndex((chart) => chart.id === id)
        if (indexToUpdate !== -1) {
          // Warn: 更改atom的引用，防止只修改属性不触发react rerender
          const updatedCharts = [...prevAllCharts]
          updatedCharts[indexToUpdate] = {
            ...updatedCharts[indexToUpdate],
            rows: data,
          }
          return updatedCharts
        }
        return prevAllCharts
      })
    },
    [setAllCharts],
  )

  const renderChartList = () => {
    if (isLoading) {
      return <Skeleton active />
    }

    if (isError instanceof Error) {
      return (
        <Empty
          className="mx-auto pt-10"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="加载图表出错，请联系管理员排查"
        />
      )
    }

    if (allCharts.length === 0) {
      return (
        <Empty
          className="mx-auto pt-10"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="当前列表为空，快来保存你的第一个图表吧！"
        />
      )
    }

    return (
      <div className="chart-manage-wrapper flex h-full flex-grow justify-center text-base text-black dark:bg-slate-900 dark:text-slate-100">
        <div className="chart-manage-box flex w-full flex-grow flex-wrap content-start justify-center gap-2 md:justify-start">
          {allCharts.map((chartItemData, index) => {
            if (!chartItemData.rows) {
              return (
                <div
                  className={
                    'h-[308px] w-[calc(100%-2rem)] items-center justify-center rounded-md dark:border-slate-300 dark:bg-slate-700 md:w-[calc(50%-0.25rem)]'
                  }
                  key={index}
                >
                  <ChartItemHeader
                    chartId={chartItemData.id}
                    chartTitle={chartItemData.chartTitle}
                    updatedAt={chartItemData.updatedAt}
                    onDelete={() => {
                      handleDelete(chartItemData.id)
                    }}
                  />
                  <div className="error-detail flex h-[240px] flex-col items-center rounded-b-md border-2 border-t-0 border-solid p-4">
                    <div>
                      {`无法展示 ${chartItemData.id} 图表`}
                      <br />
                    </div>
                    <div className="mt-2 line-clamp-4 text-ellipsis break-all">
                      {'Error:'}
                      {chartItemData.errorMessage}
                    </div>
                  </div>
                </div>
              )
            }
            return (
              <ChartItem
                key={index}
                dataset={tempDataset}
                chartItemData={chartItemData}
                theme={theme}
                onDelete={handleDelete}
                onTableDataChange={handleTableDataChange}
              />
            )
          })}
        </div>
      </div>
    )
  }

  return (
    <div className={clsx('chart-manage-page flex h-full w-full flex-grow justify-center', className)}>
      <div className="flex w-[1024px] flex-col gap-1 md:px-6">
        {renderDatasetSelect}
        {renderChartList()}
      </div>
    </div>
  )
}

const ChartItem = (props: {
  chartItemData: ReadyChartResponse
  dataset: DatasetDatum | null
  theme: ThemeType
  onDelete: (id: string) => void
  onTableDataChange: (data: OlapRow[], chartId: string) => void
}) => {
  const navigate = useNavigate()
  const { chartItemData, dataset, theme, onDelete, onTableDataChange } = props

  const metricConfig = useAtomValue(metricConfigAtom)
  const setCurrentDataset = useSetAtom(currentDatasetAtom)

  const memoizedCard = React.useMemo(() => {
    const chartContentData: AssistantChartChatItem = {
      type: 'chart',
      sceneId: chartItemData.sceneId,
      sql: chartItemData.sql,
      chartTitle: chartItemData.chartTitle,
      chartType: chartItemData.chartType,
      originalChartType: chartItemData.chartType,
      rows: chartItemData.rows,
      originRows: chartItemData.originRows,
      recommendChartTypes: chartItemData.recommendChartTypes,
      rowsMetadata: chartItemData.rowsMetadata,
      chartThemeType: chartItemData.chartThemeType,
      taskType: chartItemData.taskType,
      isPartialRow: chartItemData.isPartialRow,
      partialRowMsg: chartItemData.partialRowMsg,
      infoTexts: chartItemData.infoTexts,
    }
    return (
      <Card
        title={chartItemData.chartTitle}
        theme={theme}
        className="w-full max-w-[350px] md:max-w-[468px]"
        bodyClassName="px-2"
        metricConfig={metricConfig}
        disableShare={true}
        disableDownload={true}
        disableChartTypeChange={true}
        chartType={chartItemData.chartType}
        data={chartItemData.rows}
        originalChartType={chartItemData.chartType}
        recommendChartTypes={chartItemData.recommendChartTypes}
        onChartTypeChange={() => void 0}
        showHeader={false}
      >
        <ChartWrapper
          data={chartContentData}
          theme={theme}
          onTableDataChange={(data) => {
            onTableDataChange(data, chartItemData.id)
          }}
        />
      </Card>
    )
  }, [chartItemData, theme, metricConfig, onTableDataChange])

  const handleEdit = (mode: 'overwrite' | 'duplicate') => {
    // 赋值currentDataset 跳转到新的界面
    setCurrentDataset(dataset)

    if (mode === 'overwrite') {
      navigate(askBIPageUrls.metricStore.charts.edit(chartItemData.id))
    } else if (mode === 'duplicate') {
      navigate(askBIPageUrls.metricStore.charts.copy(chartItemData.id))
    }
  }

  return (
    <div className="chartComponent h-fit w-[calc(100%-2rem)] rounded-md md:w-[calc(50%-0.25rem)]">
      <ChartItemHeader
        chartId={chartItemData.id}
        chartTitle={chartItemData.chartTitle}
        updatedAt={chartItemData.updatedAt}
        onDelete={onDelete}
        onEdit={handleEdit}
      />
      <div
        className={clsx(
          'flex h-[256px] items-center justify-center overflow-auto rounded-b-md border-8 border-t-0 border-solid bg-white dark:border-slate-300 dark:bg-slate-700',
          {
            'items-stretch':
              chartItemData.chartType === 'Kpi' ||
              chartItemData.chartType === 'SimpleTable' ||
              chartItemData.chartType === 'RankBarChart',
          },
        )}
      >
        {memoizedCard}
      </div>
    </div>
  )
}

const ChartItemHeader = (props: {
  chartId: string
  chartTitle: string
  updatedAt: Date
  onDelete: (id: string) => void
  onEdit?: (mode: 'overwrite' | 'duplicate') => void
}) => {
  const navigate = useNavigate()
  const { chartId, chartTitle, updatedAt, onDelete, onEdit } = props

  const chartMethodItems: MenuProps['items'] = [
    onEdit
      ? {
          key: 'edit-overwrite',
          label: (
            <div
              className={buttonClassName}
              onClick={() => {
                onEdit('overwrite')
              }}
            >
              <SvgIcon className="h-5 w-5" icon={editChartIcon} />
              编辑
            </div>
          ),
        }
      : null,
    onEdit
      ? {
          key: 'edit-duplicate',
          label: (
            <div
              className={buttonClassName}
              onClick={() => {
                onEdit('duplicate')
              }}
            >
              <SvgIcon className="h-5 w-5" icon={duplicateIcon} />
              复制
            </div>
          ),
        }
      : null,
    {
      key: 'delete-chart',
      label: (
        <Popconfirm
          onConfirm={() => {
            onDelete(chartId)
          }}
          title="删除此图表"
          description="图表删除后不可恢复，请确认!"
          okText="删除"
          cancelText="取消"
        >
          <div className={buttonClassName}>
            <SvgIcon className="h-5 w-5" icon={deleteIcon} />
            删除
          </div>
        </Popconfirm>
      ),
    },
  ]

  return (
    <div className="chart-header flex items-center gap-2 rounded-t-md border-2 p-2 dark:border-slate-300">
      <div className="mx-1">
        <SvgIcon className="h-5 w-5" icon={chartDescriptionIcon} />
      </div>
      <div className="chart-item-title w-full">
        <div className="flex flex-col gap-1">
          <div
            className="flex cursor-pointer font-bold"
            onClick={() => {
              navigate(askBIPageUrls.metricStore.charts.detail(chartId))
            }}
          >
            <span className="w-0 flex-1 overflow-hidden text-ellipsis whitespace-nowrap">{chartTitle}</span>
          </div>
          <div
            className={clsx('flex text-sm', {
              'leading-[44px]': !chartTitle,
            })}
          >
            <span className="w-0 flex-1 overflow-hidden text-ellipsis whitespace-nowrap">
              最近修改于 {dayjs(updatedAt).format('YYYY-MM-DD HH:mm:ss')}
            </span>
          </div>
        </div>
      </div>
      <Dropdown menu={{ items: chartMethodItems }} placement="bottomRight">
        <a onClick={(e) => e.preventDefault()} className="cursor-pointer text-black">
          <SvgIcon icon={omitIcon} className="mr-1 h-6 w-6" />
        </a>
      </Dropdown>
    </div>
  )
}

export default ChartManagePage
