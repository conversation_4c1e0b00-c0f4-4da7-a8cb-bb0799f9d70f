import { XGroup, XRole, XUser, Casbin<PERSON>ule, Prisma } from '@prisma/client'
import { SelectProps } from 'antd'
import React, { ReactNode, useCallback } from 'react'
import {
  actionTypeMap,
  ActionTypes,
  extractInfoFromEnforcer,
  ResourceTypes,
  ruleTypeMap,
  RuleTypes,
} from 'src/shared/auth'
import { Llm } from 'src/shared/common-types'

export type Scene = Prisma.SemanticSceneGetPayload<{ include: { semanticProject: true } }> & { modelNames?: string[] }

export type Project = Prisma.SemanticProjectGetPayload<{ include: { semanticScenes: true } }> & {
  scenes?: Scene[]
  semanticScenes?: Scene[]
}

export type Resource = Prisma.XResourceGetPayload<{ include: { rules: true } }>

export interface ResourceOps {
  toOptions: (v: { projects?: Project[]; llms?: Llm[] }) => SelectProps['options']
  toTypeDataDisplay: (v: any, raw: string[]) => ReactNode
}

export interface RuleOps {
  toOptions: (v: { users?: XUser[]; roles?: XRole[]; groups?: XGroup[] }) => SelectProps['options']
  toRuleDisplay: (v: { users?: XUser[]; roles?: XRole[]; groups?: XGroup[]; rule: CasbinRule }) => ReactNode
}

const allOptions: NonNullable<SelectProps['options']> = [
  {
    label: 'ALL',
    value: '*',
  },
]

const defaultOptions: SelectProps['options'] = []

const toDefaultOptions: () => SelectProps['options'] = () => defaultOptions

const toDefaultDisplay: () => ReactNode = () => <></>

const projectResourceOps: ResourceOps = {
  toOptions: ({ projects }) =>
    allOptions.concat(projects?.map(({ id, name }) => ({ value: id, label: name })) ?? defaultOptions),
  toTypeDataDisplay: (projects: Project[], raw) => [
    ...(projects?.map((v) => <div key={v.id}>{v.name}</div>) ?? []),
    ...(raw?.filter((v) => !projects.find((p) => p.id === v)).map((v, i) => <div key={i}>{v}</div>) ?? []),
  ],
}

const sceneResourceOps: ResourceOps = {
  toOptions: ({ projects }) =>
    allOptions.concat(
      projects?.map(({ id, name, scenes }) => {
        return {
          label: name,
          title: id,
          options: scenes?.map(({ id, label }) => {
            return {
              label: label,
              value: id,
            }
          }),
        }
      }) ?? defaultOptions,
    ),
  toTypeDataDisplay: (scenes: Scene[], raw) => [
    ...(scenes?.map((v) => (
      <div key={v.id}>
        {v.semanticProject?.name}-{v.label}
      </div>
    )) ?? []),
    ...(raw?.filter((v) => !scenes.find((p) => p.id === v)).map((v, i) => <div key={i}>{v}</div>) ?? []),
  ],
}

const llmResourceOps: ResourceOps = {
  toOptions: ({ llms }) =>
    allOptions.concat(llms?.map(({ type, name }) => ({ label: name, value: type })) ?? defaultOptions),
  toTypeDataDisplay: (llms: Llm[], raw) => [
    ...(llms?.map((v) => <div key={v.id}>{v.name}</div>) ?? []),
    ...(raw?.filter((v) => !llms.find((p) => p.type === v)).map((v, i) => <div key={i}>{v}</div>) ?? []),
  ],
}

const pageResourceOps: ResourceOps = {
  toOptions: () => allOptions,
  toTypeDataDisplay: (_, raw) => [...(raw?.map((v, i) => <div key={i}>{v}</div>) ?? [])],
}

const defaultResourceOps: ResourceOps = {
  toOptions: toDefaultOptions,
  toTypeDataDisplay: toDefaultDisplay,
}

const resourceOpsMap: Record<ResourceTypes, ResourceOps> = {
  project: projectResourceOps,
  scene: sceneResourceOps,
  llm: llmResourceOps,
  page: pageResourceOps,
}

const roleRuleOps: RuleOps = {
  toOptions: ({ roles }) => roles?.map(({ id, roleName }) => ({ value: id, label: roleName })) ?? defaultOptions,
  toRuleDisplay: ({ roles, rule }) => {
    const { id, type } = extractInfoFromEnforcer(rule.v0!)
    const role = roles?.find(({ id: vid }) => id === vid)
    if (!role) return <div key={rule.id + rule.v0}>未知角色</div>
    return (
      <div key={rule.id + rule.v0}>
        {ruleTypeMap[type as RuleTypes].label}
        <b>{role.roleName}</b>
        拥有<b>{actionTypeMap[rule.v2 as ActionTypes].label}</b>权限
      </div>
    )
  },
}

const userRuleOps: RuleOps = {
  toOptions: ({ users }) => users?.map(({ id, username }) => ({ value: id, label: username })) ?? defaultOptions,
  toRuleDisplay: ({ users, rule }) => {
    const { id, type } = extractInfoFromEnforcer(rule.v0!)
    const user = users?.find(({ id: vid }) => id === vid)
    if (!user) return <div key={rule.id + rule.v0}>未知用户</div>
    return (
      <div key={rule.id + rule.v0}>
        {ruleTypeMap[type as RuleTypes].label}
        <b>{user.username}</b>拥有<b>{actionTypeMap[rule.v2 as ActionTypes].label}</b>权限
      </div>
    )
  },
}

const ruleOpsMap: Record<RuleTypes, RuleOps> = {
  role: roleRuleOps,
  user: userRuleOps,
}

const defaultRuleOps: RuleOps = {
  toOptions: toDefaultOptions,
  toRuleDisplay: toDefaultDisplay,
}

export function useResourceOps() {
  const getResourceOps = useCallback(
    (type?: ResourceTypes): ResourceOps => resourceOpsMap[type!] ?? defaultResourceOps,
    [],
  )
  const getRuleOps = useCallback((type?: RuleTypes): RuleOps => ruleOpsMap[type!] ?? defaultRuleOps, [])
  return { getResourceOps, getRuleOps }
}
