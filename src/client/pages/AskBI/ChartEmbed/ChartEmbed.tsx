/**
 * @description 嵌入图表的页面
 */
import React, { useState } from 'react'
import { useAtomValue } from 'jotai'
import { useParams } from 'react-router-dom'
import { useRequest } from 'ahooks'
import axios, { AxiosResponse } from 'axios'
import { produce } from 'immer'
import { APIResponse, AssistantChartChatItem, ReadyChartResponse } from '@shared/common-types'
import ChartWrapper from '@charts/ChartWrapper'
import Card from 'src/client/charts/Card'
import { askBIApiUrls } from 'src/shared/url-map'
import { metricConfigAtom, themeAtom } from '../askBIAtoms'

const textCenterClass = 'flex h-screen items-center justify-center z-1'

export function ChartEmbedComponent(props: { className: string }) {
  const { className } = props
  const { chartId } = useParams()
  const theme = useAtomValue(themeAtom)
  const metricConfig = useAtomValue(metricConfigAtom)
  const [chartData, setChartData] = useState<ReadyChartResponse | null | undefined>(null)

  const { error: isError, loading: isLoading } = useRequest<AxiosResponse<APIResponse<ReadyChartResponse>>, unknown[]>(
    () => {
      if (chartId != null) {
        return axios.get(askBIApiUrls.charts.detail(chartId))
      } else {
        return Promise.reject('chartId is null')
      }
    },
    {
      onSuccess(response) {
        setChartData(response.data?.data)
      },
    },
  )

  if (chartId == null) {
    return <div className={textCenterClass}>URL 错误，找不到 chart id</div>
  }

  if (isLoading) {
    return <div className={textCenterClass}>正在加载图表...</div>
  }

  if (isError) {
    return (
      <div className={textCenterClass}>
        加载图表出错，请联系管理员排查，具体原因如下：
        <br />
        {isError.message}
      </div>
    )
  }

  console.info('chartData ===', chartData)
  if (chartData == null) {
    return <div>图表不存在，或图表的信息为空</div>
  }

  const chartContent: AssistantChartChatItem = {
    type: 'chart',
    chartType: chartData.chartType,
    originalChartType: chartData.chartType,
    chartTitle: chartData.chartTitle,
    rows: chartData.rows,
    originRows: chartData.originRows,
    sql: chartData.sql,
    sceneId: chartData.sceneId,
    recommendChartTypes: chartData.recommendChartTypes,
    rowsMetadata: chartData.rowsMetadata,
    chartThemeType: chartData.chartThemeType,
    taskType: chartData.taskType,
    isPartialRow: chartData.isPartialRow,
    partialRowMsg: chartData.partialRowMsg,
    infoTexts: chartData.infoTexts,
  }

  return (
    <Card
      title={chartData.chartTitle}
      theme={theme}
      className={className}
      metricConfig={metricConfig}
      bodyClassName="px-2 md:p-4"
      disableShare={true}
      disableDownload={true}
      disableChartTypeChange={true}
      chartType={chartData.chartType}
      data={chartData.rows}
      originalChartType={chartData.chartType}
      recommendChartTypes={chartData.recommendChartTypes}
      onChartTypeChange={() => void 0}
    >
      <ChartWrapper
        data={chartContent}
        theme={theme}
        onTableDataChange={(data) => {
          setChartData((prevChartData) =>
            produce(prevChartData, (draft) => {
              if (draft) {
                draft.rows = data
              }
            }),
          )
        }}
      />
    </Card>
  )
}

const ChartEmbedPage = () => {
  // 启动 embed 模式有 2 种 case：被 iframe 嵌入自动启动，或者 url 中带有 embed=true 的参数
  const isInFrame = window.self !== window.top
  const enableEmbed = isInFrame || window.location.search?.includes('embed=true')
  if (enableEmbed) {
    return (
      <div className="chart-embed-page flex h-screen w-full items-center justify-center">
        <ChartEmbedComponent className="h-full w-full" />
      </div>
    )
  }

  return (
    <div className="chart-embed-page m-auto flex h-screen flex-col items-center justify-center bg-[#183a5e]">
      <img src="/img/embed-bg.webp" alt="" className="embed-bg fixed left-0 top-0 blur-3xl" />
      <h1 className="relative mb-4 text-slate-300">请复制页面 URL 并嵌入 BI 系统，你将只看到如下图表：</h1>
      <ChartEmbedComponent className="relative bg-white hover:shadow-lg md:min-w-[532px]" />
    </div>
  )
}

export default ChartEmbedPage
