/**
 * 场景doc配置
 */
import { CloudUploadOutlined, SearchOutlined } from '@ant-design/icons'
import { useRequest } from 'ahooks'
import { App, Button, Form, Input, PaginationProps, Select, Table, Tooltip } from 'antd'
import axios from 'axios'
import dayjs from 'dayjs'
import { nanoid } from 'nanoid'
import React, { useEffect, useState } from 'react'
import debounce from 'lodash/debounce'
import { useAtom, useAtomValue } from 'jotai'
import { Doc, DocumentSearchType } from 'src/shared/askdoc-types'
import { askBIApiUrls, askDocApiUrls } from 'src/shared/url-map'
import { FileMimeTypeInfo } from 'src/shared/common-types'
import { Scenario } from 'src/shared/metric-types'
import { SvgIcon } from 'src/client/components/SvgIcon'
import { docStatusMap, getIconComponent } from 'src/client/utils'
import CustomUploadModal from 'src/client/components/CustomUploadModal'
import { mimeTypeList } from 'src/shared/constants'
import useIsAllowedRanger from 'src/client/hooks/useIsAllowedRanger'
import { selectFileIdsAtom } from '../../AskDoc/askDocAtoms'
import { brandInfoAtom, currentLoginUserAtom } from '../askBIAtoms'

const DefaultPagination = {
  pageSize: 10,
  current: 1,
  total: 0,
}

const width = 190

interface Props {
  isModalInstance?: boolean // 表示是弹窗进来的
}

export default function DocumentData(props: Props) {
  const { message: antdMessage } = App.useApp()
  const { isModalInstance } = props
  const [form] = Form.useForm()
  const [pagination, setPatination] = useState<PaginationProps>(DefaultPagination)
  const [fileList, setFileList] = useState<Doc[]>([]) // 显示数据
  const [listTotal, setListTotal] = useState<number>(0) // 待搜索文件类型
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [scenesList, setSceneList] = useState<Scenario[]>([])
  const [filterParams, setFilterParams] = useState<DocumentSearchType | {}>({})
  const [selectFileIds, setSelectFileIds] = useAtom(selectFileIdsAtom)
  const [isShowUploadModal, setIsShowUploadModal] = useState(false)
  const brandInfo = useAtomValue(brandInfoAtom)
  const currentLoginUser = useAtomValue(currentLoginUserAtom)
  const isAdmin = useIsAllowedRanger()

  useRequest(
    async () => {
      const response = await axios.get(askBIApiUrls.auth.scene.rest, {
        params: { current: 1, pageSize: 1000 },
      })
      setSceneList(response.data?.data?.list || [])
    },
    {
      onError: (error) => {
        console.error('获取场景信息失败', error)
      },
    },
  )

  // 获取文档数据列表
  const { run: getDocumentList } = useRequest(
    async ({ fileName, fileType, sceneId }: DocumentSearchType, pagination: PaginationProps) => {
      const queryData = {
        sceneId: sceneId || '',
        fileName: fileName || '',
        fileType: fileType || '',
        creator: !isAdmin ? currentLoginUser?.username : '',
        page: pagination.current,
        pageSize: pagination.pageSize,
      }
      const response = await axios.get(askDocApiUrls.fileListV2, { params: queryData })
      const res = response.data.data.files
      setFileList(res || [])
      setListTotal(response.data.data?.total || 0)
      setIsLoading(false)
      setPatination({
        ...pagination,
        total: response.data.data?.total,
      })
    },
    {
      manual: true,
      onError: (error: any) => {
        setIsLoading(false)
        console.error('获取文档数据失败 =', error)
        antdMessage.error(error?.response.data.msg || '获取文档数据失败')
      },
    },
  )

  useEffect(() => {
    getDocumentList({}, DefaultPagination)
  }, [getDocumentList])

  // Table 分页
  const handlePageChange = (pagination: PaginationProps) => {
    const { current, pageSize } = pagination
    // getDatasourceList(pagination)
    setPatination({ ...pagination, current, pageSize })
    getDocumentList(filterParams, pagination)
  }

  // 上传文档点击事件
  const handleUploadFile = () => {
    console.info('upload')
    setIsShowUploadModal(true)
  }

  const handleValuesChange = (_changedValues: any, allValues: DocumentSearchType) => {
    // 这里调用接口查询，传入当前表单的所有值
    setFilterParams(allValues)
    getDocumentList(allValues, pagination)
  }

  const debounceHandleValuesChange = debounce(handleValuesChange, 500)

  const onFinishUpload = () => {
    getDocumentList({}, DefaultPagination)
  }

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectFileIds(newSelectedRowKeys.map(Number))
  }

  const rowSelection = {
    selectedRowKeys: selectFileIds,
    onChange: onSelectChange,
  }
  const hasSelected = selectFileIds.length > 0

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 300,
      render: (value: string, record: Doc) => {
        return (
          <div className="my-2 flex items-center">
            <SvgIcon icon={getIconComponent(record.mimeType as FileMimeTypeInfo)} className="mr-2 h-5 w-5" />
            <Tooltip title={value}>
              <p className="w-64 truncate text-xs font-medium text-link">{value}</p>
            </Tooltip>
          </div>
        )
      },
    },
    {
      title: '类型',
      dataIndex: 'mimeType',
      key: 'mimeType',
      render: (value: string) => {
        return <p className="text-xs text-[#171717]">{mimeTypeList.find((item) => item.value === value)?.label}</p>
      },
    },
    {
      title: '文档状态',
      dataIndex: 'fileStatus',
      key: 'fileStatus',
      render: (value: string) => {
        /** 文档 embed 的状态 Pending异常 Ready解析中 Done解析完成 Fail失败 */
        const status = docStatusMap[value] || { label: value, colorClass: 'text-gray-900' } // 默认颜色
        return <p className={`text-xs ${status.colorClass} font-medium`}>{status.label}</p>
      },
    },
    {
      title: '来源',
      dataIndex: 'source',
      key: 'source',
      render: (value: string) => {
        return <p className="text-xs text-[#171717]">{value}</p>
      },
    },
    {
      title: '关联场景',
      dataIndex: 'sceneIdList',
      key: 'sceneIdList',
      render: (value: string[]) => {
        if (value && scenesList) {
          const selectedSceneNames = (scenesList || [])
            .filter((scene) => value.includes(scene.id.toString()))
            .map((scene) => scene.label)
            .join(', ')
          return <p className="text-xs text-link">{selectedSceneNames}</p>
        }
        return <p className="text-xs text-link">{''}</p>
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdTime',
      key: 'createdTime',
      render: (value: string) => {
        return <p className="text-xs text-[#171717]"> {dayjs(Number(value) * 1000).format('YYYY-MM-DD HH:mm:ss')}</p>
      },
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      key: 'creator',
      render: (value: string) => {
        return (
          <>
            {value && (
              <div className="flex items-center">
                <img className="mr-2 h-4 w-4 cursor-pointer" src={brandInfo?.chatUserIcon} alt="" />
                <p>{value}</p>
              </div>
            )}
          </>
        )
      },
    },
  ]

  return (
    <div>
      <div className="flex h-9 items-center justify-between">
        <div className="flex items-center">
          <p className="mr-2 text-black">共{listTotal}个</p>
          <div className="items-center justify-center rounded bg-[#D0F2EA] px-2 py-1.5 text-xs">
            已选
            <span className="ml-2 font-medium"> {hasSelected ? selectFileIds.length : 0}</span>
          </div>
        </div>
        {!isModalInstance && (
          <Button type="primary" icon={<CloudUploadOutlined />} onClick={() => handleUploadFile()}>
            上传文档
          </Button>
        )}
      </div>
      <div className="my-3 flex">
        <Form layout={'inline'} form={form} onValuesChange={debounceHandleValuesChange} autoComplete="off">
          <Form.Item label="" name="fileName">
            <Input placeholder={'文件名'} style={{ width }} prefix={<SearchOutlined />} />
          </Form.Item>
          <Form.Item label="类型" name="fileType">
            <Select options={mimeTypeList} placeholder={'类型'} style={{ width }} />
          </Form.Item>
          <Form.Item label="场景" name="sceneId">
            <Select
              mode="tags"
              placeholder={'场景'}
              style={{ minWidth: width }}
              options={(scenesList || []).map((item) => ({ label: item.label, value: item.id }))}
            />
          </Form.Item>
          {/* <Form.Item label="创建人" name="creator">
            <Input placeholder={'创建人'} style={{ width }} />
          </Form.Item> */}
        </Form>
      </div>

      <Table
        rowSelection={rowSelection}
        loading={isLoading}
        columns={columns}
        dataSource={fileList}
        size="small"
        rowKey={(record) => record.id || nanoid()} // 确保每一行都有唯一的 key
        pagination={{ ...pagination, hideOnSinglePage: true }}
        scroll={{ x: 'max-content' }}
        onChange={handlePageChange}
      />

      <CustomUploadModal
        isShowUploadModal={isShowUploadModal}
        setIsShowUploadModal={setIsShowUploadModal}
        onGetFoldersList={onFinishUpload}
        isDragUpload={true}
        multiple={true}
        accept={'.pdf,.docx,.xlsx'}
        folderId={''}
        uploadType={'doc'}
      />
    </div>
  )
}
