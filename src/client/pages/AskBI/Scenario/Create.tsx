import React, { useEffect, useState } from 'react'
import clsx from 'clsx'
import { App, Steps, Tabs, Button, Space, ConfigProvider, message } from 'antd'
import axios from 'axios'
import { useRequest } from 'ahooks'
import { useNavigate } from 'react-router-dom'
import { useForm } from 'antd/es/form/Form'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import zhCN from 'antd/lib/locale/zh_CN'
import { askBIApiUrls, askBIPageUrls, askDocApiUrls } from 'src/shared/url-map'
import PageHeader from 'src/client/components/PageHeader'
import { getAntdConfigProviderTheme } from 'src/client/utils'
import { BotFeature, APIResponse } from 'src/shared/common-types'
import { ProjectType } from 'src/shared/metric-types'
import { NullComponent } from '../../MetricStore/components/NullComponent'
import {
  configScene<PERSON>ara<PERSON><PERSON><PERSON>,
  currentD<PERSON>set<PERSON>tom,
  currentLoginUser<PERSON>tom,
  requestDefault<PERSON><PERSON>set<PERSON>tom,
  theme<PERSON>tom,
  semanticProjectInfoAtom,
} from '../askBIAtoms'
import { selectFileIdsAtom } from '../../AskDoc/askDocAtoms'
import ScenarioBasicInfoForm from './ScenarioBasicInfoForm'
import ScenarioConfig from './ScenarioConfig'
import DocumentData from './DocumentData'
import type { IScenarioConfigType } from './ScenarioConfig'

const StepItems = [
  {
    key: 'basicInfo',
    title: '场景基础信息',
  },
  {
    key: 'dataConfig',
    title: '数据配置',
  },
]

interface BasicInfoType {
  label: string
  projectId: string
  agent: string[]
  description: string
  iconType: number
}

const ScenariosCreate = ({ className }: { className?: string }) => {
  const [activeKey, setActiveKey] = useState<'basicInfo' | 'dataConfig'>('basicInfo')
  const [basicInfo, setBasicInfo] = useState<BasicInfoType | null>(null)
  const [configInfo, setConfigInfo] = useState<IScenarioConfigType>()
  const [selectAgent, setSelectAgent] = useState<string[]>([])
  const [currentBotActive, setCurrentBotActive] = useState<BotFeature>('BI')
  const [selectFileIds, setSelectFileIds] = useAtom(selectFileIdsAtom)
  const currentUserInfo = useAtomValue(currentLoginUserAtom)
  const [_configSceneParams, setConfigSceneParams] = useAtom(configSceneParamsAtom)
  const setSemanticProjectInfoAtom = useSetAtom(semanticProjectInfoAtom)
  const [basicInfoForm] = useForm()

  const navigate = useNavigate()

  // 文件id和场景 关联
  const handleAddSceneFile = async (id: string) => {
    try {
      if (selectFileIds.length === 0) return message.warning('请选择要关联的文档')
      await axios.post(askDocApiUrls.addSceneFile, { sceneId: id, fileIds: selectFileIds })
    } catch (error) {
      console.error(error)
    }
  }

  const { run: createScenario, loading: isCreateScenarioLoading } = useRequest(
    async (args) => {
      const { agent = [], ...restArgs } = args || {}
      const paramsData = {
        ...restArgs,
        agent: Array.isArray(agent) ? agent.join(',') : '',
        creationUser: currentUserInfo?.username,
      }
      // 如果只有一个agent并且是doc的时候
      if (agent.length === 1 && agent.includes('Doc')) {
        paramsData.tableName = ''
      }

      const response = await axios.post(askBIApiUrls.auth.scene.rest, paramsData)
      return response.data.data
    },
    {
      manual: true,
      async onSuccess(data, args) {
        message.success('创建成功')
        const { data: projectInfo } = (
          await axios.get<APIResponse<{ projects: ProjectType[]; DEFAULT_SELECT_PROJECT: boolean }>>(
            askBIApiUrls.auth.projects,
          )
        ).data
        const semanticProjectInfoData = projectInfo?.projects
        setSemanticProjectInfoAtom(semanticProjectInfoData || [])
        if (data) {
          if (data.agent.includes('Doc')) {
            handleAddSceneFile(data.id)
          }
          const queryObject = new URLSearchParams({
            projectId: data.projectId || '',
            scenarioId: data.id || '',
            name: args[0].modelId || '',
          })
          navigate(`${askBIPageUrls.scenarios.detail}?${queryObject.toString()}`)
        }
        setSelectFileIds([])
        setConfigSceneParams([])
      },
      onError: (error: any) => {
        console.error('get currentDimensionInfo =', error)
        let msg = '创建失败'
        if (error.message) {
          msg += `：${error.message}`
        }
        message.error(msg)
      },
    },
  )
  const [currentStepIndex, setCurrentStepIndex] = useState(0)

  async function handleStepChange(changeNumber: number) {
    if (changeNumber === 1) {
      const formStatus = await basicInfoForm.validateFields()
      const { agent } = basicInfoForm.getFieldsValue()
      setCurrentBotActive(agent.length === 1 && agent.includes('Doc') ? 'Doc' : 'BI')
      setSelectAgent(agent)
      if (formStatus.errorFields?.length > 0) {
        return
      }
      basicInfoForm.submit()
    }

    const nextStepIndex = currentStepIndex + changeNumber

    const item = StepItems[nextStepIndex]
    if (item) {
      setCurrentStepIndex(nextStepIndex)
      setActiveKey(item.key as 'basicInfo' | 'dataConfig')
    }
  }

  useEffect(() => {
    const preColor = document.body.style.backgroundColor
    document.body.style.backgroundColor = '#F4F4F4'
    return () => {
      document.body.style.backgroundColor = preColor
    }
  }, [])

  const [theme, _setTheme] = useAtom(themeAtom)
  const requestDefaultDataset = useSetAtom(requestDefaultDatasetAtom)
  const currentDataset = useAtomValue(currentDatasetAtom)

  // 加载默认的 dataset
  useEffect(() => {
    // 先判断 currentDatasetAtom 是否为空，当为空的时候才去请求
    if (currentDataset == null) {
      requestDefaultDataset()
    }
  }, [currentDataset, requestDefaultDataset])

  const handleAskBotChange = (e: string) => {
    setCurrentBotActive(e as BotFeature)
  }

  // 是否有ranger的权限
  return (
    <ConfigProvider locale={zhCN} theme={getAntdConfigProviderTheme(theme)}>
      <App>
        <div
          className={clsx(
            'layout-root relative flex h-screen flex-col overflow-hidden text-black dark:bg-slate-900 dark:text-slate-100 md:overflow-visible',
          )}
        >
          <div className={className}>
            <PageHeader
              className="padding-[12px] items-center justify-end gap-[12px] bg-white"
              extra={
                <div className="w-[240px] text-right">
                  <Space>
                    <Button
                      onClick={() => {
                        navigate(askBIPageUrls.manage.scenarios.manage)
                      }}
                    >
                      取消
                    </Button>
                    <Button
                      className="m-[12px]"
                      type="primary"
                      onClick={() => {
                        if (currentStepIndex === StepItems.length - 1) {
                          if (basicInfo && basicInfo.agent.length === 1 && basicInfo.agent.includes('BI')) {
                            if (!configInfo?.modelId) {
                              return message.error('请选择模型')
                            }
                          }
                          createScenario({ ...(basicInfo || {}), ...(configInfo || {}) })
                        } else {
                          handleStepChange(+1)
                        }
                      }}
                    >
                      {currentStepIndex === StepItems.length - 1 ? '完成' : '下一步'}
                    </Button>
                  </Space>
                </div>
              }
              title={
                <div className="flex items-center">
                  <div className="inline-block w-[140px] text-left">
                    {currentStepIndex > 0 && (
                      <Button
                        className="m-[12px]"
                        onClick={() => {
                          handleStepChange(-1)
                        }}
                      >
                        上一步
                      </Button>
                    )}
                  </div>
                </div>
              }
            />
            <div className="m-[auto] my-5 w-[320px] text-[13px] font-medium leading-5">
              <Steps progressDot current={currentStepIndex} size="small" items={StepItems} />
            </div>
            <div
              className={clsx(
                "text-grey-900 font-['PingFang SC'] mx-auto flex w-full flex-grow flex-col justify-start gap-2 overflow-y-auto pt-1 text-[13px] text-base font-medium leading-5 dark:text-slate-100 md:max-w-screen-lg md:py-5",
                {
                  'pl-2': selectAgent.length === 1,
                  'pl-5': selectAgent.length > 1,
                },
              )}
            >
              {StepItems[currentStepIndex].title}
            </div>
            {/* <button onClick={createScenario}>createScenario</button> */}

            <div className="scenario-list-page flex h-full w-full justify-center">
              {selectAgent.length > 1 && currentStepIndex > 0 && (
                <div className="-ml-20">
                  <Tabs
                    onChange={handleAskBotChange}
                    tabPosition={'left'}
                    items={[
                      {
                        label: 'BI数据',
                        key: 'BI',
                      },
                      {
                        label: '文档数据',
                        key: 'Doc',
                      },
                    ]}
                  />
                </div>
              )}
              <div
                className={clsx(
                  'scenario-list-page flex h-full w-full flex-grow flex-col justify-start gap-2 overflow-y-auto bg-white p-3 pt-1 text-base text-black dark:bg-slate-900 dark:text-slate-100 md:max-w-screen-lg md:p-6 md:pt-3',
                )}
              >
                <Tabs
                  activeKey={activeKey}
                  defaultActiveKey="batch"
                  className="rounded-tremor-default flex min-h-[360px] w-full max-w-full flex-col rounded-xl bg-white dark:bg-slate-700"
                  centered
                  renderTabBar={() => <NullComponent />}
                  items={[
                    {
                      key: 'basicInfo',
                      label: `场景基础配置`,
                      children: (
                        <ScenarioBasicInfoForm
                          form={basicInfoForm}
                          onOk={(info) => {
                            setCurrentStepIndex(1)
                            setBasicInfo(info)
                            setActiveKey('dataConfig')
                          }}
                        />
                      ),
                    },
                    {
                      key: 'dataConfig',
                      label: `数据配置`,
                      children: (
                        <>
                          {currentBotActive === 'BI' ? (
                            <ScenarioConfig
                              createLoading={isCreateScenarioLoading}
                              onOk={(configInfo) => {
                                setConfigInfo(configInfo)
                              }}
                            />
                          ) : (
                            <DocumentData />
                          )}
                        </>
                      ),
                    },
                  ]}
                />
              </div>
            </div>
          </div>
        </div>
      </App>
    </ConfigProvider>
  )
}

export default ScenariosCreate
