import { App, But<PERSON>, Dropdown, MenuProps, Space, Typography, type TableProps } from 'antd'
import React, { useRef, useState, useCallback } from 'react'
import clsx from 'clsx'
import axios from 'axios'
import { EyeOutlined, PlusOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import { useAtom } from 'jotai'
import { MetricModelType } from '@shared/metric-types'
import { askBIApiUrls } from 'src/shared/url-map'
import { APIResponse } from 'src/shared/common-types'
import ReadingMetricModelDrawer from 'src/client/pages/MetricStore/components/ReadingMetricModelDrawer'
import ValidatedUploadFile from 'src/client/components/ValidatedUploadFile'
import request from 'src/shared/xengineAxios'
import CountSearchTable from 'src/client/components/CountSearchTable'
import MetricModelCreate from '../../MetricStore/components/CreateMetricModelDrawer'
import { configScene<PERSON>ara<PERSON><PERSON>tom } from '../askBIAtoms'

const modelColumn: ({
  onClickPreviewModel,
}: {
  onClickPreviewModel: (record: { name: string }) => void
}) => TableProps<MetricModelType>['columns'] = ({ onClickPreviewModel }) => [
  {
    title: '指标模型',
    dataIndex: 'name',

    render(value, record) {
      return (
        <Space>
          {value}
          <Typography.Link onClick={() => onClickPreviewModel(record)}>
            <EyeOutlined /> 预览
          </Typography.Link>
        </Space>
      )
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    render(value) {
      return dayjs(value).format('YYYY-MM-DD HH:mm:ss')
    },
  },
]
export type IScenarioConfigType = {
  modelNames: string[]
  timeDimensionFormat?: string
  timeGranularityMin?: string
  aggTimeDimension?: string
  timeDimensionType?: string
  modelId: string
  tableName: string
}

export default function ScenarioConfig({
  className,
  onOk,
}: {
  createLoading: boolean
  onOk: (configInfo: IScenarioConfigType) => void
  className?: string
  updateList?: () => void
}) {
  const { message: antdMessage } = App.useApp()
  const [drawerOpen, setDrawerOpen] = useState(false)
  const [previewMetricModelDrawerOpen, setPreviewMetricModelDrawerOpen] = useState(false)
  const [previewName, setPreviewName] = useState('')
  const [configSceneParams, setConfigSceneParams] = useAtom(configSceneParamsAtom)
  const actionRef = useRef<{ refresh: () => void; reload: () => void }>()
  const modelListRef = useRef<MetricModelType[]>()
  const [createModelType, setCreateModelType] = useState<'createMetricModel' | 'createNestModel'>('createMetricModel')
  const [uploadCSVModalOpen, setUploadCSVModalOpen] = useState(false)
  const items: MenuProps['items'] = [
    {
      key: 'createMetricModel',
      label: (
        <span
          onClick={() => {
            setCreateModelType('createMetricModel')
            setDrawerOpen(true)
            return
          }}
        >
          创建指标模型
        </span>
      ),
    },
    {
      key: 'createNestModel',
      label: (
        <span
          onClick={() => {
            setCreateModelType('createNestModel')
            setDrawerOpen(true)
            return
          }}
        >
          创建嵌套模型
        </span>
      ),
    },
    {
      key: 'CSVUpload',
      label: (
        <span
          onClick={() => {
            setUploadCSVModalOpen(true)
          }}
        >
          CSV文件创建
        </span>
      ),
    },
  ]

  const rowSelection = {
    selectedRowKeys: configSceneParams?.modelNames || [],
    onChange: async (selectedRowKey: React.Key[]) => {
      const selectedModelId = selectedRowKey[0].toString()
      const formatMap = { YEAR: 'yyyy', MONTH: 'yyyyMM', DAY: 'yyyyMMDD' }
      const modelList = modelListRef.current || []
      const model = modelList?.find((i) => i.name === selectedModelId)
      if (model) {
        const completeModelData =
          (await request.get<{ name: string }, MetricModelType>(askBIApiUrls.model.meta, {
            params: { name: selectedModelId },
          })) || {}
        const { timeColumnDesc } = completeModelData.dataModelDesc || {}
        const data: IScenarioConfigType = {
          modelNames: [selectedModelId],
          modelId: selectedModelId,
          tableName: completeModelData.dataModelDesc?.factTable,
        }
        if (timeColumnDesc) {
          const columnType = timeColumnDesc.column?.type
          Object.assign(data, {
            timeDimensionFormat: formatMap[timeColumnDesc.granularity as keyof typeof formatMap],
            timeGranularityMin: timeColumnDesc.granularity?.toLowerCase?.(),
            aggTimeDimension: timeColumnDesc.column?.name,
            timeDimensionType: /CHAR|text/gi.test(columnType) ? 'string' : columnType,
          })
        }
        onOk(data)
        setConfigSceneParams(data)
      }
    },
  }

  return (
    <div
      className={clsx(
        'metric-list-page mx-auto flex w-full flex-grow flex-col gap-2 overflow-y-auto bg-white text-base text-black dark:bg-slate-900 dark:text-slate-100 md:max-w-screen-lg',
        className,
      )}
    >
      <CountSearchTable
        className="mb-4"
        isFullList
        extra={
          <Dropdown menu={{ items }} placement="bottomRight" arrow>
            <Button className="mr-2" icon={<PlusOutlined />}>
              指标模型
            </Button>
          </Dropdown>
        }
        api={useCallback(async () => {
          const response = await axios.get<APIResponse<{ list: MetricModelType[]; total: number }>>(
            askBIApiUrls.model.list,
            {
              params: { current: 1, pageSize: 1000 },
            },
          )
          const defaultData = { list: [], total: 0 }
          const res = response.data.data || defaultData
          modelListRef.current = res.list
          return res
        }, [])}
        actionRef={actionRef}
        searchKey="name"
        tableProps={{
          columns: modelColumn({
            onClickPreviewModel(record) {
              setPreviewMetricModelDrawerOpen(true)
              setPreviewName(record.name)
            },
          }),
          rowKey: 'name',
          rowSelection: {
            type: 'radio',
            ...rowSelection,
          },
          pagination: {
            hideOnSinglePage: true,
            showSizeChanger: true,
          },
        }}
      />
      <MetricModelCreate
        type={createModelType}
        drawerOpen={drawerOpen}
        setDrawerOpen={setDrawerOpen}
        onSuccess={() => {
          actionRef.current?.refresh && actionRef.current?.refresh()
        }}
      />
      <ReadingMetricModelDrawer
        open={previewMetricModelDrawerOpen}
        name={previewName}
        onClose={() => {
          setPreviewMetricModelDrawerOpen(false)
        }}
      />
      <ValidatedUploadFile
        uploadApi={(uploadFile) => {
          const formData = new FormData()
          uploadFile && formData.append('file', uploadFile)
          return request.post(askBIApiUrls.model.csvUpload, formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          })
        }}
        onSuccess={async () => {
          antdMessage.success('创建成功')
          setUploadCSVModalOpen(false)
          await new Promise((resolve) => setTimeout(resolve, 800))
          actionRef.current?.refresh && actionRef.current?.refresh()
        }}
        modalProps={{
          title: 'CSV文件上传',
          open: uploadCSVModalOpen,
          onCancel: () => setUploadCSVModalOpen(false),
        }}
        size={50}
        acceptTypes={['.csv']}
        samples={[
          {
            fileName: '示例.csv',
            fileUrl: `${askBIApiUrls.model.CSVUploadTemplate}?type=BI_DATA_MODEL`,
          },
        ]}
      />
    </div>
  )
}
