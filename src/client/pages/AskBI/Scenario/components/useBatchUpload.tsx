import { PlusOutlined } from '@ant-design/icons'
import { useBoolean } from 'ahooks'
import { App, Dropdown, Button } from 'antd'
import axios from 'axios'
import React, { useState } from 'react'
import ValidatedUploadFile from 'src/client/components/ValidatedUploadFile'
import { formatPathWithBaseUrl } from 'src/client/utils'
import { askBIApiUrls } from 'src/shared/url-map'

export function useBatchUpload({
  semanticProjectId,
  semanticModelId,
  semanticSceneId,
  refresh,
}: {
  semanticProjectId: string
  semanticModelId: string
  semanticSceneId: string
  refresh: () => void
}) {
  const { message } = App.useApp()
  const [validatedUploadFileModalVisible, validatedUploadFileModalVisibleOps] = useBoolean(false)
  const [mode, setMode] = useState<'create' | 'update'>('create')
  const label = mode === 'create' ? '创建' : '更新'
  const triggerNode = (
    <Dropdown
      menu={{
        items: [
          {
            key: 'create',
            label: (
              <span
                onClick={() => {
                  validatedUploadFileModalVisibleOps.setTrue()
                  setMode('create')
                }}
              >
                从XLSX文件创建指标
              </span>
            ),
          },
          {
            key: 'update',
            label: (
              <span
                onClick={() => {
                  validatedUploadFileModalVisibleOps.setTrue()
                  setMode('update')
                }}
              >
                从XLSX文件更新指标
              </span>
            ),
          },
          {
            key: 'download',
            label: (
              <span
                onClick={() => {
                  const link = document.createElement('a')
                  link.href = formatPathWithBaseUrl('/files/metric-upload.xlsx')
                  link.download = '示例.xlsx'
                  document.body.appendChild(link)
                  link.click()
                  link.remove()
                }}
              >
                下载XLSX模版
              </span>
            ),
          },
        ],
      }}
      placement="bottomRight"
      arrow
    >
      <Button type="primary" icon={<PlusOutlined />}>
        批量上传
      </Button>
    </Dropdown>
  )
  const modalNode = (
    <ValidatedUploadFile
      modalProps={{
        title: `从XLSX${label}指标`,
        open: validatedUploadFileModalVisible,
        onCancel: validatedUploadFileModalVisibleOps.setFalse,
      }}
      uploadApi={(uploadFile) => {
        if (!uploadFile) {
          return Promise.reject('文件不存在')
        }
        const formData = new FormData()
        formData.append('file', uploadFile)
        formData.append('semanticProjectId', semanticProjectId)
        formData.append('semanticModelId', semanticModelId)
        formData.append('semanticSceneId', semanticSceneId)
        return mode === 'create'
          ? axios.post(askBIApiUrls.metrics.createFromFile, formData, {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            })
          : axios.patch(askBIApiUrls.metrics.updateFromFile, formData, {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            })
      }}
      acceptTypes={['.xlsx']}
      samples={[
        {
          fileName: '示例.xlsx',
          fileUrl: formatPathWithBaseUrl('/files/metric-upload.xlsx'),
        },
      ]}
      onSuccess={() => {
        message.success(`指标${label}成功`)
        refresh()
        validatedUploadFileModalVisibleOps.setFalse()
      }}
    />
  )
  return { triggerNode, validatedUploadFileModalVisible, validatedUploadFileModalVisibleOps, modalNode }
}
