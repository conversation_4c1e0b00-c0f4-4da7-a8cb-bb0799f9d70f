import React from 'react'
import { <PERSON>pp, Button, Modal, Space } from 'antd'
import { useRequest } from 'ahooks'
import axios from 'axios'
import { askBIApiUrls } from 'src/shared/url-map'
import { SvgIcon, warningIcon } from 'src/client/components/SvgIcon'

type PropsType = {
  metric: { id: string; name: string; label: string }
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

export default function DeleteMetricModal(props: PropsType) {
  const { onClose, open, metric, onSuccess } = props
  const { message } = App.useApp()
  const { loading, run: deleteMetric } = useRequest(
    () => {
      return axios.delete(askBIApiUrls.metrics.delete(metric.id))
    },
    {
      manual: true,
      onSuccess() {
        message.success('删除成功')
        onSuccess && onSuccess()
      },
      onError() {
        message.error('删除失败')
      },
    },
  )
  function renderFooterButtons() {
    return (
      <Space>
        <Button
          className="font-[PingFang SC] w-[74px] rounded bg-white text-[13px] font-medium leading-5"
          onClick={() => {
            onClose && onClose()
          }}
        >
          关闭
        </Button>
        <Button
          className="font-[PingFang SC] mr-[6px] w-[74px] rounded text-[13px] font-medium leading-5"
          type="primary"
          loading={loading}
          onClick={() => {
            if (!metric.id) {
              message.error('缺少参数')
            } else {
              deleteMetric()
            }
          }}
        >
          确定
        </Button>
      </Space>
    )
  }

  return (
    <Modal
      onCancel={() => {
        onClose && onClose()
      }}
      className="grey-modal-footer"
      title={<div className="pl-5">删除确认</div>}
      open={open}
      footer={<div className="rounded-b bg-[#F4F4F4] px-5 py-3">{renderFooterButtons()}</div>}
    >
      <div className="mb-[40px] mt-[20px] flex items-center justify-center">
        <SvgIcon icon={warningIcon} className="mr-4 h-6 w-6" />
        <div>该指标将被删除</div>
      </div>
    </Modal>
  )
}
