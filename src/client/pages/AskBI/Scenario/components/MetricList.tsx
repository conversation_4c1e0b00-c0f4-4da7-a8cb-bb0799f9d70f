import React, { use<PERSON>allback, useEffect, useMemo, useRef, useState } from 'react'
import { useLocation } from 'react-router-dom'
import { <PERSON><PERSON>, <PERSON><PERSON>, App, Popconfirm, notification } from 'antd'
import axios from 'axios'
import { useBoolean } from 'ahooks'
import { MoreOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import { SemanticMetric } from '@prisma/client'
import { useSetAtom } from 'jotai'
import type { TableProps } from 'antd'
import CountSearchTable, { type ApiArgsType } from 'src/client/components/CountSearchTable'
import CreateMetricModal from 'src/client/pages/MetricStore/Metrics/fusion/Create'
import { SynonymsForm } from 'src/client/pages/MetricStore/Metrics/fusion/SynonymsForm'
import { askBIApiUrls } from 'src/shared/url-map'
import SynonymsText from 'src/client/components/SynonymsText'
import { MetricTypeNamesWithAll } from 'src/shared/metric-types'
import { currentDataset<PERSON>tom } from '../../askBIAtoms'
import DeleteMetricModal from './DeleteMetricModal'
import { useBatchUpload } from './useBatchUpload'

export default function MetricList() {
  const { message } = App.useApp()
  const [notificationApi, notificationContextHolder] = notification.useNotification()
  const [createMetricModalOpen, setCreateMetricModalOpen] = useState(false)
  const [synonymsFormOpen, setSynonymsFormOpen] = useState(false)
  const [deleteMetricModalOpen, setDeleteMetricModalOpen] = useState(false)
  const [semanticProjectId, setSemanticProjectId] = useState('')
  const [semanticModelId, setSemanticModelId] = useState('')
  const [semanticSceneId, setSemanticSceneId] = useState('')
  const location = useLocation()
  const actionRef = useRef<{ reload: (args: ApiArgsType) => void; refresh: () => void }>()
  const [currentMetric, setCurrentMetric] = useState<SemanticMetric>()
  const [createMetricValues, setCreateMetricValues] = useState<SemanticMetric>()
  const [synonymsIsReadOnly, setSynonymsIsReadOnly] = useState(false)
  const [createMetricModelIsEdit, setCreateMetricModelIsEdit] = useState(false)
  const updateCurrentDataset = useSetAtom(currentDatasetAtom)
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [deleting, deletingOps] = useBoolean(false)
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search)
    setSemanticModelId(urlParams.get('name') || '')
    setSemanticProjectId(urlParams.get('projectId') || '')
    setSemanticSceneId(urlParams.get('scenarioId') || '')
  }, [location.search])

  const { triggerNode, modalNode } = useBatchUpload({
    semanticProjectId,
    semanticModelId,
    semanticSceneId,
    refresh: () => actionRef.current?.refresh(),
  })
  return (
    <>
      <CountSearchTable
        actionRef={actionRef}
        isFullList
        extra={
          <div className="flex gap-1">
            <Button
              className="ml-1"
              type="primary"
              onClick={() => {
                setCreateMetricModelIsEdit(false)
                setCreateMetricModalOpen(true)
              }}
            >
              创建指标
            </Button>
            {triggerNode}
            <Popconfirm
              placement="top"
              title={'删除提示'}
              description={`确认删除这些指标吗？`}
              okText="确定"
              cancelText="取消"
              onConfirm={() => {
                deletingOps.setTrue()
                axios
                  .delete(askBIApiUrls.metrics.batchDelete, { data: { ids: selectedRowKeys } })
                  .then(() => {
                    message.success('指标删除成功')
                    setSelectedRowKeys([])
                    actionRef.current?.refresh?.()
                  })
                  .catch((err) => {
                    if (err instanceof Error) {
                      notificationApi.error({
                        message: '删除失败',
                        description: err.message.split('\n'),
                      })
                    } else {
                      console.info('批量删除时发生未知错误', err)
                    }
                  })
                  .finally(() => {
                    deletingOps.setFalse()
                  })
              }}
            >
              <Button loading={deleting} className="ml-1" type="primary" danger disabled={selectedRowKeys.length === 0}>
                批量删除
              </Button>
            </Popconfirm>
          </div>
        }
        searchKey={['name', 'label']}
        api={useCallback(async () => {
          const response = await axios.get(askBIApiUrls.metrics.list, {
            params: { semanticProjectId, semanticModelId, semanticSceneId },
          })
          const data = response.data.data as { list: any[]; total: number }
          return data
        }, [semanticProjectId, semanticModelId, semanticSceneId])}
        tableProps={{
          rowSelection: {
            type: 'checkbox',
            onChange: (selectedRowKeys: React.Key[]) => {
              setSelectedRowKeys(selectedRowKeys)
            },
          },
          rowKey: 'id',
          scroll: { x: 1400 },
          columns: [
            {
              title: '指标ID',
              dataIndex: 'name',

              width: 160,
              render(name) {
                return <div className="text-wrap break-all">{name}</div>
              },
            },
            {
              title: '中文名',
              dataIndex: 'label',
            },
            {
              title: '同义词',
              dataIndex: 'synonyms',
              width: 200,
              render(synonyms, record) {
                return (
                  <SynonymsText
                    disabled={record.autoCreateByMeasure}
                    synonyms={synonyms}
                    handleViewSynonyms={(synonyms) => {
                      setSynonymsFormOpen(true)
                      setCurrentMetric({
                        ...(record || {}),
                        synonyms,
                      })
                      setSynonymsIsReadOnly(true)
                    }}
                    handleEditSynonyms={(synonyms) => {
                      setSynonymsIsReadOnly(false)
                      setCurrentMetric({
                        ...(record || {}),
                        synonyms,
                      })
                      setSynonymsFormOpen(true)
                    }}
                  />
                )
              },
            },
            {
              title: '类型',
              dataIndex: 'type',
              width: 90,
              render(type) {
                const text = MetricTypeNamesWithAll[type as keyof typeof MetricTypeNamesWithAll]
                return text ? text : type || '-'
              },
            },
            {
              title: '描述',
              dataIndex: 'description',
            },
            {
              title: '配置和过滤条件',
              dataIndex: 'typeParams',
              render(value, record) {
                if (record.type === 'simple' && record.filter != null && record.filter.length > 0) {
                  return (
                    <div>
                      <b>配置：</b>
                      <br />
                      <code>{JSON.stringify(value)}</code>
                      <br />
                      <b>过滤条件：</b>
                      <br />
                      <code>{record.filter}</code>
                    </div>
                  )
                }
                return (
                  <div>
                    <b>配置：</b>
                    <br />
                    <code>{JSON.stringify(value)}</code>
                  </div>
                )
              },
            },
            {
              title: '创建',
              dataIndex: 'createdAt',
              render(t) {
                return t ? dayjs(t).format('YYYY-MM-DD HH:mm:ss') : '-'
              },
            },
            {
              title: '操作',
              dataIndex: 'operation',
              width: 60,
              fixed: 'right',
              render: (_, record) => {
                return (
                  <Popover
                    placement="top"
                    content={
                      <div className="w-[74px]">
                        <Button
                          type="text"
                          block
                          onClick={() => {
                            setCurrentMetric(record as SemanticMetric)
                            setDeleteMetricModalOpen(true)
                          }}
                        >
                          删除指标
                        </Button>
                        <br />
                        <Button
                          type="text"
                          block
                          disabled={record.autoCreateByMeasure}
                          onClick={() => {
                            setCreateMetricModelIsEdit(true)
                            setCreateMetricValues(record as SemanticMetric)
                            setCreateMetricModalOpen(true)
                          }}
                        >
                          编辑指标
                        </Button>
                      </div>
                    }
                  >
                    <MoreOutlined className="cursor-pointer" />
                  </Popover>
                )
              },
            },
          ] as TableProps<SemanticMetric>['columns'],
        }}
      />
      <DeleteMetricModal
        open={deleteMetricModalOpen}
        metric={currentMetric as SemanticMetric}
        onSuccess={() => {
          actionRef.current?.refresh?.()
          setDeleteMetricModalOpen(false)
        }}
        onClose={() => {
          setDeleteMetricModalOpen(false)
        }}
      />
      <SynonymsForm
        couldDeleteAll
        isReadOnly={synonymsIsReadOnly}
        onOk={async (synonyms: string[]) => {
          if (currentMetric) {
            const { id } = currentMetric
            await axios
              .put(askBIApiUrls.metrics.update(id), {
                synonyms,
                name: currentMetric.name,
                description: currentMetric.description,
              })
              .then(
                () => {
                  actionRef.current?.refresh?.()
                  message.success('同义词更新成功')
                },
                () => {
                  message.error('同义词更新失败')
                },
              )
          }
        }}
        createSynonymsOpen={synonymsFormOpen}
        setSynonymsCalculateOpen={setSynonymsFormOpen}
        initValue={(currentMetric?.synonyms || []) as string[]}
      />
      <CreateMetricModal
        baseInfo={useMemo(
          () => ({
            semanticModelName: semanticModelId,
            semanticProjectId: semanticProjectId,
            semanticSceneId: semanticSceneId,
          }),
          [semanticModelId, semanticProjectId, semanticSceneId],
        )}
        isEdit={createMetricModelIsEdit}
        values={createMetricValues}
        onOk={() => {
          actionRef.current?.refresh?.()
          updateCurrentDataset(null)
        }}
        createMetricModalOpen={createMetricModalOpen}
        setCreateMetricModalOpen={setCreateMetricModalOpen}
      />
      {notificationContextHolder}
      {modalNode}
    </>
  )
}
