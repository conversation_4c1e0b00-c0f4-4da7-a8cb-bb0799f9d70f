import React, { useCallback, useRef, useState } from 'react'
import { Button, App, Modal, Space, Dropdown, Spin, Tooltip } from 'antd'
import { ColumnsType } from 'antd/es/table'
import axios from 'axios'
import { useRequest } from 'ahooks'
import { MoreOutlined, PlusOutlined } from '@ant-design/icons'
import { useNavigate, useSearchParams, Link } from 'react-router-dom'
import dayjs from 'dayjs'
import ProjectDetailModal from 'src/client/pages/MetricStore/components/ProjectDetailModal'
import { ResponseErrorType, Scenario } from 'src/shared/metric-types'
import { askBIApiUrls, askBIPageUrls } from 'src/shared/url-map'
import TextHighlight from 'src/client/components/TextHighlight'
import { SvgIcon, folderIcon } from 'src/client/components/SvgIcon'
import PageHeader from 'src/client/components/PageHeader'
import AdminPage from 'src/client/components/AdminPage'
import CountSearchTable, { ApiArgsType } from 'src/client/components/CountSearchTable'

const ScenariosManage = () => {
  const navigate = useNavigate()
  const actionRef = useRef<{ reload: (args: ApiArgsType) => void; refresh: () => void }>()
  const { message: antdMessage } = App.useApp()
  const [searchParams] = useSearchParams()
  const projectId = searchParams.get('projectId') || ''
  const [projectName, setProjectName] = useState('')
  const [openDeleteScenarioModal, setOpenDeleteScenarioModal] = useState(false)
  const [deletingScenario, setDeletingScenario] = useState<Scenario>()
  const [searchValue, setSearchValue] = useState('')
  const [projectDetailModalOpen, setProjectDetailModalOpen] = useState(false)

  const { loading: projectDetailLoading, run: getProjectDetail } = useRequest(
    async () => {
      if (projectId) {
        const res = await axios.get(askBIApiUrls.auth.project.rest, { params: { id: projectId } })
        if (res.data.code !== 0) return new Error(res.data.msg)
      }
      return null
    },
    {
      onSuccess(data) {
        setProjectName(data?.name || '')
      },
    },
  )

  const { run: handleDelete, loading: isDeleteLoading } = useRequest(
    async (id) => {
      await axios.delete(askBIApiUrls.metrics.deleteByScene(id))
      return axios.delete(askBIApiUrls.auth.scene.rest, { data: { id } })
    },
    {
      manual: true,
      onSuccess: () => {
        antdMessage.success('删除成功')
        setOpenDeleteScenarioModal(false)
        actionRef.current?.refresh()
      },
      onError: (error: Error & ResponseErrorType) => {
        antdMessage.error('删除场景失败：' + error.msg || error.error || '未知原因')
        console.error('deleteScenario error =', error)
      },
    },
  )
  const scenarioColumn: ColumnsType<Scenario> = [
    {
      title: '场景名称',
      dataIndex: 'label',
      width: 200,
      render(value, record) {
        const name = record?.modelNames?.[0] || ''
        const to = `${askBIPageUrls.scenarios.detail}?projectId=${record.semanticProjectId}&scenarioId=${record.id}&name=${name}`
        return (
          <div>
            <Link to={to} className="text-link">
              <TextHighlight text={value} highlight={searchValue} />
            </Link>
          </div>
        )
      },
    },
    {
      title: '场景描述',
      dataIndex: 'description',
      ellipsis: true,
      render(value) {
        return (
          <Tooltip title={value} className="cursor-pointer">
            <div className="inl overflow-hidden text-ellipsis whitespace-nowrap">{value || '-'}</div>
          </Tooltip>
        )
      },
    },
    {
      title: '关联Agent',
      dataIndex: 'agent',
      width: 120,
      render(value) {
        return <TextHighlight text={value} highlight={searchValue} />
      },
    },
    {
      title: '所属项目',
      dataIndex: ['semanticProject', 'name'],
      width: 120,
    },
    // {
    //   title: '关联指标',
    //   dataIndex: 'metricsNums',
    //   width: 90,
    // },
    // {
    //   title: '关联数据',
    //   dataIndex: 'dataNums',
    //   width: 90,
    // },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 180,
      render(t) {
        return t ? dayjs(t).format('YYYY-MM-DD HH:mm:ss') : '-'
      },
    },
    {
      title: '创建人',
      dataIndex: 'createdBy',
      width: 80,
    },
    {
      title: '操作',
      dataIndex: 'id',
      width: 80,
      render(_value, record) {
        return (
          <Space>
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'detail',
                    label: (
                      <Button
                        type="link"
                        onClick={() => {
                          const name = (record.modelNames && record.modelNames[0]) || ''
                          navigate(
                            `${askBIPageUrls.scenarios.detail}?projectId=${record.semanticProjectId}&scenarioId=${record.id}&name=${name}`,
                          )
                        }}
                      >
                        详情
                      </Button>
                    ),
                  },
                  {
                    key: 'delete',
                    label: (
                      <Button
                        type="link"
                        danger
                        onClick={() => {
                          setOpenDeleteScenarioModal(true)
                          setDeletingScenario(record)
                        }}
                      >
                        删除
                      </Button>
                    ),
                  },
                ],
              }}
              placement="bottomRight"
              arrow={{ pointAtCenter: true }}
            >
              <Button type="link">
                <MoreOutlined />
              </Button>
            </Dropdown>
          </Space>
        )
      },
    },
  ]

  return (
    <AdminPage
      title={
        <PageHeader
          onBack={projectName && projectId ? () => window.history.back() : undefined}
          title={
            <>
              {projectDetailLoading ? (
                <Spin />
              ) : (
                <div>
                  {projectName && projectId ? (
                    <div className="flex items-center">
                      <SvgIcon icon={folderIcon} className="h-6 w-6" />
                      <span className="ml-1">{projectName || '-'}</span>
                      <MoreOutlined
                        className="ml-2 cursor-pointer"
                        onClick={() => {
                          setProjectDetailModalOpen(true)
                        }}
                      />
                    </div>
                  ) : (
                    '场景管理'
                  )}
                </div>
              )}
            </>
          }
          extra={
            <Button
              type="primary"
              icon={<PlusOutlined />}
              size="middle"
              onClick={() => {
                const projectId = searchParams.get('projectId') || ''
                window.open(`${askBIPageUrls.manage.scenarios.create}?projectId=${projectId}`, '_blank')
              }}
            >
              创建场景
            </Button>
          }
        />
      }
    >
      <CountSearchTable
        actionRef={actionRef}
        placeholder="场景名称"
        isFullList
        onSearch={(search: string) => setSearchValue(search)}
        api={useCallback(async () => {
          const res =
            (await axios.get(askBIApiUrls.auth.scene.list, {
              params: projectId ? { projectId } : {},
            })) || []
          const scenarioList = res.data.data
          return {
            list: scenarioList.list,
            total: scenarioList.total,
          }
        }, [projectId])}
        className="mt-2"
        searchKey="label"
        tableProps={{
          rowKey: 'id',
          columns: scenarioColumn,
        }}
        onError={() => {
          antdMessage.error('获取场景信息失败')
        }}
      />
      <Modal
        destroyOnClose
        title={`确认删除${deletingScenario?.label}`}
        open={openDeleteScenarioModal}
        onOk={() => handleDelete(deletingScenario?.id)}
        okButtonProps={{ loading: isDeleteLoading }}
        onCancel={() => setOpenDeleteScenarioModal(false)}
      >
        该场景包含的数据将全部被删除，请确认
      </Modal>
      <ProjectDetailModal
        onClose={() => {
          setProjectDetailModalOpen(false)
          getProjectDetail()
        }}
        open={projectDetailModalOpen}
        projectId={projectId}
      />
    </AdminPage>
  )
}

export default ScenariosManage
