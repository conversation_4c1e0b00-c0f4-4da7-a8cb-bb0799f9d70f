import { use<PERSON><PERSON><PERSON><PERSON> } from 'jotai'
import React, { useRef } from 'react'
import rehypeKatex from 'rehype-katex'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import clsx from 'clsx'
import { useBoolean } from 'ahooks'
import ReactMarkdown from 'react-markdown'
import { CaretDownOutlined, CaretUpOutlined, SyncOutlined } from '@ant-design/icons'
import { Tooltip } from 'antd'
import { nanoid } from 'nanoid'
import { ChatNewAnswerLinkType, TextNode } from 'src/shared/askdoc-types'
import { AssistantDocItem, ChatStatus } from 'src/shared/common-types'
import { IS_H5 } from 'src/shared/constants'
import { askdocAnswerLinkAtom, askdocAnswerLinkKeyTagAtom } from '../askDocAtoms'
import useDocStreamDataFetch from './useDocStreamDataFetch'

export interface AnswerViewProps {
  content: AssistantDocItem
  status: keyof typeof ChatStatus
  isViewMode?: boolean
}

function Markdown(props: { content: string; className: string }) {
  return (
    <ReactMarkdown
      className={clsx(`askbi-markdown mb-[20px] text-[16px] font-[400] leading-[28px]`, props.className)}
      remarkPlugins={[remarkGfm, remarkMath]}
      rehypePlugins={[rehypeKatex]}
    >
      {props.content}
    </ReactMarkdown>
  )
}

export default function AnswerView(props: AnswerViewProps) {
  const {
    status,
    content: { text },
  } = props

  const setAskdocAnswerLink = useSetAtom(askdocAnswerLinkAtom)
  const setAskdocAnswerLinkKeyTag = useSetAtom(askdocAnswerLinkKeyTagAtom)
  const containerRef = useRef<HTMLDivElement | null>(null)
  const [thinking, thinkingOps] = useBoolean(true)

  const { thinkContent, resultContent, sourceNodesRef, indexType, loadingData } = useDocStreamDataFetch(props)
  /**
   * href的点击事件
   * @param event
   * @param data 获取点击的item的其他参数，例如哪一页，文档id等
   */
  const handleLinkClick = (data: ChatNewAnswerLinkType) => {
    if (IS_H5) {
      return
    }
    setAskdocAnswerLink(data)
    setAskdocAnswerLinkKeyTag(nanoid())
  }

  /**
   * 检查文本中是否包含 HTML 标签，如果包含，则会将 HTML 标签去除，只保留纯文本内容。
   * @param text
   * @param maxLength
   * @returns
   */
  const truncateText = (text: string) => {
    // 如果文本内容为空，直接返回文本内容
    if (!text) {
      return text
    }

    // 检查是否包含 HTML 标签
    const isHTML = /<[a-z][\s\S]*>/i.test(text)
    if (isHTML) {
      // 创建一个 DOMParser 实例来解析 HTML 文本
      const parser = new DOMParser()
      const parsedDoc = parser.parseFromString(text, 'text/html')

      // 获取解析后的纯文本内容
      const plainText = parsedDoc.body.textContent || parsedDoc.body.innerText || ''

      return plainText
    } else {
      return text
    }
  }

  return (
    <div ref={containerRef} className="w-full">
      {status === ChatStatus.failure ? (
        <div className="text-red-400 dark:text-red-500">{text}</div>
      ) : (
        <div>
          {thinkContent ? (
            <div className="flex h-[24px] items-center justify-end">
              <div
                className="flex h-full shrink-0 cursor-pointer items-center justify-end"
                onClick={thinkingOps.toggle}
              >
                <span className="text-[14px] font-[400] text-[#575757]">收起</span>
                {thinking ? (
                  <CaretUpOutlined className="ml-[4px] h-[20px] w-[20px] text-[#858585]" />
                ) : (
                  <CaretDownOutlined className="ml-[4px] h-[20px] w-[20px] text-[#858585]" />
                )}
              </div>
            </div>
          ) : null}
          {thinking && thinkContent ? <Markdown content={thinkContent} className="text-[#8B8B8B]" /> : null}
          {resultContent ? <Markdown content={resultContent} className="mb-[16px] text-[#404040]" /> : null}
          {status === ChatStatus.success && sourceNodesRef.current && sourceNodesRef.current.textNodes?.length > 0 && (
            <div className="mt-2">
              <p className="py-2 font-bold">引用原文</p>
              {sourceNodesRef?.current.textNodes?.map((item: TextNode, index: number) => {
                const isExcelFile = item.fileName.endsWith('.xlsx') || item.fileName.endsWith('.xls')
                const columnIndex = item?.columnIndex || false
                const unit = isExcelFile ? (columnIndex ? '列' : '行') : '页'
                return (
                  <div key={index}>
                    <Tooltip
                      title={<div dangerouslySetInnerHTML={{ __html: item.content }} />}
                      placement="bottomLeft"
                      overlayInnerStyle={{ width: IS_H5 ? '320px' : '420px', maxHeight: '300px', overflow: 'auto' }}
                    >
                      <p onClick={() => handleLinkClick(item)} className="line-clamp-2 cursor-pointer text-[#515154]">
                        {indexType === 'Folder' ? (
                          <span className="text-sm">《{item.fileName}》</span>
                        ) : (
                          <span className="text-sm">{index + 1}.</span>
                        )}
                        <span className="font-bold">{` 第${item.page}${unit}：`}</span>
                        <span>{truncateText(item.content)}</span>
                      </p>
                    </Tooltip>
                  </div>
                )
              })}
            </div>
          )}
          {loadingData && (
            <div className="rounded-[4px] px-[16px]">
              <div className="flex">
                {text}
                <SyncOutlined spin className="ml-4 flex flex-none text-xl text-gray-400 dark:text-gray-500" />
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
