import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useAtomValue, useSet<PERSON>tom } from 'jotai'
import { throttle } from 'lodash'
import { produce } from 'immer'
import axios from 'axios'
import {
  AnsDocChatItem,
  AskDocSourceNodes,
  AssistantDocItemPayload,
  Chat,
  ChatStatus,
  DocResultType,
} from 'src/shared/common-types'
import { scrollToBottom } from 'src/client/utils'
import { createTraceId } from 'src/shared/common-utils'
import { TOKEN_BI, TOKEN_RANGER, U_TOKEN_RANGER } from 'src/shared/constants'
import { askBIApiUrls, askDocApiUrls } from 'src/shared/url-map'
import { currentSelectFileBySceneAtom } from '../askDocAtoms'
import { cancelTokenSourceAtom, chatsAtom, isSubmittingAtom } from '../../AskBI/askBIAtoms'
import { AnswerViewProps } from './AnswerView'

export default function useDocStreamDataFetch({ content, status, isViewMode }: AnswerViewProps) {
  const { payload, result } = content
  const { msg, conversationId, chatId, llmType, sceneId } = payload || ({} as AssistantDocItemPayload)
  const logUploadedRef = useRef(false)
  const loadingRef = useRef(false)
  const [loadingData, setLoadingData] = useState(false)
  const setIsSubmitting = useSetAtom(isSubmittingAtom)
  const currentSelectFileByScene = useAtomValue(currentSelectFileBySceneAtom)
  const cancelTokenSource = useAtomValue(cancelTokenSourceAtom)
  const setChats = useSetAtom(chatsAtom)

  const [rawContent, setRawContent] = useState<string>(result?.content || '')
  const rawContentRef = useRef<string>(result?.content || '')
  const sourceNodesRef = useRef<AskDocSourceNodes | null>(result?.sourceNodes || null)

  const indexType = currentSelectFileByScene ? 'Document' : 'Folder'

  const { thinkContent, resultContent } = useMemo(() => {
    const thinkStartReg = /<think>/g
    const thinkEndReg = /<\/think>/g
    const tagReg = /<think>[\s\S]*<\/think>/g
    let thinkContent = ''
    let resultContent = ''
    const startIdx = Array.from(rawContent.matchAll(thinkStartReg))[0]?.index
    const endIdx = Array.from(rawContent.matchAll(thinkEndReg))[0]?.index
    if (startIdx >= 0 && endIdx >= 0) {
      const data = Array.from(rawContent.matchAll(tagReg))
      thinkContent = data[0][0]
      resultContent = rawContent.replaceAll(thinkContent, '')
    } else if (endIdx === undefined && startIdx >= 0) {
      thinkContent = rawContent.substring(startIdx) + '</think>'
    } else if (startIdx === undefined && endIdx >= 0) {
      thinkContent = '<think>' + rawContent.substring(0, endIdx + `</think>`.length)
      resultContent = rawContent.substring(endIdx + `</think>`.length)
    } else {
      resultContent = rawContent
    }
    const updateKatex = (s: string) =>
      s.replaceAll(/\\\[[\s\S]*\\\]/g, (val) => `\n$$\n${val.substring(2, val.length - 2)}\n$$\n`)
    return {
      thinkContent: updateKatex(thinkContent.substring(`<think>`.length, thinkContent.length - `</think>`.length)),
      resultContent: updateKatex(resultContent),
    }
  }, [rawContent])
  const toBottom = throttle(scrollToBottom, 50)

  const docSetChats = useCallback(
    ({
      sourceNodes,
      text,
      status,
    }: {
      sourceNodes: typeof sourceNodesRef.current
      text: string
      status: keyof typeof ChatStatus
    }) => {
      const result = {
        indexType,
        content: rawContentRef.current,
        sourceNodes: sourceNodes,
      } as DocResultType
      const content = [
        {
          type: 'doc-result',
          payload: payload,
          text,
          result,
        },
      ] as AnsDocChatItem['content']
      setChats(
        produce((draft: Chat[]) => {
          const theChat = draft.find((item) => item.id === chatId)
          if (!theChat) {
            return
          }

          theChat.docAns = {
            role: 'assistant',
            status,
            ansTime: new Date(),
            content,
          }
        }),
      )
      const logParams = {
        chatId,
        docResult: status === ChatStatus.success ? content[0] : '回答失败',
      }
      if (!logUploadedRef.current) {
        axios.post(askBIApiUrls.converChats.updateDocChat, logParams)
        logUploadedRef.current = true
      }
    },
    [chatId, indexType, payload, setChats],
  )

  const loadData = useCallback(async () => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      traceId: createTraceId(),
    }
    const token = localStorage.getItem(TOKEN_BI)
    const rangerToken = localStorage.getItem(TOKEN_RANGER)
    const rangerUToken = localStorage.getItem(U_TOKEN_RANGER)
    if (token) headers['Authorization'] = `Bearer ${token}`
    if (rangerToken) headers[TOKEN_RANGER] = rangerToken
    if (rangerUToken) headers[U_TOKEN_RANGER] = rangerUToken
    const requestData = {
      messages: [{ content: msg, type: 'question', role: 'user' }],
      conversationId,
      chatId,
      id: currentSelectFileByScene ? currentSelectFileByScene.id.toString() : sceneId, // scene 场景id
      indexType,
      modelType: llmType, // 模型
      stream: true,
    }
    return fetch(askDocApiUrls.askQuestion, {
      method: 'POST',
      body: JSON.stringify(requestData),
      headers,
      signal: cancelTokenSource.signal,
    })
      .then(async (response) => {
        if (!response.ok) {
          throw new Error('请求失败')
        }

        if (response.body) {
          const reader = response.body.getReader() // 获取 ReadableStream 的 reader
          const decoder = new TextDecoder('utf-8') // 用于解码二进制数据
          let buffer = '' // 用于缓存数据片段
          let rawContent = ''
          let nodes = {} as AskDocSourceNodes

          let streamDone = false
          while (!streamDone) {
            const { done, value } = await reader.read() // 读取下一个 chunk

            if (done) {
              streamDone = true
              break
            }

            buffer += decoder.decode(value, { stream: true }) // 解码并拼接数据片段

            // 检查是否收到完整的消息（以双换行符或空行为标志）
            const messages = buffer.split(/\n\n|\r\n\r\n/) // 分割消息
            if (messages.length > 1) {
              // 处理完整的消息
              for (let i = 0; i < messages.length - 1; i++) {
                const message = messages[i].trim()
                if (message) {
                  try {
                    const resData = parseSSEMessage(message) // 解析 SSE 消息
                    if (typeof resData.data === 'string') {
                      if (/\[DONE\]/.test(resData.data)) {
                        // 结束了
                        setIsSubmitting(false)
                        docSetChats({ sourceNodes: nodes, text: '', status: ChatStatus.success })
                      }
                    } else {
                      switch (resData.data.event) {
                        case 'llm_content': {
                          const newContent = resData.data.content
                          rawContent = rawContent + newContent
                          rawContentRef.current = rawContent
                          setRawContent(rawContent)
                          break
                        }
                        case 'nodes': {
                          nodes = { ...nodes, ...resData.data.nodes }
                          sourceNodesRef.current = nodes
                          break
                        }
                        default: {
                          break
                        }
                      }
                      toBottom()
                    }
                  } catch (error) {
                    console.error('Failed to parse message:', error)
                  }
                }
              }
              buffer = messages[messages.length - 1] // 保留未处理完的数据片段
            }
          }
        }
      })
      .catch((err: Error) => {
        const status = rawContentRef.current ? ChatStatus.success : ChatStatus.failure
        const text = typeof err === 'string' ? err : '查询知识库出错'
        docSetChats({ sourceNodes: null, text, status })
      })
  }, [
    cancelTokenSource,
    chatId,
    conversationId,
    currentSelectFileByScene,
    indexType,
    llmType,
    msg,
    sceneId,
    toBottom,
    setIsSubmitting,
    docSetChats,
  ])
  useEffect(() => {
    if (status === ChatStatus.pending && !loadingRef.current && !rawContentRef.current && payload && !isViewMode) {
      setLoadingData(true)
      loadingRef.current = true
      loadData().finally(() => {
        setIsSubmitting(false)
        setLoadingData(false)
      })
    }
  }, [status, isViewMode, loadingData, payload, setIsSubmitting, loadData])

  // 解析 SSE 消息
  function parseSSEMessage(message: string) {
    const lines = message.split('\n')
    const data = {} as { data: any }

    lines.forEach((line: string) => {
      if (line.startsWith('data:')) {
        const value = line.slice(5).trim()
        try {
          data.data = JSON.parse(value) // 解析 JSON 数据
        } catch (error) {
          data.data = value // 如果非 JSON，直接存储字符串
        }
      }
    })

    return data
  }

  return { thinkContent, resultContent, sourceNodesRef, indexType, loadingData }
}
