import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useRequest } from 'ahooks'
import axios from 'axios'
import { App } from 'antd'
import AdminCard from 'src/client/components/AdminCard'
import AdminPage from 'src/client/components/AdminPage'
import { askBIApiUrls, askBIPageUrls, askDocApiUrls } from 'src/shared/url-map'
import ReportManagementTable from './ReportManagementTable'

type PPTTemplateType = {
  id: string
  name: string
  thumbnailPath: string
}
export default function ReportManagement() {
  const navigate = useNavigate()
  const { message: antdMessage } = App.useApp()
  const [pptTemplateList, setPPTTemplateList] = useState<PPTTemplateType[]>([])

  useRequest(
    async () => {
      const response = await axios.get(askBIApiUrls.pptGenerate.pptTemplateList)
      setPPTTemplateList(response.data.data.pptTemplateList || [])
    },
    {
      onError: (error: any) => {
        antdMessage.error(`PPT模版列表获取失败：${error.message}`)
        console.error('get currentUserBasicInfo =', error)
      },
    },
  )

  const handleGoPPTTemplate = (id: string) => {
    navigate(`${askBIPageUrls.metricStore.smartReport.templateManagement}?pptTemplate=true&id=${id}`)
  }

  return (
    <AdminPage title="报告管理">
      <AdminCard>
        <div>
          <div className="font-bold text-black">商情中标分析模版PPT</div>
          <div className="flex gap-4">
            {pptTemplateList.map((item, index) => {
              const finalUrl = item.thumbnailPath
                ? askDocApiUrls.fileUrlProxy(encodeURIComponent(item.thumbnailPath), encodeURIComponent('image/jpeg'))
                : ''
              return (
                <div
                  key={index}
                  className="mb-2 cursor-pointer"
                  onClick={() => {
                    handleGoPPTTemplate(item.id)
                  }}
                >
                  <div className="flex h-[138px] w-[194px] flex-col">
                    <img className="h-[114px] w-[194px]" src={finalUrl} alt={''} />
                    <div className="mt-3 flex-grow text-center text-black">{item.name}</div>
                  </div>
                </div>
              )
            })}
          </div>

          <ReportManagementTable />
        </div>
      </AdminCard>
    </AdminPage>
  )
}
