import { App, Button, Form, Input, Modal, Popover } from 'antd'
import React, { useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useRequest } from 'ahooks'
import axios from 'axios'
import { useAtomValue, useSetAtom } from 'jotai'
import { MoreOutlined } from '@ant-design/icons'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import AdminCard from 'src/client/components/AdminCard'
import AdminPage from 'src/client/components/AdminPage'
import { askBIApiUrls, askBIPageUrls, askDocApiUrls } from 'src/shared/url-map'
import { ReportOutlineItemType, ReportTemplateType } from 'src/shared/common-types'
import { reportBackIcon, SvgIcon } from 'src/client/components/SvgIcon'
import { currentLoginUserAtom, currentReportTemplateAtom } from '../../AskBI/askBIAtoms'
import InfoItem from './components/InfoItem'
import PPTView from './components/PPTView'

/**
 * 智能报告引擎 - 模板管理
 * @returns
 */
export default function TemplateManagement() {
  const navigate = useNavigate()
  const [form] = Form.useForm()
  const { message: antdMessage } = App.useApp()
  const [templateList, setTemplateList] = useState<ReportTemplateType[]>([])
  const setCurrentReportTemplate = useSetAtom(currentReportTemplateAtom)
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false)
  const [createLoading, setCreateLoading] = useState<boolean>(false)
  const currentLoginUserInfo = useAtomValue(currentLoginUserAtom)
  const [searchParams] = useSearchParams()
  const isPPtTemplate = searchParams.get('pptTemplate') === 'true'

  /* 获取模版列表 */
  const { run: getTemplateList } = useRequest(
    async () => {
      const response = await axios.get(askBIApiUrls.reportGenerate.getTemplateList)
      setTemplateList(response.data.data.templateList || [])
    },
    {
      onError: (error: any) => {
        antdMessage.error(error?.message)
        console.error('获取模版列表失败', error)
      },
    },
  )

  /**
   * 跳转到报告生成页面
   */
  const handleReportGeneration = (item: ReportTemplateType) => {
    setCurrentReportTemplate(item)
    navigate(`${askBIPageUrls.metricStore.smartReport.reportGeneration}?generation=true&id=${item.id}`)
  }

  /**
   * 查看模板
   * @returns 无返回值
   */
  const handleTemplate = (item: ReportTemplateType) => {
    setCurrentReportTemplate(item)
    navigate(`${askBIPageUrls.metricStore.smartReport.reportGeneration}?viewTemplate=true&id=${item.id}`)
  }

  /**
   * 处理编辑模板操作
   *
   * @param item 报告模板对象
   */
  const handleEditTemplate = (item: ReportTemplateType) => {
    setCurrentReportTemplate(item)
    navigate(`${askBIPageUrls.metricStore.smartReport.reportGeneration}?editTemplate=true&id=${item.id}`)
  }

  /**
   * 处理创建模板的操作
   */
  const handleOk = async () => {
    try {
      await form.validateFields()
      setCreateLoading(true)
      const fields = form.getFieldsValue()
      await axios.post(askBIApiUrls.reportGenerate.createTemplate, {
        name: fields.name,
        createUser: currentLoginUserInfo?.username,
      })
      antdMessage.success('创建成功')
      setCreateLoading(false)
      handleCancel()
      getTemplateList()
    } catch (error: any) {
      setCreateLoading(false)
      antdMessage.error(`创建失败: ${error?.message}`)
    }
  }

  const handleCancel = () => {
    form.resetFields()
    setIsModalOpen(false)
  }

  /**
   * 删除模板
   */
  const handleDeleteTemplate = async (id: number) => {
    try {
      await axios.delete(askBIApiUrls.reportGenerate.deleteTemplate, {
        params: { templateId: id },
      })
      antdMessage.success('删除成功')
      getTemplateList()
    } catch (error: any) {
      console.error(error)
      antdMessage.error(`删除失败: ${error?.message}`)
    }
  }

  const renderPPTContent = () => {
    return (
      <div className="flex h-full flex-col">
        <div className="flex items-center">
          <div
            className="cursor-pointer"
            onClick={() => {
              navigate(-1)
            }}
          >
            <SvgIcon icon={reportBackIcon} className="h-6 w-6 dark:text-white" />
          </div>
          <p className="ml-3 text-xl font-semibold text-[#171717]">报告生成</p>
        </div>

        <div className="flex-1 flex-grow">
          <PPTView />
        </div>
      </div>
    )
  }

  return (
    <div className="h-full">
      {isPPtTemplate ? (
        renderPPTContent()
      ) : (
        <AdminPage title="模板管理">
          <div className="absolute right-8 mb-3">
            <Button
              className="mr-4 font-bold"
              type="primary"
              onClick={() => {
                setIsModalOpen(true)
              }}
            >
              创建模板
            </Button>
          </div>

          <AdminCard>
            <div className="flex flex-wrap gap-4">
              {templateList.map((item, index: number) => {
                const finalUrl = item.thumbnailPath
                  ? askDocApiUrls.fileUrlProxy(encodeURIComponent(item.thumbnailPath), encodeURIComponent('image/jpeg'))
                  : ''
                return (
                  <div className="h-[300px] w-[580px] rounded border py-4" key={index}>
                    <p className="mb-1 px-4 text-base font-medium text-[#171717]">{item.name}</p>
                    <div className="flex justify-between px-4">
                      <div className="flex">
                        <InfoItem label="创建人：" value={item.createUser} />
                        <InfoItem label="创建日期：" value={item.createAt} />
                      </div>
                      {/* <p
                        className="cursor-pointer text-xs text-[#6A58EC]"
                        onClick={() => {
                          navigate(askBIPageUrls.metricStore.smartReport.reportManagement)
                        }}
                      >
                        查看模板关联的报告
                      </p> */}
                    </div>
                    <div className="my-2 flex h-40 justify-between bg-[#f9fbfd] px-4 py-2">
                      <div className="mr-2 max-h-40 w-3/4 overflow-y-auto break-words p-2">
                        {(item.outline || []).map((ite: ReportOutlineItemType, idx: number) => (
                          <div key={idx}>
                            <ReactMarkdown remarkPlugins={[remarkGfm]} rehypePlugins={[rehypeRaw]}>
                              {ite.content}
                            </ReactMarkdown>
                          </div>
                        ))}
                      </div>
                      <div className="w-1/4 flex-shrink-0">
                        {finalUrl && <img src={finalUrl} className="h-[136px] w-full border" />}
                      </div>
                    </div>
                    <div className="flex justify-between pt-2">
                      <div>
                        <Button
                          className="ml-4 font-medium"
                          type="primary"
                          onClick={() => {
                            handleReportGeneration(item)
                          }}
                        >
                          生成报告
                        </Button>
                        <Button
                          className="ml-4 border-[#6759e4] font-medium text-[#6759e4]"
                          onClick={() => {
                            handleTemplate(item)
                          }}
                        >
                          查看模板
                        </Button>
                        <Button
                          className="ml-4 border-[#6759e4] font-medium text-[#6759e4]"
                          onClick={() => {
                            handleEditTemplate(item)
                          }}
                        >
                          编辑模板
                        </Button>
                      </div>

                      {item.id !== 1 && (
                        <div className="mr-2">
                          <Popover
                            content={
                              <div className="flex flex-col">
                                <Button
                                  type="text"
                                  onClick={() => {
                                    handleDeleteTemplate(item.id)
                                  }}
                                  className="px-1"
                                >
                                  删除
                                </Button>
                              </div>
                            }
                          >
                            <MoreOutlined className="cursor-pointer" />
                          </Popover>
                        </div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </AdminCard>
          <Modal
            title="创建模板"
            open={isModalOpen}
            onOk={handleOk}
            onCancel={handleCancel}
            maskClosable={false}
            confirmLoading={createLoading}
          >
            <div className="pt-3">
              <Form form={form}>
                <Form.Item
                  name="name"
                  label="模板名称"
                  rules={[
                    {
                      required: true,
                      message: '请输入模板名称',
                    },
                  ]}
                >
                  <Input placeholder="请输入模板名称" autoComplete="off" />
                </Form.Item>
              </Form>
            </div>
          </Modal>
        </AdminPage>
      )}
    </div>
  )
}
