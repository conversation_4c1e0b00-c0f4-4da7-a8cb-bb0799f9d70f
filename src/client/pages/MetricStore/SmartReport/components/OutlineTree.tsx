import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react'
import { App, Button, Input, Popover, Tooltip, Tree } from 'antd'
import type { TreeProps, TreeDataNode } from 'antd'
import { MoreOutlined } from '@ant-design/icons'
import { nanoid } from 'nanoid'
import { useImmer } from 'use-immer'
import { produce } from 'immer'
import axios from 'axios'
import { useRequest } from 'ahooks'
import clsx from 'clsx'
import { assertExhaustive, length, numberToChineseNumber } from '@shared/common-utils'
import { OutlineItemType } from 'src/shared/common-types'
import { askBIApiUrls } from 'src/shared/url-map'
import cs from './OutlineTree.module.scss'
/**
 * 报告生成段落节点
 */
export type Node = {
  allowChildren: boolean
  children: Node[]
  content: string
  dependsOn: number[]
  id: number
  maxChildrenCount: number
  title: string
}

// 定义 traverse 函数，T 表示输入数据类型，U 表示经过 callback 处理后的数据类型
const traverse = <T extends { children?: T[] }, U>(
  data: T[],
  callback: (item: T, index: number, arr: T[], depth: number) => U,
  currentDepth = 0,
): U[] => {
  return data.map((item, index, curArr) => {
    // 对当前节点调用 callback，返回处理后的节点
    const newItem = callback(item, index, curArr, currentDepth)

    // 如果当前节点有 children，递归处理 children，并将处理后的 children 加入结果中
    if (item.children) {
      return {
        ...newItem,
        children: traverse(item.children, callback, currentDepth + 1),
      }
    }

    return newItem
  })
}

// 定义 addKey 函数，将 key 属性添加到每个节点上
const addKey = <T extends { children?: T[]; id: string | number }>(item: T) => {
  return { ...item, key: String(item.id) }
}
const getObjForSavingTree = <T extends { children?: T[]; id: string | number }>(item: T) => {
  return { ...item }
}
const addChildren = <T extends { children?: T[]; id: string | number }>(item: T) => {
  return { ...item, children: item.children || [] }
}
type IBusinessTreeDataNode = OutlineItemType & {
  key: string
}

const OutlineTree = forwardRef(function OutlineTree(
  {
    currentOutlineContent,
    handleSectionConfig,
    setCurrentOutlineContent,
    couldEdit = true,
  }: {
    handleSectionConfig: (item: OutlineItemType, index: number) => void
    currentOutlineContent: OutlineItemType[]
    setCurrentOutlineContent?: (data: IBusinessTreeDataNode[]) => void
    couldEdit: boolean
  },
  ref: React.Ref<{
    addNodeAtBaseRoot: () => void
    editAllNode: () => void
  }>,
) {
  const [editMode, setEditMode] = useState(false)

  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    addNodeAtBaseRoot: () => {
      setShowOperateButton(true)
      saveDataBeforeEdit()
      addChild()
    },
    editAllNode: () => {
      setShowOperateButton(true)
      saveDataBeforeEdit()
      setDraggable(false)
      setEditMode(true)
    },
  }))
  // 定义你的组件状态，使用 traverse 函数为每个节点添加 key 属性
  const [treeData, setTreeData] = useImmer<IBusinessTreeDataNode[]>(
    traverse(traverse<OutlineItemType, OutlineItemType & { key: string }>(currentOutlineContent, addKey), addChildren),
  )
  useEffect(() => {
    setTreeData(
      traverse(
        traverse<OutlineItemType, OutlineItemType & { key: string }>(currentOutlineContent, addKey),
        addChildren,
      ),
    )
  }, [currentOutlineContent, setTreeData])
  // 点击编辑之前的状态 用来做取消编辑
  const [preTreeData, setPreTreeData] = useImmer<IBusinessTreeDataNode[]>([])
  // 点击编辑之前的展开状态 用来做取消编辑
  const [preExpandedKeys, setPreExpandedKeys] = useImmer<string[]>([])
  const { message: antdMessage } = App.useApp()

  const [expandedKeys, setExpandedKeys] = useState<string[]>([])
  const [editingKeys, setEditingKeys] = useState<string[]>([])
  const [draggable, setDraggable] = useState(true) // 是否可拖拽  拖拽不算编辑状态， 拖拽完成之后 直接请求保存接口， 更新预览内容
  const [showOperateButton, setShowOperateButton] = useState(false)
  const saveDataBeforeEdit = () => {
    setPreTreeData(treeData)
    setPreExpandedKeys(expandedKeys)
  }
  const resumeDataWhenCancelEdit = () => {
    setTreeData(preTreeData)
    setExpandedKeys(preExpandedKeys)
  }
  const getMaxChildDepth = (root: IBusinessTreeDataNode, initialDepth: number = 1) => {
    let maxDepth = 0
    const findMaxDepth = (node: IBusinessTreeDataNode, currentDepth: number) => {
      if (length(node.children)) {
        node.children.forEach((child) => {
          findMaxDepth(child, currentDepth + 1)
        })
      } else {
        maxDepth = Math.max(maxDepth, currentDepth)
      }
    }
    findMaxDepth(root, initialDepth)
    return maxDepth
  }
  const getDepth = (key: string, treeQueue: IBusinessTreeDataNode[], initialDepth: number = 1): number => {
    let depth = 0
    const findDepth = (node: IBusinessTreeDataNode, currentDepth: number) => {
      if (node.key === key) {
        depth = currentDepth
      }
      if (node.children?.length) {
        node.children.forEach((child) => {
          findDepth(child, currentDepth + 1)
        })
      }
    }
    treeQueue.forEach((node) => {
      findDepth(node, initialDepth)
    })
    return depth
  }
  //   对 key  执行 callback 操作
  const loop = (
    data: IBusinessTreeDataNode[],
    key: React.Key,
    callback: (node: IBusinessTreeDataNode, i: number, data: IBusinessTreeDataNode[], depth: number) => void,
    depth: number = 0,
  ) => {
    for (let i = 0; i < data.length; i++) {
      if (data[i].key === key) {
        return callback(data[i], i, data, depth)
      }
      if (data[i].children) {
        loop(data[i].children!, key, callback, depth + 1)
      }
    }
  }
  const onDrop: TreeProps['onDrop'] = (info) => {
    const dropKey = String(info.node.key)
    const dragKey = String(info.dragNode.key)
    let rootForGetMaxChildDepth: IBusinessTreeDataNode
    loop(treeData, dragKey, (item: IBusinessTreeDataNode) => {
      rootForGetMaxChildDepth = item
    })
    const baseDepth = getDepth(dropKey, treeData)
    const addonDepth = getMaxChildDepth(rootForGetMaxChildDepth!)
    if (baseDepth + addonDepth > 3) {
      return antdMessage.warning('目录维多为三层')
    }
    // 拖拽完成之后 直接请求保存接口， 更新预览内容
    saveOutlineTitle()
    // todo update preview

    setTreeData(
      produce(treeData, (draft) => {
        const dropPos = info.node.pos.split('-') // 0 - 0 - 0
        const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]) // the drop position relative to the drop node, inside 0, top -1, bottom 1

        const data = draft

        // Find dragObject and remove from origin place
        let dragObj: IBusinessTreeDataNode
        loop(data, dragKey, (item, index, arr) => {
          arr.splice(index, 1)
          dragObj = item
        })

        if (!info.dropToGap) {
          // Drop on the content
          loop(data, dropKey, (item) => {
            item.children = item.children || []
            // where to insert. New item was inserted to the start of the array in this example, but can be anywhere
            item.children.unshift(dragObj)
          })
        } else {
          let ar: IBusinessTreeDataNode[] = []
          let i: number
          loop(data, dropKey, (_item, index, arr) => {
            ar = arr
            i = index
          })
          if (dropPosition === -1) {
            // Drop on the top of the drop node
            ar.splice(i!, 0, dragObj!)
          } else {
            // Drop on the bottom of the drop node
            ar.splice(i! + 1, 0, dragObj!)
          }
        }
        return data
      }),
    )
  }

  const addChild = (node?: TreeDataNode) => {
    const key = nanoid()
    const newChild = {
      id: Math.floor(Math.random() * 10000000), // 这个定义是后端的 不应该是number
      key: key,
      title: '',
      content: '',
      allowChildren: true,
      maxChildrenCount: 10,
      dependsOn: [],
      children: [],
    }
    const traverseAddChild = (child: TreeDataNode) => {
      if (child.key === node?.key) {
        child.children = (child.children || []).concat(newChild)
      }
    }
    setEditingKeys((pre) => [...pre, key])
    node && setExpandedKeys((pre) => [...pre, String(node.key)])
    setTreeData((draft) => {
      // 有节点 就在节点里面加 没有节点就加在根节点
      if (node) {
        traverse(draft, traverseAddChild)
      } else {
        draft.push(newChild)
      }
    })
  }
  const editNodeName = (key: string, name: string) => {
    setTreeData((draft) => {
      traverse<IBusinessTreeDataNode, IBusinessTreeDataNode>(draft, (child) => {
        if (child.key === key) {
          child.title = name
        }
        return child
      })
    })
  }

  const { run: saveOutlineTitle, loading: postOutlineDataLoading } = useRequest(
    async () => {
      const searchParams = new URLSearchParams(window.location.search)
      const id = searchParams.get('id') || ''
      const reqData = {
        outline: traverse(treeData, getObjForSavingTree),
        templateId: id,
      }

      await axios.post(askBIApiUrls.reportGenerate.updateOutline, reqData)
    },
    {
      manual: true,
      onSuccess: () => {
        setShowOperateButton(false)
        setCurrentOutlineContent?.(traverse(treeData, getObjForSavingTree))
        antdMessage.success(`保存成功`)
      },
      onError: (error: any) => {
        console.error('saveOutlineTitle error', error)
        antdMessage.error(`保存失败${error.message}`)
      },
    },
  )
  const removeNode = (key: string) => {
    setTreeData(
      produce(treeData, (draft) => {
        loop(draft, key, (item, index, arr) => {
          if (item.key === key) {
            arr.splice(index, 1)
          }
        })
      }),
    )
    setCurrentOutlineContent?.(traverse(treeData, getObjForSavingTree))
    setTimeout(() => {
      saveOutlineTitle()
    }, 300)
  }
  return (
    <div>
      <Tree
        className={`draggable-tree ${cs.outlineTree} bg-transparent`}
        defaultExpandedKeys={expandedKeys}
        onExpand={(keys) => {
          setExpandedKeys(keys.map(String))
        }}
        expandedKeys={expandedKeys}
        draggable={draggable && couldEdit}
        blockNode
        onDrop={onDrop}
        treeData={treeData}
        titleRender={(nodeData) => {
          let displayTitle = ''
          // 通过 loop 函数获取展示的 title
          loop(treeData, nodeData.key, (node, index, _arr, depth) => {
            const allowedDepths = [0, 1, 2, 3, 4]
            if (!allowedDepths.includes(depth)) {
              return console.error(`Invalid depth value: ${depth}, must be one of ${allowedDepths.join(', ')}`)
            }
            const curIndex = index + 1
            const chineseNumber = numberToChineseNumber(curIndex)
            switch (depth) {
              case 0:
                displayTitle = `${chineseNumber}、${node.title}`
                break
              case 1:
                displayTitle = `(${chineseNumber}) ${node.title}`
                break
              case 2:
                displayTitle = `${curIndex}. ${node.title}`
                break
              case 3:
                displayTitle = `${curIndex}.${curIndex}. ${node.title}`
                break
              case 4:
                displayTitle = `${curIndex}.${curIndex}.${curIndex}. ${node.title}`
                break
              default:
                assertExhaustive(depth as never)
            }
          })
          return (
            <div
              key={nodeData.key}
              className="group flex h-9 items-center justify-between py-3 text-sm text-[#101828] hover:bg-[#EEEFF1]"
            >
              <Tooltip title={nodeData.title as string}>
                {editingKeys.includes(String(nodeData.key)) || editMode ? (
                  <div className="flex w-full min-w-0">
                    <Input
                      defaultValue={typeof nodeData.title === 'string' ? nodeData.title : ''}
                      onChange={(e) => {
                        editNodeName(String(nodeData.key), e.target.value)
                      }}
                    />
                  </div>
                ) : (
                  <p className="mr-1 w-auto min-w-0 max-w-xs truncate text-sm">{displayTitle as string}</p>
                )}
              </Tooltip>

              <div className={clsx('invisible flex-shrink-0', couldEdit && 'group-hover:visible')}>
                <Popover
                  placement="right"
                  content={
                    <div className="flex flex-col">
                      <Button
                        type="text"
                        onClick={() => {
                          saveDataBeforeEdit()
                          addChild(nodeData)
                          setShowOperateButton(true)
                        }}
                        className="px-1"
                      >
                        增加子章节
                      </Button>
                      <Button
                        type="text"
                        onClick={() => {
                          loop(treeData, nodeData.key, (item: IBusinessTreeDataNode) => {
                            handleSectionConfig(item, 0)
                          })
                        }}
                        className="px-1"
                      >
                        段落配置
                      </Button>
                      <Button
                        type="text"
                        onClick={() => {
                          saveDataBeforeEdit()
                          setShowOperateButton(true)
                          setEditingKeys((pre) => [...pre, String(nodeData.key)])
                        }}
                        className="px-1"
                      >
                        重命名
                      </Button>
                      <Button
                        type="text"
                        onClick={() => {
                          removeNode(String(nodeData.key))
                        }}
                        className="px-1"
                      >
                        删除
                      </Button>
                    </div>
                  }
                >
                  <MoreOutlined className="cursor-pointer" />
                </Popover>
              </div>
            </div>
          )
        }}
      />
      {showOperateButton && (
        <div className="flex">
          <Button
            className="ml-14 mr-4 border-none bg-primary font-medium text-white"
            loading={postOutlineDataLoading}
            onClick={() => {
              setDraggable(true)
              setEditingKeys([])
              setEditMode(false)
              saveOutlineTitle()
            }}
          >
            保存
          </Button>
          <Button
            className="mr-3 border-none bg-white font-medium dark:bg-slate-500"
            onClick={() => {
              setDraggable(true)
              setEditingKeys([])
              resumeDataWhenCancelEdit()
              setEditMode(false)
              setShowOperateButton(false)
            }}
          >
            取消
          </Button>
        </div>
      )}
    </div>
  )
})

export default OutlineTree
