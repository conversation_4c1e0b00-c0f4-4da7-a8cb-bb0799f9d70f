import React, { useC<PERSON>back, useState } from 'react'
import { App, But<PERSON>, DatePicker, Form, Input, Modal, Select, Space, Spin, Table } from 'antd'
import dayjs, { Dayjs } from 'dayjs'
import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons'
import './DataOperatorModal.css'
import axios from 'axios'
import { useRequest } from 'ahooks'
import { useSearchParams } from 'react-router-dom'
import { useAtomValue } from 'jotai'
import { askBIApiUrls } from 'src/shared/url-map'
import { DOC_REPORT_TIMEOUT } from 'src/shared/constants'
import { DataFilterType, DataOperatorType } from 'src/shared/common-types'
import { reportDataTimeParamsAtom } from 'src/client/pages/AskBI/askBIAtoms'

interface Props {
  templateId: string
  currentModelName: string
  sectionId: number // 所属章节id
  showDataOperatorModal: boolean
  dataOperatorDetail: DataOperatorType | null // 数据算子详情
  setDataOperatorModal: (value: boolean) => void
  setDataOperatorDetail: (value: DataOperatorType | null) => void
  updateSectionConfig: () => void
}

interface TableDataSourceType {
  key: number
  name: string
  value: string
}

const { RangePicker } = DatePicker

interface ColumnClassifyType {
  metrics: Option[]
  dimensions: Option[]
  timeDimensions: Option[]
}

interface Option {
  code: string
  name: string
}

interface OptionsList {
  options: Option[]
}

interface CommonOptionsType {
  key: string
  value: string
}

// 将 JSON 数据转换为表格数据格式的函数
const transformData = (data: string, isRate: boolean = false) => {
  if (!data) return []
  const parsedData = JSON.parse(data)
  const metric = Object.keys(parsedData)[0] // 获取第一个键

  const formatValue = (value: any) => {
    // 检查 value 是否为数字，然后进行格式化
    if (typeof value === 'number') {
      if (isRate) {
        return (value * 100).toFixed(2) + '%' // 乘以100并加上%符号
      }
      return value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
    }
    return value || '-' // 如果不是数字，则返回原值或 '-'
  }

  if (typeof parsedData[metric] === 'object' && parsedData[metric] !== null) {
    return Object.keys(parsedData[metric]).map((key, index) => ({
      key: index,
      name: key,
      value: formatValue(parsedData[metric][key]), // 格式化 value
    }))
  } else {
    return [
      {
        key: 0,
        name: metric,
        value: formatValue(parsedData[metric]), // 格式化 value
      },
    ]
  }
}

// 数据分段方式
const segmentationOptions = [
  {
    name: '自定义',
    value: 'custom',
  },
  {
    name: '均值',
    value: 'mean',
  },
]

// 枚举顺序
const enumOrder = [
  {
    name: '升序',
    value: 'asc',
  },
  {
    name: '降序',
    value: 'desc',
  },
]

/**
 * 数据算子模态框组件
 *
 * @param showDataOperatorModal 是否显示数据算子配置模态框
 * @param setDataOperatorModal 设置数据算子操作模态框显示状态的回调函数
 * @returns 返回数据操作模态框组件的JSX元素
 */
const DataOperatorModal = (props: Props) => {
  const {
    showDataOperatorModal,
    setDataOperatorModal,
    templateId,
    sectionId,
    currentModelName,
    dataOperatorDetail,
    setDataOperatorDetail,
    updateSectionConfig,
  } = props
  const [form] = Form.useForm()
  const [searchParams] = useSearchParams()
  const editTemplate = searchParams.get('editTemplate') === 'true'
  const reportGeneration = searchParams.get('generation') === 'true'
  const { message: antdMessage } = App.useApp()
  const [previewLoading, setPreviewLoading] = useState<boolean>(false)
  const [columnClassifyLoading, setColumnClassifyLoading] = useState<boolean>(false)
  const [selectFieldsOptions, setSelectFieldsOptions] = useState<OptionsList[]>([])
  // 获取数据算子的码值
  const [columnValues, setColumnValues] = useState<{ [key: string]: string[] }>({})
  const [filterTypes, setFilterTypes] = useState<number[]>([])
  const [columnClassify, setColumnClassify] = useState<ColumnClassifyType>()
  const [selectMetricsFields, setSelectMetricsFields] = useState<Option[]>([])
  const [selectDimensionsFields, setSelectDimensionsFields] = useState<Option[]>([])
  // 码值筛选loading
  const [columnValueLoading, setColumnValueLoading] = useState<{
    [key: string]: boolean // 键是字符串，值是 boolean
  }>({})
  const [selectTimeRange, setSelectTimeRange] = useState<string[]>([])
  const [segmentationType, setSegmentationType] = useState<string>('')
  const [previewResult, setPreviewResult] = useState<TableDataSourceType[] | string>()
  // 算子计算类型列表
  const [computeTypeList, setComputeTypeList] = useState<CommonOptionsType[]>()
  // 获取算子的类型列表
  const [dataOperatorTypeList, setDataOperatorTypeList] = useState<CommonOptionsType[]>([])
  // 获取运算符列表
  const [reportOpList, setReportOpList] = useState<{ key: string; value: string }[]>([])
  // 获取时间粒度
  const [timeGranularity, setTimeGranularity] = useState<{ key: string; value: string }[]>([])
  // 算子类型
  const [computeType, setComputeType] = useState<string>()
  // 定义表格的列
  const [columns, setColumns] = useState<any>([])
  // 数据算子配置参数
  const [dataOperatorData, setDataOperatorData] = useState<DataOperatorType>()
  // 保存配置loading
  const [saveLoading, setSaveLoading] = useState<boolean>(false)
  // 原始的数据算子预览结果
  const [previewSourceResult, setPreviewSourceResult] = useState<string>('')

  const reportDataTimeParams = useAtomValue(reportDataTimeParamsAtom)

  /**
   * 获取数据算子的码值
   */
  const getListDataOperatorColumnValue = useCallback(
    async (columnCode: string, index: number, modelName: string) => {
      try {
        const response = await axios.get(askBIApiUrls.reportGenerate.listDataOperatorColumnValue, {
          params: { columnCode, templateId, modelName: modelName || currentModelName },
          timeout: DOC_REPORT_TIMEOUT,
        })
        const result = response.data.data

        if (result) {
          setColumnValueLoading((prev) => ({
            ...prev,
            [index]: false,
          }))
          setColumnValues((prev) => ({
            ...prev,
            [index]: response.data.data.valueList || [],
          }))
        }
      } catch (error: any) {
        setColumnValueLoading((prev) => ({
          ...prev,
          [index]: false,
        }))
        console.error('获取数据算子的码值', error)
        antdMessage.error(`获取数据算子的码值${error.message}`, 2)
      }
    },
    [templateId, currentModelName, antdMessage],
  )

  useRequest(
    async () => {
      // 获取指标模型字段分类情况
      const response = await axios.get(askBIApiUrls.reportGenerate.getColumnClassify, {
        params: { modelName: currentModelName },
        timeout: DOC_REPORT_TIMEOUT,
      })
      const result = response.data.data
      if (result) {
        setSelectMetricsFields(result.metrics)
        setSelectDimensionsFields(result.dimensions)
        setColumnClassify(result)
        setColumnClassifyLoading(false)
      }
      if (dataOperatorDetail && form) {
        let tempDataFilter: DataFilterType[] = []
        if (dataOperatorDetail.dataFilter && JSON.stringify(dataOperatorDetail.dataFilter) !== '[{}]') {
          const newOptionsList = [...selectFieldsOptions]
          tempDataFilter = (dataOperatorDetail.dataFilter || []).map((item: DataFilterType, index) => {
            // 判断 operator 是否为 "in"
            if (item.operator === 'in') {
              item = { ...item, filterType: item?.filterType ?? 1 }
              getListDataOperatorColumnValue(item.columnCode, index, currentModelName)
            } else {
              item = { ...item, filterType: item.filterType ?? 2 }
              // 将 values 数组转为字符串
              if (Array.isArray(item.values)) {
                item.values = item.values.join(',')
              }
            }
            // 业务口径筛选 选择字段值的回显
            newOptionsList[index] = { options: item.operator === 'in' ? result.dimensions : result.metrics }
            setSelectFieldsOptions(newOptionsList)
            return item
          })
        }
        parsePreviewResult(dataOperatorDetail.result)
        setSegmentationType(dataOperatorDetail.segmentationOptions)
        setFilterTypes(tempDataFilter.map((items: { filterType: number }) => items.filterType))
        setComputeType(dataOperatorDetail.computeType)
        const outputDataSectionParams = (dataOperatorDetail.outputDataSectionParams || []).map((item) => {
          return { segmentationInput: item }
        })
        dataOperatorDetail.timeRangeStart &&
          setSelectTimeRange([dataOperatorDetail?.timeRangeStart, dataOperatorDetail?.timeRangeEnd])
        const formValue = {
          ...dataOperatorDetail,
          valueRangeParams:
            dataOperatorDetail.segmentationOptions === 'custom' && dataOperatorDetail.outputDataSectionParams.length > 0
              ? outputDataSectionParams
              : [{}],
          dataFilter: tempDataFilter.length > 0 ? tempDataFilter : [{}],
          timeRange: dataOperatorDetail.timeRangeStart && [
            dayjs(dataOperatorDetail?.timeRangeStart, 'YYYY-MM-DD'),
            dayjs(dataOperatorDetail?.timeRangeEnd, 'YYYY-MM-DD'),
          ],
        }
        form.setFieldsValue(formValue)
      }
    },
    {
      onError: (error: any) => {
        console.error('initDetail', error)
        antdMessage.error(`获取数据算子详情失败${error.message}`, 2)
      },
    },
  )

  /**
   * 解析预览结果函数
   *
   * @param result 预览结果字符串
   * @returns 无返回值
   */
  const parsePreviewResult = (result: string) => {
    if (result) {
      const fields = form.getFieldsValue()
      const parsedData = JSON.parse(result)
      const metric = Object.keys(parsedData)[0] // 获取第一个键
      setColumns([
        {
          title: fields.groupBy,
          dataIndex: 'name',
          key: 'name',
        },
        {
          title: metric,
          dataIndex: 'value',
          key: 'value',
        },
      ])

      const isRate = computeType === '总计环比' || computeType === '总计同比' || computeType === '总计同比波动'
      setPreviewResult(transformData(result, isRate))
      setPreviewSourceResult(result)
    }
  }

  const handleFormDataOp = () => {
    const fields = form.getFieldsValue()
    const valueRange = fields.valueRangeParams
    fields.computeType = computeType
    const outputDataSectionParams = (valueRange || []).map(
      (item: { segmentationInput: string }) => item.segmentationInput,
    )
    const timeRangeStart = selectTimeRange[0] || '2020-01-01'
    const timeRangeEnd = selectTimeRange[1] || '2024-03-31'
    const resultParams = {
      ...fields,
      outputDataSectionParams,
      sectionId,
      dataFilter: JSON.stringify(fields.dataFilter) === '[{}]' ? [] : fields.dataFilter,
      timeRangeStart,
      timeRangeEnd,
      templateId: Number(templateId),
      dataOpId: dataOperatorDetail?.dataOpId || undefined,
    }
    delete resultParams.timeRange
    return resultParams
  }

  /**
   * 处理预览操作
   */
  const handlePreview = async () => {
    setPreviewResult('')
    await form.validateFields()
    const resultParams = handleFormDataOp()
    try {
      setPreviewLoading(true)
      const res = await axios.post(
        askBIApiUrls.reportGenerate.dataOperatorPreview,
        {
          dataOp: resultParams,
        },
        {
          timeout: DOC_REPORT_TIMEOUT,
        },
      )

      parsePreviewResult(res.data.data)
      setDataOperatorData(resultParams)
      setPreviewLoading(false)
    } catch (error: any) {
      setPreviewLoading(false)
      setPreviewResult('')
      antdMessage.error(`预览失败${error.message}`)
    }
  }

  /**
   * 保存数据算子配置操作
   *
   * @returns 无返回值
   */
  const handleSaveConfig = async () => {
    try {
      await form.validateFields()
      const resultParams = handleFormDataOp()
      setSaveLoading(true)
      await axios.post(askBIApiUrls.reportGenerate.createOrUpdateDataOperator, {
        dataOp: dataOperatorData ? dataOperatorData : resultParams,
        result: previewSourceResult,
      })
      antdMessage.success('数据算子配置保存成功')
      setSaveLoading(false)
      setDataOperatorModal(false)
      updateSectionConfig()
    } catch (error: any) {
      setSaveLoading(false)
      console.error('获取数据算子的码值', error)
      antdMessage.error(`数据算子配置保存失败${error.message}`, 2)
    }
  }

  /**
   * 处理取消模态框操作
   *
   * 当用户点击取消按钮时，该函数会被调用。
   * 它会将当前的数据操作详情设为null，重置表单字段，并将数据操作模态框设置为关闭状态。
   */
  const handleCancelModal = () => {
    setDataOperatorDetail(null)
    form.resetFields()
    setDataOperatorModal(false)
  }

  /**
   * 处理筛选类型改变事件
   *
   * @param value 筛选类型值
   * @param index 筛选类型在数组中的索引
   * @returns 无返回值
   */
  const handleFilterTypeChange = (value: number, index: number) => {
    const newFilterTypes = [...filterTypes]
    newFilterTypes[index] = value
    setFilterTypes(newFilterTypes)

    const currentValues = form.getFieldValue('dataFilter') || []
    if (currentValues.length > 0) {
      currentValues[index].values = []
      currentValues[index].columnName = undefined
      currentValues[index].operator = currentValues[index].filterType === 2 ? currentValues[index].operator : 'in'
      form.setFieldsValue({
        dataFilter: currentValues,
      })
    }
    const newOptionsList = [...selectFieldsOptions]
    newOptionsList[index] = { options: value === 1 ? selectDimensionsFields : selectMetricsFields }
    setSelectFieldsOptions(newOptionsList)
    setColumnValueLoading((prev) => ({
      ...prev,
      [index]: false,
    }))
  }

  /**
   * 获取算子计算类型列表
   */
  useRequest(
    async () => {
      const response = await axios.get(askBIApiUrls.reportGenerate.computeTypeList, {
        timeout: DOC_REPORT_TIMEOUT,
      })
      const result = response.data.data

      if (result) {
        setComputeTypeList(result.computeTypeList)
      }
    },
    {
      onError: (error: any) => {
        setComputeTypeList([])
        console.error('获取算子计算类型列表', error)
        antdMessage.error(`获取算子计算类型列表${error.message}`, 2)
      },
    },
  )

  /**
   * 获取时间粒度
   */
  useRequest(
    async () => {
      const response = await axios.get(askBIApiUrls.reportGenerate.timeGranularity, {
        timeout: DOC_REPORT_TIMEOUT,
      })
      const result = response.data.data

      if (result && result.timeGranularityList) {
        setTimeGranularity(result.timeGranularityList)
      }
    },
    {
      onError: (error: any) => {
        setComputeTypeList([])
        console.error('获取算子计算类型列表', error)
        antdMessage.error(`获取算子计算类型列表${error.message}`, 2)
      },
    },
  )

  /**
   * 获取算子的类型列表
   */
  useRequest(
    async () => {
      const response = await axios.get(askBIApiUrls.reportGenerate.dataOperatorType, {
        timeout: DOC_REPORT_TIMEOUT,
      })
      const result = response.data.data

      if (result) {
        setDataOperatorTypeList(result.dataOperatorTypeList)
      }
    },
    {
      onError: (error: any) => {
        setDataOperatorTypeList([])
        console.error('获取算子计算类型列表', error)
        antdMessage.error(`获取算子计算类型列表${error.message}`, 2)
      },
    },
  )

  /**
   * 获取运算符列表_new
   */
  useRequest(
    async () => {
      const response = await axios.get(askBIApiUrls.reportGenerate.dataFilterOperator)
      setReportOpList(response.data.data.opList || [])
    },
    {
      onError: (error: any) => {
        setReportOpList([])
        console.error('获取运算符列表_new', error)
        antdMessage.error(`获取运算符列表_new${error.message}`, 2)
      },
    },
  )

  /**
   * 获取维度码值_new
   */
  const getReportColumnValue = async (columnCode: string, index: number, modelName?: string) => {
    try {
      const response = await axios.get(askBIApiUrls.reportGenerate.getColumnValue, {
        params: {
          columnCode,
          modelName: currentModelName || modelName,
        },
        timeout: DOC_REPORT_TIMEOUT,
      })
      setColumnValueLoading((prev) => ({
        ...prev,
        [index]: false,
      }))
      setColumnValues((prev) => ({
        ...prev,
        [index]: response.data.data.valueList || [],
      }))
    } catch (error: any) {
      setColumnValueLoading((prev) => ({
        ...prev,
        [index]: false,
      }))
      antdMessage.error(error?.message)
      console.error('获取码值列表失败', error)
    }
  }

  // 选择字段
  const onSelectFieldsChange = (value: string, index: number) => {
    const tempFieldsOptions = selectFieldsOptions[index]?.options || []
    const selectedScene = tempFieldsOptions.find((item) => item.code === value)
    // 清空筛选条件
    const dataFilter = form.getFieldValue('dataFilter')
    if (dataFilter && dataFilter[index].filterType === 1) {
      getReportColumnValue(value, index)
    }
    setColumnValueLoading((prev) => ({
      ...prev,
      [index]: true,
    }))
    setColumnValues((prev) => ({
      ...prev,
      [index]: [],
    }))
    // getReportColumnValue(value, index)
    const columnName = selectedScene?.name || ''
    const columnCode = selectedScene?.code || ''
    getListDataOperatorColumnValue(columnCode, index, columnName)
    dataFilter[index].values = []
    dataFilter[index].columnName = selectedScene?.name
    dataFilter[index].columnCode = selectedScene?.code
    form.setFieldsValue({
      dataFilter,
    })
  }

  /**
   * 处理分段变化的函数
   *
   * @param value 分段类型字符串
   */
  const handleSegmentationChange = (value: string) => {
    setSegmentationType(value)
  }

  /**
   * 日期范围变更处理函数
   *
   * @param _dates 变更后的日期范围，可能为 null 或 Dayjs 对象数组（允许数组元素为 null）
   * @param dateStrings 变更后的日期字符串数组
   */
  const onRangeChange = (_dates: null | (Dayjs | null)[], dateStrings: string[]) => {
    setSelectTimeRange(dateStrings || [])
  }

  /**
   * 处理计算类型变化的函数
   *
   * @param value 新的计算类型值
   */
  const handleComputeTypeChange = (value: string) => {
    form.setFieldsValue({
      computeType: value,
    })
    setComputeType(value)
  }

  const footer = (
    <div className="mb-2">
      <Button onClick={handleCancelModal}>取消</Button>
      <Button className="ml-2" type="primary" onClick={handleSaveConfig} loading={saveLoading}>
        保存
      </Button>
    </div>
  )

  return (
    <Modal
      className="data-operator-modal"
      title="数据算子配置"
      open={showDataOperatorModal}
      onCancel={handleCancelModal}
      maskClosable={false}
      confirmLoading={previewLoading}
      width={previewLoading ? 1300 : previewResult ? 1300 : 750}
      footer={previewResult && editTemplate ? footer : null}
    >
      <div className="flex h-[720px] overflow-hidden">
        <div className="relative flex-1 flex-grow overflow-x-hidden pb-8">
          <div className="h-full overflow-y-auto overflow-x-hidden pr-4">
            <Form
              form={form}
              initialValues={{
                dataFilter: [{}],
                valueRangeParams: [{}],
              }}
              labelCol={{ span: 3 }} // 调整label的栅格数
            >
              <Form.Item
                name="name"
                label="算子名称"
                rules={[
                  {
                    required: true,
                    message: '请输入算子名称',
                  },
                ]}
              >
                <Input placeholder="请输入算子名称" disabled={reportGeneration} />
              </Form.Item>
              <Form.Item
                name="computeType"
                label="计算类型"
                rules={[
                  {
                    required: true,
                    message: '请选择计算类型',
                  },
                ]}
              >
                <Select
                  allowClear
                  showSearch
                  disabled={reportGeneration}
                  value={computeType}
                  placeholder="请选择计算类型"
                  options={computeTypeList}
                  fieldNames={{ label: 'key', value: 'value' }}
                  onChange={(value) => {
                    handleComputeTypeChange(value)
                  }}
                />
                <Space>
                  <p className="text-xs text-[#797979]">
                    {computeType === '总计' && '针对目标度量，在指定算子下进行聚合'}
                    {computeType === '总计环比' && '针对目标度量，在指定算子下计算目标时间范围的周期环比'}
                    {computeType === '总计同比' && '针对目标度量，在指定算子下计算目标时间范围的周期同比'}
                    {computeType === '总计同比波动' &&
                      '针对目标度量，在指定算子下计算目标时间范围的周期同比，比上一周期同比数值的变化'}
                  </p>
                </Space>
              </Form.Item>

              <Form.Item
                name="metric"
                label="目标字段"
                rules={[
                  {
                    required: true,
                    message: '请选择目标字段',
                  },
                ]}
              >
                <Select
                  allowClear
                  showSearch
                  disabled={reportGeneration}
                  placeholder="请选择目标字段"
                  options={columnClassify?.metrics || []}
                  fieldNames={{ label: 'name', value: 'name' }}
                />
              </Form.Item>
              <Form.Item
                name="operator"
                label="算子"
                rules={[
                  {
                    required: true,
                    message: '请选择算子',
                  },
                ]}
              >
                <Select
                  allowClear
                  showSearch
                  disabled={reportGeneration}
                  placeholder="请选择算子"
                  options={dataOperatorTypeList}
                  fieldNames={{ label: 'key', value: 'value' }}
                />
              </Form.Item>
              <Form.Item name="groupBy" label="分组">
                <Select
                  allowClear
                  showSearch
                  disabled={reportGeneration}
                  placeholder="请选择分组"
                  options={columnClassify?.dimensions || []}
                  fieldNames={{ label: 'name', value: 'name' }}
                />
              </Form.Item>
              <Space className="ml-8 block">
                <p className="-ml-3 mb-1 text-xs font-medium text-[#101828] dark:text-white">业务口径筛选</p>
                <Form.List name="dataFilter">
                  {(fields, { add }) => (
                    <>
                      {fields.map(({ key, name, ...restField }, index) => (
                        <div key={key} className="filterFormListItem">
                          <Space className="flex w-full" align="baseline">
                            <Form.Item
                              {...restField}
                              name={[name, 'filterType']}
                              className="ml-2 flex"
                              label="筛选类型"
                              labelCol={{ offset: 2 }}
                            >
                              <Select
                                disabled={reportGeneration}
                                style={{ width: 170 }}
                                options={[
                                  { label: '普通筛选', value: 1 },
                                  { label: '特殊筛选', value: 2 },
                                ]}
                                placeholder="请选择筛选类型"
                                onChange={(value) => handleFilterTypeChange(value, index)}
                              />
                            </Form.Item>
                            <Form.Item
                              {...restField}
                              name={[name, 'columnName']}
                              label="选择字段"
                              labelCol={{ offset: 4 }}
                            >
                              <Select
                                loading={columnClassifyLoading}
                                showSearch
                                disabled={reportGeneration}
                                style={{ width: 170 }}
                                placeholder="请选择字段"
                                options={selectFieldsOptions[index]?.options || []}
                                fieldNames={{ label: 'name', value: 'code' }}
                                onChange={(value) => onSelectFieldsChange(value, index)}
                              />
                            </Form.Item>
                          </Space>
                          <Space className="flex">
                            {filterTypes[index] === 1 && (
                              <Form.Item
                                {...restField}
                                name={[name, 'values']}
                                className="ml-2 flex"
                                label="筛选条件"
                                labelCol={{ offset: 1 }}
                              >
                                <Select
                                  disabled={reportGeneration}
                                  loading={columnValueLoading[`values_${index}`]}
                                  className="mr-5"
                                  style={{ width: 380 }}
                                  mode="multiple"
                                  options={(columnValues[name] || []).map((item) => ({ label: item, value: item }))}
                                  placeholder="请选择筛选条件"
                                />
                              </Form.Item>
                            )}
                            {filterTypes[index] === 2 && (
                              <Space>
                                <Form.Item
                                  {...restField}
                                  name={[name, 'operator']}
                                  className="ml-1 flex"
                                  label="筛选条件"
                                  labelCol={{ offset: 2 }}
                                >
                                  <Select
                                    disabled={reportGeneration}
                                    style={{ width: 170 }}
                                    options={reportOpList.filter((option) => option.key !== 'in')}
                                    fieldNames={{ label: 'value', value: 'key' }}
                                    placeholder="请选择特殊筛选条件"
                                  />
                                </Form.Item>
                                <Form.Item {...restField} name={[name, 'values']} className="ml-6">
                                  <Input placeholder="请输入" />
                                </Form.Item>
                              </Space>
                            )}

                            {!reportGeneration && filterTypes[index] && (
                              <Space>
                                <PlusCircleOutlined onClick={() => add()} />
                                <MinusCircleOutlined
                                  onClick={() => {
                                    if (fields.length === 1) {
                                      form.setFieldsValue({ dataFilter: [{}] })
                                    } else {
                                      const arr = form.getFieldValue('dataFilter')
                                      const newArr = arr.filter((_: any, idx: number) => idx !== index)
                                      form.setFieldsValue({
                                        dataFilter: newArr,
                                      })
                                      setFilterTypes(newArr.map((items: { filterType: number }) => items.filterType))
                                    }
                                  }}
                                />
                              </Space>
                            )}
                          </Space>
                        </div>
                      ))}
                    </>
                  )}
                </Form.List>
                <p className="-ml-3 mb-1 text-xs font-medium text-[#101828] dark:text-white">分析时间范围</p>
                <Form.Item
                  name="timeColumn"
                  label="分析时间维度"
                  labelCol={{ offset: 0 }}
                  rules={[
                    {
                      required: true,
                      message: '请选择时间维度',
                    },
                  ]}
                >
                  <Select
                    disabled={reportGeneration}
                    placeholder="请选择时间维度"
                    options={columnClassify?.timeDimensions || []}
                    fieldNames={{ label: 'name', value: 'name' }}
                  />
                </Form.Item>
                <Form.Item
                  name="timeRange"
                  label="分析时间范围"
                  initialValue={[
                    dayjs(reportDataTimeParams?.timeRangeStart || '2020-01-01'),
                    dayjs(reportDataTimeParams?.timeRangeEnd || '2024-03-31'),
                  ]}
                  labelCol={{ offset: 0 }}
                  rules={[
                    {
                      required: true,
                      message: '请选择分析时间范围',
                    },
                  ]}
                >
                  <RangePicker
                    disabled={reportGeneration}
                    className="w-full"
                    format="YYYY-MM-DD"
                    onChange={onRangeChange}
                  />
                </Form.Item>
                <Form.Item
                  name="timeGranularity"
                  label="分析时间粒度"
                  labelCol={{ offset: 0 }}
                  rules={[
                    {
                      required: true,
                      message: '请选择时间粒度',
                    },
                  ]}
                >
                  <Select
                    allowClear
                    disabled={reportGeneration}
                    placeholder="请选择时间粒度"
                    options={timeGranularity}
                    fieldNames={{ label: 'key', value: 'value' }}
                  />
                </Form.Item>
              </Space>
              <Space className="ml-8 block">
                <p className="-ml-3 mb-1 text-xs font-medium text-[#101828] dark:text-white">取值范围</p>
                <Form.Item className="ml-1" name="segmentationOptions" label="数据分段方式" labelCol={{ offset: 0 }}>
                  <Select
                    allowClear
                    disabled={reportGeneration}
                    placeholder="请选择数据分段方式"
                    options={segmentationOptions}
                    fieldNames={{ label: 'name', value: 'value' }}
                    onChange={(value) => handleSegmentationChange(value)}
                  />
                </Form.Item>
                {segmentationType === 'custom' && (
                  <Form.List name="valueRangeParams">
                    {(fields, { add, remove }) => (
                      <Space className="ml-[86px] flex">
                        {fields.map(({ key, name, ...restField }) => (
                          <Space key={key} className="flex w-full" align="baseline">
                            <Form.Item {...restField} name={[name, 'segmentationInput']} label=" " colon={false}>
                              {/* <Space align="baseline"> */}
                              <Input
                                disabled={reportGeneration}
                                style={{ width: 100 }}
                                placeholder="请输入"
                                className="relative"
                              />
                              {/* {index !== 0 && <p className="absolute right-3 top-1">%</p>} */}
                              {/* </Space> */}
                            </Form.Item>
                          </Space>
                        ))}

                        <Space>
                          {!reportGeneration && (
                            <div className="-mt-3 ml-2">
                              {fields.length < 5 && <PlusCircleOutlined onClick={() => add()} />}
                              <MinusCircleOutlined
                                className="ml-2"
                                onClick={() => {
                                  if (fields.length === 1) {
                                    form.setFieldsValue({ valueRangeParams: [{}] })
                                  } else {
                                    remove(fields.length - 1)
                                  }
                                }}
                              />
                            </div>
                          )}
                        </Space>
                      </Space>
                    )}
                  </Form.List>
                )}
                <Form.Item className="ml-1" name="enumOrder" label="枚举顺序" labelCol={{ offset: 1 }}>
                  <Select
                    allowClear
                    disabled={reportGeneration}
                    placeholder="请选择枚举顺序"
                    options={enumOrder}
                    fieldNames={{ label: 'name', value: 'value' }}
                  />
                </Form.Item>
              </Space>
            </Form>
          </div>
          <Space className="absolute bottom-2 right-0 flex justify-end">
            <Button loading={previewLoading} type="primary" block onClick={handlePreview}>
              预览
            </Button>
          </Space>
        </div>

        {(previewResult || previewLoading) && (
          <div className="h-full flex-1 overflow-y-auto overflow-x-hidden">
            {previewLoading ? (
              <div className="flex h-full min-h-10 w-full items-center justify-center">
                <Spin spinning={previewLoading} />
              </div>
            ) : (
              <div className="ml-3 flex h-full w-full">
                <Table columns={columns} dataSource={previewResult as TableDataSourceType[]} pagination={false} />
              </div>
            )}
          </div>
        )}
      </div>
    </Modal>
  )
}

export default DataOperatorModal
