import { useRequest } from 'ahooks'
import { App, But<PERSON>, DatePicker, Empty, Form, Skeleton, Space } from 'antd'
import axios from 'axios'
import clsx from 'clsx'
import { getDocument, PageViewport, PDFDocumentProxy } from 'pdfjs-dist'
import React, { useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import dayjs from 'dayjs'
import { askBIApiUrls, askDocApiUrls } from 'src/shared/url-map'

export const generateThumbnails = async (url: string, getDocumentFn: typeof getDocument): Promise<string[] | null> => {
  try {
    const pdf: PDFDocumentProxy = await getDocumentFn(url).promise
    const thumbnails: string[] = []
    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      const page = await pdf.getPage(pageNum)
      const viewport: PageViewport = page.getViewport({ scale: 2 })
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      canvas.width = viewport.width
      canvas.height = viewport.height
      canvas.style.width = '100%'
      canvas.style.height = '100%'

      if (context) {
        await page.render({
          canvasContext: context,
          viewport: viewport,
        }).promise

        const thumbnailUrl = canvas.toDataURL('image/jpeg')
        thumbnails.push(thumbnailUrl)
      }
    }

    return thumbnails
  } catch (error) {
    console.error('Error generating thumbnails:', error)
    return null
  }
}

export default function PPTView() {
  const [form] = Form.useForm()
  const { message: antdMessage } = App.useApp()
  const [pptData, setPptData] = useState<any[]>()
  const [activeItem, setActiveItem] = useState<any>()
  const [loading, setLoading] = useState<boolean>(false)
  const [searchParams] = useSearchParams()
  const id = searchParams.get('id') || undefined
  const isPPtTemplate = searchParams.get('pptTemplate') === 'true'
  const [saveDateLoading, setSaveDateLoading] = useState<boolean>(false)
  const [pptDetail, setPPTDetail] = useState<{ pdfUrl: string; pptUrl: string }>()

  /**
   * 获取缩略图列表
   */
  const getThumbnails = (previewPath: string) => {
    const images: React.SetStateAction<any[]> = []

    setLoading(true)

    generateThumbnails(askDocApiUrls.downloadFileProxy(encodeURIComponent(previewPath)), getDocument).then(
      (thumbnails) => {
        if (thumbnails) {
          thumbnails.forEach((thumbnail, index) => {
            images.push({ id: index, image: thumbnail })
          })
          setPptData(images)
          setActiveItem(images[0])
          setLoading(false)
        } else {
          console.error('Failed to generate thumbnails')
          antdMessage.error('Failed to generate thumbnails')
          setLoading(false)
        }
      },
    )
  }

  useRequest(
    async () => {
      if (isPPtTemplate && id) {
        setLoading(true)
        const response = await axios.get(askBIApiUrls.pptGenerate.pptTemplateList)
        const pptTemplateList = response.data.data.pptTemplateList || []
        const pptDetail = pptTemplateList.find((item: { id: number }) => item.id === Number(id))
        if (pptDetail && pptDetail.previewPath) {
          getThumbnails(pptDetail.previewPath)
        }
      }
    },
    {
      onError: (error: any) => {
        antdMessage.error(`PPT模版列表获取失败：${error.message}`)
        console.error('get currentUserBasicInfo =', error)
      },
    },
  )

  /**
   * 处理保存日期
   */
  const handleSaveDate = async () => {
    const fields = form.getFieldsValue()
    const formattedDate = fields.timeStr ? fields.timeStr.format('YYYY-MM') : null
    try {
      setSaveDateLoading(true)
      const res = await axios.post(askBIApiUrls.pptGenerate.pptGenerate, { timeStr: formattedDate, templateId: id })
      setSaveDateLoading(false)
      if (res.data.data) {
        antdMessage.success('修改成功')
        setPPTDetail(res.data.data)
        getThumbnails(res.data.data.pdfUrl)
      } else {
        antdMessage.error(`结果为空，请联系管理员`)
      }
    } catch (error: any) {
      setSaveDateLoading(false)
      antdMessage.error(`修改失败: ${error?.message}`)
    }
  }

  /**
   * 处理下载PPT文件的函数
   */
  const handleDownloadPPT = () => {
    if (pptDetail && pptDetail.pptUrl) {
      window.location.href = askDocApiUrls.downloadFileProxy(encodeURIComponent(pptDetail.pptUrl))
    }
  }

  const handleLeftClick = (item: { id: string; name: string; image: string }) => {
    setActiveItem(item)
  }

  return (
    <div className="flex h-full w-full flex-col">
      <div className="my-3">
        <Form
          form={form}
          layout="inline"
          initialValues={{
            timeStr: dayjs(),
          }}
        >
          <Form.Item name="timeStr" label="日期">
            <DatePicker size="middle" picker="month" />
          </Form.Item>
          <Space className="flex gap-2">
            <Button type="primary" onClick={handleSaveDate} loading={saveDateLoading}>
              保存修改
            </Button>
            {pptDetail && pptDetail.pptUrl && (
              <Button className="ml-2" onClick={handleDownloadPPT}>
                下载
              </Button>
            )}
          </Space>
        </Form>
      </div>

      <div className="h-full w-full flex-1 flex-shrink-0 flex-grow bg-[#f5f5f5]">
        {loading && <Skeleton active={loading} className="p-4" />}
        {pptData && pptData.length > 0 && !loading ? (
          <div className="flex h-full w-full">
            <div
              className="left w-48 flex-shrink-0 overflow-y-auto bg-[#FAFAFA] p-3"
              style={{ maxHeight: 'calc(100vh - 200px)' }}
            >
              {(pptData || []).map((item, index) => {
                return (
                  <div className="mb-6 flex" key={index} onClick={() => handleLeftClick(item)}>
                    <p className="mr-2 text-[#797979]">{index + 1}</p>
                    <div
                      className={clsx(
                        'flex h-[91px] w-[154px] cursor-pointer items-center justify-center rounded-lg',
                        activeItem.id === item.id && 'border-[2px] border-[#6A58EC]',
                      )}
                    >
                      <div className="h-[83px] w-[146px] rounded-lg border bg-white">
                        <img src={item.image} alt="" className="h-full w-fit rounded-lg" />
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>

            <div className="right flex h-full flex-1 items-center justify-center">
              <div className="content h-[500px] w-[1000px] bg-white">
                {activeItem && <img src={activeItem.image} alt="" className="h-full w-fit rounded-lg" />}
              </div>
            </div>
          </div>
        ) : (
          <div className="h-full w-full">
            <Empty className="mt-32" />
          </div>
        )}
      </div>
    </div>
  )
}
