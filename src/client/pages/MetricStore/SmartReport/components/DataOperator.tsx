import React from 'react'
import { <PERSON>vider, Space, Button, Select, Checkbox, Tag } from 'antd'
import type { SelectProps } from 'antd'
import clsx from 'clsx'
import { CheckboxChangeEvent } from 'antd/es/checkbox'
import { length } from '@shared/common-utils'
import { dataOperatorConfig, SvgIcon } from 'src/client/components/SvgIcon'
import { DataOperatorType, TextOperatorType } from 'src/shared/common-types'

type TagRender = SelectProps['tagRender']

function DataOperator(props: {
  value?: string[] // 兼容单选和多选
  onChange?: (value: string | string[]) => any
  optionValueKey: 'dataOpId' | 'textOpId'
  options: (DataOperatorType | TextOperatorType)[]
  onEditOperator?: (id: string) => void
  onCreateNewOperator?: () => void
  showConfigIcon?: boolean
  showCreateButton?: boolean
  closable?: boolean
  disabled?: boolean
  isMultiple?: boolean // 新增变量，控制是否多选
}) {
  const {
    value,
    showConfigIcon,
    showCreateButton,
    closable,
    optionValueKey,
    onChange,
    options,
    onEditOperator,
    onCreateNewOperator,
    disabled = false,
    isMultiple = false, // 默认是单选模式
  } = props

  const getId = (option: DataOperatorType | TextOperatorType): number => {
    return optionValueKey === 'dataOpId' ? (option as DataOperatorType).dataOpId : (option as TextOperatorType).textOpId
  }

  const normalizeValue = (val: string | string[] | undefined): string[] => {
    if (Array.isArray(val)) {
      return val
    }
    if (val) {
      return [String(val)]
    }
    return []
  }

  const triggerChangeEvent = (val: string) => {
    const currentValue = normalizeValue(value)
    if (isMultiple) {
      const newValue = [...currentValue]
      const index = newValue.indexOf(val)
      if (index >= 0) {
        newValue.splice(index, 1) // 取消选中
      } else {
        newValue.push(val) // 选中
      }
      onChange?.(newValue)
    } else {
      onChange?.(val) // 单选模式，直接设置值
    }
  }

  const optionsList = options?.map((v) => ({
    label: v.name,
    value: String(getId(v)),
  }))

  const indeterminate = length(value) > 0 && length(value) < length(optionsList)

  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    onChange?.(e.target.checked ? optionsList.map((e) => e.value) : [])
  }

  const tagRender: TagRender = (tagProps) => {
    const { label: tagLabel, onClose: onTagClose, value: tagValue } = tagProps
    return (
      <Tag
        className={clsx(
          'm-[4px] flex items-center gap-[4px] rounded-[2px] p-[3px] px-[8px] text-[14px] font-medium text-[#101828]',
          disabled ? 'bg-white' : 'bg-[#F2F3F5]',
        )}
        closable={closable}
        onClose={onTagClose}
        style={{ marginInlineEnd: 4 }}
        bordered={false}
      >
        {tagLabel}
        <span
          className={clsx('cursor-pointer', showConfigIcon ? 'block' : 'hidden')}
          onMouseDown={(e: React.MouseEvent<HTMLElement>) => {
            e.preventDefault()
            e.stopPropagation()
            onEditOperator?.(tagValue)
            if (e.defaultPrevented) {
              return
            }
          }}
        >
          <SvgIcon icon={dataOperatorConfig} className="flex h-[14px] items-center dark:text-white" />
        </span>
      </Tag>
    )
  }

  return (
    <Select
      value={normalizeValue(value)}
      style={{ width: '100%' }}
      showSearch
      allowClear
      disabled={disabled}
      placeholder="输入筛选"
      optionRender={(optionItem) => {
        const { label, value: optionValue } = optionItem
        const isChecked = isMultiple
          ? normalizeValue(value).includes(String(optionValue ?? ''))
          : String(value ?? '') === String(optionValue ?? '')

        return (
          <div
            style={{
              overflow: 'hidden',
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <Checkbox checked={isChecked} onChange={() => triggerChangeEvent(String(optionValue))}>
              <span>{label}</span>
            </Checkbox>
          </div>
        )
      }}
      onChange={(val) => onChange?.(val)}
      tagRender={tagRender}
      menuItemSelectedIcon={null}
      dropdownRender={(list) => {
        return (
          <>
            {/* 顶部操作区域（保留“创建新算子”按钮，去掉全选Checkbox） */}
            <Space
              style={{
                padding: '8px 8px 4px 12px',
                width: '100%',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              {isMultiple && (
                <Checkbox
                  indeterminate={indeterminate}
                  onChange={onCheckAllChange}
                  checked={length(value) === length(optionsList)}
                  className="text-[14px] font-medium"
                >
                  全选
                  <span className='ml-2 font-["PingFang_SC"] text-[12px] font-normal not-italic leading-[20px]'>
                    {length(value)} / {length(optionsList)}
                  </span>
                </Checkbox>
              )}
              {/* {showCreateButton && (
                <Button
                  type="primary"
                  ghost
                  onClick={() => {
                    onCreateNewOperator?.()
                  }}
                  size="small"
                >
                  创建新算子
                </Button>
              )} */}
            </Space>

            <Divider style={{ margin: '4px 0' }} />
            {list}
            <Divider style={{ margin: '4px 0' }} />

            {/* 底部操作区域（保留“重置”按钮） */}
            {/* <Space className="flex justify-end">
              <Button
                htmlType="button"
                onClick={() => {
                  onChange?.(isMultiple ? [] : '')
                }}
              >
                重置
              </Button>
            </Space> */}
          </>
        )
      }}
      options={optionsList}
      mode={isMultiple ? 'multiple' : undefined} // 控制单选/多选模式
    />
  )
}

export default DataOperator
