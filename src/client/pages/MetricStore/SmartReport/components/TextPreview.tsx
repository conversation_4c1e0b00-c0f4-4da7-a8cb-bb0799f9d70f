import React, { useState } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import { Button } from 'antd'
import axios from 'axios'
import { askBIApiUrls } from 'src/shared/url-map'
import { DOC_REPORT_TIMEOUT } from 'src/shared/constants'

interface Props {
  title?: string
  className?: string
  fullTextPreview: string
  currentReportId?: number | undefined
  currenetTemplateId?: number | undefined
}

export default function TextPreview(props: Props) {
  const { className, fullTextPreview, title, currentReportId, currenetTemplateId } = props
  const [content, setContent] = useState(fullTextPreview)
  const [loading, setLoading] = useState(false)

  const handleGetFullReport = async () => {
    setLoading(true)
    try {
      const response = await axios.get(askBIApiUrls.reportGenerate.getFullReport, {
        timeout: DOC_REPORT_TIMEOUT,
        params: {
          templateId: currenetTemplateId,
        },
      })
      const result = response.data.data
      setContent(result)
    } catch (err) {
      console.error('获取全文失败:', err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="w-full flex-1 overflow-y-auto px-4">
      <div className="flex h-11 items-center justify-between border-b">
        {currenetTemplateId && (
          <Button type="primary" onClick={handleGetFullReport} loading={loading}>
            获取全文
          </Button>
        )}
      </div>
      <div className={`askbi-markdown py-3 ${className}`}>
        <ReactMarkdown remarkPlugins={[remarkGfm]} rehypePlugins={[rehypeRaw]}>
          {content}
        </ReactMarkdown>
      </div>
    </div>
  )
}
