import React, { useState, useCallback, useRef } from 'react'
import { App, Button, Input, Popover, Modal, Space, Form, type TableProps } from 'antd'
import { PlusOutlined, MoreOutlined } from '@ant-design/icons'
import { SemanticProject } from '@prisma/client'
import { useRequest } from 'ahooks'
import dayjs from 'dayjs'
import { Link } from 'react-router-dom'
import axios from 'axios'
import PageHeader from 'src/client/components/PageHeader'
import { askBIApiUrls, askBIPageUrls } from 'src/shared/url-map'
import { SvgIcon, warningIcon, folderIcon } from 'src/client/components/SvgIcon'
import { type ResponseErrorType } from 'src/shared/metric-types'
import CountSearchTable, { type ApiArgsType } from 'src/client/components/CountSearchTable'
import TextHighlight from 'src/client/components/TextHighlight'
import AdminPage from 'src/client/components/AdminPage'

export default function List() {
  const [createForm] = Form.useForm()
  const { message } = App.useApp()
  const [createModalOpen, setCreateModalOpen] = useState(false)
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [currentEditProject, setCurrentEditProject] = useState<SemanticProject>()
  const actionRef = useRef<{ reload: (args: ApiArgsType) => void; refresh: () => void }>()
  const [searchValue, setSearchValue] = useState('')

  const { loading: createProjectLoading, run: createProject } = useRequest(
    async (values) => {
      const res = await axios.post(askBIApiUrls.auth.project.rest, values)
      if (res.data.code !== 0) throw new Error(res.data.msg)
    },

    {
      manual: true,
      onSuccess() {
        message.success('创建成功')
        createForm.resetFields()
        setCreateModalOpen(false)
        actionRef.current?.refresh && actionRef.current?.refresh()
      },
      onError(error: Error & ResponseErrorType) {
        message.error(error?.msg || error?.error || '创建失败')
      },
    },
  )
  const { loading: deleteProjectLoading, run: deleteProject } = useRequest(
    async () => {
      const projectId = currentEditProject?.id
      if (projectId) {
        await axios.delete(askBIApiUrls.auth.project.rest, { data: { id: projectId } })
      }
    },

    {
      manual: true,
      onSuccess() {
        message.success('删除成功')
        createForm.resetFields()
        setDeleteModalOpen(false)
        actionRef.current?.refresh && actionRef.current?.refresh()
      },
      onError() {
        message.error('删除失败')
      },
    },
  )

  const columns: TableProps<SemanticProject>['columns'] = [
    {
      title: '项目名称',
      dataIndex: 'name',
      render(name: string, record) {
        return (
          <Space>
            <SvgIcon icon={folderIcon} className="mr-1 h-6 w-6" />
            <Link
              to={`${askBIPageUrls.manage.manageProject.detail}?project=${record.name}&projectId=${record.id}`}
              className="text-link"
            >
              <TextHighlight text={name} highlight={searchValue} />
            </Link>
          </Space>
        )
      },
    },
    {
      title: '项目描述',
      dataIndex: 'description',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 200,
      render(t) {
        return t ? dayjs(t).format('YYYY-MM-DD HH:mm:ss') : '-'
      },
    },
    {
      title: '创建人',
      dataIndex: 'createdBy',
    },
    {
      title: '操作',
      dataIndex: 'operations',
      width: 60,
      render: (_, record) => {
        return (
          <Popover
            placement="top"
            content={
              <div className="w-[74px]">
                <Button type="text" block>
                  <Link to={`${askBIPageUrls.scenarios.manage}?project=${record.name}&projectId=${record.id}`}>
                    查看项目
                  </Link>
                </Button>
                <Button
                  type="text"
                  block
                  danger
                  onClick={() => {
                    setDeleteModalOpen(true)
                    setCurrentEditProject(record)
                  }}
                >
                  删除项目
                </Button>
              </div>
            }
          >
            <MoreOutlined className="cursor-pointer" />
          </Popover>
        )
      },
    },
  ]

  return (
    <AdminPage
      title={
        <PageHeader
          title="项目管理"
          extra={
            <Button type="primary" icon={<PlusOutlined />} onClick={() => setCreateModalOpen(true)}>
              <span>新建项目</span>
            </Button>
          }
        />
      }
    >
      <CountSearchTable
        actionRef={actionRef}
        placeholder="项目名称"
        isFullList
        onSearch={(search: string) => setSearchValue(search)}
        api={useCallback(async () => {
          const res = await axios(askBIApiUrls.auth.project.list)
          return res.data.data
        }, [])}
        className="mt-2"
        searchKey="name"
        tableProps={{
          rowKey: 'name',
          columns,
        }}
      />
      {/* 删除Modal */}
      <Modal
        open={deleteModalOpen}
        title="确认删除"
        onOk={deleteProject}
        okButtonProps={{
          loading: deleteProjectLoading,
        }}
        onCancel={() => setDeleteModalOpen(false)}
      >
        <div className="mb-[40px] mt-[20px] flex justify-center">
          <Space className="m-auto w-[300px]">
            <SvgIcon icon={warningIcon} className="h-6 w-6" />
            <div>
              项目将被删除，
              <br />
              是否确认删除该项目下所有场景，此操作不可复原
            </div>
          </Space>
        </div>
      </Modal>

      {/* 新建Modal */}
      <Modal
        open={createModalOpen}
        title="新建项目"
        onOk={async () => {
          const values = await createForm.validateFields()
          createProject(values)
        }}
        okButtonProps={{ loading: createProjectLoading }}
        onCancel={() => {
          setCreateModalOpen(false)
          createForm.resetFields()
        }}
      >
        <Form form={createForm} layout="vertical" className="mt-5">
          <Form.Item
            name="name"
            label="项目名称"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Input placeholder="请输入" />
          </Form.Item>
          <Form.Item label="描述" name="description">
            <Input placeholder="请输入" />
          </Form.Item>
        </Form>
      </Modal>
    </AdminPage>
  )
}
