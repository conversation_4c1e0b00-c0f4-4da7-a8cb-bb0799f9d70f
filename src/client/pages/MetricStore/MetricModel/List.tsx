import React, { useState, useRef, useCallback } from 'react'
import { App, Button, Popover, Space, Typography, Dropdown, type MenuProps, TableProps } from 'antd'
import { PlusOutlined, MoreOutlined } from '@ant-design/icons'
import { useRequest } from 'ahooks'
import dayjs from 'dayjs'
import Grey<PERSON>ooterModel from '@components/GreyFooterModel'
import PageHeader from 'src/client/components/PageHeader'
import { askBIApiUrls } from 'src/shared/url-map'
import request from 'src/shared/xengineAxios'
import { SvgIcon, warningIcon, metricmodelIcon } from 'src/client/components/SvgIcon'
import CountSearchTable, { type ApiArgsType } from 'src/client/components/CountSearchTable'
import ReadingMetricModelDrawer from 'src/client/pages/MetricStore/components/ReadingMetricModelDrawer'
import ValidatedUploadFile from 'src/client/components/ValidatedUploadFile'
import AdminPage from 'src/client/components/AdminPage'
import { type MetricModelType } from 'src/shared/metric-types'
import MetricModelCreate from '../../MetricStore/components/CreateMetricModelDrawer'
export default function List() {
  const { message } = App.useApp()
  const [createModalOpen, setCreateModalOpen] = useState(false)
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const currentDeleteModelRef = useRef<MetricModelType>()
  const actionRef = useRef<{ reload: (args: ApiArgsType) => void; refresh: () => void }>()
  const [currentUpdateModel, setCurrentUpdateModel] = useState<MetricModelType>()
  const [isMetricModelCreateEdit, setIsMetricModelCreateEdit] = useState(false)
  const [createModelType, setCreateModelType] = useState<'createMetricModel' | 'createNestModel'>('createMetricModel')
  const [previewMetricModelDrawerOpen, setPreviewMetricModelDrawerOpen] = useState(false)
  const [currentPreviewModelName, setCurrentPreviewModelName] = useState('')
  const [uploadCSVModalOpen, setUploadCSVModalOpen] = useState(false)

  const { loading: deleteModelLoading, run: deleteModel } = useRequest(
    async () => {
      const name = currentDeleteModelRef.current?.name
      if (name) {
        return request.delete(askBIApiUrls.model.delete, {
          params: { modelName: name },
        })
      }
    },

    {
      manual: true,
      onSuccess() {
        message.success('删除成功')
        actionRef.current?.refresh()
        setDeleteModalOpen(false)
      },
      onError() {
        message.error('删除失败')
      },
    },
  )

  const { loading: modelDetailLoading, run: getModelDetail } = useRequest(
    async (name: string) => {
      return request.get<{ name: string }, MetricModelType>(askBIApiUrls.model.meta, { params: { name } })
    },
    {
      manual: true,
      onSuccess(modelData) {
        setCurrentUpdateModel(modelData)
        setCreateModelType('createMetricModel')
      },
    },
  )

  const metricModelColumns = [
    {
      title: '指标模型',
      dataIndex: 'name',
      render(name) {
        return (
          <div className="flex items-center">
            <SvgIcon icon={metricmodelIcon} className="mr-2 h-5 w-6" />
            <Typography.Link
              onClick={() => {
                setCurrentPreviewModelName(name)
                setPreviewMetricModelDrawerOpen(true)
              }}
            >
              {name}
            </Typography.Link>
          </div>
        )
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 200,
      render(value) {
        return dayjs(value).format('YYYY-MM-DD HH:mm:ss')
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 60,
      render: (_, record) => {
        return (
          <Popover
            placement="top"
            content={
              <div className="w-[74px]">
                <Button
                  block
                  type="text"
                  onClick={() => {
                    setCurrentPreviewModelName(record.name)
                    setPreviewMetricModelDrawerOpen(true)
                  }}
                >
                  查看模型
                </Button>
                <Button
                  block
                  type="text"
                  danger
                  onClick={() => {
                    setDeleteModalOpen(true)
                    currentDeleteModelRef.current = record
                  }}
                >
                  删除模型
                </Button>
                <Button
                  block
                  type="text"
                  onClick={() => {
                    setCreateModalOpen(true)
                    getModelDetail(record.name)
                    setIsMetricModelCreateEdit(true)
                  }}
                >
                  编辑模型
                </Button>
              </div>
            }
          >
            <MoreOutlined className="cursor-pointer" />
          </Popover>
        )
      },
    },
  ] as TableProps<MetricModelType>['columns']

  const items: MenuProps['items'] = [
    {
      key: 'createMetricModel',
      label: (
        <span
          onClick={() => {
            setCreateModelType('createMetricModel')
            setCreateModalOpen(true)
            setIsMetricModelCreateEdit(false)
          }}
        >
          创建指标模型
        </span>
      ),
    },
    {
      key: 'createNestModel',
      label: (
        <span
          onClick={() => {
            setCreateModelType('createNestModel')
            setCreateModalOpen(true)
            setIsMetricModelCreateEdit(false)
          }}
        >
          创建嵌套模型
        </span>
      ),
    },
    {
      key: 'CSVUpload',
      label: (
        <span
          onClick={() => {
            setUploadCSVModalOpen(true)
          }}
        >
          CSV文件创建
        </span>
      ),
    },
  ]

  return (
    <AdminPage
      title={
        <PageHeader
          title="指标模型管理"
          extra={
            <Dropdown menu={{ items }} placement="bottomRight" arrow>
              <Button type="primary" icon={<PlusOutlined />}>
                指标模型
              </Button>
            </Dropdown>
          }
        />
      }
    >
      <CountSearchTable
        actionRef={actionRef}
        placeholder="场景模型名称"
        isFullList
        api={useCallback(() => {
          return request.get<{ current: number; pageSize: number }, { list: MetricModelType[]; total: number }>(
            askBIApiUrls.model.list,
            {
              params: { current: 1, pageSize: -1 },
            },
          )
        }, [])}
        className="mt-2"
        searchKey="name"
        tableProps={{
          rowKey: 'name',
          columns: metricModelColumns,
        }}
      />
      <MetricModelCreate
        loading={modelDetailLoading}
        isEdit={isMetricModelCreateEdit}
        values={currentUpdateModel}
        type={createModelType}
        drawerOpen={createModalOpen}
        setDrawerOpen={setCreateModalOpen}
        onSuccess={actionRef.current?.refresh}
      />
      <ReadingMetricModelDrawer
        open={previewMetricModelDrawerOpen}
        name={currentPreviewModelName}
        onClose={() => {
          setPreviewMetricModelDrawerOpen(false)
        }}
      />
      {/* 删除Modal */}
      <GreyFooterModel
        open={deleteModalOpen}
        title="确认删除"
        onOk={deleteModel}
        okButtonProps={{
          loading: deleteModelLoading,
        }}
        onCancel={() => setDeleteModalOpen(false)}
      >
        <div className="mb-[40px] mt-[20px] flex justify-center">
          <Space className="m-auto w-[300px]">
            <SvgIcon icon={warningIcon} className="h-6 w-6" />
            <div>删除模型后，用该模型的场景将失效</div>
          </Space>
        </div>
      </GreyFooterModel>

      <ValidatedUploadFile
        uploadApi={(uploadFile) => {
          const formData = new FormData()
          uploadFile && formData.append('file', uploadFile)
          return request.post(askBIApiUrls.model.csvUpload, formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          })
        }}
        onSuccess={async () => {
          message.success('创建成功')
          setUploadCSVModalOpen(false)
          await new Promise((resolve) => setTimeout(resolve, 1000))
          actionRef.current?.refresh && actionRef.current.refresh()
        }}
        modalProps={{
          title: 'CSV文件上传',
          open: uploadCSVModalOpen,
          onCancel: () => setUploadCSVModalOpen(false),
        }}
        size={50}
        acceptTypes={['.csv']}
        samples={[
          {
            fileName: '示例.csv',
            fileUrl: `${askBIApiUrls.model.CSVUploadTemplate}?type=BI_DATA_MODEL`,
          },
        ]}
      />
    </AdminPage>
  )
}
