import React from 'react'
import { Form, Input } from 'antd'

export default function JDBCForm() {
  return (
    <>
      <Form.Item label="JDBC URL" name="jdbcUrl" required rules={[{ required: true }]}>
        <Input placeholder="请输入" />
      </Form.Item>
      <Form.Item label="用户名" name="userName">
        <Input placeholder="请输入" />
      </Form.Item>
      <Form.Item label="密码" name="passWord">
        <Input.Password placeholder="请输入" />
      </Form.Item>
    </>
  )
}
