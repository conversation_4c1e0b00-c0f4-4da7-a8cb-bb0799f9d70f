import React, { useEffect, useState } from 'react'
import { List, Select, Space, Typography, Card } from 'antd'
import { useRequest } from 'ahooks'
import request from 'src/shared/xengineAxios'
import { askBIApiUrls } from 'src/shared/url-map'
import { getLastDotStr } from '../conf/util'
interface PropsType {
  catalogName: string
  className?: string
}
const pageSize = 20
const DbWithTable = (props: PropsType): React.ReactElement => {
  const { catalogName } = props

  const [db, setDb] = useState<string>()
  const [current, setCurrent] = useState<number>(0)
  const [total, setTotal] = useState<number>(0)
  const [list, setList] = useState<string[]>([])

  const getDbList = () => {
    if (catalogName) {
      return request.get<{ catalog: string }, string[]>(askBIApiUrls.xengineDatasource.databaseList, {
        params: { catalog: catalogName },
      })
    }
    return Promise.resolve([])
  }

  const { loading: dbLoading, data: dbList } = useRequest(getDbList, {
    refreshDeps: [catalogName],
    onSuccess: (res: string[]) => {
      if (res && res.length > 0) {
        setDb(getLastDotStr(res[0]))
      }
    },
  })

  const { loading, run } = useRequest(
    (params: { catalog: string; database: string; current: number; pageSize: number }) =>
      request.get(askBIApiUrls.xengineDatasource.tableList, { params }),
    {
      manual: true,
      onSuccess: (res: any) => {
        if (res.list.length > 0) {
          setTotal(res.total)
          setList(res.list)
        }
      },
    },
  )

  const pageNumberChange = (page: number) => {
    setCurrent(page)
    if (db && catalogName) {
      run({ catalog: catalogName, database: db, current: page, pageSize })
    }
  }

  useEffect(() => {
    setCurrent(1)
    setList([])
    if (db && catalogName) {
      run({ catalog: catalogName, database: db, current: 1, pageSize })
    }
  }, [db, catalogName, run])

  if (!catalogName) return <></>

  return (
    <Card
      className={props.className ? props.className : ''}
      title={
        <Space align="baseline">
          <Typography.Title level={5}>数据库：</Typography.Title>
          <Select
            placeholder="请选择"
            style={{ width: 200 }}
            value={db}
            loading={dbLoading}
            onChange={setDb}
            options={dbList?.map((item: string) => {
              return { label: getLastDotStr(item), value: getLastDotStr(item) }
            })}
          />
        </Space>
      }
    >
      <List
        loading={loading}
        dataSource={list}
        renderItem={(item: string) => (
          <List.Item>
            <List.Item.Meta title={getLastDotStr(item)} />
          </List.Item>
        )}
        pagination={{
          pageSize,
          current,
          total,
          onChange: (page: number) => pageNumberChange(page),
          showSizeChanger: false,
        }}
      />
    </Card>
  )
}

export default React.memo(DbWithTable)
