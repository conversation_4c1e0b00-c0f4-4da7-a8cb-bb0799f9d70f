import React, { useCallback, useState } from 'react'
import { Form, Input, message, Modal } from 'antd'
import { useRequest } from 'ahooks'
import request from 'src/shared/xengineAxios'
import DBSelect from 'src/client/components/DBSelect/DBSelect'
import { askBIApiUrls } from 'src/shared/url-map'

interface PropsType {
  children?: any
  sourceTable?: string
  sourceDatabase?: string
  sourceCatalog?: string
}

export default function VtSetting(props: PropsType) {
  const { children, sourceTable, sourceDatabase, sourceCatalog } = props
  const [openModal, setOpenModal] = useState(false)

  const { runAsync: createVirtualTable, loading } = useRequest(
    (requestBody) => request.post(askBIApiUrls.xengineDatasource.createVirtualTable, requestBody),
    {
      manual: true,
      onSuccess: () => {
        message.success('创建虚拟表成功')
        setOpenModal(false)
      },
      onError: (err: Error & { msg?: string }) => {
        err?.msg && message.error(err?.msg)
      },
    },
  )

  const [form] = Form.useForm()

  const handleOpenModal = () => {
    setOpenModal(true)
    if (sourceTable) {
      form.setFieldValue('targetTable', 'vt_' + sourceTable)
    }
  }
  const handleModalClose = useCallback(() => {
    form.resetFields()
    setOpenModal(false)
  }, [form])

  return (
    <>
      <div onClick={handleOpenModal}>{children}</div>
      <Modal
        open={openModal}
        title="选择贴源虚拟表配置"
        destroyOnClose={true}
        onOk={() => {
          const values = form.getFieldsValue()
          createVirtualTable({
            targetCatalog: values.catalog,
            targetDatabase: values.database,
            targetTable: values.targetTable,
            sourceTable,
            sourceDatabase,
            sourceCatalog,
          })
        }}
        okButtonProps={{
          loading,
        }}
        okText="创建贴源虚拟表"
        cancelText="取消"
        onCancel={handleModalClose}
        width={640}
      >
        <Form form={form} labelCol={{ span: 4 }}>
          <DBSelect span={24} required={{ catalog: true, database: true }} setFieldValue={form.setFieldValue} />
          <Form.Item
            label="虚拟表名称"
            name="targetTable"
            required
            rules={[{ required: true, message: '请输入虚拟表名称' }]}
          >
            <Input placeholder={'请输入虚拟表的名称'} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}
