import React from 'react'
import { Form, Input } from 'antd'

export default function KafkaForm() {
  return (
    <>
      <Form.Item label="Broker地址" name="brokers" required rules={[{ required: true }]}>
        <Input placeholder="请输入" />
      </Form.Item>
      <Form.Item label="用户名" name="userName">
        <Input placeholder="请输入" />
      </Form.Item>
      <Form.Item label="密码" name="passWord">
        <Input.Password placeholder="请输入" />
      </Form.Item>
    </>
  )
}
