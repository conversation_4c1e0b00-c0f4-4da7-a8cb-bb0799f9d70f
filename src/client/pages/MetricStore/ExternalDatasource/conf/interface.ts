export interface CatalogDataItem {
  catalogID: string
  name: string
  type: string
  remark?: string
  dbNum: string
  creator: string
  createTime: string
  tableNum: string
  settings: string
  canEdit: boolean
}

export interface TableItem {
  catalogType?: string
  catalog: string
  catalogId: string
  database: string
  tableName: string
  creator: string
  createTime: string
  columnCount: string | number | null
  fieldCount: string | number | null
  mark?: string
  schema: [
    {
      Default: string
      Extra: string
      Filed: string
      Key: string
      Null: string
      Type: string
    },
  ]
  partitionColumn: string
  partitionFormat: string
  partitionKey: string
  size: string
  rowCount: number
}
