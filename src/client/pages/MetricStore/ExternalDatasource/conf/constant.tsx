import React from 'react'
import { Space, TabsProps, Tooltip, Typography } from 'antd'
import { Link } from 'react-router-dom'
import type { ColumnsType } from 'antd/es/table/interface'
import { InfoCircleOutlined } from '@ant-design/icons'
import { askBIPageUrls } from 'src/shared/url-map'
import { CatalogDataItem, TableItem } from '../conf/interface'
import { getLastDotStr } from '../conf/util'
import JDBCForm from '../components/JDBCForm'
import VtSetting from '../components/VtSetting'
import KafkaForm from '../components/KafkaForm'
import HiveForm from '../components/HiveForm'

export const catalogTypeList = [
  {
    label: 'Hive',
    value: 'hive',
  },
  {
    label: 'Oracle',
    value: 'oracle',
  },
  {
    label: 'OceanBase',
    value: 'oceanbase',
  },
  {
    label: 'Postgres',
    value: 'postgres',
  },
  {
    label: 'StarRocks',
    value: 'starrocks',
  },
  {
    label: 'MySQL',
    value: 'mysql',
  },
  {
    label: 'Kafka',
    value: 'kafka',
  },
  {
    label: 'DB2',
    value: 'db2',
  },
]

export const catalogFormMap = {
  hive: (method: number) => <HiveForm initMethod={method} />,
  oracle: () => <JDBCForm />,
  oceanbase: () => <JDBCForm />,
  postgres: () => <JDBCForm />,
  starrocks: () => <JDBCForm />,
  mysql: () => <JDBCForm />,
  kafka: () => <KafkaForm />,
  db2: () => <JDBCForm />,
} as const

export const formItems = [
  {
    tag: 'Input',
    label: '数据源名称',
    name: 'name',
    fieldProps: {
      allowClear: true,
    },
  },
  {
    tag: 'Select',
    label: '数据源类型',
    name: 'type',
    fieldProps: {
      options: catalogTypeList,
    },
  },
  {
    tag: 'DateRangePicker',
    label: '创建时间',
    name: 'createTime',
    fieldProps: {
      showTime: true,
      format: 'YYYY/MM/DD HH:mm:ss',
    },
  },
  {
    tag: 'Input',
    label: '数据源备注',
    name: 'remark',
    fieldProps: {
      allowClear: true,
    },
  },
]

export const VIRTUAL_TABLE_DETAIL_PANELS = {
  COLUMN_TYPE: 0, // schema类型
  DATA_PREVIEW: 1, //数据预览
  PARTITION_INFO: 2, //数据分区情况
}

export const detailTabItems: Exclude<TabsProps['items'], undefined> = [
  {
    key: '0',
    label: `schema预览`,
    children: `Content of Tab Pane 1`,
  },
  {
    key: '1',
    label: `数据预览`,
    children: `Content of Tab Pane 2`,
  },
]

export const catalogColumns = [
  {
    dataIndex: 'name',
    key: 'name',
    title: '数据源名称',
    width: 200,
  },
  {
    dataIndex: 'type',
    key: 'type',
    title: '数据源类型',
  },
  {
    dataIndex: 'remark',
    key: 'remark',
    width: 200,
    ellipsis: true,
    title: '数据源备注',
    render: (t: string) => t || '-',
  },
  {
    dataIndex: 'dbNum',
    key: 'dbNum',
    title: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span style={{ marginRight: 5 }}>数据库数量</span>{' '}
        <Tooltip title="为了便于展现，Oracle下Schema=数据库">
          <InfoCircleOutlined />
        </Tooltip>
      </div>
    ),
    width: 150,
  },
  {
    dataIndex: 'tableNum',
    key: 'tableNum',
    title: '数据表数量',
  },
  {
    dataIndex: 'creator',
    key: 'creator',
    title: '创建人',
  },
  {
    dataIndex: 'createTime',
    key: 'createTime',
    title: '创建时间',
    width: 150,
  },
  {
    dataIndex: 'actions',
    key: 'actions',
    title: '操作',
    render: (_: any, record: CatalogDataItem) => (
      <Space>
        <Typography.Link disabled={!record.canEdit}>
          <span data-type="update">编辑</span>
        </Typography.Link>
        <Typography.Link disabled={!record.canEdit}>
          <span data-type="delete">删除</span>
        </Typography.Link>
      </Space>
    ),
  },
]

export const tableColumns: ColumnsType<any> = [
  {
    dataIndex: 'tableName',
    key: 'tableName',
    title: '数据表名称',
    render: (v: string) => getLastDotStr(v),
    sorter: (a: TableItem, b: TableItem) => getLastDotStr(a.tableName).localeCompare(getLastDotStr(b.tableName)),
  },
  {
    dataIndex: 'catalog',
    key: 'catalog',
    title: '所属数据源',
  },
  {
    dataIndex: 'database',
    key: 'database',
    title: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span style={{ marginRight: 5 }}>所属数据库</span>{' '}
        <Tooltip title="为了便于展现，Oracle下Schema=数据库">
          <InfoCircleOutlined />
        </Tooltip>
      </div>
    ),
  },
  {
    dataIndex: 'actions',
    key: 'actions',
    title: '操作',
    render: (_: any, record: TableItem) => (
      <Space>
        <Link
          to={`${askBIPageUrls.manage.externalDatasource.tableDetail}?name=${getLastDotStr(
            record.tableName,
          )}&database=${record.database}&catalog=${record.catalog}`}
        >
          详情
        </Link>
        <VtSetting
          sourceTable={getLastDotStr(record.tableName)}
          sourceDatabase={getLastDotStr(record.database)}
          sourceCatalog={getLastDotStr(record.catalog)}
        >
          <a>创建贴源虚拟表</a>
        </VtSetting>
      </Space>
    ),
  },
]

export const tableSchemaColumns = [
  {
    title: '字段名',
    dataIndex: 'Field',
  },
  {
    title: '字段类型',
    dataIndex: 'Type',
  },
  {
    title: '是否可以为NULL',
    dataIndex: 'NULL',
    render: (v: string) => (v === 'true' ? '是' : '否'),
  },
  {
    title: '主键',
    dataIndex: 'Key',
    render: (v: string) => (v === 'true' ? '是' : '否'),
  },
  {
    title: '分区键',
    dataIndex: 'PartitionKeys',
    render: (_: any, record: any) => (record['Partition By'] === 'true' || record['PartitionKeys'] ? '是' : '否'),
  },
  {
    title: '分区格式',
    dataIndex: '1',
  },
  {
    title: '字段说明',
    dataIndex: 'Comment',
  },
]
