import React, { useState } from 'react'
import { Button, Form, Select, Table, Row, Col, Input, Space, Card } from 'antd'
import { useAntdTable, useRequest } from 'ahooks'
import { pickBy } from 'lodash'
import { useNavigate } from 'react-router-dom'
import BatchUpload from '@model/batchUpload'
import PageHeader from 'src/client/components/PageHeader'
import { askBIApiUrls } from 'src/shared/url-map'
import request from 'src/shared/xengineAxios'
import { routerMap } from 'src/client/x-engine/widget/router/routerMap'
import { tableColumns } from './conf/constant'

const PageSize = 10
export default function TableList() {
  const [show, setShow] = useState<boolean>(false)
  const [form] = Form.useForm()
  const navigate = useNavigate()

  const { loading: catalogLoading, data: catalogData } = useRequest(
    () => request.get<{}, string[]>(askBIApiUrls.xengineDatasource.listNames),
    {
      onSuccess: (res) => {
        const initCatalog = res && res[0]
        if (initCatalog) {
          form.setFieldValue('catalog', initCatalog)
          getDatabaseRun(initCatalog)
        }
      },
    },
  )

  const {
    loading: dbLoading,
    data: dbData,
    run: getDatabaseRun,
    mutate: changeDbData,
  } = useRequest(
    async (catalog) => {
      if (catalog) {
        return request.get<{ catalog: string }, string[]>(askBIApiUrls.xengineDatasource.databaseList, {
          params: { catalog },
        })
      }
      return []
    },
    {
      manual: true,
      onError: () => {
        changeDbData([])
        form.resetFields(['database'])
      },
      onSuccess: (res, request) => {
        const catalog = request[0]
        const initDatabase = res && res[0]
        form.setFieldValue('database', initDatabase)
        if (catalog) {
          const values = form.getFieldsValue()
          run({ current: 1, pageSize: PageSize }, values)
        }
      },
    },
  )

  const { loading, run, tableProps, search } = useAntdTable(
    async (pagination: { current: number; pageSize: number }, formData) => {
      const { catalog } = formData
      if (catalog) {
        const requestParams = pickBy(
          {
            ...(formData || {}),
            current: pagination.current,
            pageSize: pagination.pageSize,
          },
          Boolean,
        ) as { current: number; pageSize: number; catalog: string; database?: string; tableName?: string }
        return request.get(askBIApiUrls.xengineDatasource.tableListDetail, {
          params: requestParams,
        }) as Promise<{
          list: any[]
          total: number
        }>
      }

      return {
        list: [],
        total: 0,
      }
    },
    {
      form,
      manual: true,
    },
  )

  return (
    <>
      <PageHeader
        className="mb-4"
        title="表列表"
        extra={
          <Space>
            <Button
              type="primary"
              onClick={() => {
                navigate(`${routerMap.dataModel.createStreamVirtualTable.path}`)
              }}
            >
              创建流式表
            </Button>
            <Button type="primary" onClick={() => setShow(true)}>
              批量创建贴源虚拟表
            </Button>
          </Space>
        }
      />
      <Card className="mb-4">
        <Form
          form={form}
          layout="vertical"
          onValuesChange={(changeValueItem) => {
            const changeName = Object.keys(changeValueItem).join('')
            const changeValue = changeValueItem[changeName]
            switch (changeName) {
              case 'catalog': {
                getDatabaseRun(changeValue)
                break
              }
              default: {
                break
              }
            }
          }}
        >
          <Row gutter={[16, 0]}>
            <Col span={6}>
              <Form.Item label="数据源选择" name="catalog">
                <Select
                  placeholder="请选择"
                  loading={catalogLoading}
                  options={catalogData?.map((item: string) => {
                    return { label: item, value: item }
                  })}
                />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item label="数据库选择" name="database">
                <Select
                  placeholder="请选择"
                  loading={dbLoading}
                  options={dbData?.map((item: string) => {
                    return { label: item, value: item }
                  })}
                />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item label="表名称" name="table">
                <Input placeholder="请输入" />
              </Form.Item>
            </Col>

            <Col span={24} className="text-right">
              <Space>
                <Button type="primary" onClick={search.submit} loading={loading}>
                  搜索
                </Button>
                <Button
                  onClick={() => {
                    changeDbData([])
                    search.reset()
                  }}
                >
                  重置
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>

      <Card>
        <Table columns={tableColumns} rowKey="tableName" {...tableProps} loading={loading} />
      </Card>
      <BatchUpload
        title="批量创建贴源虚拟表"
        sampleType="likeVT"
        open={show}
        onClose={() => setShow(false)}
        uploadUrl={askBIApiUrls.xengine.VTable.createBatchVT}
      />
    </>
  )
}
