import React, { useEffect, useState } from 'react'
import { Button, Form, Input, message, Result, Select, Space, Card } from 'antd'
import { useRequest } from 'ahooks'
import { useSearchParams } from 'react-router-dom'
import request from 'src/shared/xengineAxios'
import { objectToFormData } from 'src/shared/common-utils'
import PageHeader from 'src/client/components/PageHeader'
import { askBIApiUrls } from 'src/shared/url-map'
import DbWithTable from './components/DbWithTable'
import { catalogTypeList, catalogFormMap } from './conf/constant'

type CatalogUpdateFormDataType = {
  principal?: number
  type: string
  jdbcUrl: string
  address: string
  odbcUrl?: string
  userName?: string
  passWord?: string
}
export default function CatalogUpdate() {
  const [searchParams] = useSearchParams()
  const initCatalogName = searchParams.get('catalogName') || ''
  const [form] = Form.useForm()

  const [catalogName, setCatalogName] = useState<string>(initCatalogName)
  const [success, setSuccess] = useState<boolean>(false)
  const [type, setType] = useState<string>('hive')
  const [method, _setMethod] = useState<number>(1)
  const [isEdit, setIsEdit] = useState(!!initCatalogName)

  const { loading, run } = useRequest(
    (data) => {
      const path = isEdit ? askBIApiUrls.xengineDatasource.edit : askBIApiUrls.xengineDatasource.add
      return request.post(path, objectToFormData(data), {
        headers: { 'Content-Type': 'multipart/form-data' },
      })
    },
    {
      manual: true,
      onSuccess: (res: string) => {
        if (res) {
          setCatalogName(res)
        }
        setSuccess(true)
      },
      onError() {
        const msg = isEdit ? '更新数据源失败' : '创建数据源失败'
        message.error(msg)
      },
    },
  )

  const { loading: tLoading, run: runTest } = useRequest(
    (data) =>
      request.post(askBIApiUrls.xengineDatasource.testing, objectToFormData(data), {
        headers: { 'Content-Type': 'multipart/form-data' },
      }),
    {
      manual: true,
      onSuccess: (res: { meta: boolean }) => {
        if (res.meta) {
          void message.success('数据源可用')
        } else {
          void message.error('数据源不可用')
        }
      },
      onError() {
        void message.error('数据源不可用')
      },
    },
  )

  const removeEmpty = <T extends Record<string, any>>(obj: T): null | Partial<T> => {
    if (!obj) return null
    for (const key in obj) {
      if (
        obj[key] === '' ||
        obj[key] === null ||
        obj[key] === 'null' ||
        obj[key] === undefined ||
        Number.isNaN(obj[key])
      ) {
        delete obj[key]
      }
    }
    return obj
  }

  const testDatasource = async () => {
    const data = await form.validateFields()
    runTest(removeEmpty(data))
  }

  const onFormFinish = async () => {
    const formData = await form.validateFields()
    run(removeEmpty(formData))
  }

  const continueTask = () => {
    setSuccess(false)
    if (!initCatalogName) {
      form.resetFields()
      setCatalogName('')
    }
  }

  useEffect(() => {
    if (initCatalogName) {
      request
        .get<
          { catalog: string },
          CatalogUpdateFormDataType
        >(askBIApiUrls.xengineDatasource.detail, { params: { catalog: initCatalogName } })
        .then((res) => {
          if (res) {
            const jsonForm = removeEmpty<CatalogUpdateFormDataType>(res) as CatalogUpdateFormDataType
            const formData = jsonForm
            form.setFieldsValue({
              ...formData,
            })
            setIsEdit(true)
            setType(jsonForm.type?.toLowerCase())
            setCatalogName(initCatalogName)
          }
        })
    }
  }, [form, initCatalogName])

  return (
    <>
      <PageHeader title={`${isEdit ? '编辑' : '添加'}数据源`} onBack={() => window.history.back()} className="mb-4" />
      <Card className="mb-4">
        {success ? (
          <Result
            status="success"
            title={(isEdit ? '修改' : '创建') + '数据源成功'}
            extra={[
              <Button type="primary" onClick={() => history.back()} key="return">
                返回上级
              </Button>,
              <Button onClick={continueTask} key="create">
                {isEdit ? '继续修改' : '继续创建'}
              </Button>,
            ]}
          />
        ) : (
          <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }} style={{ maxWidth: 600 }}>
            <Form.Item label="数据源类型" name="type" required initialValue={type}>
              <Select options={catalogTypeList} value={type} disabled={!!catalogName} onChange={setType} />
            </Form.Item>
            <Form.Item
              label="数据源名称"
              name="name"
              required
              rules={[
                { required: true, message: '请填写' },
                {
                  pattern: /^[A-Za-z0-9_]+$/,
                  message: '数据源名称是包含大写字母、小写字母、数字、下划线的任意字符串',
                },
              ]}
            >
              <Input placeholder="请输入" disabled={!!catalogName} />
            </Form.Item>
            {type &&
              catalogFormMap[type as keyof typeof catalogFormMap] &&
              catalogFormMap[type as keyof typeof catalogFormMap](method)}
            <Form.Item label="数据源备注" name="remark">
              <Input.TextArea placeholder="请输入" autoSize={{ minRows: 1, maxRows: 6 }} />
            </Form.Item>
            <Form.Item wrapperCol={{ span: 16, offset: 6 }}>
              <Space>
                <Button loading={tLoading} disabled={loading} onClick={testDatasource}>
                  测试数据源
                </Button>
                <Button type="primary" disabled={tLoading} loading={loading} onClick={onFormFinish}>
                  连接数据源
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Card>

      {type !== 'kafka' && <DbWithTable catalogName={catalogName} />}
    </>
  )
}
