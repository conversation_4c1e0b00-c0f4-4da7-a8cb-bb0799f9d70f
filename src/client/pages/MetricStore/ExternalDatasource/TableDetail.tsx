import { Button, Descriptions, message, Table, Tooltip, Card } from 'antd'
import { useSearchParams } from 'react-router-dom'
import React, { useEffect, useState } from 'react'
import { useAntdTable } from 'ahooks'
import { nanoid } from 'nanoid'
import { InfoCircleOutlined } from '@ant-design/icons'
import { askBIApiUrls } from 'src/shared/url-map'
import PageHeader from 'src/client/components/PageHeader'
import request from 'src/shared/xengineAxios'
import { ResponseErrorType } from 'src/shared/metric-types'
import { streamTableCatalogType } from 'src/client/x-engine/common/libs/util'
import VtSetting from './components/VtSetting'
import { getLastDotStr } from './conf/util'
import { TableItem } from './conf/interface'
import { detailTabItems, VIRTUAL_TABLE_DETAIL_PANELS, tableSchemaColumns } from './conf/constant'
import { TableDetailVo } from '@api'

export default function TableDetail() {
  const [activeTabKey, setActiveTabKey] = useState<string>('0')
  const [info, setInfo] = useState<TableDetailVo & TableItem>()
  const [searchParams] = useSearchParams()
  const tableName = searchParams.get('name') || ''
  const catalogName = searchParams.get('catalog') || ''
  const database = searchParams.get('database') || ''

  const { loading, tableProps: schemaTableProps } = useAntdTable(
    () =>
      request
        .get<{ catalog: string; database: string; tableName: string }, TableDetailVo & TableItem>(
          askBIApiUrls.xengineDatasource.tableInfo,
          {
            params: { catalog: catalogName, database, tableName },
          },
        )
        .then((res) => {
          setInfo(res)
          return {
            list: res.schema,
            total: res.schema.length,
          }
        }),

    {
      cacheKey: 'schemeTypeCacheKey',
      onError(e: Error & ResponseErrorType) {
        message.error(e?.msg || '获取虚拟表字段信息出错')
      },
    },
  )

  detailTabItems[VIRTUAL_TABLE_DETAIL_PANELS.COLUMN_TYPE].children = (
    <Table
      key={VIRTUAL_TABLE_DETAIL_PANELS.COLUMN_TYPE}
      {...schemaTableProps}
      loading={loading}
      columns={tableSchemaColumns}
      rowKey={() => nanoid()}
      scroll={{ x: 'max-content' }}
    />
  )

  const { data: previewData, tableProps: previewDataProps } = useAntdTable(
    async () =>
      request
        .get<
          {
            catalog: string
            database: string
            tableName: string
          },
          any[]
        >(askBIApiUrls.xengineDatasource.previewData, { params: { catalog: catalogName, database, tableName } })
        .then((res) => ({
          list: res,
          total: res.length ?? 0,
        })),
    {
      onError(e: Error & ResponseErrorType) {
        console.error(e?.msg || '获取虚拟表数据出错')
      },
      defaultPageSize: 10,
    },
  )

  detailTabItems[VIRTUAL_TABLE_DETAIL_PANELS.DATA_PREVIEW].children = (
    <Table
      key={VIRTUAL_TABLE_DETAIL_PANELS.DATA_PREVIEW}
      {...previewDataProps}
      dataSource={previewData?.list}
      columns={Object.keys(previewData?.list[0] ?? []).map((e) => ({
        title: getLastDotStr(e),
        dataIndex: e,
        width: 200,
        render: (d: string) => d || '-',
      }))}
      scroll={{ x: 1100 }}
      rowKey={() => nanoid()}
    />
  )

  const {
    loading: loadingPartition,
    tableProps: partitionTableProps,
    refresh: refreshPartition,
  } = useAntdTable(
    async (params) => {
      if (!info || (info && info.catalogType !== 'hive')) {
        return { list: [], total: 0 }
      }
      return request
        .get<{}, { list: any[]; total: number }>(askBIApiUrls.xengineDatasource.partitions, {
          params: { catalog: catalogName, database, table: tableName, ...params },
        })
        .then((res) => {
          const order = params?.sorter?.order
          if (order === 'descend') {
            return {
              ...res,
              list: res.list?.sort((a, b) => b.partition - a.partition),
            }
          }
          if (order === '"ascend"') {
            return {
              ...res,
              list: res.list?.sort((a, b) => a.partition - b.partition),
            }
          }
          return res
        })
    },
    {
      onError(e: Error & ResponseErrorType) {
        console.error(e?.msg || '获取数据分区信息出错')
      },
    },
  )

  useEffect(() => {
    if (info && info?.catalogType === 'hive') {
      detailTabItems.push({
        key: '2',
        label: `数据分区情况`,
        children: 'Content of Tab Pane 3',
      })
      refreshPartition()
    }
    return () => {
      if (detailTabItems.length > 2) {
        detailTabItems.splice(2, 1)
      }
    }
  }, [info, refreshPartition])

  if (detailTabItems.length > 2 && info && info?.catalogType === 'hive') {
    detailTabItems[VIRTUAL_TABLE_DETAIL_PANELS.PARTITION_INFO].children = (
      <Table
        key={VIRTUAL_TABLE_DETAIL_PANELS.PARTITION_INFO}
        {...partitionTableProps}
        loading={loadingPartition}
        columns={[
          { title: 'partition', dataIndex: 'partition', sorter: true },
          { title: '行数', dataIndex: 'numRows' },
        ]}
        scroll={{ x: 1100 }}
        rowKey={() => nanoid()}
      />
    )
  }

  return (
    <>
      <PageHeader
        className="mb-4"
        title="表详情"
        onBack={() => window.history.back()}
        extra={
          <VtSetting sourceTable={tableName} sourceDatabase={database} sourceCatalog={catalogName}>
            <Button type="primary">创建虚拟表</Button>
          </VtSetting>
        }
      />
      <Card loading={loading}>
        {streamTableCatalogType.includes(String(info?.catalogType)) ? (
          <Descriptions column={4} bordered>
            <Descriptions.Item label="数据表名称">{info?.tableName}</Descriptions.Item>
            <Descriptions.Item label="创建时间">{info?.createTime || '-'}</Descriptions.Item>
            <Descriptions.Item label="创建人">{info?.creator || '-'}</Descriptions.Item>
            <Descriptions.Item label="数据库">{info?.database}</Descriptions.Item>
            <Descriptions.Item label="数据来源">{info?.catalog}</Descriptions.Item>
            <Descriptions.Item label="来源 topic">{info?.kafkaTopic || ''}</Descriptions.Item>
            <Descriptions.Item label="消费体格式">{info?.kafkaFormat || ''}</Descriptions.Item>
            <Descriptions.Item label="时间列">{info?.kafkaTimeColumn || '-'}</Descriptions.Item>
          </Descriptions>
        ) : (
          <Descriptions column={4} bordered>
            <Descriptions.Item label="数据表名称">{info?.tableName}</Descriptions.Item>
            <Descriptions.Item label="数据表类型">{info?.catalogType}</Descriptions.Item>
            <Descriptions.Item label="创建者">{info?.creator || '-'}</Descriptions.Item>
            <Descriptions.Item label="创建时间">{info?.createTime || '-'}</Descriptions.Item>
            <Descriptions.Item label="列数">{info?.columnCount || '-'}</Descriptions.Item>
            <Descriptions.Item label="行数">{info?.fieldCount || '-'}</Descriptions.Item>
            <Descriptions.Item label="所属数据源">{info?.catalog}</Descriptions.Item>
            <Descriptions.Item
              label={
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <span style={{ marginRight: 5 }}>所属数据库</span>{' '}
                  <Tooltip title="为了便于展现，Oracle下Schema=数据库">
                    <InfoCircleOutlined />
                  </Tooltip>
                </div>
              }
            >
              {info?.database}
            </Descriptions.Item>
            <Descriptions.Item label="数据大小">{info?.size || '-'}</Descriptions.Item>
            <Descriptions.Item label="备注" span={3}>
              {info?.mark || '-'}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Card>
      <Card
        className="my-4 overflow-hidden"
        tabList={detailTabItems.map((i) => {
          return { key: i.key, tab: i.label }
        })}
        activeTabKey={activeTabKey}
        onTabChange={(i) => {
          setActiveTabKey(i)
        }}
      >
        {detailTabItems[parseInt(activeTabKey)]?.children}
      </Card>
    </>
  )
}
