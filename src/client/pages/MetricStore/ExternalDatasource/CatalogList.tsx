import React, { useRef, useState } from 'react'
import { <PERSON>ton, Modal, TableProps, message } from 'antd'
import dayjs from 'dayjs'
import { useNavigate } from 'react-router-dom'
import { pickBy } from 'lodash'
import request from 'src/shared/xengineAxios'
import Search, { type SearchActionType, type ItemType } from 'src/client/components/Search'
import { askBIApiUrls, askBIPageUrls } from 'src/shared/url-map'
import PageHeader from 'src/client/components/PageHeader'
import { formItems, catalogColumns } from './conf/constant'
type CatalogListRequest = {
  name?: string
  type?: string
  createTime?: [string, string]
  remark?: string
  pageSize: number
  current: number
}

function searchBeforeTransform(data: CatalogListRequest) {
  if (Array.isArray(data.createTime)) {
    const createTime = data.createTime.map((t: string) => dayjs(t).valueOf()).join('-')
    return { ...(data || {}), createTime }
  }
  return data
}

export default function CatalogList() {
  const [formItem] = useState(formItems as ItemType[])
  const navigate = useNavigate()
  const actionRef = useRef<SearchActionType>(null)
  function onRowChange(record: CatalogListRequest) {
    return {
      onClick: (e: any) => {
        if (e.target.innerHTML && e.target.dataset.type) {
          const type = e.target.dataset.type
          if (type === 'update') {
            catalogUpdate(record.name)
          }
          if (type === 'delete') {
            Modal.confirm({
              content: '是否删除该数据源',
              onOk() {
                request.get(askBIApiUrls.xengineDatasource.del, { params: { catalogName: record.name } }).then(() => {
                  message.success('删除成功')
                  actionRef.current && actionRef.current.refresh()
                })
              },
            })
          }
        }
      },
    }
  }

  function catalogUpdate(name?: string) {
    let navigatePath = askBIPageUrls.manage.externalDatasource.catalogUpdate
    if (name) {
      navigatePath += `?catalogName=${name}`
    }
    navigate(navigatePath)
  }

  return (
    <>
      <PageHeader
        className="mb-4"
        title="数据源管理"
        onBack={() => navigate(-1)}
        extra={
          <Button type="primary" onClick={() => catalogUpdate()}>
            新增数据源
          </Button>
        }
      />
      <Search
        key="catalog-list-search"
        items={formItem}
        actionRef={actionRef}
        request={async (pagination = { pageSize: 10, current: 1 }, formData = {}) => {
          const params = pickBy<CatalogListRequest>(
            { pageSize: pagination.pageSize, current: pagination.current, ...formData },
            Boolean,
          ) as CatalogListRequest
          return request.get(askBIApiUrls.xengineDatasource.catalogList, {
            params: searchBeforeTransform(params),
          })
        }}
        table={{
          columns: catalogColumns as TableProps<CatalogListRequest>['columns'],
          rowKey: 'name',
          onRow: onRowChange,
        }}
      />
    </>
  )
}
