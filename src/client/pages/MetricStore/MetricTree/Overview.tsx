import React, { Suspense, useCallback, useEffect, useMemo, useState } from 'react'
import axios from 'axios'
import { App, Button, Empty, Input, Pagination, Select, Popconfirm } from 'antd'
import { useRequest } from 'ahooks'
import { useAtomValue } from 'jotai'
import { PlusOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { Tooltip } from 'antd/lib'
import AdminPage from 'src/client/components/AdminPage'
import AdminCard from 'src/client/components/AdminCard'
import { askBIApiUrls } from 'src/shared/url-map'
import { APIResponse } from 'src/shared/common-types'
import type { Metric, MetricTreeNodeDataType } from 'src/shared/metric-types'
import { formatPathWithBaseUrl } from 'src/client/utils'
import { currentDatasetAtom, semanticProjectInfoAtom } from '../../AskBI/askBIAtoms'
const MetricTreeGraph = React.lazy(() => import('src/client/components/MetricTreeGraph/MetricTreeGraph'))

function getLabelByMetricName(metricList: Metric[], metricName: string) {
  const metric = metricList.find((item) => item.name === metricName)
  return metric?.label || '-'
}

const MetricTreeOverview = () => {
  const navigate = useNavigate()
  const { message: antdMessage } = App.useApp()
  const currentDataset = useAtomValue(currentDatasetAtom)
  const [isPopoverVisible, setIsPopoverVisible] = useState(false)
  const [currentSelectedProjectId, setCurrentSelectProjectId] = useState<string | null>(null)
  const [currentSelectedMetricName, setCurrentSelectedMetricName] = useState<string | null>(null)
  const [metricList, setMetricList] = useState([])
  const [metricTreeTitle, setMetricTreeTitle] = useState('')
  const [loadingDeleteId, setLoadingDeleteId] = useState<string>()
  const semanticProjectInfo = useAtomValue(semanticProjectInfoAtom)
  const [projectAllMetricList, setProjectAllMetricList] = useState<Metric[]>([])

  useEffect(() => {
    const loadMetricList = async () => {
      if (currentSelectedProjectId) {
        await axios
          .get(askBIApiUrls.semanticProject.listAllMetrics(currentSelectedProjectId))
          .then((result) => {
            setMetricList(result.data.data.allMetrics)
          })
          .catch((error) => {
            console.error(error)
          })
      }
    }
    loadMetricList()
  }, [currentSelectedProjectId])

  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(15)
  // 当前页数据列表状态
  const [currentPageData, setCurrentPageData] = useState<MetricTreeNodeDataType[]>([])
  const [isCreatingTree, setIsCreatingTree] = useState(false)

  const {
    run: loadMetricTree,
    data: metricTreeData,
    loading: isLoading,
    refresh: getMetricTreeAgain,
    error: isError,
  } = useRequest(
    async (projectId: string) => {
      const response = await axios.get<APIResponse<{ list: MetricTreeNodeDataType[]; total: number }>>(
        askBIApiUrls.semanticMetricTree.list(projectId),
      )
      return response.data.data
    },
    {
      manual: true,
      onError: (error) => {
        antdMessage.error('获取指标树数据失败')
        console.error('Load SemanticMetricTree data error:', error)
      },
    },
  )
  const loadProjectAllMetricList = useCallback(async () => {
    if (currentDataset) {
      const response = await axios.get<APIResponse<{ allMetrics: Metric[] }>>(
        askBIApiUrls.semanticProject.listAllMetrics(currentDataset.projectId),
      )
      const allMetrics = response?.data?.data?.allMetrics || []
      setProjectAllMetricList(allMetrics)
    }
  }, [currentDataset])

  const { run: deleteMetricTree, loading: deleteLoading } = useRequest(
    async (projectId: string, treeId: string) => {
      const response = await axios.delete(askBIApiUrls.semanticMetricTree.delete(projectId, treeId))
      return response.data.data
    },
    {
      manual: true,
      onSuccess: () => {
        getMetricTreeAgain()
      },
      onError: (error) => {
        antdMessage.error('删除指标树数据失败')
        console.error('delete SemanticMetricTree data error:', error)
      },
      onFinally: () => {
        setLoadingDeleteId('')
      },
    },
  )

  const initMetricTree = useCallback(
    (tree: MetricTreeNodeDataType): MetricTreeNodeDataType => {
      if (!tree || !tree.metricName) {
        return tree
      }
      const metricName = tree.metricName
      const label = getLabelByMetricName(projectAllMetricList, metricName)
      const newChildren = tree.children?.map(initMetricTree).filter(Boolean) || []
      return {
        ...tree,
        label,
        children: newChildren,
      } as MetricTreeNodeDataType
    },
    [projectAllMetricList],
  )

  useEffect(() => {
    if (currentDataset) {
      loadMetricTree(currentDataset.projectId)
      loadProjectAllMetricList()
    }
  }, [currentDataset, loadMetricTree, loadProjectAllMetricList])

  const updateCurrentPageData = useCallback(
    (page: number, pageSize: number) => {
      if (!metricTreeData) return
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + pageSize
      const metricTreeDataList = (metricTreeData.list || []).slice(startIndex, endIndex)
      const updateCurrentPageData = metricTreeDataList.map((metricTree) => {
        return initMetricTree(metricTree)
      })
      setCurrentPageData(updateCurrentPageData)
    },
    [metricTreeData, initMetricTree],
  )

  const handlePaginationChange = (page: number, newPageSize: number) => {
    setCurrentPage(page)
    setPageSize(newPageSize)
    updateCurrentPageData(page, newPageSize)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  useEffect(() => {
    updateCurrentPageData(currentPage, pageSize)
  }, [metricTreeData, currentPage, pageSize, updateCurrentPageData])

  const MetricTreeGraphCfg = useMemo(
    () => ({
      height: 150,
      defaultZoom: 0.8,
    }),
    [],
  )

  const renderChartList = () => {
    if (isLoading) {
      return (
        <Empty className="mx-auto pt-10" image={Empty.PRESENTED_IMAGE_SIMPLE} description="正在加载指标树列表..." />
      )
    }

    if (isError instanceof Error) {
      return (
        <Empty
          className="mx-auto pt-10"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="加载指标树出错，请联系管理员排查"
        />
      )
    }

    if (metricTreeData?.total === 0) {
      return <Empty className="mx-auto pt-10" image={Empty.PRESENTED_IMAGE_SIMPLE} description="当前指标树列表为空！" />
    }

    return (
      <div className="metric-tree-card-wrapper grid cursor-pointer grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {currentPageData.map((tree) => (
          <div key={tree.id}>
            <div className="metric-tree-card group/item cursor-pointer rounded-md p-2 ring-1 ring-gray-200 dark:ring-gray-700">
              <div
                className={`relative h-[150px] w-full min-w-[240px] cursor-pointer overflow-hidden`}
                onClick={() => {
                  currentDataset &&
                    currentDataset.projectId &&
                    navigate(
                      formatPathWithBaseUrl(`/metric-store/metric-tree/detail/${currentDataset.projectId}/${tree.id}`),
                    )
                }}
              >
                <Suspense fallback={<div>Loading...</div>}>
                  <MetricTreeGraph data={tree} graphCfg={MetricTreeGraphCfg} />
                </Suspense>

                <div className="absolute bottom-0 left-0 right-0 top-0 cursor-pointer" />
              </div>
              <div className="mx-2 mt-2 flex justify-between align-middle font-medium">
                <div className="text-base/[32px]">{tree.treeName}</div>
                <Popconfirm
                  title="删除指标树"
                  description={`删除当前${tree.treeName}指标树，删除后将无法恢复，确认删除？`}
                  onConfirm={() => {
                    setLoadingDeleteId(tree.id)
                    deleteMetricTree(currentDataset?.projectId || '', tree.id)
                  }}
                  okButtonProps={{ loading: deleteLoading && tree.id === loadingDeleteId }}
                  okText="确认"
                  cancelText="取消"
                >
                  <Button
                    className="delete-metric-tree-btn"
                    danger
                    type="text"
                    loading={deleteLoading && tree.id === loadingDeleteId}
                  >
                    删除
                  </Button>
                </Popconfirm>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  const filterOption = (input: string, option?: { label: string; value: string }) =>
    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())

  return (
    <AdminPage title="指标树概览">
      <AdminCard>
        <div className="flex flex-col">
          <div className="create-metric-tree-wrapper relative ml-auto p-1">
            {/* <Search
              style={{ width: 150 }}
              placeholder="搜索指标"
              allowClear
              onSearch={() => {
                return
              }}
            /> */}
            {metricTreeData?.total && metricTreeData.total >= 1 ? (
              <Tooltip title="只支持新建一颗指标树，你可以编辑">
                <span style={{ display: 'inline-block', cursor: 'not-allowed' }}>
                  <Button disabled>新建指标树</Button>
                </span>
              </Tooltip>
            ) : (
              <Button
                className="mb-2 ml-2"
                type="primary"
                icon={<PlusOutlined />}
                size="middle"
                onClick={() => {
                  setIsPopoverVisible(true)
                }}
              >
                新建指标树
              </Button>
            )}
            {isPopoverVisible && (
              <div className="absolute right-1 top-full z-10 w-72 rounded border">
                <div className="rounded bg-white p-2 text-base font-medium">新建指标树</div>
                <div className="flex flex-col gap-1 bg-[#F9FBFD] p-2">
                  <span className="text-[#575757]">选择项目</span>
                  <Select
                    showSearch
                    filterOption={filterOption}
                    placeholder="请选择项目"
                    options={semanticProjectInfo.map((e) => ({
                      label: e.name,
                      value: e.id,
                    }))}
                    onChange={(e) => {
                      setCurrentSelectProjectId(e)
                      setCurrentSelectedMetricName(null)
                    }}
                  />
                  <span className="text-[#575757]">指标树名称</span>
                  <Input
                    value={metricTreeTitle}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setMetricTreeTitle(event.target.value)
                    }}
                  />
                  <span className="text-[#575757]">选择根指标</span>
                  <Select
                    showSearch
                    filterOption={filterOption}
                    placeholder="请选择根指标"
                    options={(metricList || []).map((e: Metric) => ({
                      label: `${e.label}（${e.name}）`,
                      value: e.name,
                    }))}
                    onChange={(value) => {
                      setCurrentSelectedMetricName(value)
                    }}
                  />
                </div>
                <div className="flex justify-end gap-2 rounded bg-[#F8F8F8] p-2">
                  <Button
                    type="default"
                    onClick={() => {
                      setIsPopoverVisible(false)
                    }}
                  >
                    取消
                  </Button>
                  <Button
                    type="primary"
                    loading={isCreatingTree}
                    onClick={async () => {
                      if (currentSelectedProjectId && currentSelectedMetricName) {
                        setIsCreatingTree(true)
                        await axios
                          .post(askBIApiUrls.semanticMetricTree.create, {
                            treeName: metricTreeTitle.length > 0 ? metricTreeTitle : currentSelectedMetricName,
                            metricName: currentSelectedMetricName,
                            projectId: currentSelectedProjectId,
                          })
                          .then((result) => {
                            const treeId = result.data.data.id
                            navigate(
                              formatPathWithBaseUrl(
                                `/metric-store/metric-tree/detail/${currentSelectedProjectId}/${treeId}`,
                              ),
                            )
                            setCurrentSelectProjectId(null)
                            setCurrentSelectedMetricName(null)
                          })
                          .catch((error) => {
                            antdMessage.error(error.response?.data?.msg || '创建失败')
                          })
                          .finally(() => {
                            setIsCreatingTree(false)
                          })
                      } else {
                        antdMessage.error('请正确选择')
                      }
                    }}
                  >
                    保存
                  </Button>
                </div>
              </div>
            )}
          </div>
          {renderChartList()}
          {!!metricTreeData?.total && metricTreeData.total > 0 && (
            <Pagination
              className="mt-4 self-end"
              current={currentPage}
              hideOnSinglePage
              onChange={handlePaginationChange}
              pageSize={pageSize}
              total={metricTreeData.total}
              showSizeChanger
            />
          )}
        </div>
      </AdminCard>
    </AdminPage>
  )
}

export default MetricTreeOverview
