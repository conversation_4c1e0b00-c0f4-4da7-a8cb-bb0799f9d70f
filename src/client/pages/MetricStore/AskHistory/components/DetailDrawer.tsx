import { Drawer } from 'antd'
import React, { useMemo } from 'react'
import { format as sqlFormatter } from 'sql-formatter'
import Syntax<PERSON>ighlighter from 'react-syntax-highlighter'
import './DetailDrawer.css'
import AnswerView from 'src/client/pages/AskDoc/DocDetail/AnswerView'
import DetailBIResult from './DetailBIResult'
import { AskHistoryItem } from './Table'

type Props = { visible: boolean; onClose: () => void; chatData: AskHistoryItem | null }

const topBarHeight = 62

export default function DetailDrawer({ visible, onClose, chatData }: Props) {
  const chatResponse = chatData?.response
  const docResponse = chatData?.docResponse
  const data = useMemo(() => {
    return chatResponse?.[0] || {}
  }, [chatResponse])
  const title = chatData?.ask
  return (
    <Drawer
      className="ask-history-detail-drawer"
      open={visible}
      placement="bottom"
      height={`calc(100% - ${topBarHeight}px)`}
      onClose={onClose}
      title={title}
      maskClosable
      // 关闭后不销毁,会导致图表渲染出问题
      destroyOnClose
    >
      {docResponse && docResponse.length > 0 && (
        <div className="response-doc">
          <div className="mb-2">Doc回答结果:</div>
          <div className="rounded border px-6 py-4">
            <AnswerView content={docResponse[0]} status={'success'} isViewMode={true} />
          </div>
        </div>
      )}
      {data?.ready === true ? (
        <div className="flex gap-4">
          <div className="response-data w-1/2">
            <div className="mb-2">数据结果:</div>
            <div className="rounded border p-0">
              <DetailBIResult data={chatData} />
            </div>
          </div>
          {data.sql ? (
            <div className="response-sql w-1/2">
              <div className="mb-2">SQL:</div>
              <div className="rounded border px-6 py-4">
                <div>
                  <SyntaxHighlighter
                    language="sql"
                    customStyle={{ background: 'bg-slate-50 dark:bg-slate-700', padding: 0 }}
                  >
                    {sqlFormatter(data.sql, { indentStyle: 'tabularLeft' })}
                  </SyntaxHighlighter>
                </div>
              </div>
            </div>
          ) : null}
        </div>
      ) : null}
    </Drawer>
  )
}
