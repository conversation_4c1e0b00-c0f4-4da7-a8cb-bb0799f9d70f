import React from 'react'
import axios from 'axios'
import { Modal, Table } from 'antd'
import { APIResponse, ElkResponse } from 'src/shared/common-types'
import { askBIApiUrls } from 'src/shared/url-map'
import { AskHistoryItem } from './Table'

const contentRenderer = (type: string) => {
  const Renderer = (content: string) => (
    <span
      className="cursor-pointer whitespace-nowrap text-blue-400"
      onClick={() =>
        Modal.info({
          icon: null,
          title: type,
          centered: true,
          width: '100%',
          style: { height: '80%', overflowY: 'auto' },
          content: <pre style={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word' }}>{content}</pre>,
          maskClosable: true,
          onCancel: () => {},
        })
      }
    >
      展开
    </span>
  )
  Renderer.displayName = `ContentRenderer(${type})`
  return Renderer
}

const elkColumns = [
  {
    title: '时间',
    dataIndex: 'timestamp',
    width: 600,
  },
  {
    title: '服务',
    width: 40,
    dataIndex: 'service_type',
  },
  {
    title: '步骤',
    width: 60,
    dataIndex: 'module_type',
  },
  {
    title: '输入',
    width: 80,
    dataIndex: 'input',
    render: contentRenderer('输入'),
  },
  {
    title: '输出',
    width: 80,
    dataIndex: 'output',
    render: contentRenderer('输出'),
  },
  {
    title: '耗时',
    width: 80,
    dataIndex: 'duration',
  },
  {
    title: '返回码',
    width: 160,
    dataIndex: 'result_code',
  },
  {
    title: 'debug',
    width: 80,
    dataIndex: 'debug',
    render: contentRenderer('debug'),
  },
]

export const displayParamsExactResponse = async (record: AskHistoryItem) => {
  const modal = Modal.info({
    icon: null,
    title: '提参结果',
    centered: true,
    width: '100%',
    style: { height: '80%', overflowY: 'auto' },
    content: (
      <>
        <pre style={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word' }}>{JSON.stringify(record.response, null, 2)}</pre>
      </>
    ),
  })

  try {
    if (record.traceId) {
      const elkLogs = await axios
        .get<
          APIResponse<Array<{ _id: string; _source: ElkResponse }>>
        >(askBIApiUrls.elk.fetchLogByTraceId(record.traceId))
        .then((resp) => resp.data.data)

      modal.update({
        content: (
          <>
            <pre style={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word' }}>
              {JSON.stringify(record.response, null, 2)}
            </pre>
            <p>ELK日志</p>
            <Table
              columns={elkColumns}
              dataSource={(elkLogs || [])
                .map((e) => {
                  if (e['_source'].module_type !== 'express_app_middleware') {
                    return { ...e['_source'], id: e['_id'] }
                  }
                })
                .filter((e) => e)}
              rowKey="id"
              pagination={false}
              style={{ width: '100%' }}
            />
          </>
        ),
      })
    }
  } catch (error) {
    console.error('Error fetching logs:', error)
  }
}
