import { useAtomValue } from 'jotai/react'
import React, { useRef } from 'react'
import { BiResultContent } from 'src/client/components/ChatHistoryItem'
import { themeAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { readyResponseToChatAns } from 'src/client/pages/AskBI/Chat/utils'
import { BlobWithRatio, Chat } from 'src/shared/common-types'
import { AskHistoryItem } from './Table'

export default function DetailBIResult({ data }: { data: AskHistoryItem | null }) {
  const theme = useAtomValue(themeAtom)

  const chartWrapperRef = useRef<{
    downloadPNG: () => void
    copyPNG: () => void
    getBlob: () => Promise<BlobWithRatio | string>
  }>(null)

  if (!data) {
    return null
  }
  const projectId = data.conver.semanticProjectId
  const sceneId = data.conver.semanticSceneId

  const ansData = readyResponseToChatAns(data.response[0], sceneId)

  const currentChat: Chat = {
    id: data.id,
    ask: {
      role: 'user',
      content: data.ask,
      jsonContent: data.ask,
    },
    docAns: {
      content: [{ text: '正在为您查询这个问题...', type: 'text' }],
      role: 'assistant',
      status: 'pending',
    },
    selectedSceneId: sceneId,
    askTime: data.createdAt,
    ans: [{ content: ansData.content, role: 'assistant', sceneId, status: 'success' }],
  }

  return (
    <BiResultContent
      theme={theme}
      currentChat={currentChat}
      projectId={projectId}
      currentSelectedSceneId={sceneId}
      chartWrapperRef={chartWrapperRef}
      chatAnsItem={currentChat.ans[0]}
      isViewMode={true}
    />
  )
}
