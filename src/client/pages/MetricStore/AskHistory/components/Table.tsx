import { <PERSON>Form, ProFormItem, ProFormSelect } from '@ant-design/pro-components'
import { useRequest } from 'ahooks'
import { App, Button, Form, Input, Spin, Table } from 'antd'
import dayjs from 'dayjs'
import axios from 'axios'
import { useAtomValue } from 'jotai/react'
import React, { useEffect, useMemo, useState } from 'react'
import { currentDatasetAtom, isProjectChosenAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { APIResponse, AssistantDocItem, ConverChatErrorTypes, ConverChatErrorTypesMap } from 'src/shared/common-types'
import { askBIApiUrls } from 'src/shared/url-map'
import DetailDrawer from './DetailDrawer'
import { useExportModal } from './useExportModal'
import { displayParamsExactResponse } from './ParamsExactResponse'
import './Table.css'

export type AskHistoryItem = {
  conver: {
    username: string
    semanticProjectId: string
    semanticSceneId: string
  }
  ask: string
  createdAt: Date
  id: string
  converId: string
  parentId: string | null
  traceId: string | null
  response: any[]
  llmResponse: any[]
  docResponse: AssistantDocItem[]
  updatedAt: Date
  errorType: string | null
}

const initPagination = {
  current: 1,
  pageSize: 20,
}

export default function AskHistoryTable() {
  const isProjectChosen = useAtomValue(isProjectChosenAtom)
  const { node: ExportModalNode, visibleOps: exportModalVisibleOps } = useExportModal(isProjectChosen)
  const { message } = App.useApp()
  const currentDataset = useAtomValue(currentDatasetAtom)
  const [chatItem, setChatItem] = useState<AskHistoryItem | null>(null)
  const [isShowDrawer, setIsShowDrawer] = useState(false)
  const [form] = Form.useForm()
  const [total, setTotal] = useState(0)
  const [pagination, setPagination] = useState(initPagination)

  const errorTypeOptions = useMemo(() => {
    const options = Object.keys(ConverChatErrorTypesMap).map((key) => {
      return {
        label: ConverChatErrorTypesMap[key as keyof typeof ConverChatErrorTypesMap],
        value: key,
      }
    })
    return [{ label: '全部', value: '' }].concat(options)
  }, [])

  const {
    data: askList,
    loading: isLoading,
    run: getList,
  } = useRequest<any[] | undefined, unknown[]>(
    async () => {
      const { projectId, sceneId } = currentDataset || {}
      if (!projectId) {
        // 没有项目id就不请求
        return []
      }
      try {
        const { current, pageSize } = pagination
        const formData = form.getFieldsValue()
        const params = {
          ...formData,
          page: current,
          pageSize,
          projectId,
          sceneId,
          isProjectChosen,
        }
        const response = await axios.get<APIResponse<{ list: AskHistoryItem[]; total: number }>>(
          askBIApiUrls.converChats.askHistory,
          { params },
        )

        const { list: result, total = 0 } = response.data.data || {}
        setTotal(total)
        return result
      } catch (error: any) {
        message.error(`获取最近提问列表失败：${error?.message}`, 0.5)
        console.error('Conversations error =', error)
      }
    },
    { manual: true },
  )

  useEffect(() => {
    if (currentDataset?.projectId || currentDataset?.sceneId) {
      setPagination({ current: 1, pageSize: pagination.pageSize })
    }
  }, [pagination.pageSize, currentDataset])

  const onViewResult = (record: AskHistoryItem) => {
    setChatItem(record)
    setIsShowDrawer(true)
  }

  const onCloseDrawer = () => {
    setChatItem(null)
    setIsShowDrawer(false)
  }

  const columns = [
    {
      title: '提问',
      dataIndex: 'ask',
      width: 600,
    },
    {
      title: '提问人',
      width: 80,
      dataIndex: ['conver', 'username'],
    },
    {
      title: '提问时间',
      width: 120,
      dataIndex: 'createdAt',
      render(value: string) {
        return dayjs(value).format('YYYY-MM-DD HH:mm:ss')
      },
    },
    {
      title: '错误类型',
      dataIndex: 'errorType',
      width: 100,
      render(value?: keyof typeof ConverChatErrorTypesMap) {
        if (value?.includes(ConverChatErrorTypesMap.DATA_ERROR_FEEDBACK)) {
          return value
        }
        if (value && ConverChatErrorTypesMap[value]) {
          return ConverChatErrorTypesMap[value]
        }
        return value
      },
    },
    {
      title: '操作',
      width: 40,
      render(record: AskHistoryItem) {
        return (
          <div>
            {!record.errorType ||
            record.errorType?.includes(ConverChatErrorTypesMap.DATA_ERROR_FEEDBACK) ||
            record.errorType === ConverChatErrorTypes.DATA_ERROR_FEEDBACK ? (
              <Button type="link" onClick={() => onViewResult(record)}>
                回答结果
              </Button>
            ) : null}
            <Button
              type="link"
              onClick={() => {
                displayParamsExactResponse(record)
              }}
            >
              提参结果
            </Button>
          </div>
        )
      },
    },
  ]

  useEffect(() => {
    getList()
  }, [pagination, getList])

  const onPageChange = (current: number, pageSize: number) => {
    setPagination({ current, pageSize })
  }

  const handleSearch = () => {
    onPageChange(1, pagination.pageSize)
  }
  return (
    <div className="ask-history-table">
      <div className="mb-4 flex items-center justify-between">
        <div className="flex gap-[4px]">
          <div className="font-bold">全部({total})</div>
          <Button size="small" type="primary" onClick={exportModalVisibleOps.setTrue}>
            导出Excel
          </Button>
        </div>
        <ProForm form={form} layout="inline" submitter={false}>
          {/* 暂时不根据提问人筛选 */}
          {/* <ProFormSelect
            width={180}
            onChange={getList}
            options={
              currentLoginUserInfo
                ? [{ label: currentLoginUserInfo.username, value: currentLoginUserInfo.username }]
                : []
            }
            showSearch
            allowClear
            name="username"
            label="提问人"
            placeholder="请选择提问人"
          /> */}
          <ProFormSelect
            width={180}
            onChange={handleSearch}
            options={errorTypeOptions}
            showSearch
            name="errorType"
            label="错误类型"
            placeholder="请选择错误类型"
          />
          <ProFormItem name="content">
            <Input.Search width={180} placeholder="搜索问题内容" onSearch={handleSearch} />
          </ProFormItem>
        </ProForm>
      </div>
      <Spin spinning={isLoading}>
        <Table
          columns={columns}
          dataSource={askList || []}
          rowKey="id"
          pagination={{
            onChange: onPageChange,
            ...pagination,
            total: total,
          }}
        />
      </Spin>

      <DetailDrawer visible={isShowDrawer} onClose={onCloseDrawer} chatData={chatItem} />
      {ExportModalNode}
    </div>
  )
}
