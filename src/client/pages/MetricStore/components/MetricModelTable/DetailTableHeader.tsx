import React, { useState } from 'react'
import { get } from 'lodash'
import { useSearchParams } from 'react-router-dom'
import {
  SvgIcon,
  metricModelIcon,
  catalogIcon,
  databaseIcon,
  virtualTableIcon,
  timeIcon,
} from 'src/client/components/SvgIcon'
import { type MetricModelType, TimeGranularityMap } from 'src/shared/metric-types'
import ReplaceMetricModelModal from 'src/client/pages/MetricStore/components/ReplaceMetricModelModal'
import CreateMetricModelDrawer from '../CreateMetricModelDrawer'

type PropsType = {
  data: MetricModelType
  className?: string
  onReplaceMetricModelModalSuccess?: (modelNames: string[]) => void
  onUpdateMetricModelSuccess?: () => void
}
const VTableIcons = {
  catalogName: catalogIcon,
  databaseName: databaseIcon,
  name: virtualTableIcon,
} as const
export default function DetailTableHeader(props: PropsType) {
  const { data, onReplaceMetricModelModalSuccess, onUpdateMetricModelSuccess } = props
  const [searchParams] = useSearchParams()
  const scenarioId = searchParams.get('scenarioId') || ''
  const [drawerOpen, setDrawerOpen] = useState(false)
  const [replaceMetricModelModalOpen, setReplaceMetricModelModalOpen] = useState(false)

  const dataModelDesc = data?.dataModelDesc
  if (!dataModelDesc) {
    return <></>
  }

  const granularity = get(dataModelDesc, 'timeColumnDesc.granularity') as keyof typeof TimeGranularityMap
  return (
    <div className={`${props.className} overflow-x-auto text-gray-900 dark:text-slate-100`}>
      <div className="mb-2 flex flex-wrap items-center text-[11px]">
        <div className="mb-1 mr-6 flex items-center">
          <span className="mr-2">模型</span>
          <div className="flex items-center bg-[#F3F1FF] px-3 py-1">
            <SvgIcon icon={metricModelIcon} className="mr-1 h-5 w-5" />
            <div className="border-r border-r-[#E2E2E2] pr-2 text-gray-900">{data?.name || '-'}</div>
            {scenarioId && (
              <div
                className="cursor-pointer pl-2 font-medium text-link"
                onClick={() => {
                  setReplaceMetricModelModalOpen(true)
                }}
              >
                替换
              </div>
            )}

            <div
              className="cursor-pointer pl-2 font-medium text-link"
              onClick={() => {
                setDrawerOpen(true)
              }}
            >
              修改
            </div>
          </div>
        </div>

        <div className="mb-1 mr-6 flex items-center">
          <span className="mr-2">来源表</span>
          <div className="flex">
            {Object.keys(VTableIcons).map((key, index) => {
              return (
                <React.Fragment key={key}>
                  {data[key as keyof typeof VTableIcons] && (
                    <div className="flex items-center">
                      <SvgIcon
                        icon={VTableIcons[key as keyof typeof VTableIcons]}
                        className="mr-2 h-5 w-5"
                        key={index}
                      />
                      <span> {data?.dataModelDesc?.joinDag?.vertices?.[0]?.table?.split('.')?.[index] || '-'}</span>
                      {index < 2 && <span className="mx-1">/</span>}
                    </div>
                  )}
                </React.Fragment>
              )
            })}
          </div>
        </div>

        <div className="mb-1 mr-6 flex items-center">
          <span className="mr-2">时间字段</span>
          <div className="flex items-center bg-[#F3F1FF] px-3 py-1 text-gray-900">
            <SvgIcon icon={timeIcon} className="mr-2 h-5 w-5" />
            <div>{dataModelDesc?.timeColumnDesc?.column?.name || '-'}</div>
          </div>
        </div>

        <div className="mb-1 mr-6 flex items-center">
          <span className="mr-2 items-center">最小时间粒度</span>
          <div className="bg-[#F3F1FF] px-3 py-1 text-gray-900">{TimeGranularityMap[granularity] || '-'}</div>
        </div>

        <div className="mb-1 mr-6 flex items-center">
          <span className="mr-2">主时间维度</span>
          <div className="flex items-center bg-[#F3F1FF] px-3 py-1 text-gray-900">
            <SvgIcon icon={timeIcon} className="mr-2 h-5 w-5" />
            <div>{dataModelDesc?.timeColumnDesc?.column?.name || '-'}</div>
          </div>
        </div>
      </div>
      <CreateMetricModelDrawer
        isEdit
        values={data}
        type="createMetricModel"
        drawerOpen={drawerOpen}
        setDrawerOpen={setDrawerOpen}
        onSuccess={() => {
          setDrawerOpen(false)
          onUpdateMetricModelSuccess && onUpdateMetricModelSuccess()
        }}
      />
      {scenarioId && (
        <ReplaceMetricModelModal
          scenarioId={scenarioId}
          open={replaceMetricModelModalOpen}
          onClose={() => {
            setReplaceMetricModelModalOpen(false)
          }}
          onSuccess={(modelNames: string[]) => {
            setReplaceMetricModelModalOpen(false)
            onReplaceMetricModelModalSuccess && onReplaceMetricModelModalSuccess(modelNames)
          }}
        />
      )}
    </div>
  )
}
