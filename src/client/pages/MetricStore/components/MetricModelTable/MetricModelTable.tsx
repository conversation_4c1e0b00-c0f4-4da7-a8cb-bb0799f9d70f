import React, { useState, useEffect, useCallback, useMemo, useRef, memo } from 'react'
import { Table, Space, Input, message, Tooltip, Button, Form, Select, Cascader, type TableProps } from 'antd'
import {
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  PlusOutlined,
  MinusSquareOutlined,
  PlusSquareOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons'
import { debounce, pick, cloneDeep } from 'lodash'
import { produce } from 'immer'
import { nanoid } from 'nanoid'
import { FormInstance } from 'antd/lib'
import { CalculateForm } from 'src/client/pages/MetricStore/Metrics/fusion/CalculateForm'
import { SynonymsForm } from 'src/client/pages/MetricStore/Metrics/fusion/SynonymsForm'
import { MetricModelType, type MetricFuncType } from 'src/shared/metric-types'
import type { VTableColumnType, MetricModelColumnType } from './conf'
import {
  getListIndex,
  formatSynonymsText,
  resolveInitColumnData,
  renderColumnTypeIcon,
  EditableCell,
  EditableRow,
  renderCountTableHeader,
  MetricTypeColumn,
  timeGranularity,
  displayRender,
} from './conf'
import './styles.css'

type PropsType = {
  initMetricModelTableData: VTableColumnType[]
  loading?: boolean
  dataRef?: React.MutableRefObject<MetricModelColumnType[]>
  isEdit?: boolean
  headerType?: 'count'
  className?: string
  timeColumnForm: FormInstance
  VTableInfo: {
    catalog: string
    database: string
    tableName: string
  }
}
type TableMetricFuncType = MetricFuncType & { pTId: string; tId: string }

function filterModelList(value: string, fullModelList: MetricModelColumnType[]) {
  const reg = new RegExp(value, 'i')
  return fullModelList?.filter((item) => {
    const { columnName, columnAlias } = item
    return reg.test(columnName || '') || reg.test(columnAlias || '')
  })
}

const addDisableProperty = (data: MetricModelColumnType[]) => {
  const t = data.map((item) => {
    return { ...item, disabled: item.dimensions?.some((d) => d.dimensionType === 'TIME_DEFAULT') }
  })
  return t
}
const MetricModelTable = memo(function MetricModelTable({
  initMetricModelTableData,
  loading,
  dataRef,
  isEdit = false,
  headerType,
  VTableInfo,
  className,
  timeColumnForm,
}: PropsType) {
  const [currentEditRowIndex, setCurrentEditRowIndex] = useState<{ pIdx: number; idx?: number }>({ pIdx: -1 })

  const [metricModelTableData, setMetricModelTableData] = useState<MetricModelColumnType[]>()
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([])

  const [metricModelTableFullData, setMetricModelTableFullData] = useState<MetricModelColumnType[]>([])
  const [createCalculateOpen, setCreateCalculateOpen] = useState(false)
  const [createSynonymsOpen, setCreateSynonymsOpen] = useState(false)
  const [isCalculateFormReadonly, setIsCalculateFormReadonly] = useState(false)
  const searchValueRef = useRef('')
  // 更新metricModelTableData的值
  const updateMetricModelTableData = useCallback(
    (data: MetricModelColumnType[]) => {
      if (dataRef) {
        dataRef.current = data
      }
      setMetricModelTableFullData(data)
      const filterList = addDisableProperty(filterModelList(searchValueRef.current, data))
      setMetricModelTableData(filterList)
    },
    [dataRef],
  )
  // 初始化值
  useEffect(() => {
    const initData = resolveInitColumnData(initMetricModelTableData)
    updateMetricModelTableData(initData)
  }, [initMetricModelTableData, updateMetricModelTableData])

  // 算子表单CalculateForm的值
  const currentEditItem = useMemo(() => {
    const currentPIdx = currentEditRowIndex.pIdx
    if (metricModelTableFullData && currentPIdx >= 0) {
      return metricModelTableFullData[currentPIdx]
    }
    return null
  }, [currentEditRowIndex, metricModelTableFullData])

  // 当前edit同义词的值
  const currentEditSynonymsValue = useMemo(() => {
    if (!currentEditItem) {
      return []
    }
    const { metricType } = currentEditItem
    const { idx } = currentEditRowIndex
    const funcs = metricType && currentEditItem[metricType]
    const currentSynonymsParent = funcs && funcs[idx as number]
    return currentSynonymsParent?.synonyms
  }, [currentEditItem, currentEditRowIndex])

  // 判断是否可写，主要判断第一行
  // 可写的情况：无算子（维度）、单个算子、多个算子 - 展开
  // 不可写的情况：多个算子 - 折叠
  function isAvailable(record: MetricModelColumnType & MetricFuncType & { pTId?: string }) {
    const { tId, metricType, pTId = '' } = record
    const functions = (metricType && record[metricType]) || []
    const isNoFuncAndDimensionType = functions.length <= 1 && metricType === 'dimensions'
    return (
      functions?.length === 1 ||
      isNoFuncAndDimensionType ||
      expandedRowKeys.includes(tId) ||
      expandedRowKeys.includes(pTId)
    )
  }
  // 处理点击编辑维度/度量列的单元格
  function handleEditMetrics(record: MetricModelColumnType) {
    if (!record.metricType) {
      message.info('请先选择该列是属于度量列还是维度列')
      return
    }
    const [curIndex] = getListIndex(record, metricModelTableFullData)

    setCurrentEditRowIndex({ pIdx: curIndex })
    setCreateCalculateOpen(true)
    setIsCalculateFormReadonly(false)
  }

  // 点击产看度量/维度列的单元格
  function handleViewMetrics(record: MetricModelColumnType) {
    const [curIndex] = getListIndex(record, metricModelTableFullData)
    setCurrentEditRowIndex({ pIdx: curIndex })
    setCreateCalculateOpen(true)
    setIsCalculateFormReadonly(true)
  }
  // 处理点击同义词列的单元格
  function handleSynonyms(record: MetricModelColumnType) {
    const [pIdx, idx] = getListIndex(record, metricModelTableFullData)
    setCurrentEditRowIndex({ pIdx, idx })
    setCreateSynonymsOpen(true)
  }
  // 完成编辑维度/度量列
  function handleFinishEditMetrics(
    metricFuncInfo: { dimensions?: MetricModelType['dataModelDesc']['dimensions']; measures?: TableMetricFuncType[] },
    formValue: any,
  ) {
    const currentPIdx = currentEditRowIndex.pIdx
    if (metricModelTableFullData && currentPIdx >= 0) {
      const record = metricModelTableFullData[currentPIdx]
      if (record?.metricType) {
        const { metricType } = record
        const preMetricFuncs = record[metricType]
        const metricFuncs = metricFuncInfo[metricType] || []
        const nextMetricModelTableData = produce(metricModelTableFullData, (draft) => {
          const funcs = metricFuncs.map((met, index) => ({
            ...(preMetricFuncs?.[index] || {}),
            ...met,
            pTId: record.tId,
            tId: nanoid(),
          }))
          if (metricType === 'dimensions') {
            const ensureDimensionType = (func: any) => ({
              ...func,
              dimensionType: func.dimensionType || draft[currentPIdx].dimensionType || '', // 设置默认值
            })

            // 使用map函数处理funcs数组
            const processedFuncs = funcs.map(ensureDimensionType)

            // 现在processedFuncs中的每个对象都具有明确的dimensionType属性
            draft[currentPIdx].dimensions = processedFuncs
          } else if (metricType === 'measures') {
            draft[currentPIdx].measures = funcs
          }
          const childrenMetricFuncInfo = funcs.slice(1)
          draft[currentPIdx].children = childrenMetricFuncInfo
          draft[currentPIdx].formValue = formValue
        })
        updateMetricModelTableData(nextMetricModelTableData)
        setCreateCalculateOpen(false)
        setExpandedRowKeys((keys) => [...keys, record.tId])
      }
    }
  }

  // 渲染中文名、ID、同义词、单位、描述的列
  function renderFuncColumn(
    key: keyof typeof record,
    record: MetricFuncType & MetricModelColumnType & { pTId?: string },
  ) {
    if (!record) {
      return ''
    }
    const available = isAvailable(record)
    const firstFunc = record[record.metricType || 'measures']?.[0]
    const target = record.pTId ? record : firstFunc || record
    const value = target[key as keyof typeof target]
    return available ? value : ''
  }
  // 在renderMetricTypeColumn中更新值
  function updateMetricType(
    metricType: 'dimensions' | 'measures' | '',
    record: MetricModelColumnType,
    dimensionType?: 'CATEGORY' | 'TIME' | 'TIME_DEFAULT',
    granularity?: string,
    timeFormatPattern?: string,
  ) {
    const [currentIndex] = getListIndex(record, metricModelTableFullData)
    let nextMetricModelTableData = metricModelTableData
    if (metricType === 'dimensions' || metricType === 'measures') {
      const newMetricModelTableData = produce(metricModelTableFullData, (draft: MetricModelColumnType[]) => {
        draft[currentIndex].metricType = metricType
        // property for dimension otherwise it will be undefined
        draft[currentIndex].dimensionType = dimensionType
        draft[currentIndex].granularity = granularity
        draft[currentIndex].timeFormatPattern = timeFormatPattern
        if (draft[currentIndex][metricType]) {
          draft[currentIndex][metricType] = draft[currentIndex][metricType]?.map((item) => ({
            ...item,
            dimensionType,
            granularity,
            timeFormatPattern,
          }))
        }
        const children = (metricType && draft[currentIndex][metricType]?.slice(1)) || []
        draft[currentIndex].children = children
      })
      nextMetricModelTableData = newMetricModelTableData
    } else {
      const newMetricModelTableData = produce(metricModelTableFullData, (draft: MetricModelColumnType[]) => {
        draft[currentIndex].metricType = undefined
        // property for dimension otherwise it will be undefined
        draft[currentIndex].dimensionType = undefined
        draft[currentIndex].granularity = undefined
        draft[currentIndex].timeFormatPattern = undefined

        const children = [] as TableMetricFuncType[]
        draft[currentIndex].children = children
      })
      nextMetricModelTableData = newMetricModelTableData
    }
    setExpandedRowKeys((expandedRowKeys) => expandedRowKeys.filter((key) => key !== record.tId))
    updateMetricModelTableData(nextMetricModelTableData || [])
  }

  // 表格渲染列，其中会根据isEdit进行判断渲染columns
  const defaultMetricModelTableColumns = [
    {
      title: (
        <Input
          placeholder="搜索"
          prefix={<SearchOutlined />}
          variant="filled"
          allowClear
          onChange={debounce((e) => {
            const value = e.target.value
            searchValueRef.current = value
            const filterList = filterModelList(value, metricModelTableFullData)
            setMetricModelTableData(filterList || [])
          }, 400)}
        />
      ),
      fixed: 'left',
      dataIndex: 'columnName',
      width: 300,
      render: (columnName, record) => {
        const { columnAlias } = record
        return (
          <Space>
            {columnName && renderColumnTypeIcon(record.columnType)}
            <div className="text-wrap break-all">{columnAlias ? `${columnName}/${columnAlias}` : columnName}</div>
          </Space>
        )
      },
    },
    {
      title: <span className="red-require-dot">度量/维度</span>,
      dataIndex: 'metricType',
      fixed: 'left',
      width: 290,
      render(_, record) {
        return <MetricTypeColumn record={record as MetricModelColumnType} isEdit={isEdit} onChange={updateMetricType} />
      },
    },
    {
      title: '度量/维度算子',
      dataIndex: 'function',
      fixed: 'left',
      width: 200,
      render(_, record) {
        const { tId, metricType, columnName } = record
        const functions = metricType && record[metricType]
        const funcCnt = functions?.filter?.((func: MetricFuncType) => func.function?.function).length || 0
        if (!columnName && record.function) {
          return <>{record.function.function}</>
        }
        if (funcCnt === 0) {
          return (
            isEdit && (
              <Button
                disabled={record.disabled}
                onClick={() => handleEditMetrics(record as MetricModelColumnType)}
                block
                type="dashed"
              >
                <span>未配置</span>
              </Button>
            )
          )
        }
        const showText =
          !expandedRowKeys.includes(tId) && funcCnt > 1 ? `${funcCnt}个算子` : functions?.[0]?.function?.function || '-'
        return (
          <div className="flex items-center justify-between">
            {showText}
            <div>
              <EyeOutlined
                onClick={() => handleViewMetrics(record as MetricModelColumnType)}
                className="cursor-pointer"
              />
              {isEdit && (
                <EditOutlined
                  onClick={() => handleEditMetrics(record as MetricModelColumnType)}
                  className="ml-1 cursor-pointer"
                />
              )}
            </div>
          </div>
        )
      },
    },
    {
      title: <span className="red-require-dot">中文名</span>,
      dataIndex: 'nameZh',
      editable: true,
      ellipsis: true,
      render(_, record) {
        const text = renderFuncColumn('nameZh', record as MetricModelColumnType & MetricFuncType)
        return (
          <Tooltip title={text} placement="topLeft">
            {text}
          </Tooltip>
        )
      },
    },
    {
      title: <span className="red-require-dot">ID</span>,
      dataIndex: 'name',
      editable: true,
      ellipsis: true,
      render(_, record) {
        const text = renderFuncColumn('name', record as MetricModelColumnType & MetricFuncType)
        return (
          <Tooltip title={text} placement="topLeft">
            {text}
          </Tooltip>
        )
      },
    },
    {
      title: '同义词',
      dataIndex: 'synonyms',
      width: 220,
      render(_, record: MetricModelColumnType & MetricFuncType & { pTId?: string }) {
        const available = isAvailable(record)
        const firstFunc = record[record.metricType || 'measures']?.[0]
        const target = record.pTId ? record : firstFunc || record
        const synonymsArr = target['synonyms'] || []
        const synonymsText = synonymsArr.join(',')
        if (synonymsText && available) {
          return (
            <div className="flex items-center justify-between">
              <Tooltip title={synonymsText}>
                <div className="cursor-pointer">{formatSynonymsText(synonymsText, synonymsArr.length)}</div>
              </Tooltip>
              <div>
                <EyeOutlined
                  className="cursor-pointer"
                  onClick={() => handleSynonyms(target as MetricModelColumnType)}
                />
                {isEdit && (
                  <EditOutlined
                    className="ml-1 cursor-pointer"
                    onClick={() => handleSynonyms(target as MetricModelColumnType)}
                  />
                )}
              </div>
            </div>
          )
        }
        return (
          <>
            {isEdit && available && (
              <PlusOutlined
                className="cursor-pointer"
                onClick={() => handleSynonyms(target as MetricModelColumnType)}
              />
            )}
          </>
        )
      },
    },
    {
      title: () => {
        return (
          <span>
            格式化字符串
            <Tooltip
              title={
                <>
                  <div>示例1：,.2f万元，代表2位小数点精度，单位为万元，逗号为数字之间的分隔符（10,000）；</div>
                  <div>示例2：.2fw，代表2位小数点精度，单位为w，数字之间没有分隔符（10000）</div>
                </>
              }
            >
              <QuestionCircleOutlined />
            </Tooltip>
          </span>
        )
      },
      dataIndex: 'formatTemplate',
      editable: true,
      width: 120,
      render(_, record) {
        return renderFuncColumn('formatTemplate', record as MetricModelColumnType & MetricFuncType)
      },
    },
    {
      title: '描述',
      dataIndex: 'comment',
      editable: true,
      ellipsis: true,
      render(_, record) {
        const text = renderFuncColumn('comment', record as MetricModelColumnType & MetricFuncType)
        return (
          <Tooltip title={text} placement="topLeft">
            {text}
          </Tooltip>
        )
      },
    },
  ] as (Exclude<TableProps['columns'], undefined>[number] & { editable: boolean; dataIndex: string })[]

  // isEdit模式下，进行表格编辑化处理
  const metricModelEditTableColumns = defaultMetricModelTableColumns.map((col) => {
    if (!isEdit || !col?.editable) {
      return col
    }
    return {
      ...col,
      onCell: (record: MetricModelColumnType & MetricFuncType) => {
        return {
          isAvailable: isAvailable(record),
          fullData: metricModelTableFullData,
          record,
          editable: false,
          disabled: true,
          dataIndex: col.dataIndex,
          title: col.title,
          handleSave: updateFuncs,
        }
      },
    }
  }) as TableProps['columns']

  // isEdit模式下，处理编辑完列
  const updateFuncs = (updateData: MetricFuncType & MetricModelColumnType) => {
    if (metricModelTableFullData) {
      const [pIdx, idx = 0] = getListIndex(updateData, metricModelTableFullData)
      const nextMetricModelTableData = produce(metricModelTableFullData, (draft: any) => {
        const draftRow = draft[pIdx]
        if (draftRow) {
          const { metricType } = draftRow
          const functions = metricType && draftRow[metricType]
          const func = functions && functions[idx]
          if (!draft[pIdx][metricType]) {
            draft[pIdx][metricType] = []
          }
          const updateItemData = {
            ...(func || {}),
            ...pick(updateData, ['nameZh', 'formatTemplate', 'comment', 'name', 'synonyms']),
          }
          if (metricType === 'dimensions') {
            const dimensionColumn = `${VTableInfo.catalog}.${VTableInfo.database}.${VTableInfo.tableName}.${draftRow.columnName}`
            Object.assign(updateItemData, {
              expression: updateData.expression || '',
              granularity: updateData.granularity,
              timeFormatPattern: updateData.timeFormatPattern,
              dimensionType: updateData.dimensionType,
              ...pick(updateData, ['formatTemplate', 'comment', 'synonyms', 'timeDimension']),
              columnDesc: {
                vertexId: `${VTableInfo.catalog}.${VTableInfo.database}.${VTableInfo.tableName}`,
                name: dimensionColumn,
              },
            })
          }
          draft[pIdx][metricType][idx] = updateItemData
          if (!draft[pIdx][metricType][idx].tId) {
            draft[pIdx][metricType][idx].tId = nanoid()
            draft[pIdx][metricType][idx].pTId = updateData.tId
          }
        }
      })
      updateMetricModelTableData(nextMetricModelTableData)
    }
  }

  // isEdit模式下，对表格内容进行编辑替换
  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell,
    },
  }
  // 无论是维度还是度量 都要获取到维度数据
  const getDimensionFromRecord = (
    record: MetricModelColumnType,
    dimensionType: string,
    granularity: string,
    timeFormatPattern: string,
  ) => {
    // 从 measure 或者 dimensions 数据构建 dimension
    const tempDimensionData = record.dimensions?.length
      ? record.dimensions
      : record.measures?.length
        ? record.measures
        : []
    return tempDimensionData?.map((i) => ({
      ...i,
      columnDesc: i.columnDesc,
      dimensionType,
      granularity,
      timeFormatPattern,
    }))
  }
  // 当在顶部修改主时间维度表单的时候，更新表格时间维度格式
  const handleTimeFormatChange = (val: string[]) => {
    const granularity = val[0] ?? ''
    const timeFormatPattern = val[1] ?? ''
    const defaultTimeColumnName = timeColumnForm.getFieldValue('defaultTimeColumnName')
    updateMetricModelTableData(
      addDisableProperty(
        dataRef?.current?.map((item) => {
          if (item.columnName === defaultTimeColumnName) {
            // 新主时间维度 重置原本数据
            const k = {
              ...item,
              dimensions: getDimensionFromRecord(item, 'TIME_DEFAULT', granularity, timeFormatPattern),
            }
            return k
          }
          return { ...item }
        }) || [],
      ),
    )
  }
  // 当在顶部修改主时间维度表单的时候，处理数据变动  2. 修改主时间维度时：A和B首先都默认出现在字段列表里，主时间维度下拉列表里A换成B，A的维度格式继承原主时间维度格式、id和中文名进入可编辑状态；B的维度格式+id和中文名放空置灰不可编辑。（置灰状态下，保存时，该行不参与空值不允许保存的校验）
  const handleTimeDefaultChange = (columnName: string) => {
    let preTimeDefaultRecord = dataRef?.current?.find((item) => item?.dimensions?.[0]?.dimensionType === 'TIME_DEFAULT')
    preTimeDefaultRecord = preTimeDefaultRecord && cloneDeep(preTimeDefaultRecord)
    const [granularity, timeFormatPattern] = timeColumnForm.getFieldValue('granularityAndFormat') || []
    updateMetricModelTableData(
      addDisableProperty(
        dataRef?.current?.map((item) => {
          if (preTimeDefaultRecord?.columnName === item.columnName) {
            // 旧的主时间维度， 去掉主时间维度特征
            const k = {
              ...item,
              columnAlias: '',
              timeDefault: false,
              metricType: 'dimensions' as const,
              measures: [],
              disabled: false,
              dimensions: (item?.dimensions || []).map((d) => ({
                ...d,
                dimensionType: 'TIME' as const,
                nameZh: '',
                granularity,
                timeFormatPattern,
                columnDesc: { ...d.columnDesc, comment: '' } as { vertexId: string; name: string },
              })),
            }
            return k
          } else if (item.columnName === columnName) {
            // 新主时间维度 重置原本数据
            const k = {
              ...item,
              timeDefault: true,
              dimensions: getDimensionFromRecord(item, 'TIME_DEFAULT', granularity, timeFormatPattern),
              metricType: 'dimensions' as const,
              measures: [],
              disabled: true,
            }
            return k
          }
          return { ...item, disabled: false, timeDefault: false }
        }) || [],
      ),
    )
  }
  return (
    <>
      <div className="flex justify-between">
        {isEdit && (
          <Form layout={'inline'} form={timeColumnForm}>
            <Form.Item
              label="主时间维度"
              name="defaultTimeColumnName"
              rules={[
                {
                  required: true,
                  message: '请输入地址',
                },
              ]}
            >
              <Select
                onChange={handleTimeDefaultChange}
                style={{ width: 160 }}
                options={metricModelTableData}
                fieldNames={{ label: 'columnName', value: 'columnName' }}
                placeholder={'请选择'}
              />
            </Form.Item>
            <Form.Item
              label="时间粒度&格式"
              name="granularityAndFormat"
              rules={[
                {
                  required: true,
                  message: '请输入地址',
                },
              ]}
            >
              <Cascader
                onChange={handleTimeFormatChange}
                style={{ width: 190 }}
                options={timeGranularity}
                expandTrigger="hover"
                displayRender={displayRender}
                placeholder="请选择"
              />
            </Form.Item>
          </Form>
        )}
        {headerType === 'count' && renderCountTableHeader(headerType, metricModelTableFullData)}
      </div>
      <Table
        className={className}
        loading={!!loading}
        components={isEdit ? components : undefined}
        dataSource={metricModelTableData}
        columns={metricModelEditTableColumns}
        rowKey="tId"
        pagination={{ defaultPageSize: 100, showSizeChanger: true }}
        scroll={{ x: 'max-content' }}
        expandable={{
          expandIcon: ({ expanded, onExpand, record }) => {
            if (!record.children || record.children.length <= 0) {
              return null
            }
            const icon = expanded ? (
              <MinusSquareOutlined onClick={(e) => onExpand(record, e)} className="text-base text-gray-600" />
            ) : (
              <PlusSquareOutlined onClick={(e) => onExpand(record, e)} className="text-base text-gray-600" />
            )
            return <span className="float-left mr-1 inline-block h-[16px] w-[16px]">{icon}</span>
          },
          expandedRowKeys,
          expandIconColumnIndex: 2,
          indentSize: 0,
          onExpandedRowsChange(expandedRowKeys) {
            setExpandedRowKeys(expandedRowKeys as string[])
          },
        }}
      />
      <CalculateForm
        readonly={isCalculateFormReadonly}
        columnName={currentEditItem?.columnName || ''}
        createCalculateOpen={createCalculateOpen}
        setCreateCalculateOpen={setCreateCalculateOpen}
        initValue={currentEditItem}
        type={currentEditItem?.metricType || 'measures'}
        onOk={handleFinishEditMetrics}
        {...VTableInfo}
      />
      <SynonymsForm
        couldDeleteAll
        isReadOnly={!isEdit}
        setSynonymsCalculateOpen={setCreateSynonymsOpen}
        createSynonymsOpen={createSynonymsOpen}
        onOk={(values) => {
          const updateInfo = {
            ...(currentEditItem || {}),
            synonyms: values,
          } as MetricFuncType & MetricModelColumnType
          updateFuncs(updateInfo)
        }}
        initValue={currentEditSynonymsValue}
      />
    </>
  )
})
export default MetricModelTable
