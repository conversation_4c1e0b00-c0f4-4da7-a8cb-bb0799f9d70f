import React, { useState, useEffect } from 'react'
import { Button, Input, Modal, message, Skeleton, Switch } from 'antd'
import { useRequest } from 'ahooks'
import { SemanticScene } from '@prisma/client'
import axios from 'axios'
import dayjs from 'dayjs'
import { useSetAtom } from 'jotai'
import { askBIApiUrls } from 'src/shared/url-map'
import { SvgIcon, robotIcon, bookIcon, scenarioUserIcon } from 'src/client/components/SvgIcon'
import { requestDefaultDatasetAtom } from '../../AskBI/askBIAtoms'

type PropsType = {
  scenarioId: string
  open: boolean
  onClose: () => void
}

type ScenarioType = {
  id: string
  agent: 'BI' | 'DOC'
  creationTime: string
  creationUser: string
  description: string
  enableMetricExactMatch: boolean
  enableFollowUpQuestion: boolean
  enableTryQueryUp: boolean
  enableSelectToastWhenEmptyData: boolean
  enableAccMetricToastWhenEmptyData: boolean
}
export default function ScenarioDetailModal(props: PropsType) {
  const requestDefaultDataset = useSetAtom(requestDefaultDatasetAtom)
  const { open, onClose, scenarioId } = props
  const [values, setValues] = useState<ScenarioType>()
  const [isEdit, setIsEdit] = useState(false)

  const { run: getScenario, loading: getScenarioLoading } = useRequest(
    async () => {
      const res = await axios.get(askBIApiUrls.auth.scene.rest, { params: { id: scenarioId } })
      return res.data.data as SemanticScene
    },
    {
      manual: true,
      onSuccess(data) {
        if (data) {
          setValues({
            id: data.id,
            agent: data.agent as ScenarioType['agent'],
            creationTime: new Date(data.createdAt).getTime().toString(),
            creationUser: data.createdBy ?? '',
            description: data.description ?? '',
            enableMetricExactMatch: data.enableMetricExactMatch ?? false,
            enableFollowUpQuestion: data.enableFollowUpQuestion ?? false,
            enableTryQueryUp: data.enableTryQueryUp ?? false,
            enableSelectToastWhenEmptyData: data.enableSelectToastWhenEmptyData ?? false,
            enableAccMetricToastWhenEmptyData: data.enableAccMetricToastWhenEmptyData ?? false,
          })
        }
      },
    },
  )

  useEffect(() => {
    if (open) {
      getScenario()
    }
  }, [open, getScenario])

  const { loading: updateScenarioLoading, run: updateScenario } = useRequest(
    async () => {
      const res = await axios.put(askBIApiUrls.auth.scene.rest, {
        id: scenarioId,
        description: values?.description,
        enableMetricExactMatch: values?.enableMetricExactMatch,
        enableFollowUpQuestion: values?.enableFollowUpQuestion,
        enableTryQueryUp: values?.enableTryQueryUp,
        enableSelectToastWhenEmptyData: values?.enableSelectToastWhenEmptyData,
        enableAccMetricToastWhenEmptyData: values?.enableAccMetricToastWhenEmptyData,
      })
      if (res.data.code !== 0) throw new Error(res.data.msg)
    },
    {
      manual: true,
      onSuccess() {
        setIsEdit(false)
        getScenario()
        requestDefaultDataset()
        message.success('修改成功')
      },
      onError() {
        getScenario()
        message.success('修改失败')
      },
    },
  )

  function cancelUpdate() {
    setValues(undefined)
    setIsEdit(false)
    onClose && onClose()
  }

  function renderFooterButtons() {
    if (isEdit) {
      return (
        <>
          <Button
            className="font-[PingFang SC] mr-[6px] w-[74px] rounded bg-white text-[13px] font-medium leading-5 text-[#171717]"
            onClick={cancelUpdate}
          >
            取消
          </Button>
          <Button
            className="font-[PingFang SC] w-[74px] rounded bg-white text-[13px] font-medium leading-5 text-[#171717]"
            loading={updateScenarioLoading}
            onClick={() => {
              updateScenario()
            }}
          >
            保存
          </Button>
        </>
      )
    }

    return (
      <>
        <Button
          className="font-[PingFang SC] mr-[6px] w-[74px] rounded bg-white text-[13px] font-medium leading-5 text-[#171717]"
          onClick={() => setIsEdit(true)}
        >
          编辑
        </Button>
        <Button
          className="font-[PingFang SC] w-[74px] rounded bg-white text-[13px] font-medium leading-5 text-[#171717]"
          onClick={cancelUpdate}
        >
          关闭
        </Button>
      </>
    )
  }

  return (
    <Modal
      onCancel={cancelUpdate}
      className="grey-modal-footer"
      title={<div className="pl-5">场景详情</div>}
      open={open}
      footer={<div className="rounded-b bg-[#F4F4F4] px-5 py-3">{renderFooterButtons()}</div>}
    >
      {getScenarioLoading ? (
        <Skeleton active className="p-5" />
      ) : (
        <div className="p-5">
          <div className="mb-2 flex justify-between align-middle">
            <div className="font-[PingFang SC] text-[13px] font-medium leading-5 text-[#A2A2A2]">描述</div>
          </div>
          {!isEdit && <div className="mb-4 text-[13px] leading-5 text-[#575757]">{values?.description || '-'}</div>}
          {isEdit && (
            <Input.TextArea
              value={values?.description}
              className="mb-4"
              onChange={(e) => {
                if (values) {
                  setValues({
                    ...values,
                    description: e.target.value,
                  })
                }
              }}
            />
          )}
          <div>
            <div className="mb-2 flex justify-between align-middle">
              <div className="font-[PingFang SC] text-[13px] font-medium leading-5 text-[#A2A2A2]">置信度</div>
            </div>
            <Switch
              checked={values?.enableMetricExactMatch}
              className="mb-4"
              onChange={(data: boolean) => {
                if (values) {
                  setValues({ ...values, enableMetricExactMatch: data })
                }
              }}
              disabled={!isEdit}
            />
          </div>
          <div>
            <div className="mb-2 flex justify-between align-middle">
              <div className="font-[PingFang SC] text-[13px] font-medium leading-5 text-[#A2A2A2]">默认追问</div>
            </div>
            <Switch
              checked={values?.enableFollowUpQuestion}
              className="mb-4"
              onChange={(data: boolean) => {
                if (values) {
                  setValues({ ...values, enableFollowUpQuestion: data })
                }
              }}
              disabled={!isEdit}
            />
          </div>
          <div>
            <div className="mb-2 flex justify-between align-middle">
              <div className="font-[PingFang SC] text-[13px] font-medium leading-5 text-[#A2A2A2]">无数据往前追溯</div>
            </div>
            <Switch
              checked={values?.enableTryQueryUp}
              className="mb-4"
              onChange={(data: boolean) => {
                if (values) {
                  setValues({ ...values, enableTryQueryUp: data })
                }
              }}
              disabled={!isEdit}
            />
          </div>
          <div>
            <div className="mb-2 flex justify-between align-middle">
              <div className="font-[PingFang SC] text-[13px] font-medium leading-5 text-[#A2A2A2]">
                置信度选择空数据提示
              </div>
            </div>
            <Switch
              checked={values?.enableSelectToastWhenEmptyData}
              className="mb-4"
              onChange={(data: boolean) => {
                if (values) {
                  setValues({ ...values, enableSelectToastWhenEmptyData: data })
                }
              }}
              disabled={!isEdit}
            />
          </div>
          <div>
            <div className="mb-2 flex justify-between align-middle">
              <div className="font-[PingFang SC] text-[13px] font-medium leading-5 text-[#A2A2A2]">
                可累加指标无数据区间提示
              </div>
            </div>
            <Switch
              checked={values?.enableAccMetricToastWhenEmptyData}
              className="mb-4"
              onChange={(data: boolean) => {
                if (values) {
                  setValues({ ...values, enableAccMetricToastWhenEmptyData: data })
                }
              }}
              disabled={!isEdit}
            />
          </div>
          <div className="mb-2 flex justify-between align-middle">
            <div className="font-[PingFang SC] text-[13px] font-medium leading-5 text-[#A2A2A2]">关联agent</div>
          </div>
          {values?.agent === 'BI' && (
            <div className="mb-4 inline-flex items-center rounded-sm bg-[#E8F7FF] px-2">
              <SvgIcon icon={robotIcon} className="h-3 w-3" />
              <span className="ml-1 text-xs font-medium leading-5 text-[#3491FA]">数据洞察</span>
            </div>
          )}
          {values?.agent === 'DOC' && (
            <div className="mb-4 inline-flex items-center rounded-sm bg-[#E8FFFB] px-2">
              <SvgIcon icon={bookIcon} className="h-3 w-3" />
              <span className="ml-1 text-xs font-medium leading-5 text-[#0FC6C2]">知识洞察</span>
            </div>
          )}

          <div className="mb-2 flex justify-between align-middle">
            <div className="font-[PingFang SC] text-[13px] font-medium leading-5 text-[#A2A2A2]">创建人</div>
          </div>

          <div className="mb-4 inline-flex rounded-sm bg-[#F1F1F1] px-[6px] py-[2px]">
            <SvgIcon icon={scenarioUserIcon} className="h-5 w-5" />
            <span className="ml-1 leading-5">{values?.creationUser}</span>
          </div>
          <div className="mb-2 flex justify-between align-middle">
            <div className="font-[PingFang SC] text-[13px] font-medium leading-5 text-[#A2A2A2]">创建时间</div>
          </div>
          <span className="leading-5 text-[#575757]">
            {values?.creationTime ? dayjs(values?.creationTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
          </span>
        </div>
      )}
    </Modal>
  )
}
