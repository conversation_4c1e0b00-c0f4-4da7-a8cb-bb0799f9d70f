import React, { useCallback, useRef, useState } from 'react'
import { <PERSON><PERSON>, But<PERSON> } from 'antd'
import axios from 'axios'
import CountSearchTable, { type ActionType } from 'src/client/components/CountSearchTable'
import { askBIApiUrls } from 'src/shared/url-map'
import request from 'src/shared/xengineAxios'
import SynonymsText from 'src/client/components/SynonymsText'
import { SynonymsForm } from 'src/client/pages/MetricStore/Metrics/fusion/SynonymsForm'
import { APIResponse } from 'src/shared/common-types'

const defaultResponse = {
  total: 0,
  list: [],
}

type PropsType = {
  modelName: string
  columnName: string
  open: boolean
  onClose?: () => void
}

type ColumnCodeValueType = {
  codeValue: string
  synonyms: string[]
}

export default function ColumnCodeDrawer({ modelName, columnName, open, onClose }: PropsType) {
  const [synonymsIsReadOnly, setSynonymsIsReadOnly] = useState(false)
  const [synonymsFormOpen, setSynonymsFormOpen] = useState(false)
  const [currentColumnCodeInfo, setCurrentColumnCodeInfo] = useState<{ codeValue: string; synonyms: string[] }>({
    codeValue: '',
    synonyms: [],
  })
  const actionRef = useRef<ActionType>()
  const handleSynonymsFormOk = useCallback(
    (synonyms: string[]) => {
      return axios
        .post<APIResponse<null>>(askBIApiUrls.model.updateColumnCodeValueSynonyms, {
          modelName,
          columnName,
          codeValueWithSynonyms: {
            codeValue: currentColumnCodeInfo.codeValue,
            synonyms,
          },
        })
        .then(() => {
          actionRef.current?.refresh()
        })
    },
    [currentColumnCodeInfo, columnName, modelName],
  )
  return (
    <Drawer
      open={open}
      onClose={() => onClose?.()}
      placement="bottom"
      height="calc(100vh - 20px)"
      title={columnName}
      closeIcon={null}
      extra={<Button onClick={() => onClose?.()}>取消</Button>}
    >
      <CountSearchTable
        actionRef={actionRef}
        api={useCallback(async () => {
          if (!modelName || !columnName || !open) {
            return defaultResponse
          }
          const codeValues = await request.get<{ modelName: string; columnName: string }, ColumnCodeValueType[]>(
            askBIApiUrls.model.listColumnCodeValues,
            {
              params: { modelName, columnName },
            },
          )
          return {
            list: codeValues,
            total: codeValues?.length,
          }
        }, [columnName, modelName, open])}
        placeholder="输入码值关键词"
        isFullList
        searchKey={['codeValue', 'synonyms']}
        tableProps={{
          rowKey: 'codeValue',
          columns: [
            {
              title: '码值',
              dataIndex: 'codeValue',
            },
            {
              title: '同义词',
              dataIndex: 'synonyms',
              render(synonyms, record) {
                return (
                  <SynonymsText
                    synonyms={synonyms}
                    handleEditSynonyms={(synonyms) => {
                      setSynonymsIsReadOnly(false)
                      setCurrentColumnCodeInfo({
                        codeValue: record.codeValue,
                        synonyms,
                      })
                      setSynonymsFormOpen(true)
                    }}
                    handleViewSynonyms={(synonyms) => {
                      setSynonymsIsReadOnly(true)
                      setCurrentColumnCodeInfo({
                        codeValue: record.codeValue,
                        synonyms,
                      })
                      setSynonymsFormOpen(true)
                    }}
                  />
                )
              },
            },
          ],
        }}
      />
      <SynonymsForm
        isReadOnly={synonymsIsReadOnly}
        onOk={handleSynonymsFormOk}
        createSynonymsOpen={synonymsFormOpen}
        setSynonymsCalculateOpen={setSynonymsFormOpen}
        initValue={currentColumnCodeInfo.synonyms}
      />
    </Drawer>
  )
}
