import { useRequest } from 'ahooks'
import { App } from 'antd'
import axios from 'axios'
import clsx from 'clsx'
import React from 'react'
import { useNavigate } from 'react-router-dom'
import { useAbortController } from '@client/utils'
import Spark<PERSON>hart from 'src/client/components/SparkChart'
import TextHighlight from 'src/client/components/TextHighlight'
import TextTruncate from 'src/client/components/TextTruncate'
import { APIResponse } from 'src/shared/common-types'
import { assertExhaustive } from 'src/shared/common-utils'
import { APIError } from 'src/shared/config'
import { Metric, MetricTrendResponse, MetricTypeNames } from 'src/shared/metric-types'
import { askBIApiUrls, askBIPageUrls } from 'src/shared/url-map'

export default function MetricCard({
  metric,
  sceneId,
  searchValue,
}: {
  metric: Metric
  sceneId: string
  /** 搜索高亮的字符串 */
  searchValue?: string
}) {
  const ac = useAbortController()
  const { message } = App.useApp()
  const navigate = useNavigate()
  const [dataNotExists, setDataNotExists] = React.useState(false)

  const {
    data: metricTrendData,
    error: _isError,
    loading: _isLoading,
  } = useRequest<MetricTrendResponse | undefined, unknown[]>(async () => {
    // 只有 simple ratio derived 才会有趋势图
    if (['simple', 'ratio', 'derived'].indexOf(metric.type) === -1) {
      return undefined
    }
    try {
      const response = await axios.get<APIResponse<MetricTrendResponse>>(
        askBIApiUrls.metrics.trendInScene(sceneId, metric.name),
        {
          signal: ac.signal,
        },
      )
      return response.data.data
    } catch (error: any) {
      if (error instanceof APIError) {
        if (error.response.code === 414) {
          console.info('指标对应的数据库字段不存在', metric)
          setDataNotExists(true)
          return
        }
      }
      if (error.code !== 'ERR_CANCELED') {
        message.error('获取指标数据出错:' + (error as any).message || '未知原因')
        console.error('获取指标数据出错 error =', error)
      }
    }
  })

  let latestValue = '-'
  if (metricTrendData != null && metricTrendData.value.length > 0) {
    latestValue = (+metricTrendData.value[metricTrendData.value.length - 1]).toLocaleString()
  }

  const renderMetricData = () => {
    const metricType = metric.type
    switch (metricType) {
      case 'simple':
      case 'derived':
        return (
          <>
            <span className="text-3xl font-bold">{latestValue}</span>
            {metricTrendData == null ? (
              <div className={clsx('flex', `h-[50px]`, `w-[150px]`)} />
            ) : (
              <SparkChart
                chartType="line"
                width={150}
                height={50}
                className="ml-auto"
                xAxisData={metricTrendData.date}
                data={metricTrendData.value}
              />
            )}
          </>
        )
      case 'list':
        return (
          <>
            <span className="text-3xl font-bold">{latestValue}</span>
          </>
        )
      case 'ratio':
        return null
      default:
        return assertExhaustive(metricType)
    }
  }

  return (
    <div
      key={metric.name}
      className={clsx(
        'flex flex-col rounded-md p-2 ring-1 ring-gray-200 hover:ring-2 hover:ring-indigo-500 hover:ring-opacity-50 dark:ring-gray-700',
        {
          'ring-red-300': dataNotExists,
        },
      )}
      onClick={() => navigate(askBIPageUrls.metricStore.metrics.detail(sceneId, metric.name))}
    >
      <div className="metric-header flex flex-row truncate text-gray-600 dark:text-gray-300">
        <TextTruncate className="w-1/2" placement="topLeft">
          <TextHighlight text={metric.name} highlight={searchValue} />
        </TextTruncate>
        <div className="mx-2 my-1 w-[1px] bg-gray-200" />
        <TextTruncate className="w-1/2" placement="topLeft">
          <TextHighlight text={metric.label} highlight={searchValue} />
        </TextTruncate>
      </div>
      <div className="metric-data flex flex-grow gap-2 py-2">
        {dataNotExists ? (
          <span className="text-red-500">指标对应的数据库字段被删除，请删除此指标</span>
        ) : (
          renderMetricData()
        )}
      </div>
      <div className="metric-footer flex flex-row gap-4 text-sm text-gray-600 dark:text-gray-300">
        <div className="flex whitespace-nowrap rounded-md bg-gray-200 px-2 dark:bg-gray-700">
          {MetricTypeNames[metric.type]}
        </div>
        <TextTruncate className="flex-grow">{metric.description}</TextTruncate>
      </div>
    </div>
  )
}
