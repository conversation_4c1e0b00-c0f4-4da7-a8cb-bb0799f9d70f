import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON>, <PERSON>confirm, Table, App, Modal, Tooltip, Tag, Space, Result } from 'antd'
import { ColumnsType } from 'antd/es/table'
import axios from 'axios'
import { useRequest } from 'ahooks'
import { useAtomValue } from 'jotai'
import Search from 'antd/es/input/Search'
import { PlusOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import AdminCard from '@components/AdminCard'
import AdminPage from 'src/client/components/AdminPage'
import { DEFAULT_SCENE_ID, Id, Metric, MetricConfigResponse, MetricTypeNames } from 'src/shared/metric-types'
import { askBIApiUrls, askBIPageUrls } from 'src/shared/url-map'
import { APIResponse } from 'src/shared/common-types'
import TextHighlight from 'src/client/components/TextHighlight'
import { currentDatasetAtom } from '../../AskBI/askBIAtoms'
import CreateMetricForm from './CreateMetricForm'

const MetricsManage = () => {
  const { message: antdMessage } = App.useApp()
  const navigate = useNavigate()
  const currentDataset = useAtomValue(currentDatasetAtom)
  const [showMetricForm, setShowMetricForm] = useState(false)
  const [editMetricId, setEditMetricId] = useState<string>()
  const [searchValue, setSearchValue] = useState('')

  const {
    run: loadMetricConfig,
    data: metricConfig,
    loading: isMetricListLoading,
    error: metricListError,
  } = useRequest(
    async () => {
      const response = await axios.get<APIResponse<MetricConfigResponse>>(askBIApiUrls.auth.metrics, {
        params: { sceneId: currentDataset?.sceneId || DEFAULT_SCENE_ID },
      })
      if (!response.data.data) {
        return []
      }
      return response.data.data.allMetrics
    },
    {
      onError: (error) => {
        antdMessage.error('获取指标信息失败')
        console.error('Load metric list with error', error)
      },
    },
  )

  useEffect(() => {
    loadMetricConfig()
  }, [currentDataset, loadMetricConfig])

  const { run: handleDelete, loading: isDeleteLoading } = useRequest(
    (id) => {
      return axios.delete(askBIApiUrls.metrics.delete(id))
    },
    {
      manual: true,
      onSuccess: () => {
        antdMessage.success('删除成功')
        loadMetricConfig()
      },
      onError: (error) => {
        antdMessage.error('删除指标失败：' + error.message || '未知原因')
        console.error('deleteRole error =', error)
      },
    },
  )

  const metricsColumn: ColumnsType<Metric & Id> = [
    {
      title: '标识符',
      dataIndex: 'name',
      render(value) {
        return <TextHighlight text={value} highlight={searchValue} />
      },
    },
    {
      title: '中文名',
      dataIndex: 'label',
      render(value) {
        return <TextHighlight text={value} highlight={searchValue} />
      },
    },
    {
      title: '同义词',
      dataIndex: 'synonyms',
      render: (value: string[]) => (
        <div className="grid gap-1">
          {value?.map?.((text, index) => (
            <span key={index}>
              <Tag bordered={false} color="default">
                {text}
              </Tag>
            </span>
          ))}
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      render(value: keyof typeof MetricTypeNames) {
        return MetricTypeNames[value]
      },
    },

    {
      title: '描述',
      dataIndex: 'description',
      ellipsis: true,
      render(value) {
        return (
          <Tooltip placement="top" title={value}>
            {value}
          </Tooltip>
        )
      },
    },
    {
      title: '配置和过滤条件',
      dataIndex: 'typeParams',
      width: 300,
      render(value, record: Metric & Id) {
        if (record.type === 'simple' && record.filter != null && record.filter.length > 0) {
          return (
            <div>
              <b>配置：</b>
              <br />
              <code>{JSON.stringify(value)}</code>
              <br />
              <b>过滤条件：</b>
              <br />
              <code>{record.filter}</code>
            </div>
          )
        }
        return (
          <div>
            <b>配置：</b>
            <br />
            <code>{JSON.stringify(value)}</code>
          </div>
        )
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      render: (_, record) => {
        return (
          <div className="flex flex-row">
            <Button
              onClick={() => {
                setShowMetricForm(true)
                setEditMetricId(record.id)
              }}
              type="link"
            >
              编辑
            </Button>
            <Popconfirm
              title="删除指标"
              description={`确认删除当前${record?.name}指标吗？`}
              onConfirm={() => {
                return handleDelete(record.id)
              }}
              okButtonProps={{ loading: isDeleteLoading }}
              okText="确认"
              cancelText="取消"
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </div>
        )
      },
    },
  ]

  const handleFormSubmit = () => {}

  const handleSearch = (value: string) => {
    setSearchValue(value)
  }

  const filteredMetrics = metricConfig?.filter((metric) => {
    return (
      metric.name.toLowerCase().includes(searchValue.toLowerCase()) ||
      metric.label.toLowerCase().includes(searchValue.toLowerCase())
    )
  })

  return (
    <AdminPage title="指标管理">
      <AdminCard
        title="指标列表"
        actions={
          <Space>
            <Search style={{ width: 150 }} placeholder="搜索指标名" allowClear onSearch={handleSearch} />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              size="middle"
              onClick={() => {
                navigate(`${askBIPageUrls.metricStore.metrics.create}`)
              }}
            >
              创建指标
            </Button>
          </Space>
        }
      >
        {metricListError != null ? (
          <Result status="error" title="获取指标列表失败" subTitle="请稍后再试" />
        ) : (
          <Table
            columns={metricsColumn}
            loading={isMetricListLoading}
            bordered
            rowKey="id"
            size="small"
            dataSource={filteredMetrics}
            pagination={{
              hideOnSinglePage: true,
              showSizeChanger: true,
              showTotal: (total) => `共 ${total} 个指标`,
            }}
          />
        )}
        <Modal
          destroyOnClose
          title="编辑指标"
          open={showMetricForm}
          onOk={handleFormSubmit}
          onCancel={() => setShowMetricForm(false)}
          footer={null}
        >
          <CreateMetricForm
            onOk={handleFormSubmit}
            onCancel={() => setShowMetricForm(false)}
            isForm={true}
            metricId={editMetricId}
            updateList={() => {
              setShowMetricForm(false)
              loadMetricConfig()
            }}
          />
        </Modal>
      </AdminCard>
    </AdminPage>
  )
}

export default MetricsManage
