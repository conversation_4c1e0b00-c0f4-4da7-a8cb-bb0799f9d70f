/**
 * 指标详情页面，上面是指标的基本信息，下面是 QueryParamSelector 再下面是 Chart
 */
import React, { useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { useRequest } from 'ahooks'
import axios from 'axios'
import { App, Result, Skeleton } from 'antd'
import { useAtomValue } from 'jotai'
import { APIResponse, AssistantChartChatItem, ChartType, OlapRow } from '@shared/common-types'
import { askBIApiUrls } from 'src/shared/url-map'
import AdminPage from 'src/client/components/AdminPage'
import AdminCard from 'src/client/components/AdminCard'
import { DATE_ALIAS, MetricTypeNames, QueryParams, QueryParamsVerified } from 'src/shared/metric-types'
import QueryParamsSelector from 'src/client/components/QueryParamsSelector'
import ChartWrapper from 'src/client/charts/ChartWrapper'
import IconButtonGroup from 'src/client/components/IconButtonGroup'
import { getVisibleChartIconMap } from 'src/client/charts/Card'
import { currentDatasetAtom, metricConfigAtom, themeAtom } from '../../AskBI/askBIAtoms'
import { readyResponseToChatAns } from '../../AskBI/Chat/utils'

export default function MetricDetail({ className }: { className?: string }) {
  const { sceneId, metricName } = useParams()
  const { message: antdMessage } = App.useApp()
  const currentDataset = useAtomValue(currentDatasetAtom)
  const theme = useAtomValue(themeAtom)
  const metricConfig = useAtomValue(metricConfigAtom)
  const queryParams = {
    groupBys: [],
    metricNames: [metricName || ''],
    timeQueryParams: metricConfig?.timeDimensionDatum && {
      timeDimensionName: metricConfig.timeDimensionDatum.timeDimensionName,
      timeGranularity: 'month',
      timeStartFunction: {
        type: 'recentMonths',
        months: 12,
      },
      timeEndFunction: {
        type: 'recentMonths',
        months: 0,
      },
    },
  } as QueryParams
  const [chartType, setChartType] = React.useState<ChartType>('LineChart')
  const [queryParamsVerified, setQueryParamsVerified] = React.useState<QueryParamsVerified>({
    originalQueryParams: queryParams,
    queryParams: queryParams,
    extraParams: {
      extraMetricNames: [],
      extraGroupBys: [],
      extraOrderBys: [],
    },
  })

  const {
    run: loadChartData,
    data: chartData,
    loading: isChartDataLoading,
    error: chartDataError,
  } = useRequest(
    async (queryParamsVerified: QueryParamsVerified) => {
      const response = await axios.post(askBIApiUrls.queryMetric, {
        queryParamsVerified,
        sceneId,
      })
      // 更新推荐的图表
      setChartType(response.data.chartType)
      return response.data
    },
    {
      manual: true,
      onError: (error) => {
        console.error('Load chart data with error', error)
      },
    },
  )

  useEffect(() => {
    if (queryParamsVerified.queryParams != null && sceneId != null) {
      loadChartData(queryParamsVerified)
    }
  }, [loadChartData, sceneId, queryParamsVerified])

  const {
    data: metricTime,
    loading: _isMetricTimeLoading,
    error: _metricTimeError,
  } = useRequest(
    async () => {
      if (sceneId != null && metricName != null) {
        const response = await axios.get<APIResponse<{ list: OlapRow[] }>>(
          askBIApiUrls.metrics.detailWithTime(sceneId, metricName),
        )
        return response.data.data
      } else {
        antdMessage.error('URL 错误，指标 ID 不能为空')
      }
    },
    {
      onError: (error) => {
        console.error('Load metric detail with error', error)
      },
    },
  )

  const handleQueryParamsChange = (newValue: QueryParamsVerified) => {
    setQueryParamsVerified(newValue)
  }

  const metricDetail = metricConfig?.allMetrics.find((metric) => metric.name === metricName)

  if (metricDetail == null) {
    return <Result status="error" title="获取指标详情失败" subTitle={`指标不存在，指标标识为${metricName}`} />
  }

  const handleChartTypeChange = (newValue: ChartType) => {
    setChartType(newValue)
  }

  const renderTrend = () => {
    if (chartDataError) {
      return <Result status="error" title="获取图表数据失败" subTitle={chartDataError.message} />
    }

    if (isChartDataLoading) {
      return <Skeleton active />
    }

    if (chartData == null) {
      return <Result status="error" title="获取图表数据失败" subTitle="图表数据为空" />
    }

    if (chartData && chartData.taskType === 'chat-error') {
      return <Result status="error" title="获取图表数据失败" subTitle={chartData.unreadyReason} />
    }
    const assistantChartChatItem = readyResponseToChatAns(chartData, currentDataset?.sceneId || '')
      .content[0] as AssistantChartChatItem

    return (
      <div className="flex flex-col">
        <ChartWrapper
          data={{ ...assistantChartChatItem, chartType }}
          theme={theme}
          onTableDataChange={() => {
            /** table 有排序，暂不支持 */
          }}
        />
        <div>
          <span className="mt-2">切换图表：</span>
          <IconButtonGroup<ChartType>
            options={getVisibleChartIconMap(assistantChartChatItem.recommendChartTypes)}
            value={chartType}
            onChange={handleChartTypeChange}
          />
        </div>
      </div>
    )
  }

  const renderDetailItem = (label: string, value: string | number | null | undefined) => {
    return (
      <div className="w-full md:w-1/2">
        {label}: <span className="font-bold">{value || '-'}</span>
      </div>
    )
  }

  return (
    <AdminPage title={`${metricDetail?.name} 的指标详情`} className={className}>
      <AdminCard title="基本信息">
        <div className="flex flex-row flex-wrap">
          {renderDetailItem('标识', metricDetail?.name)}
          {renderDetailItem('名称', metricDetail?.label)}
          {renderDetailItem('类型', MetricTypeNames[metricDetail.type])}
          {metricDetail.type === 'list'
            ? renderDetailItem('子指标', metricDetail.typeParams.metrics.map((o) => o.name).join(', '))
            : renderDetailItem('计算方式', metricDetail.displayExpr)}
          {metricConfig?.timeDimensionDatum && (
            <>
              {renderDetailItem(
                '数据起始于',
                metricTime?.list && metricTime?.list?.length > 0 ? metricTime.list[0][DATE_ALIAS] : '-',
              )}
              {renderDetailItem(
                '数据结束于',
                metricTime?.list && metricTime?.list?.length > 0
                  ? metricTime.list[metricTime.list.length - 1][DATE_ALIAS]
                  : '-',
              )}
            </>
          )}
        </div>
        {metricDetail?.description && (
          <div className="flex flex-row flex-wrap">{renderDetailItem('描述', metricDetail?.description)}</div>
        )}
      </AdminCard>
      {metricConfig?.timeDimensionDatum != null &&
        (metricDetail?.type === 'simple' || metricDetail?.type === 'derived') && (
          <>
            <AdminCard title="数据趋势">
              <div className="flex flex-col">
                <QueryParamsSelector
                  queryParamsVerified={queryParamsVerified}
                  onChange={handleQueryParamsChange}
                  metricConfig={metricConfig}
                />
                {renderTrend()}
              </div>
            </AdminCard>

            {/* <AdminCard title="归因分析">
            <Result status="info" title="暂未开放" subTitle="敬请期待" />
          </AdminCard> */}
          </>
        )}
    </AdminPage>
  )
}
