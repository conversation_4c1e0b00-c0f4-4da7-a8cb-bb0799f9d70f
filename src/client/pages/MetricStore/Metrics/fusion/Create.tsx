import React, { useCallback, useEffect, useRef, useState } from 'react'
import clsx from 'clsx'
import { App, Button, Col, Form, Input, Modal, Radio, Row, Select, Space, Switch, Typography } from 'antd'
import { useAtom } from 'jotai'
import { MinusOutlined, PlusOutlined } from '@ant-design/icons'
import axios from 'axios'
import { useRequest } from 'ahooks'
import get from 'lodash/get'
import { pick } from 'lodash'
import { SemanticMetric } from '@prisma/client'
import { FormatNumberReg } from 'src/shared/constants'
import { metricListAtomForOption } from 'src/client/pages/AskBI/askBIAtoms'
import { MetricConfigResponse, MetricModelType } from 'src/shared/metric-types'
import { askBIApiUrls } from 'src/shared/url-map'
import { SvgIcon, measureIcon } from 'src/client/components/SvgIcon'
import { assertExhaustive, formatNumber } from 'src/shared/common-utils'
import { APIResponse } from 'src/shared/common-types'
import { Synonyms } from 'src/client/components/Synonyms'
import FormLayout from './FormLayout'

const supportMetricTypeNames = {
  simple: '原子指标',
  ratio: '比值指标',
  derived: '派生指标',
  // rank: '排名指标',
  list: '列表指标',
} as const
type MetricTypeNameKey = keyof typeof supportMetricTypeNames
const Option = Select.Option

const searchFilter = (searchKeys: string[], input: string, option?: Record<string, string>) =>
  (searchKeys || []).some((key) => (option?.[key] ?? '').toLowerCase().includes(input.toLowerCase()))

const MetricsCreate = ({
  className,
  createMetricModalOpen = true,
  setCreateMetricModalOpen = () => {
    return
  },
  onOk,
  isEdit,
  values,
  baseInfo,
}: {
  onOk?: () => void
  className?: string
  createMetricModalOpen: boolean
  setCreateMetricModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  isEdit?: boolean
  values?: SemanticMetric
  baseInfo: {
    semanticProjectId: string
    semanticSceneId: string
  }
}) => {
  const [form] = Form.useForm()
  const [_metricListAtomForOption] = useAtom(metricListAtomForOption)
  const MetricsOptions: { label: string; value: MetricTypeNameKey }[] = Object.entries(supportMetricTypeNames).map(
    ([key, value]) => ({
      label: value,
      value: key as MetricTypeNameKey,
    }),
  )
  const { message: antdMessage } = App.useApp()

  const willCloseWindowAfterCreate = useRef(false)
  const [semanticProjectId, setSemanticProjectId] = useState('')
  const [semanticModelId, setSemanticModelId] = useState('')
  const [semanticSceneId, setSemanticSceneId] = useState('')

  const { data: metricsList } = useRequest(
    async () => {
      const response = await axios.get<APIResponse<MetricConfigResponse>>(askBIApiUrls.auth.metrics, {
        params: { sceneId: semanticSceneId },
      })
      return response.data?.data?.allMetrics
    },
    {
      ready: Boolean(semanticProjectId && semanticModelId && semanticSceneId),
      refreshDeps: [semanticProjectId, semanticModelId, semanticSceneId],
      onError: (error) => {
        console.error('get metricArrayError =', error)
      },
    },
  )
  const { loading: isModelLoading, data: modelData } = useRequest(
    async () => {
      return axios.get<{ name: string }, MetricModelType>(askBIApiUrls.model.meta, {
        params: {
          name: semanticModelId,
        },
      })
    },
    {
      ready: Boolean(semanticModelId),
      refreshDeps: [semanticModelId],
    },
  )

  useEffect(() => {
    axios.get(askBIApiUrls.auth.scene.rest, { params: { id: baseInfo.semanticSceneId } }).then((res) => {
      const data = res.data.data
      const modelNames = data?.modelNames || []
      if (modelNames[0]) {
        setSemanticModelId(modelNames[0])
      }
    })
    setSemanticProjectId(baseInfo.semanticProjectId || '')
    setSemanticSceneId(baseInfo.semanticSceneId || '')
  }, [baseInfo])

  const initValues = useCallback(
    (values: SemanticMetric) => {
      let formattedValues
      switch (values.type) {
        case 'simple': {
          formattedValues = {
            metricType: values.type,
            ...pick(values || {}, [
              'label',
              'name',
              'description',
              'typeParams',
              'filter',
              'formatTemplate',
              'isCumulative',
              'synonyms',
            ]),
          }
          break
        }
        case 'derived': {
          formattedValues = {
            metricType: values.type,
            ...pick(values || {}, [
              'label',
              'name',
              'description',
              'typeParams',
              'filter',
              'formatTemplate',
              'isCumulative',
              'synonyms',
            ]),
          }
          break
        }
        case 'list': {
          formattedValues = {
            metricType: values.type,
            ...pick(values || {}, [
              'label',
              'name',
              'description',
              'typeParams',
              'formatTemplate',
              'isCumulative',
              'synonyms',
            ]),
          }
          break
        }
        case 'ratio': {
          formattedValues = {
            metricType: values.type,
            ...pick(values || {}, [
              'label',
              'name',
              'description',
              'typeParams',
              'formatTemplate',
              'isCumulative',
              'synonyms',
            ]),
          }
          break
        }
        default: {
          return
        }
      }
      form.setFieldsValue(formattedValues)
    },
    [form],
  )

  useEffect(() => {
    if (createMetricModalOpen) {
      if (isEdit && values) {
        initValues(values)
      } else {
        form.resetFields()
      }
    }
  }, [createMetricModalOpen, isEdit, values, initValues, form])

  const { run: createOrUpdateMetric, loading: isCreatingOrUpdatingMetric } = useRequest(
    (arg) => {
      const t = { ...arg }
      delete t.metricType
      const metric = {
        ...t,
        type: arg.metricType,
        synonyms: t?.synonyms ?? [],
      }
      // 匹配变量名的正则表达式
      const pattern = /\b[a-zA-Z_][a-zA-Z0-9_]*\b/g
      if (metric.type === 'derived') {
        metric.typeParams.metrics = (metric.typeParams.expr.match(pattern) || []).map((name: string) => {
          return { name }
        })
      }
      // console.log(variables)
      if (isEdit && values) {
        return axios.put(askBIApiUrls.metrics.update(values.id), {
          ...metric,
          semanticSceneId,
          semanticProjectId,
        })
      }
      return axios.post(askBIApiUrls.metrics.create, {
        metricInfo: metric,
        modelId: semanticModelId,
        semanticSceneId,
        semanticProjectId,
      })
    },
    {
      manual: true,
      onSuccess: () => {
        const msg = `${isEdit ? '编辑' : '新增'}指标成功！`
        antdMessage.success(msg, 0.5)
        form.resetFields()
        onOk?.()
        setCreateMetricModalOpen(!willCloseWindowAfterCreate.current)
      },
      onError: (error) => {
        console.error(error, 'create metric error')
        const defaultMsg = `${isEdit ? '更新' : '新增'}指标失败！`
        antdMessage.error(defaultMsg + error.message.slice(0, 15))
      },
    },
  )
  const formatTemplate = Form.useWatch('formatTemplate', form)
  return (
    <>
      <Modal
        destroyOnClose
        onCancel={() => setCreateMetricModalOpen(false)}
        width={1000}
        title={`新增指标`}
        open={createMetricModalOpen}
        footer={
          <Space>
            <Button onClick={() => setCreateMetricModalOpen(false)}>取消</Button>
            {!isEdit && (
              <Button
                loading={isCreatingOrUpdatingMetric}
                onClick={() => {
                  form.submit()
                  willCloseWindowAfterCreate.current = false
                }}
              >
                保存并创建下一个
              </Button>
            )}

            <Button
              type="primary"
              loading={isCreatingOrUpdatingMetric}
              onClick={() => {
                form.submit()
                willCloseWindowAfterCreate.current = true
              }}
            >
              保存
            </Button>
          </Space>
        }
      >
        <Form
          className={`my-6 w-full ${className}`}
          form={form}
          onFinish={createOrUpdateMetric}
          initialValues={{ metricType: 'simple', isCumulative: true }}
          layout={'vertical'}
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
        >
          <FormLayout required={true} title={'选择指标类型'}>
            <Form.Item name="metricType" className="m-0">
              <Radio.Group>
                {MetricsOptions.map(({ label, value }) => {
                  return (
                    <Radio value={value} key={value}>
                      {label}
                    </Radio>
                  )
                })}
              </Radio.Group>
            </Form.Item>
          </FormLayout>
          <Row gutter={24}>
            <Col span={12}>
              <FormLayout required={false} title={'基础信息'}>
                <Row gutter={12}>
                  <Col span={12}>
                    <Form.Item
                      label="指标中文名"
                      name="label"
                      rules={[{ required: true, message: '请输入指标中文名' }]}
                    >
                      <Input placeholder="请输入指标中文名" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="指标 ID" name="name" rules={[{ required: true, message: '请输入指标 ID' }]}>
                      <Input placeholder="请输入指标 ID" />
                    </Form.Item>
                  </Col>
                </Row>
                <Form.Item label="描述" name="description" rules={[{ message: '请输入描述' }]}>
                  <Input.TextArea placeholder="请输入描述" />
                </Form.Item>
                <Row gutter={12}>
                  <Col span={12}>
                    <Form.Item label="是否可累加" name="isCumulative">
                      <Switch checkedChildren="是" unCheckedChildren="否" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="单位"
                      name="formatTemplate"
                      tooltip={
                        <div>
                          {['.1f', ',.2f', '¥.3f元', formatTemplate].filter(Boolean).map((key) => {
                            const num = 100.38
                            return (
                              <div key={key}>
                                {key} = {FormatNumberReg.test(key) ? formatNumber(num, key) : ''}
                              </div>
                            )
                          })}
                        </div>
                      }
                      rules={[{ pattern: FormatNumberReg, message: '单位格式存在问题' }]}
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                </Row>
                <Form.Item name="synonyms" label="同义词">
                  <Synonyms />
                </Form.Item>
              </FormLayout>
            </Col>
            <Form.Item noStyle shouldUpdate>
              {({ getFieldValue }) => {
                const metricType = getFieldValue('metricType') as MetricTypeNameKey
                switch (metricType) {
                  case 'simple':
                    /* 原子指标右侧 原有的类型可以符合预期 */
                    return (
                      <Col span={12}>
                        <FormLayout required={false} title={'度量'}>
                          <Form.Item label="度量" name={['typeParams', 'measure']} rules={[{ required: true }]}>
                            <Select
                              placeholder="请选择度量"
                              loading={isModelLoading}
                              showSearch
                              allowClear
                              filterOption={(...args) => searchFilter(['nameZh', 'value'], ...args)}
                              optionRender={(option) => (
                                <>
                                  <div className="flex items-center">
                                    <SvgIcon icon={measureIcon} className="mr-[4px] h-[20px] w-[20px]" />
                                    <span>{option.data?.name || ''}</span>
                                  </div>
                                  <div className="mb-[4px] ml-6 self-stretch text-[13px] font-semibold">
                                    {option.data?.nameZh || ''}
                                  </div>
                                </>
                              )}
                              options={get(modelData, 'data.data.dataModelDesc.measures', []).map(
                                (item: { name: string; nameZh: string }) => ({
                                  value: item.name,
                                  name: item.name,
                                  nameZh: item.nameZh,
                                }),
                              )}
                            />
                          </Form.Item>
                        </FormLayout>
                        <FormLayout required={false} title={'筛选'} withSwitch={true} defaultVisible={true}>
                          <Form.Item name={'filter'} rules={[{ message: '请输入' }]}>
                            <Input.TextArea placeholder="请输入" />
                          </Form.Item>
                        </FormLayout>
                      </Col>
                    )
                  case 'derived':
                    return (
                      <Col span={12}>
                        <FormLayout required={false} title="表达式">
                          <Form.Item name={['typeParams', 'expr']} rules={[{ message: '请输入' }]}>
                            <Input.TextArea placeholder="请输入" />
                          </Form.Item>
                        </FormLayout>
                      </Col>
                    )
                  case 'ratio':
                    return (
                      <Col span={12}>
                        <FormLayout required={false} title="度量">
                          <Form.Item
                            label="分子"
                            name={['typeParams', 'numerator']}
                            className="min-w-[200px]"
                            rules={[{ required: true, message: '分子' }]}
                          >
                            <Select
                              placeholder="请选择指标作为分子（原子指标）"
                              notFoundContent="暂无指标，请先创建指标"
                              className="min-w-[200px]"
                              allowClear
                              showSearch
                              filterOption={(...args) => searchFilter(['label', 'value'], ...args)}
                            >
                              {/* 分子只支持 simple 类型的指标 */}
                              {metricsList
                                ?.filter((metric) => metric.type === 'simple')
                                .map((metric) => {
                                  return (
                                    <Option key={metric.name} value={metric.name} label={metric.label}>
                                      {metric.label} ({metric.name})
                                    </Option>
                                  )
                                })}
                            </Select>
                          </Form.Item>

                          <Form.Item
                            label="分母"
                            name={['typeParams', 'denominator']}
                            className="min-w-[200px]"
                            rules={[{ required: true, message: '分母' }]}
                          >
                            <Select
                              placeholder="请选择指标作为分母"
                              notFoundContent="暂无指标，请先创建指标"
                              className="min-w-[200px]"
                              showSearch
                              allowClear
                              filterOption={(...args) => searchFilter(['label', 'value'], ...args)}
                            >
                              {metricsList?.map((metric) => {
                                return (
                                  <Option key={metric.name} value={metric.name} label={metric.label}>
                                    {metric.label} ({metric.name})
                                  </Option>
                                )
                              })}
                            </Select>
                          </Form.Item>
                        </FormLayout>
                      </Col>
                    )
                  case 'list':
                    /* 列表指标右侧 */
                    return (
                      <Col span={12}>
                        <FormLayout required={false} title={'指标'}>
                          <Form.List name={['typeParams', 'metrics']} initialValue={[{}]}>
                            {(outerFields, { add, remove }) => (
                              <>
                                {outerFields.map(({ key, name: outerName }, index) => (
                                  <Row key={key} className="mb-4">
                                    <Col flex={1}>
                                      <Form.Item
                                        name={[outerName, 'name']}
                                        className="mb-[8px]"
                                        rules={[{ required: true, message: '请选择指标' }]}
                                      >
                                        <Select
                                          allowClear
                                          notFoundContent="暂无指标，请先创建指标"
                                          placeholder="请选择指标"
                                          className="min-w-[200px]"
                                          showSearch
                                          filterOption={(...args) => searchFilter(['label', 'value'], ...args)}
                                        >
                                          {metricsList?.map((i: { name: string; label: string }) => (
                                            <Option key={i.name} value={i.name} label={i.label}>
                                              {i.label} ({i.name})
                                            </Option>
                                          ))}
                                        </Select>
                                      </Form.Item>
                                    </Col>
                                    <Col
                                      className={clsx('mt-[5px] flex items-baseline justify-around', {
                                        'w-[45px]': outerFields.length > 1,
                                        'w-[30px]': outerFields.length === 1,
                                      })}
                                    >
                                      {outerFields.length > 1 ? (
                                        <Typography.Link>
                                          <MinusOutlined className="cursor-pointer" onClick={() => remove(index)} />
                                        </Typography.Link>
                                      ) : null}
                                    </Col>
                                  </Row>
                                ))}
                                <Typography.Link
                                  onClick={() => {
                                    add()
                                  }}
                                >
                                  <PlusOutlined className="mr-1 cursor-pointer" />
                                  添加指标
                                </Typography.Link>
                              </>
                            )}
                          </Form.List>
                        </FormLayout>
                      </Col>
                    )
                  default:
                    return assertExhaustive(metricType)
                }
              }}
            </Form.Item>
          </Row>
        </Form>
      </Modal>
    </>
  )
}

export default MetricsCreate
