import { Input, Select, Space, Tabs, Form, Button, App, Row, Col, Tag } from 'antd'
import React, { useMemo, useState } from 'react'

import { FormItemProps } from 'antd/es/form/FormItem'
import { SelectProps, OptionProps } from 'antd/es/select'
import { ArrowLeftOutlined, MinusCircleOutlined, PlusOutlined } from '@ant-design/icons'
import axios from 'axios'
import { useRequest } from 'ahooks'
import { useAtom, useAtomValue } from 'jotai'
import { useLocation, useNavigate } from 'react-router-dom'
import { Metric, MetricListResponse, MetricTypeNames } from 'src/shared/metric-types'
import { askBIApiUrls } from 'src/shared/url-map'
import { APIResponse } from 'src/shared/common-types'
import { SynonymsForm } from '../components/SynonymsForm'
import { currentDatasetAtom, metricConfigAtom, metricListAtomForOption } from '../../AskBI/askBIAtoms'
import { NullComponent } from '../components/NullComponent'

type MetricTypeNameKey = keyof typeof MetricTypeNames
type ActiveKey = 'metricForm' | 'selectMetricType'
const Option = Select.Option

const measureConfig = {
  FormItemProps: {
    label: '度量',
    name: ['typeParams', 'measure'],
    rules: [{ required: true, message: '请选择度量' }],
  },
  SelectProps: {
    placeholder: '请选择度量',
    showSearch: true,
    optionFilterProp: 'label',
    filterOption: (input: string, option: any) => {
      // 忽略大小写搜索
      return (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
    },
  },
  options: [],
}

const MetricsOptions: { label: string; value: MetricTypeNameKey }[] = Object.entries(MetricTypeNames).map(
  ([key, value]) => ({
    label: value,
    value: key as MetricTypeNameKey,
  }),
)

// measure  和 metrics 是需要远程加载数据的
const SimpleMetricSubForm = () => {
  const metricConfig = useAtomValue(metricConfigAtom)
  const arr = metricConfig?.allMeasures.map((i) => ({ label: `${i.label}(${i.name})`, value: i.name }))
  return (
    <>
      <SelectWithConstOption {...Object.assign(measureConfig, { options: arr })} />
      {/* <FilterInput /> */}
    </>
  )
}
/*
const RankMetricSubForm = () => {
  const metricConfig = useAtomValue(metricConfigAtom)
  const arr = metricConfig?.allMeasures.map((i) => ({ label: `${i.label}(${i.name})`, value: i.name }))
  return (
    <>
      <SelectWithConstOption {...Object.assign(measureConfig, { options: arr })} />

      <SelectWithConstOption {...orderByConfig} />
    </>
  )
}
*/
const selectOptions = {
  showSearch: true,
  optionFilterProp: 'label',
  filterOption: (input: string, option: any) => {
    // 忽略大小写搜索
    return (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
  },
}
const RatioMetricSubForm = () => {
  const metricConfig = useAtomValue(metricConfigAtom)
  const arr = metricConfig?.allMetrics.map((i) => ({ label: `${i.label}(${i.name})`, value: i.name })) || []

  return (
    <div>
      <Form.Item
        label="分子"
        name={['typeParams', 'numerator']}
        className="min-w-[200px]"
        rules={[{ required: true, message: '分子' }]}
      >
        <Select placeholder="请选择指标作为分子" {...selectOptions}>
          {arr.map(({ value, label }) => (
            <Option key={value} value={value}>
              {label}
            </Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item
        label="分母"
        name={['typeParams', 'denominator']}
        className="min-w-[200px]"
        rules={[{ required: true, message: '分母' }]}
      >
        <Select placeholder="请选择指标作为分母" {...selectOptions}>
          {arr.map(({ value, label }) => (
            <Option key={value} value={value}>
              {label}
            </Option>
          ))}
        </Select>
      </Form.Item>
    </div>
  )
}

const ListMetricSubForm = () => {
  const metricConfig = useAtomValue(metricConfigAtom)
  const arr = metricConfig?.allMetrics.map((i) => ({ label: `${i.label}(${i.name})`, value: i.name })) || []
  return (
    <>
      <Form.Item label="指标">
        <Form.List name={['typeParams', 'metrics']} initialValue={[null]}>
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name }) => (
                <>
                  <Row key={key} style={{ width: '100%', display: 'flex', marginBottom: 8 }} gutter={[10, 0]}>
                    <Col flex={1}>
                      <Form.Item name={[name, 'name']} rules={[{ required: true, message: '请选择指标' }]}>
                        <Select placeholder="请选择指标" className="min-w-[200px]">
                          {arr.map((i) => (
                            <Option key={i.value} value={i.value}>
                              {i.label}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col className="mt-[8px] flex w-[30px] items-baseline justify-around">
                      <MinusCircleOutlined onClick={() => remove(name)} />
                    </Col>
                  </Row>
                </>
              ))}
              <Form.Item>
                <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                  添加指标
                </Button>
              </Form.Item>
            </>
          )}
        </Form.List>
      </Form.Item>
    </>
  )
}

const DerivedMetricSubForm = () => {
  return (
    <>
      <ExpressionInput />
      <Form.Item label="指标" required>
        <Form.List name={['typeParams', 'metrics']} initialValue={[null]}>
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <>
                  <Row key={key} style={{ display: 'flex', marginBottom: 8 }} gutter={[10, 0]}>
                    <Col className="flex-1">
                      <Form.Item name={[name, 'name']} rules={[{ required: true, message: '请输入指标名称' }]}>
                        <Input placeholder="请输入指标名称" />
                      </Form.Item>
                    </Col>
                    <Col className="flex-1">
                      <Form.Item {...restField} name={[name, 'offsetWindow']} className="flex-1">
                        <Select className="flex-1" placeholder="选择偏移窗口">
                          <Option value="1 month">1 个月</Option>
                          <Option value="1 year">1 年</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col className="mt-[8px] flex w-[30px] items-baseline justify-around">
                      <MinusCircleOutlined className="ml-2" onClick={() => remove(name)} />
                    </Col>
                  </Row>
                  <Row key={key} style={{ display: 'flex', marginBottom: 8 }} gutter={[10, 0]}>
                    <Col flex={1}>
                      <Form.Item {...restField} name={[name, 'alias']}>
                        <Input placeholder="请输入别名" />
                      </Form.Item>
                    </Col>
                    <Col flex={1}>
                      <Form.Item {...restField} name={[name, 'filter']}>
                        <Input placeholder="请输入过滤条件" />
                      </Form.Item>
                    </Col>
                    <Col className="w-[30px]" />
                  </Row>
                </>
              ))}
              <Form.Item>
                <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                  添加指标
                </Button>
              </Form.Item>
            </>
          )}
        </Form.List>
      </Form.Item>
    </>
  )
}
const MetricTypeForm = {
  simple: <SimpleMetricSubForm />,
  ratio: <RatioMetricSubForm />,
  derived: <DerivedMetricSubForm />,
  // rank: <RankMetricSubForm />,
  list: <ListMetricSubForm />,
}

const SelectWithConstOption = (props: {
  FormItemProps: FormItemProps
  SelectProps: SelectProps
  options: Omit<OptionProps, 'children'>[]
}) => {
  return (
    <Form.Item {...props.FormItemProps}>
      <Select {...props.SelectProps}>
        {...(props.options || []).map(({ value, label }) => {
          if (value === null) {
            return
          } else if (value === undefined) {
            return
          } else {
            return (
              <Select.Option key={value} value={value}>
                {label}
              </Select.Option>
            )
          }
        })}
      </Select>
    </Form.Item>
  )
}

const ExpressionInput = () => {
  return (
    <Form.Item label="表达式" name={['typeParams', 'expr']} rules={[{ required: true, message: '请输入表达式' }]}>
      <Input placeholder="请输入表达式" />
    </Form.Item>
  )
}

const MetricForm = ({ SubForm, metricsType }: { SubForm: React.ReactNode; metricsType: MetricTypeNameKey }) => {
  return (
    <>
      <Form.Item label="类型">
        <Tag className="font-bold">
          {MetricTypeNames[metricsType]} - {metricsType}
        </Tag>
      </Form.Item>

      <Form.Item label="标识符" name="name" rules={[{ required: true, message: '请输入标识符' }]}>
        <Input placeholder="请输入标识符" />
      </Form.Item>

      <Form.Item label="中文名" name="label" rules={[{ required: true, message: '请输入中文名' }]}>
        <Input placeholder="请输入中文名" />
      </Form.Item>

      {SubForm}

      {metricsType === 'simple' && (
        <Form.Item
          label="过滤条件"
          name="filter"
          extra="请输入符合 SQL 语法的过滤表达式，如：col1 > 10 AND col2 < 20"
          rules={[{ required: false, message: '请输入过滤条件' }]}
        >
          <Input placeholder="请输入过滤条件" />
        </Form.Item>
      )}

      <SynonymsForm />

      <Form.Item label="描述" name="description" rules={[{ message: '请输入描述' }]}>
        <Input.TextArea placeholder="请输入描述" />
      </Form.Item>
    </>
  )
}

export default function CreateMetricForm({
  metricId,
  updateList,
  isForm,
  onOk,
}: {
  onOk?: () => void
  onCancel?: () => void
  isForm?: boolean
  metricId?: string
  updateList?: () => void
}) {
  const { message: antdMessage } = App.useApp()

  const location = useLocation()
  const navigate = useNavigate()

  // 解析查询参数
  const searchParams = useMemo(() => new URLSearchParams(location.search), [location.search])
  const pageMetricType = searchParams.get('metricsType') as MetricTypeNameKey | null
  const [metricType, setMetricType] = useState<MetricTypeNameKey | null>(pageMetricType)
  const [activeKey, setActiveKey] = useState<ActiveKey>(isForm || pageMetricType ? 'metricForm' : 'selectMetricType')

  const currentDataset = useAtomValue(currentDatasetAtom)
  const [form] = Form.useForm()
  const [_metricListAtomForOption, setMetricListAtomForOption] = useAtom(metricListAtomForOption)
  const [originalMetricInfo, setOriginalMetricInfo] = useState<Metric>()

  useRequest(
    async () => {
      const response = await axios.get<APIResponse<MetricListResponse>>(
        askBIApiUrls.metrics.listInScene(currentDataset?.sceneId || ''),
      )
      if (!response.data.data) {
        return { list: [], total: 0 }
      }
      setMetricListAtomForOption(response.data.data.list)
      return
    },
    {
      onSuccess: () => {},
      onError: (error) => {
        antdMessage.error('获取指标信息失败')
        console.error('Load user list with error', error)
      },
    },
  )

  const { data: editMetricInfo } = useRequest(
    async () => {
      if (metricId) {
        const response = await axios.get(askBIApiUrls.metrics.detail(metricId))
        return response.data.data
      }
    },
    {
      ready: !!metricId,
      refreshDeps: [metricId],
      onSuccess: (data) => {
        const synonyms = data.synonyms
          .filter((item: string[]) => item != null)
          .map((i: string) => {
            return { name: i }
          })
        const tempFormValue = { ...data, synonyms: synonyms.length ? synonyms : [null] }
        setMetricType(data.type)
        setOriginalMetricInfo(tempFormValue)
        data && form.setFieldsValue({ ...tempFormValue })
      },
      onError: (error: any) => {
        console.error('get currentMetricInfo =', error)
      },
    },
  )
  const { run: createOrUpdateMetric } = useRequest(
    (arg) => {
      const { id, synonyms } = arg
      const metric = {
        ...arg,
        type: metricId ? editMetricInfo.type : metricType,
        synonyms: synonyms.filter((item: string[]) => item != null).map((i: { name: string }) => i.name),
      }
      if (id) {
        return axios.put(askBIApiUrls.metrics.update(id), metric)
      } else {
        return axios.post(askBIApiUrls.metrics.create, { metricInfo: metric, sceneId: currentDataset?.sceneId })
      }
    },
    {
      manual: true,
      onSuccess: (_, arg) => {
        updateList?.()
        if (arg[0].id) {
          antdMessage.success('修改指标成功！', 0.5)
        } else {
          antdMessage.success(`新增指标成功！`, 0.5)
        }
        form.resetFields()
        onOk?.()
      },
      onError: (error, arg) => {
        if (arg[0].id) {
          antdMessage.error('修改指标失败！' + error.message.slice(0, 15))
        } else {
          antdMessage.error('新增指标失败！' + error.message.slice(0, 15))
        }
      },
    },
  )

  return (
    <Tabs
      activeKey={activeKey}
      defaultActiveKey="selectMetricType"
      onChange={(key) => {
        switch (key) {
          // TODO 这个写法要研究一下
          case 'metricForm':
          case 'selectMetricType':
            setActiveKey(key)
            return
          default:
            return
        }
      }}
      renderTabBar={() => <NullComponent />}
      className="rounded-tremor-default flex min-h-[360px] w-full max-w-full flex-col rounded-xl bg-white dark:bg-slate-700"
      centered
      items={[
        {
          label: 'selectMetricType',
          key: 'selectMetricType',
          children: (
            <div className="flex w-full cursor-pointer justify-center gap-10 md:p-4">
              {MetricsOptions.map(({ label, value }) => {
                return (
                  <div
                    key={value}
                    className="flex h-20 w-20 select-none items-center justify-center rounded-lg bg-primary text-white"
                    onClick={() => {
                      searchParams.set('metricsType', value)
                      navigate(`?${searchParams.toString()}`, { replace: true })
                      setMetricType(value)
                      setActiveKey('metricForm')
                    }}
                  >
                    {label}
                  </div>
                )
              })}
            </div>
          ),
        },
        {
          label: 'metricForm',
          key: 'metricForm',
          children: (
            <div className="flex flex-col flex-wrap justify-center md:p-4 md:pt-0">
              {/* 新建模式才有返回 */}
              {!metricId && (
                <div
                  className="cursor-pointer p-1"
                  onClick={() => {
                    searchParams.delete('metricsType')
                    navigate(`?${searchParams.toString()}`, { replace: true })
                    setMetricType(null)
                    setActiveKey('selectMetricType')
                    form.resetFields()
                  }}
                >
                  <ArrowLeftOutlined />
                  <span>上一步</span>
                </div>
              )}
              <Form
                className="mt-2 w-full"
                form={form}
                onFinish={(val) => {
                  createOrUpdateMetric({ ...val, id: metricId })
                }}
                initialValues={{}}
                layout={metricId ? 'vertical' : 'horizontal'}
                labelCol={{ span: metricId ? 24 : 4 }}
                wrapperCol={{ span: metricId ? 24 : 16 }}
              >
                {/* 这里有点过度设计，不要复用代码，不方便修改 */}
                <MetricForm
                  SubForm={MetricTypeForm[metricType as MetricTypeNameKey]}
                  metricsType={metricType as MetricTypeNameKey}
                />
                <Row justify="end">
                  <Col pull={4}>
                    <Space align="end">
                      <Button
                        onClick={() => {
                          // 编辑情况下重置为原来的数据， 新增情况下重置为空数据
                          form.setFieldsValue(originalMetricInfo)
                        }}
                      >
                        重置
                      </Button>
                      <Button type="primary" onClick={() => form.submit()}>
                        确定
                      </Button>
                    </Space>
                  </Col>
                </Row>
              </Form>
            </div>
          ),
        },
      ]}
    />
  )
}
