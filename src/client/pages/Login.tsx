/**
 * @description Login page
 */
import React, { useEffect, useState } from 'react'
import { Button, Checkbox, CheckboxProps, Divider, Form, Input, Select, Tag, message } from 'antd'
import { useNavigate } from 'react-router-dom'
import debounce from 'lodash/debounce'
import { useAtom, useAtomValue } from 'jotai'
import { nanoid } from 'nanoid'
import axios from 'axios'
import LoginBg from '/img/login-bg.webp'
import clsx from 'clsx'
import { thfundAppName, thfundDefaultUrl } from 'src/shared/thfund-constant'
import { ASK_BI_BASE, askBIApiUrls } from 'src/shared/url-map'
import { chatPath, CompanyName, IS_H5, RecordNumber, XEngineHomePath } from 'src/shared/constants'
import { SvgIcon, loginProxyIcon, tianhongIcon } from '../components/SvgIcon'
import { login } from '../utils'
import { brandInfoAtom, envAtom } from './AskBI/askBIAtoms'
import './Login.css'

// 天弘SSO登录成功后 跳转回askbi的界面
function getBackUrl() {
  const protocol = window.location.protocol
  const hostname = window.location.hostname
  const port = window.location.port
  return `${protocol}//${hostname}${port ? `:${port}` : ''}/api/sso/tianhong-login`
}

const hashToMessageMapping = {
  '#no-auth-code': 'auth code 不存在',
  '#token-verify-failed': 'token校验错误',
  '#token-parse-failed': 'token解析失败',
  '#account-invalid': '账号失效',
  '#login-failed': '登录失败，请重试',
} as const

export default function Login() {
  const navigate = useNavigate()
  const [form] = Form.useForm()
  const brandInfo = useAtomValue(brandInfoAtom)
  const [showLoading, setShowLoading] = useState<boolean>(false)
  const [isOpenProxy, setIsOpenProxy] = useState<boolean>(false)
  const [podInfo, setPodInfo] = useState<any[]>([])
  const [env] = useAtom(envAtom)
  const defaultHomePath = env?.VITE_PRODUCTS?.trim() === 'X-Engine' ? XEngineHomePath : chatPath

  useEffect(() => {
    const hashValue = window.location.hash
    const messageContent = hashToMessageMapping[hashValue as keyof typeof hashToMessageMapping]
    // hash存在并且命中了message key
    if (hashValue && messageContent) {
      message.error(messageContent).then(() => {
        history.pushState({}, document.title, window.location.pathname)
      })
    }
  }, [])

  // 确认登录
  const handleLogin = async (params: { username: string; password: string; proxy: string }, hostname: string) => {
    try {
      const data = await login({ ...params, hostname })
      setShowLoading(false)
      if (data && data.username) {
        isOpenProxy
          ? localStorage.setItem('diProxyBackendUrl', params.proxy)
          : localStorage.removeItem('diProxyBackendUrl')
        message.success('登录成功')
        await new Promise((resolve) => setTimeout(resolve, 800))
        navigate(`${ASK_BI_BASE}${defaultHomePath}`)
      }
    } catch (error: any) {
      setShowLoading(false)
      console.error('handleLogin =', error)
      message.error(error?.message ?? error?.toString() ?? '未知错误')
    }
  }

  // 在login页检测登录状态
  // useRequest(
  //   async () => {
  //     const response = await axios.get(askBIApiUrls.login.checkToken, {
  //       params: { hostname: window.location.hostname },
  //     })
  //     if (response.data.data) {
  //       navigate(`${ASK_BI_BASE}${defaultHomePath}`)
  //     }
  //     getDefaultDiProxyUrl()
  //   },
  //   {
  //     onError: (error: any) => {
  //       console.error('get currentUserBasicInfo =', error)
  //     },
  //   },
  // )

  // 获取di默认代理后台服务地址
  // const getDefaultDiProxyUrl = async () => {
  //   const response = await axios.get(askBIApiUrls.diProxyLogin.diDefaultProxy)
  //   form.setFieldValue('proxy', response.data.data)
  // }

  // 表单点击确认
  const onFinish = async () => {
    !IS_H5 && (await form.validateFields())
    setShowLoading(true)
    const fields = form.getFieldsValue()
    if (isOpenProxy && !fields.proxy) {
      message.error('请选择或输入后台服务地址')
      setShowLoading(false)
    } else {
      const { username, password } = fields
      if (username && password) {
        // 调用登录接口
        handleLogin(fields, window.location.hostname)
      } else {
        message.error('请输入账号和密码')
      }
    }
  }

  const handleTHLogin = () => {
    const redirectSsoUrl = `http://${thfundDefaultUrl}/thssoAccess?appName=${thfundAppName}&backUrl=${encodeURIComponent(
      getBackUrl(),
    )}&requestId=${encodeURIComponent(nanoid())}`
    window.location.href = redirectSsoUrl
  }

  // 是否开启代理checkbox的change事件
  const onCheckboxChange: CheckboxProps['onChange'] = (e) => {
    setIsOpenProxy(e.target.checked)
    if (e.target.checked && podInfo.length === 0) {
      getKubeNamespace()
    } else {
      localStorage.removeItem('diProxyUrl')
    }
  }

  // 表单确认按钮防抖
  const debouncedOnFinish = debounce(onFinish, 500)

  const uniqueArray = (array: any[]) => {
    const uniqueArray = array.filter(
      (value, index, self) => index === self.findIndex((obj) => obj.value === value.value),
    )
    return uniqueArray
  }

  /**
   * 代理地址发生变更，如果代理地址大于1条，则删除第一条，保留第二条
   * @param value
   */
  const onProxyChange = (value: any) => {
    if (value.length > 1) {
      value.shift()
    }

    form?.setFieldValue('proxy', value)
  }

  /** 选择或输入后台服务地址 */
  const getKubeNamespace = async () => {
    try {
      const podArray: any[] = []
      const result = await axios.get(askBIApiUrls.diProxyLogin.namespace)
      console.info(result)
      const { namespaces } = result.data.data
      namespaces.map(async (v: string) => {
        const resultService = await axios.get(askBIApiUrls.diProxyLogin.service, { params: { namespace: v } })
        // eslint-disable-next-line @typescript-eslint/naming-convention
        const { service_view } = resultService.data.data
        service_view.some((service: any) => {
          service['service_port'].some(async (servicePort: any) => {
            if (servicePort['port'] === 8044) {
              const resultPod = await axios.get(askBIApiUrls.diProxyLogin.pod, { params: { namespace: v } })
              // eslint-disable-next-line @typescript-eslint/naming-convention
              const { pods_view } = resultPod.data.data
              pods_view.some((podsView: { [x: string]: string | string[] }) => {
                if (podsView['pod_name'].indexOf('backend-') !== -1 && podsView['pod_status'] === 'Running') {
                  const localPodNode = podsView['pod_node'] as string
                  const pod = {
                    value: `http://${podsView['pod_node']}:${servicePort['node_port']}`,
                    label: (
                      <>
                        <Tag color="#108ee9">{v}</Tag>
                        <Tag color="purple">
                          {localPodNode?.split('.')[3]}:{servicePort['node_port']}
                        </Tag>
                      </>
                    ),
                  }
                  podArray.push(pod)
                  setPodInfo(uniqueArray(podArray))
                }
              })
            }
          })
        })
      })
    } catch (error) {
      message.error('Error')
    }
  }

  const renderForm = () => {
    return (
      <div className="login-form mx-7 flex w-full flex-col items-center justify-center rounded-xl bg-white pb-6 pt-9 md:w-[500px]">
        <div className="w-full px-9">
          <p className="mb-9 text-center text-2xl font-semibold text-black">登录</p>
          <Form form={form} name="basic" onFinish={debouncedOnFinish} autoComplete="off">
            <Form.Item name="username" rules={[{ required: true, message: '请输入账号' }]}>
              <Input placeholder="输入账号" autoComplete="username" size="large" maxLength={30} />
            </Form.Item>
            <Form.Item name="password" rules={[{ required: true, message: '请输入密码' }]}>
              <Input.Password
                placeholder="输入密码"
                autoComplete="current-password"
                size="large"
                maxLength={30}
                suffix={
                  <SvgIcon
                    icon={loginProxyIcon}
                    className={`h-5 w-5 ${podInfo.length > 0 ? 'text-[#16B57F]' : 'text-[#8E8E93]'}`}
                  />
                }
              />
            </Form.Item>
            <Form.Item name="proxy" hidden={!isOpenProxy}>
              <Select
                size="large"
                placeholder="请选择或输入后台服务地址"
                options={podInfo}
                mode="tags"
                suffixIcon={
                  <SvgIcon
                    icon={loginProxyIcon}
                    className={`h-5 w-5 ${podInfo.length > 0 ? 'text-[#16B57F]' : 'text-[#8E8E93]'}`}
                  />
                }
                onChange={onProxyChange}
              />
            </Form.Item>
            <Form.Item className="login-btn">
              <Button
                type="primary"
                htmlType="submit"
                className="w-full font-bold"
                loading={showLoading}
                size="large"
                onKeyDown={debouncedOnFinish}
              >
                登录
              </Button>
            </Form.Item>
            <Form.Item className="proxy-form-item mt-6 text-center">
              <Checkbox onChange={onCheckboxChange}>使用其他代理登录</Checkbox>
            </Form.Item>
          </Form>
        </div>
        {TIANHONG_LOGIN && (
          <div className="bottom-6 w-4/5 md:w-3/5">
            <Divider plain className="text-xs">
              天弘登录
            </Divider>
            <Button className="w-full font-normal" size="large" onClick={handleTHLogin}>
              <div className="flex items-center justify-center">
                <SvgIcon className="mr-2 mt-1.5 h-auto w-12" icon={tianhongIcon} />
                <span>SSO登录</span>
              </div>
            </Button>
          </div>
        )}
      </div>
    )
  }

  return (
    <div id="login-page" className="relative flex h-screen w-screen flex-col items-center">
      <img src={LoginBg} alt="" className="absolute top-0 h-full w-full object-cover" />

      <div className="header top-0 flex h-20 w-full items-center px-8">
        <img
          src={brandInfo && brandInfo.logo}
          alt=""
          className={clsx('z-10 w-auto cursor-pointer', {
            'h-10': brandInfo && brandInfo.brandName === 'China Telecom',
            'h-6': brandInfo && brandInfo.brandName === 'DIPEAK',
          })}
        />
      </div>

      <div className="middle-content absolute flex h-full w-full items-center justify-center">
        <div className="mr-40 hidden w-[580px] min-w-96 md:block">
          <p className="mb-2 text-5xl leading-[70px] text-white">用AI洞察一切数据</p>
          {brandInfo && brandInfo.brandName === 'DIPEAK' && (
            <p className="text-xl text-white">
              {`${brandInfo.companyName}的愿景是让数据智能像水电一样简单，企业面对巨量复杂 数据的洞察力突破，是我们的目标。`}
            </p>
          )}
          <p className="mt-2 text-xl text-white opacity-80">— {brandInfo && brandInfo.brandName}</p>
        </div>
        {renderForm()}
      </div>

      {brandInfo && brandInfo.brandName === 'DIPEAK' && (
        <div className="footer absolute bottom-4 text-center text-white opacity-50">
          <div className="block text-xs md:hidden">
            <p>{CompanyName}</p>
            <p>{RecordNumber}</p>
          </div>
          <p className="hidden text-xs md:block">{`${CompanyName}｜${RecordNumber}`}</p>
        </div>
      )}
    </div>
  )
}
