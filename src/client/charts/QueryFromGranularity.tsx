/**
 * @description 尝试按颗粒度查找当前区间内没有数据的日期
 */
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline'
import dayjs from 'dayjs'
import React, { useEffect, useMemo } from 'react'
import axios from 'axios'
import { useBoolean, useRequest } from 'ahooks'
import { useAtomValue } from 'jotai'
import { ClockCircleOutlined } from '@ant-design/icons'
import { produce } from 'immer'
import { Chat, ChatResponse, ChatResponseQueryMetric } from '@shared/common-types'
import { askBIApiUrls } from 'src/shared/url-map'
import { convertTimeToSpecificDate } from 'src/shared/time-utils'
import { DATE_ALIAS, Metric, QueryParamsVerified, SimpleMetric } from 'src/shared/metric-types'
import { extractDimensionAndValues, getTimeQuery } from 'src/shared/common-utils'
import { conversationIdAtom, currentDataset<PERSON>tom, llmType<PERSON>tom, metricConfigAtom } from '../pages/AskBI/askBIAtoms'

const EXPAND_THRESHOLD = 5

export function QueryFromGranularity(props: { queryParamsVerified?: QueryParamsVerified; currentChat?: Chat }) {
  const currentDataset = useAtomValue(currentDatasetAtom)
  const metricConfig = useAtomValue(metricConfigAtom)
  const llmType = useAtomValue(llmTypeAtom)
  const conversationId = useAtomValue(conversationIdAtom)
  const sceneId = currentDataset?.sceneId ?? ''
  const chatId = props.currentChat?.id ?? ''
  const { queryParamsVerified } = props
  const timeGranularityMin = metricConfig?.timeDimensionDatum?.timeGranularityMin
  const { data, run } = useRequest(
    async () => {
      const newQueryParamsVerified = produce(queryParamsVerified!, (queryParamsVerified) => {
        queryParamsVerified.queryParams.timeQueryParams!.timeGranularity = timeGranularityMin!
      })
      const response = await axios.post<ChatResponse>(askBIApiUrls.metricQuery, {
        queryParamsVerified: newQueryParamsVerified,
        llmType: llmType,
        conversationId: conversationId,
        sceneId,
        chatId,
      })
      return response.data
    },
    { manual: true },
  )

  const metrics = useMemo(
    () =>
      queryParamsVerified?.queryParams.metricNames.map((name) =>
        metricConfig?.allMetrics.find((metric) => metric.name === name),
      ) as Metric[],
    [queryParamsVerified, metricConfig],
  )

  useEffect(() => {
    // 不渲染的情况：
    // 不存在queryParamsVerified
    // 有指标不存在
    // 存在指标不可累加
    // 没有时间聚合
    // 时间聚合不是total
    // 没有场景最小颗粒度就不渲染
    // 存在多指标
    // 多个指标
    // 多个公司
    if (
      !(
        !queryParamsVerified ||
        metrics.some((v) => !v || !(v as SimpleMetric).isCumulative) ||
        !queryParamsVerified.queryParams.timeQueryParams ||
        queryParamsVerified.queryParams.timeQueryParams.timeGranularity !== 'total' ||
        !timeGranularityMin ||
        queryParamsVerified.queryParams.metricNames.length > 1 ||
        metrics.length > 1 ||
        extractDimensionAndValues(queryParamsVerified.queryParams.where).codeValues.length > 1
      )
    ) {
      const specTimeStartFunction = convertTimeToSpecificDate(
        queryParamsVerified.queryParams.timeQueryParams.timeStartFunction,
        'start',
      )
      const specTimeEndFunction = convertTimeToSpecificDate(
        queryParamsVerified.queryParams.timeQueryParams.timeEndFunction,
        'end',
      )
      // 计算时间跨度
      const timeSpan = Math.abs(
        specTimeStartFunction.year * 12 +
          specTimeStartFunction.month -
          (specTimeEndFunction.year * 12 + specTimeEndFunction.month),
      )
      if (timeSpan > 1) {
        run()
        return
      }
    }
  }, [queryParamsVerified, timeGranularityMin, metrics, run])

  const dateList = useMemo(() => {
    if (!data?.ready) {
      return null
    }
    const specTimeStartFunction = convertTimeToSpecificDate(
      queryParamsVerified!.queryParams.timeQueryParams!.timeStartFunction,
      'start',
    )
    const specTimeEndFunction = convertTimeToSpecificDate(
      queryParamsVerified!.queryParams.timeQueryParams!.timeEndFunction,
      'end',
    )
    const startDate = dayjs()
      .year(specTimeStartFunction.year)
      .month(specTimeStartFunction.month - 1)
      .date(specTimeStartFunction.day)
    const endDate = dayjs()
      .year(specTimeEndFunction.year)
      .month(specTimeEndFunction.month - 1)
      .date(specTimeEndFunction.day)
    const FORMAT = 'YYYY-MM-DD'
    const res: dayjs.Dayjs[] = []
    const existDateRecord = new Map(
      (data as ChatResponseQueryMetric).rows.map((item) => [
        dayjs(item[DATE_ALIAS])
          .startOf(timeGranularityMin as any)
          .format('YYYY-MM-DD'),
        item,
      ]),
    )
    for (let cur = startDate; cur.valueOf() <= endDate.valueOf(); cur = cur.add(1, timeGranularityMin)) {
      // console.log(`Start = ${startDate.format(FORMAT)}, End = ${endDate.format(FORMAT)}, cur = ${cur.format(FORMAT)}`)
      if (!existDateRecord.has(cur.format(FORMAT))) {
        res.push(cur)
      } else {
        const row = existDateRecord.get(cur.format(FORMAT))!
        if (queryParamsVerified?.queryParams.metricNames.some((name) => !row[name])) {
          res.push(cur)
        }
      }
    }
    return res
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data])

  const needExpand = useMemo(() => dateList && dateList.length > EXPAND_THRESHOLD, [dateList])
  const [expand, expandOps] = useBoolean(false)
  useEffect(() => {
    if (!needExpand) {
      expandOps.setTrue()
    } else {
      expandOps.setFalse()
    }
  }, [needExpand, expandOps])

  const finalDateList = useMemo(() => {
    if (!dateList) return []
    if (!expand) return dateList.slice(0, EXPAND_THRESHOLD)
    return dateList
  }, [dateList, expand])

  if (finalDateList.length === 0 || !dateList) return null

  return (
    <div className="flex w-full rounded-lg bg-[#DBF6F1] p-4">
      <div>
        <ClockCircleOutlined className="text-[16px]" />
      </div>
      <div className="ml-[10px] flex flex-col">
        <div>
          <div>在{getTimeQuery(queryParamsVerified?.queryParams.timeQueryParams)}期间以下时间没有数据：</div>
          <div className="flex flex-wrap gap-x-2">
            {finalDateList.map((v, i) => {
              return (
                <span key={i}>{timeGranularityMin === 'month' ? v.format('YYYY年MM月') : v.format('YYYY-MM-DD')}</span>
              )
            })}
            {dateList.length > EXPAND_THRESHOLD && (
              <div className="flex items-center justify-center">
                <div className="flex cursor-pointer" onClick={expand ? expandOps.setFalse : expandOps.setTrue}>
                  {expand ? '收起' : '展开'}
                  {expand ? (
                    <ChevronUpIcon className="h-[20px] w-[20px]" />
                  ) : (
                    <ChevronDownIcon className="h-[20px] w-[20px]" />
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
