/* eslint-disable no-extra-semi */
/* eslint-disable @typescript-eslint/no-extra-semi */
/**
 * @description ECharts 图表使用 React 包裹的组件，包括了 ColumnChart、LineChart、PieChart、ScatterChart、
 TreemapChart、GroupColumnChart、StackedColumnChart、MultiLineChart
 */
import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react'
import { produce } from 'immer'
import * as echarts from 'echarts'
import clsx from 'clsx'
import { App, Select, SelectProps } from 'antd'
import { debounce } from 'lodash-es'
import { BlobWithRatio, EChartsChartType, ThemeType } from '@shared/common-types'
import { generateBlobForCopy } from '../generateBlobForCopy'

interface Props {
  height?: string
  width?: string
  option: echarts.EChartsOption
  theme: ThemeType
  chartType: EChartsChartType
}

// 定义在数据量超过10的时候 为大数据模式，默认只渲染10条到图上 from zhoufanrong
const LARGE_CHART_THRESHOLD = 10

function EChartsReact(
  props: Props,
  ref: React.Ref<{
    downloadPNG: () => void
    copyPNG: () => void
    getBlob: () => Promise<BlobWithRatio | string | undefined>
  }>,
) {
  const { option: propsOption, chartType, theme } = props
  const enableLargeMode = Array.isArray(propsOption.series) && propsOption.series.length >= LARGE_CHART_THRESHOLD
  const chartRef = useRef<HTMLDivElement>(null)
  const myChartInstance = useRef<echarts.ECharts | null>(null)
  const { message } = App.useApp()
  const [option, setOption] = useState(propsOption)
  const [selectedOption, setSelectedOption] = useState<SelectProps['value']>([])
  const seriesRecord = useMemo(() => {
    if (enableLargeMode) {
      const res: Record<string, echarts.SeriesOption> = {}
      for (const item of propsOption.series as echarts.SeriesOption[]) {
        res[item.name!] = item
      }
      return res
    }
  }, [propsOption.series, enableLargeMode])

  useEffect(() => {
    if (enableLargeMode) {
      const series = selectedOption.map((name: string) => seriesRecord?.[name]).filter(Boolean)
      const newOption = produce(option, (option) => {
        option.series = series
      })
      setOption(newOption)
      myChartInstance.current?.setOption(newOption)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [enableLargeMode, selectedOption, seriesRecord])

  useImperativeHandle(ref, () => ({
    downloadPNG: () => {
      if (myChartInstance.current == null) {
        console.error('当前界面不存在此图表的dom元素!')
        return
      }
      const imgBase64 = myChartInstance.current.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff',
      })
      const link = document.createElement('a')
      imgBase64 && (link.href = imgBase64)
      link.download = `${chartType}.png`
      link.click()
    },
    copyPNG: () => {
      if (chartRef.current == null) {
        console.error('当前界面不存在此图表的dom元素!')
        message.error('图表复制失败!')
        return
      }

      const pngItem = new ClipboardItem({
        'image/png': generateBlobForCopy(chartRef.current).then((blob) => blob),
      })

      navigator.clipboard
        .write([pngItem])
        .then(() => {
          message.success('图表复制成功')
        })
        .catch((error) => {
          message.error('图表复制失败!')
          console.error(error)
        })
    },
    getBlob: async () => {
      if (chartRef.current == null) {
        console.error('当前界面不存在此图表的dom元素!')
        message.error('导出报告失败')
        return
      }
      return await generateBlobForCopy(chartRef.current)
    },
  }))

  // 防抖处理 resize 事件
  const handleResize = debounce(() => {
    if (myChartInstance.current) {
      myChartInstance.current.resize()
    }
  }, 500)

  // 首次 mount 的时候，修改 theme 的时候，修改 option 的试试
  useEffect(() => {
    if (chartRef.current == null) {
      return
    }

    const initChart = () => {
      if (chartRef.current) {
        const { clientWidth, clientHeight } = chartRef.current
        if (clientWidth > 0 && clientHeight > 0 && !myChartInstance.current) {
          myChartInstance.current = echarts.init(chartRef.current, theme === 'dark' ? 'dark' : undefined)

          if (enableLargeMode) {
            const newOption = produce(option, (option) => {
              ;(option.series as echarts.SeriesOption[]) = (option.series as echarts.SeriesOption[])!.slice(
                0,
                LARGE_CHART_THRESHOLD,
              )
            })
            setSelectedOption((newOption.series as echarts.SeriesOption[]).map((v) => v.name))
            setOption(newOption)
            myChartInstance.current.setOption(newOption)
          } else {
            setOption(option)
            myChartInstance.current.setOption(option)
          }
        }
      }
    }

    // 初始化图表
    initChart()

    // ResizeObserver 会自动监听元素尺寸的变化，当尺寸发生变化时，会触发回调函数。从而避免了 DOM 元素的宽度或高度为 0 的警告。
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect
        if (width > 0 && height > 0) {
          // 调整图表尺寸，而不是重新构建
          handleResize()
        }
      }
    })

    resizeObserver.observe(chartRef.current)
    window.addEventListener('resize', handleResize)
    return () => {
      myChartInstance.current?.dispose()
      myChartInstance.current = null
      resizeObserver.disconnect()
      window.removeEventListener('resize', handleResize)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [theme, propsOption])
  return (
    <div className="flex w-full flex-col">
      {enableLargeMode && (
        <Select
          allowClear
          // maxTagCount="responsive"
          className="w-full"
          options={(propsOption.series as echarts.SeriesOption[]).map((v) => {
            return {
              label: v.name,
              value: v.name,
            }
          })}
          mode="multiple"
          value={selectedOption}
          onChange={setSelectedOption}
        />
      )}
      <div className={clsx('echarts-react-chart h-full w-full', chartType)}>
        <div
          className="echarts-react-chart-container flex h-full min-h-[200px] w-full min-w-[200px] justify-center"
          ref={chartRef}
        />
      </div>
    </div>
  )
}

export default forwardRef(EChartsReact)
