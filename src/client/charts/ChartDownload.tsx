/**
 * @description ChartDownload 下载图表
 */
import React from 'react'
import { Dropdown, MenuProps, App } from 'antd'
import { ArrowDownTrayIcon } from '@heroicons/react/24/outline'
import { ChartType, ExportFileType } from '@shared/common-types'
import { assertExhaustive } from '@shared/common-utils'

interface Props {
  chartType: ChartType
  tableData?: Array<{ [key: string]: any }>
  onDownloadPNG: () => void
}
const ExportFileTypeOptions: { value: ExportFileType; label: string }[] = [
  {
    value: 'PNG',
    label: '导出为PNG图片',
  },
  {
    value: 'CSV',
    label: '导出为CSV文件',
  },
  {
    value: 'Excel',
    label: '导出为Excel文件',
  },
]

/** 给图表分别配置导出形式 */
const filterExportOptions = (chartType: ChartType) => {
  switch (chartType) {
    case 'AttrAnalysis':
    case 'AttrMetricAnalysis':
      return ExportFileTypeOptions.filter((item) => item.value !== 'CSV' && item.value !== 'Excel')
    case 'SimpleTable':
    case 'LineChart':
    case 'ColumnChart':
    case 'PieChart':
    case 'ScatterChart':
    case 'TreemapChart':
    case 'GroupColumnChart':
    case 'StackedColumnChart':
    case 'MultiLineChart':
    case 'RankBarChart':
    case 'Kpi':
      return ExportFileTypeOptions
    default:
      assertExhaustive(chartType)
  }
}

function ChartDownload(props: Props) {
  const { tableData, chartType } = props
  const { message } = App.useApp()

  const handleTableToCSV = () => {
    if (!tableData) {
      message.error('未获取到图表数据')
      return
    }
    const keys = Object.keys(tableData[0])
    const csvData = [keys.join(',')]

    tableData.forEach((item: any) => {
      const values = keys.map((key) => item[key])
      csvData.push(values.join(','))
    })
    const blob = new Blob([csvData.join('\n')], { type: 'text/csv;charset=utf-8;' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${chartType}.csv`
    link.click()
    URL.revokeObjectURL(url)
  }

  const handleTableToExcel = async () => {
    if (!tableData) {
      message.error('未获取到图表数据')
      return
    }
    const XLSX = await import('xlsx')
    const workbook = XLSX.utils.book_new()
    const sheet = XLSX.utils.json_to_sheet(tableData)
    XLSX.utils.book_append_sheet(workbook, sheet, 'Sheet1')
    const blob = new Blob([XLSX.writeFile(workbook, `${chartType}.xlsx`)])
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `${chartType}.xlsx`
    URL.revokeObjectURL(link.href)
  }

  const handleDownload = (value: ExportFileType) => {
    switch (value) {
      case 'PNG':
        return props.onDownloadPNG()
      case 'CSV':
        return handleTableToCSV()
      case 'Excel':
        return handleTableToExcel()
      default:
        assertExhaustive(value)
    }
  }

  // 为 table 隐藏 png 选项，因为无法复制多页
  const renderFileTypeOptions = filterExportOptions(chartType)

  const items: MenuProps['items'] = renderFileTypeOptions.map((o, index) => {
    return {
      key: index,
      label: (
        <div
          className="flex cursor-pointer items-center px-2 py-1 hover:bg-slate-50 hover:dark:bg-slate-600/30"
          onClick={() => {
            handleDownload(o.value)
          }}
        >
          {o.label}
        </div>
      ),
    }
  })

  return (
    <Dropdown menu={{ items }} placement="top">
      <div className="flex cursor-pointer items-center rounded-md px-3 py-2 ring-1 ring-inset ring-gray-300 hover:bg-primary/60 hover:text-white dark:text-gray-400 dark:ring-gray-500 dark:hover:text-white">
        <ArrowDownTrayIcon className="h-4 w-4" />
        <span className="hidden md:block">导出</span>
      </div>
    </Dropdown>
  )
}

export default ChartDownload
