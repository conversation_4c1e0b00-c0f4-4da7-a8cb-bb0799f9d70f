/**
 * @description 指标卡图表
 */
import React, { useRef, forwardRef, useImperativeHandle, useState } from 'react'
import clsx from 'clsx'
import { useAtomValue } from 'jotai'
import { InformationCircleIcon, ArrowLongUpIcon, ArrowLongDownIcon, MinusIcon } from '@heroicons/react/24/outline'
import { App, Divider, Pagination } from 'antd'
import html2canvas from 'html2canvas'
import { PeriodOverPeriodMetricConfig, Metric } from 'src/shared/metric-types'
import { AssistantChartChatItem, BlobWithRatio, OlapRow, isDimension, isMetric } from 'src/shared/common-types'
import { IS_H5 } from 'src/shared/constants'
import { formatNumber } from 'src/shared/common-utils'
import { generateBlobForCopy } from '../generateBlobForCopy'
import { metricConfigAtom } from '../pages/AskBI/askBIAtoms'

interface Props {
  icon?: string
  option: {
    dataset: {
      source: OlapRow[]
    }
  }
  data: AssistantChartChatItem
}

type BaseDataType = string | number | null

function renderData(val: BaseDataType, formatTemplate: string) {
  if (val == null || val === '') {
    return <div className="flex items-center gap-2">--</div>
  }
  return formatNumber(val, formatTemplate)
}

interface MetricCardProps {
  metricLabel: string
  quantity: BaseDataType
  formatTemplate: string
  yoyMomGrowth?: string
  needIgnorePop?: boolean
  isPredict?: boolean
}

function showBaseMetricCard(props: MetricCardProps) {
  const { metricLabel, quantity, formatTemplate, yoyMomGrowth, needIgnorePop, isPredict } = props
  const yoyNumber = parseFloat(yoyMomGrowth ?? '')
  const popNode = (
    <>
      <span className="ml-[10px] text-[16px] font-normal text-[#575757]">
        {Number.isNaN(yoyNumber) ? '无同比数据' : yoyNumber === 0 ? '同比无变化' : '同比'}
      </span>
      {yoyNumber > 0 ? (
        <ArrowLongUpIcon className={'ml-[4px] h-5 w-5 text-[#D32D2D]'} />
      ) : yoyNumber < 0 ? (
        <ArrowLongDownIcon className={'ml-[4px] h-5 w-5 text-[#43E2A9]'} />
      ) : yoyNumber === 0 ? (
        <MinusIcon className={'ml-[4px] h-5 w-5 text-[#FAA251]'} />
      ) : (
        <InformationCircleIcon className={'ml-[4px] h-5 w-5 text-[#A3A3A3]'} />
      )}
    </>
  )
  return (
    <div className="kpi-item flex flex-col gap-1 text-left" key={yoyNumber}>
      <div
        className={clsx('kpi-metric-label text-xl font-bold dark:text-slate-400', {
          'text-blue-500': isPredict,
          'text-[#575757]': !isPredict,
        })}
      >
        {metricLabel}
      </div>
      <div
        className={clsx(
          'kpi-value flex items-center truncate text-[28px] font-extrabold leading-9 text-[#0F172A] dark:text-slate-50',
          {
            'text-blue-500': isPredict,
            'text-[#0F172A]': !isPredict,
          },
        )}
      >
        <span>{renderData(quantity, formatTemplate)}</span>
        {needIgnorePop ? null : popNode}
      </div>
    </div>
  )
}

function showCard(props: { dimensionValue: string; metricsInfo: MetricCardProps[] }) {
  return (
    <div className="flex flex-col gap-4">
      <div className="kpi-dimension-value text-sm text-[#A3A3A3] dark:text-slate-300">{props.dimensionValue}</div>
      {props.metricsInfo.map((i) => {
        return showBaseMetricCard(i)
      })}
      <Divider variant="dashed" className="mb-4 mt-0 w-full" />
    </div>
  )
}

function Kpi(
  props: Props,
  ref: React.Ref<{
    downloadPNG: () => void
    copyPNG: () => void
    getBlob: () => Promise<BlobWithRatio | string | undefined>
  }>,
) {
  const metricConfig = useAtomValue(metricConfigAtom)
  const rowsMetadata = props.data.rowsMetadata
  const [pageSize, setPageSize] = useState(10)
  const [currentPage, setCurrentPage] = useState(0)

  const kpiRef = useRef<HTMLDivElement>(null)
  const { message } = App.useApp()
  const source = props.option.dataset.source
  useImperativeHandle(ref, () => ({
    downloadPNG: () => {
      if (kpiRef.current == null) {
        console.error('当前界面不存在此图表的dom元素!')
        return
      }
      html2canvas(kpiRef.current).then((canvas: HTMLCanvasElement) => {
        const pngUrl = canvas.toDataURL('image/png')
        const link = document.createElement('a')
        link.href = pngUrl
        link.download = 'Kpi.png'
        link.click()
      })
    },
    copyPNG: async () => {
      if (kpiRef.current == null) {
        console.error('当前界面不存在此图表的dom元素!')
        message.error('图表复制失败!')
        return
      }

      const pngItem = new ClipboardItem({
        'image/png': generateBlobForCopy(kpiRef.current).then((blob) => blob),
      })

      navigator.clipboard
        .write([pngItem])
        .then(() => {
          message.success('图表复制成功')
        })
        .catch((error) => {
          message.error('图表复制失败!')
          console.error(error)
        })
    },
    getBlob: async () => {
      if (kpiRef.current == null) {
        console.error('当前界面不存在此图表的dom元素!')
        message.error('导出报告失败')
        return
      }
      return await generateBlobForCopy(kpiRef.current)
    },
  }))

  if (source.length < 1) {
    return <div>无数据</div>
  }
  const metricMetaList = (props.data.queryParamsVerified?.queryParams.metricNames
    .map((name) => metricConfig?.allMetrics.find((v) => v.name === name))
    .filter(Boolean) ?? []) as Metric[]
  const metricNameFromListSet = new Set(
    metricMetaList
      .filter((v) => v.type === 'list')
      .map((v) => v.typeParams.metrics.map((v) => v.name))
      .flat(),
  )

  const metrics = rowsMetadata
    .filter(isMetric)
    .map((item) => item.value)
    .filter((v) => {
      if (!props.data.queryParamsVerified?.hasPop) {
        return v.type !== 'periodOverPeriod'
      }
      return true
    })

  const dimensions = rowsMetadata
    .filter(isDimension)
    .map((item) => item.value)
    .sort((a) => (a.type === 'categorical' ? -1 : 1))
  const dimName = dimensions[0]?.name // 默认取第一个
  const dimValues = [...new Set(source.map((s) => s[dimName]))]

  const list =
    dimensions.length === 0
      ? metrics
          .map((metric, index) => (
            <div className="flex w-full flex-col" key={index}>
              {showBaseMetricCard({
                metricLabel: metric.label,
                quantity: source[0][metric.name],
                formatTemplate: metric.formatTemplate,
                isPredict: props.data.isPredict,
              })}
            </div>
          ))
          .flat()
      : dimValues
          .map((dimValue, i) => {
            const metricsInfo = metrics.map((metric) => {
              const sourceData = source.find((s) => s[dimName] === dimValue)
              return {
                metricLabel: metric.label,
                quantity: sourceData?.[metric.name],
                formatTemplate: metric.formatTemplate,
                yoyMomGrowth: Object.entries(sourceData ?? {}).find(([k]) =>
                  k.startsWith(PeriodOverPeriodMetricConfig.yoyMonthGrowth.metricPrefix),
                )?.[1],
                needIgnorePop: metricNameFromListSet.has(metric.name),
                isPredict: props.data.isPredict,
              }
            })
            return (
              <div key={i} className="flex w-full flex-col">
                {showCard({ metricsInfo, dimensionValue: dimValue })}
              </div>
            )
          })
          .flat()

  const needPagination = list.length > 10
  const filteredList = needPagination ? list.slice(currentPage * pageSize, (currentPage + 1) * pageSize) : list

  return (
    <div className="kpi flex w-full flex-row flex-wrap justify-between" ref={kpiRef}>
      {filteredList}
      {needPagination && (
        <Pagination
          showLessItems={IS_H5}
          className="flex w-full justify-center"
          pageSize={pageSize}
          current={currentPage + 1}
          onChange={(page, pageSize) => {
            setCurrentPage(page - 1)
            setPageSize(pageSize)
          }}
          total={list.length}
        />
      )}
    </div>
  )
}

export default forwardRef(Kpi)
