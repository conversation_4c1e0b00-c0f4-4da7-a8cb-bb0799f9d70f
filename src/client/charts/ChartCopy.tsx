/**
 * @description ChartCopy复制图表
 */
import React, { useMemo } from 'react'
import { App } from 'antd'
import { DocumentDuplicateIcon } from '@heroicons/react/24/outline'
import { ChartType, RowsMetadata } from '@shared/common-types'
import { IS_H5 } from 'src/shared/constants'
import { copyToClipboard } from '../utils'

interface Props {
  chartType: ChartType
  rowsMetadata: RowsMetadata
  tableData?: Array<{ [key: string]: any }>
  onCopyPNG: () => void
}

function ChartCopy(props: Props) {
  const { tableData: originTableData, chartType, rowsMetadata } = props
  const { message } = App.useApp()

  const fieldTitleMap = useMemo(() => {
    return rowsMetadata.reduce(
      (result, next) => {
        result[next.value.name] = next.value.label
        return result
      },
      {} as { [key: string]: string },
    )
  }, [rowsMetadata])

  const tableData = useMemo(() => {
    return originTableData?.map((item) => {
      return Object.keys(item).reduce(
        (acc, key) => {
          acc[fieldTitleMap[key]] = item[key]
          return acc
        },
        {} as { [key: string]: string },
      )
    })
  }, [originTableData, fieldTitleMap])

  const handleDateToExcel = (data: Array<{ [key: string]: any }>) => {
    const keys = Object.keys(data[0])
    const headerRow = keys.join('\t')
    let excelData = `${headerRow}\n`

    data.forEach((item) => {
      const values = keys.map((key) => item[key])
      const row = values.join('\t')
      excelData += `${row}\n`
    })
    return excelData
  }

  const handleCopyToClipboard = async (text: string) => {
    try {
      if (!IS_H5) {
        await navigator.clipboard.writeText(text)
      } else {
        copyToClipboard(text)
      }
      message.success('表格数据已复制到剪贴板，可粘贴到excel查看')
    } catch (error) {
      message.error('表格数据复制失败')
      console.error('表格数据复制失败:', error)
    }
  }

  const handleCopyTableDataToExcel = () => {
    if (!tableData) {
      message.error('未获取到图表数据')
      return
    }
    const excelFormattedData = handleDateToExcel(tableData)
    handleCopyToClipboard(excelFormattedData)
  }

  const handleCopy = () => {
    if (chartType === 'SimpleTable') {
      handleCopyTableDataToExcel()
    } else {
      props.onCopyPNG()
    }
  }

  return (
    <div
      onClick={handleCopy}
      className="flex cursor-pointer items-center rounded-md px-3 py-2 ring-1 ring-inset ring-gray-300 hover:bg-primary/60 hover:text-white dark:text-gray-400 dark:ring-gray-500 dark:hover:text-white"
    >
      <DocumentDuplicateIcon className="h-4 w-4" />
      <span className="hidden md:block">复制</span>
    </div>
  )
}

export default ChartCopy
