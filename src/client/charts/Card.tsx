/**
 * @description 卡片容器，包裹图表组件，展示标题、图表类型切换、分享等功能
 */
import React from 'react'
import { ChartBarIcon, ChartPieIcon, TableCellsIcon, TicketIcon, RectangleGroupIcon } from '@heroicons/react/24/outline'
import { LineChartOutlined, DotChartOutlined } from '@ant-design/icons'
import clsx from 'clsx'
import { useAtomValue } from 'jotai'
import { ThemeType, AssistantChartChatItem, ChartType, Chat } from '@shared/common-types'
import { IconButtonGroupOption } from '@components/IconButtonGroup'
import {
  groupColumnChartIcon,
  StackedColumnChartIcon,
  MultiLineChartIcon,
  SvgIcon,
  RankBarChartIcon,
  attrAnalysisIcon,
} from '@components/SvgIcon'
import { IS_H5 } from 'src/shared/constants'
import { MetricConfigResponse, QueryParamsVerified, TimeQueryParams } from 'src/shared/metric-types'
import { isBaoWuFinancial } from 'src/shared/baowu-share-utils'
import { getTimeQuery } from 'src/shared/common-utils'
import { currentDatasetAtom } from '../pages/AskBI/askBIAtoms'
import QueryParamsSelector from '../components/QueryParamsSelector'
import MetricItems from './MetricItems'
import { QueryFromGranularity } from './QueryFromGranularity'

interface Props {
  currentChat?: Chat
  className?: string
  bodyClassName?: string
  title?: string
  queryParamsVerified?: QueryParamsVerified
  onQueryParamsChange?: (newValue: QueryParamsVerified) => void
  showHeader?: boolean
  theme: ThemeType
  children: React.ReactNode
  metricConfig: MetricConfigResponse | null
  disableShare?: boolean
  disableChartTypeChange?: boolean
  chartType: ChartType
  originalChartType: ChartType
  recommendChartTypes?: ChartType[]
  data: Array<{ [key: string]: any }>
  disableDownload?: boolean
  /** 下载的回调，只有 disableDownload 不为 true 的时候才有效  */
  onDownloadPNG?: () => void
  onCopyPNG?: () => void
  onChartTypeChange: (newValue: ChartType) => void
  /** 为嵌入使用 */
  assistantChartChatItem?: AssistantChartChatItem
  isViewMode?: boolean
}

export const CHART_ICON_MAP: IconButtonGroupOption<ChartType>[] = [
  {
    value: 'PieChart',
    iconComponent: ChartPieIcon,
  },
  {
    value: 'ColumnChart',
    iconComponent: ChartBarIcon,
  },
  {
    value: 'LineChart',
    iconNode: <LineChartOutlined className="text-xl" />,
  },
  {
    value: 'ScatterChart',
    iconNode: <DotChartOutlined className="text-xl" />,
  },
  {
    value: 'TreemapChart',
    iconComponent: RectangleGroupIcon,
  },
  {
    value: 'GroupColumnChart',
    iconNode: <SvgIcon icon={groupColumnChartIcon} className="size-5" />,
  },
  {
    value: 'StackedColumnChart',
    iconNode: <SvgIcon icon={StackedColumnChartIcon} className="size-5" />,
  },
  {
    value: 'MultiLineChart',
    iconNode: <SvgIcon icon={MultiLineChartIcon} className="size-5" />,
  },
  {
    value: 'Kpi',
    iconComponent: TicketIcon,
  },
  {
    value: 'SimpleTable',
    iconComponent: TableCellsIcon,
  },
  {
    value: 'RankBarChart',
    iconNode: <SvgIcon icon={RankBarChartIcon} className="size-5" />,
  },
  {
    value: 'AttrAnalysis',
    iconNode: <SvgIcon icon={attrAnalysisIcon} className="size-5" />,
  },
]

export function getVisibleChartIconMap(recommendChartTypes: ChartType[]): IconButtonGroupOption<ChartType>[] {
  // 根据 recommendChartTypes 的顺序来展示可以切换的图表类型
  if (recommendChartTypes && recommendChartTypes.length > 0) {
    return recommendChartTypes
      .map<IconButtonGroupOption<ChartType>>((chartType) => CHART_ICON_MAP.find(({ value }) => value === chartType)!)
      .filter(Boolean)
  } else {
    return []
  }
}

export const RenderTime = (props: { timeQueryParams?: TimeQueryParams; metricConfig: MetricConfigResponse | null }) => {
  const { timeQueryParams, metricConfig } = props
  const finalStr = timeQueryParams
    ? getTimeQuery(timeQueryParams, isBaoWuFinancial(metricConfig?.metricTableName))
    : null

  return (
    finalStr && (
      <div className="w-fit rounded-md border border-[#E4E4E4] bg-[#FCFCFC] px-2 py-1.5 text-xs">{finalStr}</div>
    )
  )
}

const RenderMetricAndDimensionList = (props: {
  queryParamsVerified?: QueryParamsVerified
  metricConfig: MetricConfigResponse | null
}) => {
  const { queryParamsVerified, metricConfig } = props

  if (
    !queryParamsVerified ||
    !queryParamsVerified.queryParams?.metricNames ||
    queryParamsVerified.queryParams.metricNames.length === 0
  ) {
    return null
  }

  return (
    <div className="flex flex-wrap gap-1">
      <MetricItems metricNames={queryParamsVerified.queryParams.metricNames} />
      <RenderTime timeQueryParams={queryParamsVerified.queryParams.timeQueryParams} metricConfig={metricConfig} />
    </div>
  )
}

export default function Card(props: Props) {
  const {
    children,
    currentChat,
    className,
    bodyClassName,
    metricConfig,
    queryParamsVerified,
    onQueryParamsChange,
    isViewMode,
  } = props
  const currentDataset = useAtomValue(currentDatasetAtom)

  return (
    // 在移动端，不能把背景设置成白色
    <div
      className={clsx(
        'card rounded-tremor-default group flex max-w-full flex-col gap-2 rounded-xl dark:bg-slate-700',
        className,
      )}
    >
      {/* {infoTexts.length > 0 && (
        <div className="mb-2 flex w-fit items-center gap-2 rounded-lg bg-yellow-100 px-2 py-1 text-base font-semibold dark:bg-yellow-700">
          <InfoCircleOutlined className="mr-1 h-4 w-4" />
          {infoTexts.map((infoText) => (
            <span key={infoText}>{infoText}</span>
          ))}
        </div>
      )} */}
      {!IS_H5 && (
        <QueryParamsSelector
          queryParamsVerified={queryParamsVerified}
          onChange={onQueryParamsChange}
          metricConfig={metricConfig}
          isViewMode={isViewMode}
        />
      )}
      <div className={clsx('card-body flex w-full flex-grow', bodyClassName)}>{children}</div>
      {IS_H5 && <RenderMetricAndDimensionList queryParamsVerified={queryParamsVerified} metricConfig={metricConfig} />}
      {currentDataset?.enableAccMetricToastWhenEmptyData && (
        <QueryFromGranularity queryParamsVerified={queryParamsVerified} currentChat={currentChat} />
      )}
    </div>
  )
}
