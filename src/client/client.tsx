import React, { useEffect } from 'react'
import ReactDOM from 'react-dom/client'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import '../shared/config'
import { GlobalWorkerOptions } from 'pdfjs-dist'
import { ConfigProvider } from 'antd'
import { getQueryState } from 'src/shared/common-utils'
import { IS_DARK } from 'src/shared/constants'
import Router from './Router'
import './client.css'
import './markdown.css'
import { ErrorBoundary, FallbackComponent } from './ErrorBoundary'
import 'github-markdown-css/github-markdown.css'
import 'react-reflex/styles.css'
import 'katex/dist/katex.min.css'
import { isIOS, autoLogin, stateStore, formatPathWithBaseUrl } from './utils'
import { queryStateAtom } from './pages/AskBI/askBIAtoms'
import { useTheme } from './hooks/useTheme'
import WaterMark from './components/WaterMark/WaterMark'

// 设置语言包
dayjs.locale('zh-cn')

window.__APP_VERSION__ = __APP_VERSION__

/** 设置 PDF.js 工作线程的源文件路径 */
GlobalWorkerOptions.workerSrc = formatPathWithBaseUrl('/js/pdf-v3.8.worker.min.js')

const App = () => {
  const [theme] = useTheme()
  // 设置页面主题 并检查用户的系统是否设置为暗黑模式
  const addThemeToDocument = () => {
    if (IS_DARK) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }

  // 用于处理输入框失去焦点事件，解决输入框被弹起下不来的问题
  const handleBlur = (event: any) => {
    if (
      document.documentElement.offsetHeight <= document.documentElement.clientHeight &&
      ['input', 'textarea'].includes(event.target.localName)
    ) {
      document.body.scrollIntoView() // 回到顶部
    }
  }

  useEffect(() => {
    addThemeToDocument()
    if (isIOS) {
      document.addEventListener('blur', handleBlur, true)
    }

    return () => {
      if (isIOS) {
        document.removeEventListener('blur', handleBlur, true)
      }
    }
  }, [])

  return (
    <React.StrictMode>
      <ErrorBoundary fallbackComponent={FallbackComponent}>
        <ConfigProvider theme={theme}>
          <Router />
          {/* 水印 */}
          <WaterMark />
        </ConfigProvider>
      </ErrorBoundary>
    </React.StrictMode>
  )
}

async function beforeRender() {
  // 初始化的时候直接设置一次query，后期状态管理存储，不再次获取query
  let queryState = getQueryState()
  // 如果用户明确表示，不需要刷新保留原有状态，可以在query增加这条
  if (!queryState.enableReloadQueryState) {
    queryState = getQueryState(true)
  }

  if (queryState.enableAutoLogin) {
    await autoLogin()
    stateStore.set(queryStateAtom, () => queryState)
  }
}

beforeRender().finally(() => {
  ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(<App />)
})
