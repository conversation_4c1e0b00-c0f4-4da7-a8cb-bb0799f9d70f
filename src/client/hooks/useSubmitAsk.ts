import { useSet<PERSON><PERSON> } from 'jotai/react'
import { useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { makeAgentRequestAtom } from '../pages/AskBI/askBIAtoms'

type Props = {
  isMiniMode: boolean
}
export default function useSubmitAsk({ isMiniMode }: Props) {
  const makeAgentRequest = useSetAtom(makeAgentRequestAtom)
  const navigate = useNavigate()

  const handleSubmit = useCallback(
    (newMessage?: string) => {
      makeAgentRequest({
        navigate,
        isMiniMode: isMiniMode,
        newMessage: newMessage,
      })
    },
    [makeAgentRequest, navigate, isMiniMode],
  )

  return [handleSubmit]
}
