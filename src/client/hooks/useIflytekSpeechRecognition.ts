import { useState, useRef, useEffect, useCallback } from 'react'
import { App } from 'antd'
import axios from 'axios'
import { useUnmount } from 'ahooks'
import { askBIApiUrlsWithoutBaseUrl } from 'src/shared/url-map'
import { formatPathWithBaseUrl } from '../utils'

function useIflytekSpeechRecognition(onSubmit: (val: string) => void) {
  const { message: antdMessage } = App.useApp()

  const [message, setMessage] = useState<string>('')
  const isRecognitionActiveRef = useRef(false)
  const audioContextRef = useRef<AudioContext | null>(null)
  const processorRef = useRef<ScriptProcessorNode | null>(null)
  const mediaStreamRef = useRef<MediaStream | null>(null)
  const audioInputRef = useRef<MediaStreamAudioSourceNode | null>(null)
  const audioData = useRef<any[]>([])

  const startRecognition = async () => {
    try {
      if (isRecognitionActiveRef.current) {
        return
      }

      const startTime = new Date().getTime()
      if (!navigator.mediaDevices) {
        antdMessage.error('当前环境无法获取麦克风权限')
        return
      }
      isRecognitionActiveRef.current = true

      await navigator.mediaDevices
        ?.getUserMedia({ audio: true })
        .then((stream) => {
          console.info('getMediaAuth audio success', new Date().getTime() - startTime)
          mediaStreamRef.current = stream
        })
        .catch((err) => {
          console.error('获取麦克风权限失败--err', err)
          let message = ''
          if (err.name === 'NotAllowedError') {
            message = '用户拒绝了麦克风权限'
          } else if (err.name === 'NotFoundError') {
            message = '未找到麦克风'
          } else if (err.name === 'OverconstrainedError') {
            message = '请求的设备无法满足'
          } else if (err.name === 'NotReadableError') {
            message = '麦克风被其他应用占用'
          }
          message && antdMessage.error(message)
        })
      if (mediaStreamRef.current === null) {
        return false
      }
      audioContextRef.current = new window.AudioContext({ sampleRate: 8000 })
      audioInputRef.current = audioContextRef.current.createMediaStreamSource(mediaStreamRef.current)

      processorRef.current = audioContextRef.current.createScriptProcessor(2048, 1, 1)
      audioInputRef.current.connect(processorRef.current)
      processorRef.current.connect(audioContextRef.current.destination)

      console.info('Init audio success', new Date().getTime() - startTime)

      if (!isRecognitionActiveRef.current) {
        // 如果走到这,isRecognitionActiveRef.current已经变成false,说明语音识别已经被停止,就不要再走下一步了
        clearEffect()
        return
      }

      processorRef.current.onaudioprocess = handleAudioProcess
    } catch (error) {
      console.error('Error accessing audio stream:', error)
      antdMessage.error(`语音识别开启失败，请联系管理员`)
    }
  }
  const uploadWav = (blob: Blob) => {
    const formData = new FormData()
    formData.append('file', blob, 'audio.wav')
    axios
      .post(formatPathWithBaseUrl(askBIApiUrlsWithoutBaseUrl.baowu.baowuSpeechRecognition), formData)
      .then((response) => {
        if (typeof response.data?.data === 'string') {
          handleSubmit(response.data.data)
        } else {
          console.error('Upload failed')
        }
      })
      .catch((error) => {
        console.error('Error:', error)
      })
  }

  const stopRecognition = async () => {
    console.info('audioData.current--stopRecognition', audioData.current, message)
    isRecognitionActiveRef.current = false
    if (audioData.current.length === 0) {
      return
    }
    const wavBlob = exportWAV(audioData.current, 8000)
    uploadWav(wavBlob)
    clearEffect()

    setMessage('')
  }

  const clearEffect = useCallback(() => {
    audioInputRef.current?.disconnect()
    if (processorRef.current) {
      processorRef.current.onaudioprocess = null
    }
    processorRef.current?.disconnect()
    processorRef.current = null

    mediaStreamRef.current?.getTracks()?.forEach((track) => track.stop())
    mediaStreamRef.current = null

    audioContextRef.current?.close()
    audioContextRef.current = null
    audioData.current = []
  }, [])

  useEffect(() => {
    if (!isRecognitionActiveRef.current) {
      clearEffect()
    }
    return () => {
      clearEffect()
    }
  }, [clearEffect])

  // 页面返回时, 停用麦克风权限(待测试验证)
  useUnmount(() => {
    clearEffect()
  })

  const handleAudioProcess = useCallback(
    (event: any) => {
      console.info('---handleAudioProcess---')
      if (!isRecognitionActiveRef.current) {
        clearEffect()
        return
      }
      const inputData = event.inputBuffer.getChannelData(0)
      const buffer = new Float32Array(inputData)

      audioData.current.push(buffer) // 存储音频数据
    },
    [clearEffect],
  )

  const handleSubmit = (message: string) => {
    try {
      onSubmit(message)
    } catch (error) {
      console.error('useIflytekSpeechRecognition handleSubmit err: ', error)
    }
  }

  return {
    message,
    isRecognitionActive: isRecognitionActiveRef.current,
    stopRecognition,
    startRecognition,
  }
}
// 将录制的数据转换为 WAV 文件
function exportWAV(audioData: Float32Array[], sampleRate: number): Blob {
  const bufferLength = audioData.length * audioData?.[0]?.length

  const dataBuffer = new Float32Array(bufferLength)
  let offset = 0

  for (let i = 0; i < audioData.length; i++) {
    dataBuffer.set(audioData[i], offset)
    offset += audioData[i].length
  }

  // 创建 WAV 文件头和数据
  const wavBuffer = encodeWAV(dataBuffer, sampleRate)
  return new Blob([wavBuffer], { type: 'audio/wav' })
}

// 编码 WAV 文件格式
function encodeWAV(samples: Float32Array, sampleRate: number): DataView {
  const buffer = new ArrayBuffer(44 + samples.length * 2)
  const view = new DataView(buffer)

  // 写入 WAV 文件头
  writeString(view, 0, 'RIFF')
  view.setUint32(4, 36 + samples.length * 2, true)
  writeString(view, 8, 'WAVE')
  writeString(view, 12, 'fmt ')
  view.setUint32(16, 16, true)
  view.setUint16(20, 1, true) // AudioFormat (PCM)
  view.setUint16(22, 1, true) // NumChannels (Mono)
  view.setUint32(24, sampleRate, true) // SampleRate
  view.setUint32(28, sampleRate * 2, true) // ByteRate
  view.setUint16(32, 2, true) // BlockAlign
  view.setUint16(34, 16, true) // BitsPerSample
  writeString(view, 36, 'data')
  view.setUint32(40, samples.length * 2, true) // Subchunk2Size

  // 写入 PCM 数据
  let offset = 44
  for (let i = 0; i < samples.length; i++, offset += 2) {
    const s = Math.max(-1, Math.min(1, samples[i]))
    view.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true)
  }

  return view
}

// 写入字符串到 DataView
function writeString(view: DataView, offset: number, string: string): void {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i))
  }
}

export default useIflytekSpeechRecognition
