import { useRequest } from 'ahooks'
import axios from 'axios'
import { APIResponse } from 'src/shared/common-types'
import { askBIApiUrls } from 'src/shared/url-map'

export function useAuth(props: { resource: string; action: string }) {
  const { resource, action } = props
  const { data, loading } = useRequest(async () => {
    const res = await axios.get<APIResponse<boolean>>(askBIApiUrls.auth.enforce, {
      params: { resource, action },
    })
    if (res.data.code === 0) {
      return !!res.data.data
    }
    return false
  })
  return { data, loading }
}
