/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/naming-convention */
import { useRequest } from 'ahooks'
import axios from 'axios'
import { useAtomValue } from 'jotai'
import { useEffect, useMemo, useState } from 'react'
import { <PERSON><PERSON>, ChatProgress, ChatResponse } from 'src/shared/common-types'
import { PlanNode } from 'src/shared/chat-progress'
import { QueryParams } from 'src/shared/metric-types'
import { askBIApiUrls } from 'src/shared/url-map'
import { isSubmittingAtom } from './../pages/AskBI/askBIAtoms'

export interface nl2IntentMsg {
  thought: string
  steps: [PlanNode['tool'], PlanNode['param_key'], PlanNode['tool_params']][]
  result: PlanNode[]
}

export interface QueryMetricFailed {
  param_key: string
  metric_names: string[]
  query_metric: QueryParams
  row: null
  message: string
  metric_query_resp: ChatResponse
}

export type Nl2agentDeltaBuild = [PlanNode['tool'], PlanNode['param_key'], PlanNode['tool_params']]

function convertNl2IntentMsg({ data }: { data: ChatProgress[] }) {
  if (data.length === 0 || !data.at(-1)?.nl2intent_msg) return null
  try {
    const intentData = JSON.parse(JSON.parse(data.at(-1)!.nl2intent_msg)) as {
      intent: string
      askbi: boolean
      askdoc: boolean
    }
    return {
      ...intentData,
      askbi: intentData.askbi ?? true,
      askdoc: intentData.askdoc ?? true,
    }
  } catch (e) {
    return null
  }
}

function checkClose({ data }: { data: ChatProgress[] }) {
  return data.some((v) => v.close)
}

function convertNl2agentBuildMsg({ data }: { data: ChatProgress[] }): nl2IntentMsg | null {
  try {
    const contentStr = data.findLast((v) => v.nl2agent_build_msg)?.nl2agent_build_msg
    if (!contentStr) return null
    const content = JSON.parse(contentStr)
    if (content['meta'] && content['meta']['query_metric'])
      content['meta']['query_metric'] = JSON.parse(content['meta']['query_metric'])
    return content
  } catch (_) {
    return null
  }
}

function convertNl2agentDeltaBuilds({ data }: { data: ChatProgress[] }): Nl2agentDeltaBuild[] | null {
  try {
    const contentData = data.findLast((v) => v.nl2agent_delta_builds)?.nl2agent_delta_builds?.at(-1)
    if (!contentData || contentData[0] === 2) return null
    const content = JSON.parse(contentData[2])
    console.log('convertNl2agentDeltaBuilds', content)
    return content
  } catch (_) {
    return null
  }
}

export function formatThought(thought?: string) {
  return thought?.replace(/<调用工具>[\s\S]*?<\/调用工具>/g, '') ?? ''
}
export interface Nl2agentStep {
  type: number
  content: QueryMetricFailed | PlanNode
}

function convertNl2agentStepsList({ data }: { data: ChatProgress[] }): Nl2agentStep[] | null {
  try {
    const list = data.findLast((v) => v.nl2agent_steps_list)?.nl2agent_steps_list
    if (!list) return null
    return list.map(([type, contentStr]) => {
      const content = JSON.parse(contentStr)
      if (type === 2 && content['query_metric']) content['query_metric'] = JSON.parse(content['query_metric'])
      if (type === 1 && content['meta'] && content['meta']['query_metric'])
        content['meta']['query_metric'] = JSON.parse(content['meta']['query_metric'])
      return {
        type,
        content,
      }
    })
  } catch (_) {
    return null
  }
}
// TODO: 临时存储， 后期AGENT后就用不到
export const chatProgressCacheMap = new Map<
  string,
  { chatProgressList: ChatProgress[]; finished: boolean; thinkingData: string }
>()

export function getChatProgressFromCache(chat: Chat) {
  if (!chatProgressCacheMap.has(chat.id))
    chatProgressCacheMap.set(chat.id, { chatProgressList: [], finished: false, thinkingData: '' })
  return chatProgressCacheMap.get(chat.id)!
}

export function useChatProgress({ chat }: { chat: Chat }) {
  const isSubmitting = useAtomValue(isSubmittingAtom)
  const [chatProgressList, setChatProgressList] = useState<ChatProgress[]>(
    getChatProgressFromCache(chat).chatProgressList,
  )
  const { nl2AgentBuildMsg, nl2AgentStepsList, nl2IntentMsg, close, nl2agentDeltaBuilds } = useMemo(
    () => ({
      nl2AgentBuildMsg: convertNl2agentBuildMsg({ data: chatProgressList }),
      close: checkClose({ data: chatProgressList }),
      nl2AgentStepsList: convertNl2agentStepsList({ data: chatProgressList }),
      nl2IntentMsg: convertNl2IntentMsg({ data: chatProgressList }),
      nl2agentDeltaBuilds: convertNl2agentDeltaBuilds({ data: chatProgressList }),
    }),
    [chatProgressList],
  )

  useEffect(() => {
    if (chatProgressList.length) {
      console.group('chatProgressList: ')
      console.log('nl2AgentBuildMsg: ', nl2AgentBuildMsg)
      console.log('close: ', close)
      console.log('nl2AgentStepsList: ', nl2AgentStepsList)
      console.log('nl2IntentMsg: ', nl2IntentMsg)
      console.log('nl2agentDeltaBuilds: ', nl2agentDeltaBuilds)
      console.groupEnd()
      // cache
      const cache = getChatProgressFromCache(chat)
      cache.chatProgressList = chatProgressList
      cache.thinkingData = formatThought(nl2AgentBuildMsg?.thought)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chatProgressList])

  async function fetchChatProgress() {
    if (!chat.taskId) return []
    const res = await axios.get(askBIApiUrls.chatProgress, {
      params: {
        taskId: chat.taskId,
      },
    })
    if (res.data.code !== 0) throw new Error('ChatProgress: fetch fail', { cause: res })
    if (!Array.isArray(res.data.data)) throw new Error('ChatProgress: Unknown data type', { cause: res })
    const data: ChatProgress[] = res.data.data
    if (data.length) {
      setChatProgressList((prev) => [...prev, ...data])
    }
    return data
  }

  const [fetchingChatProgress, setFetchingChatProgress] = useState(false)

  const { cancel: cancelFetchChatProgress, run: runFetchChatProgress } = useRequest(
    async () => {
      try {
        const data = await fetchChatProgress()
        if (checkClose({ data }) || !isSubmitting) {
          getChatProgressFromCache(chat).finished = true
          setFetchingChatProgress(false)
          cancelFetchChatProgress()
        }
      } catch (er) {
        console.error(er)
      }
    },
    {
      pollingInterval: 1000,
      manual: true,
    },
  )

  // useEffect(() => {
  //   console.log('RUN', finished)
  //   if (!finished) {
  //     run()
  //   }
  // }, [finished, run])

  return useMemo(
    () => ({
      fetchChatProgress,
      checkClose,
      convertNl2IntentMsg,
      chatProgressList,
      convertNl2agentBuildMsg,
      convertNl2agentStepsList,
      nl2AgentBuildMsg,
      runFetchChatProgress,
      close,
      cancelFetchChatProgress,
      nl2AgentStepsList,
      nl2IntentMsg,
      formatThought,
      fetchingChatProgress,
      nl2agentDeltaBuilds,
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [chatProgressList],
  )
}
