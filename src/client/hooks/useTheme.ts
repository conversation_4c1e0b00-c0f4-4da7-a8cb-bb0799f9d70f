import { useEffect, useState } from 'react'

function hasClassWithPrefix(classList: string[], prefix: string) {
  return classList.some((className) => className.startsWith(prefix))
}

export function useTheme() {
  const [theme, setTheme] = useState({})
  // todo 临时方案,后续可以改成更通用的方式
  useEffect(() => {
    const htmlEl = document.querySelector('html')
    const hasBaowuPrefix = hasClassWithPrefix([...(htmlEl?.classList || [])], 'baowu')
    if (hasBaowuPrefix) {
      // 宝武主题
      setTheme({
        token: {
          colorPrimary: '#2c6add',
          colorPrimaryActive: '#2c6add',
          colorPrimaryHover: '#2c6add',
          borderRadius: 4,
          colorLink: '#2c6add',
        },
      })
    } else {
      // 数巅主题
      setTheme({
        token: {
          colorPrimary: '#6A58EC',
          colorPrimaryActive: '#503CE4',
          colorPrimaryHover: '#503CE4',
          borderRadius: 4,
          colorLink: '#5542E1',
        },
      })
    }
  }, [])
  return [theme]
}
