/**
 * Layout 的顶部导航条 Header
 */
import React, { useCallback, useEffect, useState } from 'react'
import clsx from 'clsx'
import { Dropdown, MenuProps, message } from 'antd'
import { useLocation, useNavigate } from 'react-router-dom'
import { useAtom, useAtomValue } from 'jotai'
import axios from 'axios'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { useRequest } from 'ahooks'
import { abortControllerManager, clearAllCookies } from '@client/utils'
import { BASE_URL, chatPath, CHROME_ACTIONS, IS_CHROME_EXTENSION, IS_H5, XEngineHomePath } from '@shared/constants'
import { ASK_BI_BASE, askBIApiUrls, askBIPageUrls } from 'src/shared/url-map'
import ThemeToggle from './components/ThemeToggle'
import { brandInfoAtom, currentLoginUserAtom, envAtom, loginAppIdAtom, queryState<PERSON><PERSON> } from './pages/AskBI/askBIAtoms'
import { dipeakMobileIcon, SvgIcon } from './components/SvgIcon'
import { useAuth } from './hooks/useAuth'

export default function LayoutHeader({ showMenu = true }: { isFullWidth?: boolean; showMenu?: boolean }) {
  const [currentLoginUserInfo, setCurrentLoginUserInfo] = useAtom(currentLoginUserAtom)
  const loginAppId = useAtomValue(loginAppIdAtom)
  const brandInfo = useAtomValue(brandInfoAtom)
  const location = useLocation()
  const navigate = useNavigate()
  const queryState = useAtomValue(queryStateAtom)
  const [env] = useAtom(envAtom)
  // const accessPaths = useAtomValue(accessPathsAtom)
  const [isShowDashboard, setIsShowDashboard] = useState<boolean>(false)

  const { data: authData } = useAuth({ action: 'read', resource: `page:/xengine/dashboard/info` })

  useEffect(() => {
    setIsShowDashboard(!!authData)
  }, [authData])

  // 获取用户的信息
  useRequest(
    async () => {
      const data = await axios.get(askBIApiUrls.auth.userInfo)
      setCurrentLoginUserInfo(data.data.data)
    },
    {
      onError: (error: any) => {
        console.error('get currentUserBasicInfo =', error)
      },
    },
  )

  // 退出登录接口
  const handleLogout = async () => {
    try {
      // 在退出登陆前终止所有可追溯的请求，防止阻塞下面的接口
      abortControllerManager.forEach((ac) => ac.abort())
      await axios.post(askBIApiUrls.auth.logout)
      localStorage.clear()
      clearAllCookies()
      window.location.href = loginAppId ? `${askBIPageUrls.login}?appid=${loginAppId}` : askBIPageUrls.login
    } catch (error: any) {
      console.error(error)
      message.error(`退出登录失败：${error?.message}`)
    }
  }

  const userDropdownItems: MenuProps['items'] = [
    {
      key: 'username',
      label: (
        <div className="flex items-center px-2 py-1 font-bold dark:bg-slate-600/30">
          {currentLoginUserInfo?.username}
        </div>
      ),
    },
    {
      key: 'logout',
      label: (
        <div
          className="flex cursor-pointer items-center px-2 py-1 hover:bg-slate-50 dark:bg-slate-600/30"
          onClick={() => {
            handleLogout()
          }}
        >
          退出登录
        </div>
      ),
    },
  ]

  // chrome 插件中，点击关闭按钮，发送消息给 background，由 background 关闭插件
  const handleCloseClick = useCallback(() => {
    if (IS_CHROME_EXTENSION) {
      const parentWindow = window.parent as Window
      parentWindow.postMessage({ action: CHROME_ACTIONS.closeDisplay }, '*')
    }
  }, [])

  const navList =
    env?.VITE_PRODUCTS?.trim() === 'X-Engine'
      ? []
      : [
          {
            navTitle: '首页',
            parentPath: '/chat/',
            navPath: queryState.enableOnlyChat ? askBIPageUrls.chatNew : askBIPageUrls.home,
            show: true,
          },
          {
            navTitle: '实体',
            parentPath: '/metric-store/',
            navPath: askBIPageUrls.metricStore.document.list,
            show: true,
          },
          {
            navTitle: '管理',
            parentPath: '/manage/',
            navPath: askBIPageUrls.manage.manageProject.list,
            show: true,
          },
          // {
          //   navTitle: '看板',
          //   parentPath: '/dashboard/',
          //   navPath: askBIPageUrls.dashboard.info,
          //   show: isShowDashboard,
          // },
        ]
  const renderNavAppChange = () => {
    return (
      <div className="app-nav flex gap-x-1.5 md:gap-x-4">
        {navList
          .filter((item) => item.show) // 过滤掉不需要显示的项
          .map((item, index) => (
            <div
              key={index}
              className={`nav-item relative flex h-[44px] w-[92px] cursor-pointer items-center justify-center leading-6 ${
                location.pathname.startsWith(BASE_URL + item.parentPath)
                  ? 'rounded border border-solid border-[#2D3E63] bg-[#071126]'
                  : ''
              }`}
              onClick={(e) => {
                e.preventDefault()
                navigate(item.navPath)
              }}
            >
              <div className="text-white">{item.navTitle}</div>
            </div>
          ))}
      </div>
    )
  }

  if (brandInfo?.header === false) {
    return null
  }

  return (
    <header
      className={clsx('sticky top-0 transition-all duration-200 ease-in-out', {
        'hidden md:block': location.pathname !== askBIPageUrls.home,
      })}
    >
      <div className="absolute -z-10 h-full w-full backdrop-blur-sm backdrop:saturate-200" />
      <nav
        className={clsx('flex h-[62px] items-center justify-between bg-white px-5 md:bg-[#20194D]')}
        aria-label="Nav"
      >
        <div className="hidden md:flex">
          {brandInfo && (
            <img
              className={clsx('h-4 w-auto cursor-pointer', {
                'h-7': brandInfo && brandInfo.brandName === 'China Telecom',
                invisible: typeof brandInfo?.header === 'object' && brandInfo?.header?.logo === false,
              })}
              src={brandInfo.logo}
              alt=""
              onClick={() => {
                navigate(`${ASK_BI_BASE}${env?.VITE_PRODUCTS?.trim() === 'X-Engine' ? XEngineHomePath : chatPath}`)
              }}
            />
          )}
        </div>
        <div className="md:hidden">
          <SvgIcon icon={dipeakMobileIcon} className="w-28" />
        </div>

        {!IS_H5 &&
          showMenu &&
          !(typeof brandInfo?.header === 'object' && brandInfo?.header?.tab === false) &&
          renderNavAppChange()}
        <div className="flex justify-end gap-4">
          <div>
            {typeof brandInfo?.header === 'object' && brandInfo?.header?.themeToggle !== false && <ThemeToggle />}
          </div>
          <Dropdown menu={{ items: userDropdownItems }} placement="bottom">
            <a
              onClick={(e) => e.preventDefault()}
              className={clsx('cursor-pointer', {
                invisible: typeof brandInfo?.header === 'object' && brandInfo?.header?.userInfo === false,
              })}
            >
              <img className="h-6 w-6 cursor-pointer" src={brandInfo?.chatUserIcon} alt="" />
            </a>
          </Dropdown>
          {IS_CHROME_EXTENSION && (
            <XMarkIcon
              className="h-6 w-6 cursor-pointer text-slate-400 hover:text-primary"
              onClick={handleCloseClick}
            />
          )}
        </div>
      </nav>
    </header>
  )
}
