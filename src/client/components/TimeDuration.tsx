import React, { useState } from 'react'
import { InputNumber, Space, TimePicker } from 'antd'

type ValueType = {
  hours: number
  minutes: number
  seconds: number
}

export default function TimeDuration(props: {
  onChange: (value: number) => void
  value: ValueType
  className: string
}) {
  const [currentValue, setCurrentValue] = useState<Partial<ValueType>>()
  const triggerChange = (value: Partial<ValueType>) => {
    const newValue = {
      ...(currentValue || {}),
      ...value,
    }
    const s = (newValue.hours ?? 0) * 60 * 60 + (newValue.minutes ?? 0) * 60 + (newValue.seconds ?? 0)
    props.onChange(s * 1000)
    setCurrentValue(newValue)
  }

  return (
    <Space className={`${props.className}`}>
      <InputNumber
        changeOnWheel
        min={0}
        onChange={(hours) => {
          triggerChange({
            hours: hours ?? 0,
          })
        }}
      />
      <span>小时</span>
      <TimePicker
        format="HH:mm"
        showNow={false}
        placeholder="请选择分秒"
        onChange={(_, formatTime) => {
          const [mm, ss] = ((formatTime || '') as string).split(':')
          triggerChange({
            minutes: +mm,
            seconds: +ss,
          })
        }}
      />
      <span>分:秒</span>
    </Space>
  )
}
