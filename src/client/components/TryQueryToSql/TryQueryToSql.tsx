import React, { useEffect } from 'react'
import { useAtomValue } from 'jotai/react'
import { InfoCircleOutlined, SyncOutlined } from '@ant-design/icons'
import { useRequest } from 'ahooks'
import {
  conversationIdAtom,
  llmTypeAtom,
  metricConfigAtom,
  metricConfigOfProjectAtom,
} from 'src/client/pages/AskBI/askBIAtoms'
import { MULTI_SCENE_CHAT_MOCK_SCENE_ID } from 'src/shared/constants'
import { AssistantChatError, ChatResponseErrorTypes } from 'src/shared/common-types'
import { tryQueryToSql } from 'src/client/pages/AskBI/Chat/utils'
import { detectTimeType, getTimeQuery } from 'src/shared/common-utils'
import { scrollToBottom } from 'src/client/utils'
import { isBaoWu } from 'src/shared/baowu-share-utils'
import { renderRangePicker } from '../QueryParamsSelector'
import { BiResultContent } from '../ChatHistoryItem'
import { DeepSeekActiveButton } from '../deepseek'

export function TryQueryToSql(props: {
  biResultContentProps: Parameters<typeof BiResultContent>[0]
  noDataRender: () => React.JSX.Element
  noDataTitle?: string
  content: AssistantChatError
}) {
  const { biResultContentProps, noDataRender, noDataTitle, content } = props

  const defaultMetricConfig = useAtomValue(metricConfigAtom)
  const metricConfigResponseForProject = useAtomValue(metricConfigOfProjectAtom)
  const metricConfig =
    biResultContentProps.currentSelectedSceneId === MULTI_SCENE_CHAT_MOCK_SCENE_ID ||
    metricConfigResponseForProject === null
      ? defaultMetricConfig
      : metricConfigResponseForProject?.find((e) => e.sceneId === biResultContentProps.currentSelectedSceneId)?.data ||
        null

  const llmType = useAtomValue(llmTypeAtom)
  const conversationId = useAtomValue(conversationIdAtom)
  const chatItem = biResultContentProps.chatAnsItem.content[0]
  const queryParamsVerified =
    chatItem.type === 'chat-error' &&
    (chatItem.errType === ChatResponseErrorTypes.FUTURE_DATA_NOT_EXIST ||
      chatItem.errType === ChatResponseErrorTypes.LATEST_DATA_NOT_EXIST) &&
    chatItem.queryParamsVerified
      ? chatItem.queryParamsVerified
      : null
  const timeType = detectTimeType(queryParamsVerified?.queryParams)
  const disabledTryQuery =
    !queryParamsVerified || (isBaoWu(metricConfig?.metricTableName) && timeType !== 'month' && timeType !== 'day')
  const { data, loading } = useRequest(
    () => {
      if (disabledTryQuery) return Promise.resolve(null)
      return tryQueryToSql({
        // 暂时不考虑多场景
        queryParamsVerified,
        maxResultCount: 1,
        llmType,
        conversationId,
        chatId: biResultContentProps.currentChat.id,
        sceneId: biResultContentProps.currentSelectedSceneId,
        metricConfig,
        infoTexts: (chatItem.type === 'chat-error' && chatItem.infoTexts) || [],
      })
    },
    {
      onSuccess() {
        setTimeout(scrollToBottom, 100)
      },
    },
  )
  const queriedTime = getTimeQuery(queryParamsVerified?.queryParams?.timeQueryParams)

  const list = data?.data ?? []

  useEffect(() => {
    content.tryQueryToSqlData.setAnsChatItemList(data?.data ?? []).setQueryParamsVerified(data?.queryParamsVerified)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.data])

  const msgMap: Record<NonNullable<ReturnType<typeof detectTimeType>>, string> = {
    day: `我尝试查询了前${data?.maxCount}天的数据，都未找到数据记录。`,
    month: `我尝试查询了前${data?.maxCount}个月的数据，都未找到数据记录。`,
    quarter: `我尝试查询了前${data?.maxCount}个季度的数据，都未找到数据记录。`,
    year: `我尝试查询了前${data?.maxCount}年的数据，都未找到数据记录。`,
    'half-year': `我尝试往前查询了半年的数据，都未找到数据记录。`,
  }

  if (disabledTryQuery)
    return (
      <div className="disabled-try-query-info">
        {noDataTitle ? <h4 className="text-center text-lg font-bold">{noDataTitle}</h4> : null}
        {noDataRender()}
      </div>
    )

  const currentTimeDesc = `未找到 ${queriedTime} 的数据，`

  return (
    <>
      <div className="rounded-[8px] bg-[#DBF6F1] p-4">
        <div className="flex w-full items-center gap-1">
          <div>
            {loading ? (
              <SyncOutlined spin className="text-4 flex flex-none text-gray-400 dark:text-gray-500" />
            ) : (
              <InfoCircleOutlined className="h-4 w-4" />
            )}
          </div>
          <div className="flex flex-col">
            {loading ? (
              <>{currentTimeDesc}我正在尝试帮您查询之前的数据...</>
            ) : list.length > 0 ? (
              <>
                <span>{currentTimeDesc}我尝试找到了最新有数据的时间段：</span>
                {list
                  .map((item, i) => {
                    const queryMetricContent = item.content.find((item) => item.type === 'chart')
                    if (!queryMetricContent || !queryMetricContent.queryParamsVerified) return null
                    return (
                      <span key={i}>
                        {renderRangePicker(queryMetricContent.queryParamsVerified.queryParams, false)}
                      </span>
                    )
                  })
                  .filter(Boolean)}
              </>
            ) : data?.maxCount && data.type ? (
              <span>
                {currentTimeDesc}
                {msgMap[data.type] ?? `我尝试查询了数据，都未找到数据记录。`}
              </span>
            ) : null}
          </div>
        </div>
        {chatItem.type === 'chat-error' &&
          chatItem.infoTexts &&
          chatItem.infoTexts.map((i) => (
            <div key={i} className="flex items-center gap-1">
              <InfoCircleOutlined className="h-4 w-4" />
              <span>{i}</span>
            </div>
          ))}
      </div>
      {data?.data?.length === 0 ? (
        <>
          {noDataRender?.()}
          <DeepSeekActiveButton
            icon={null}
            chatId={props.biResultContentProps.currentChat.id}
            textProps={{ children: '用deepseek回答', className: 'font-[600]' }}
            containerProps={{ className: 'w-full justify-center mt-[20px]' }}
          />
        </>
      ) : (
        <>
          {data?.data.map((item, i) => {
            return (
              <BiResultContent
                {...biResultContentProps}
                className="px-0 pb-1 pt-2"
                chatAnsItem={item}
                key={i}
                isViewMode
              />
            )
          })}
          {data?.data && (
            <DeepSeekActiveButton
              icon={null}
              textProps={{ children: '深度分析', className: 'font-[600]' }}
              containerProps={{ className: 'w-full mt-[20px] justify-end' }}
              chatId={props.biResultContentProps.currentChat.id}
              updateDeepSeekData={(v) => v.setTrigger('deep-think')}
            />
          )}
        </>
      )}
    </>
  )
}
