/**
 * 后台页面的侧边栏
 */
import React, { useCallback, useEffect, useState } from 'react'
import clsx from 'clsx'
import { useLocation, useNavigate } from 'react-router-dom'
import { ChevronDoubleLeftIcon } from '@heroicons/react/24/outline'
import { Menu, MenuProps } from 'antd'
import { useAtomValue } from 'jotai'
import { ItemType, MenuItemType } from 'antd/es/menu/interface'
import { MenuItem } from 'src/shared/common-types'
import { themeAtom } from '../pages/AskBI/askBIAtoms'
import { findMenuItem, traversePathToGetKeys } from '../utils'
import './AdminSidebar.css'
// 定义 ItemType

export default function Sidebar(props: { menuItems: MenuItem[]; className?: string }) {
  const { menuItems, className } = props
  const location = useLocation()
  const [isCollapsed, setIsCollapsed] = useState(window.innerWidth < 1024 ? true : false)
  const [openKeys, setOpenKeys] = useState<string[]>([])
  const [selectedKeys, setSelectedKeys] = useState<string[]>([])
  const theme = useAtomValue(themeAtom)
  const navigate = useNavigate()

  const handleCollapse = useCallback(() => {
    setIsCollapsed(!isCollapsed)
  }, [isCollapsed])

  // 监听外部窗口宽度的变化，如果小于 1024px，自动收起侧边栏
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 1024) {
        setIsCollapsed(true)
      } else {
        setIsCollapsed(false)
      }
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  useEffect(() => {
    /*
      取得地址栏的path进行 / 分割，然后递归匹配menuItems的key，若匹配到则push进pathKeys。
      最后匹配到的key为高亮tab，前面的为展开tab
      【注意】，所以得注意path和menuItems的key的匹配
    */
    const pathKeys = traversePathToGetKeys(location.pathname, menuItems)

    // 如果找到了对应的 key，则将其设置为选中项和展开项
    setSelectedKeys(pathKeys.slice(-1))
    setOpenKeys(pathKeys.slice(0, -1))
  }, [location.pathname, menuItems])

  // 菜单的点击事件
  const handleMenuClick: MenuProps['onClick'] = (e) => {
    const menuItem = findMenuItem(menuItems, e.key)
    if (menuItem && menuItem.path) {
      navigate(menuItem.path)
    }
  }

  return (
    <div
      className={clsx(
        'admin-sidebar-menu duration-400 group relative hidden flex-col gap-1 bg-[#F8F8FC] pb-16 transition-all dark:border-slate-700 dark:bg-slate-800 md:flex',
        {
          'w-[208px]': !isCollapsed,
          'w-[50px] text-center': isCollapsed,
        },
        className,
      )}
    >
      <Menu
        theme={theme}
        onClick={handleMenuClick}
        mode="inline"
        selectedKeys={selectedKeys}
        // selectedKeys={['job-list', 'material-view-list']}
        inlineCollapsed={isCollapsed}
        items={
          menuItems.filter((item) => !isCollapsed || (isCollapsed && item.type !== 'group')) as ItemType<MenuItemType>[]
        } // 过滤掉 'group' 类型的菜单项
        openKeys={openKeys}
        onOpenChange={(keys) => setOpenKeys(keys)}
        className={clsx('overflow-y-auto dark:bg-slate-800', {
          'w-[50px] text-center': isCollapsed,
        })}
      />

      <div
        className={clsx(
          'absolute bottom-0 flex h-14 cursor-pointer select-none items-center gap-2 bg-[#F8F8FC] dark:bg-gray-800',
          {
            'w-[200px] px-4': !isCollapsed,
            'w-[50px] justify-center px-2': isCollapsed,
          },
        )}
        onClick={handleCollapse}
      >
        <ChevronDoubleLeftIcon
          className={clsx('h-5 w-5 text-slate-400 transition-all', {
            'rotate-180 transform': isCollapsed,
          })}
        />
        {!isCollapsed && <div className="whitespace-nowrap">收起侧边栏</div>}
      </div>
    </div>
  )
}
