import React, { ReactNode, useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react'
import clsx from 'clsx'
import { useAtomValue } from 'jotai/react'
import { isShowMatchedParamDrawerAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { IS_H5 } from 'src/shared/constants'
import { isAndroid, isIOS, scrollToBottom } from 'src/client/utils'
import { usePageHeight } from 'src/client/hooks/usePageHeightHook'

type Props = React.AllHTMLAttributes<HTMLElement> & {
  children: ReactNode
  isMiniMode: boolean
}

const times = [0, 10, 20, 30, 40, 50, 100, 150, 200, 250, 300]

export default function CustomDrawerLayout(props: Props) {
  const { children, isMiniMode } = props
  const isShowMatchedParamDrawer = useAtomValue(isShowMatchedParamDrawer<PERSON>tom)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  // 监听滚动容器的滚动事件，防止在滚动到顶部或者底部的时候会触发外部的滚动条
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current!
    let startY = 0
    function onTouchStart(event: TouchEvent) {
      startY = event.touches[0].pageY
    }
    function onTouchMove(event: TouchEvent) {
      const currentY = event.touches[0].pageY
      const distanceY = currentY - startY
      const { scrollTop, clientHeight, scrollHeight } = scrollContainer
      if ((scrollTop === 0 && distanceY > 0) || (scrollTop + clientHeight >= scrollHeight && distanceY < 0)) {
        event.preventDefault()
      }
    }
    if (isIOS) {
      scrollContainer?.addEventListener('touchstart', onTouchStart)
      scrollContainer?.addEventListener('touchmove', onTouchMove)
    }

    return () => {
      if (isIOS) {
        scrollContainer?.removeEventListener('touchstart', onTouchStart)
        scrollContainer?.removeEventListener('touchmove', onTouchMove)
      }
    }
  }, [])
  const drawerRef = useRef<HTMLDivElement>(null)
  const [visible, setVisible] = useState(false)
  const [width, setWidth] = useState<string | number>('100')
  const [bottom, setBottom] = useState(0)
  const [left, setLeft] = useState(0)
  const [innerHeight, setInnerHeight] = useState(window.innerHeight)

  const [pageHeight] = usePageHeight()

  const height = useMemo(() => {
    if (typeof pageHeight === 'number') {
      return pageHeight + 'px'
    } else {
      return pageHeight
    }
  }, [pageHeight])

  const setBounding = useCallback(() => {
    const bottomElement = document.getElementById('chat-toolbar')
    const messageElement = document.getElementById('message-input')

    setInnerHeight(window.innerHeight)

    if (bottomElement) {
      const rect = bottomElement.getBoundingClientRect()

      const newBottom = bottomElement.offsetHeight
      setBottom(newBottom)

      if (messageElement) {
        const messageRect = messageElement.getBoundingClientRect()
        setWidth(messageRect.width)
        if (!IS_H5) {
          setLeft(isMiniMode ? messageRect.left : messageRect.left - rect.left)
        }
      }
      setVisible(true)
    }
  }, [setBottom, setWidth, setVisible, isMiniMode])

  useLayoutEffect(() => {
    setBounding()
  }, [setBounding])

  useLayoutEffect(() => {
    if (isAndroid) {
      // 安卓不需要处理
      return
    }

    function scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'instant',
      })
    }
    if (isShowMatchedParamDrawer) {
      for (const time of times) {
        setTimeout(() => {
          scrollToTop()
        }, time)
      }
    } else {
      const activeElement = document.activeElement as HTMLDivElement
      activeElement?.blur()
      setTimeout(() => {
        // 加定时器才能正常滚动到最下面
        scrollToBottom('instant')
      }, 50)
    }
  }, [isShowMatchedParamDrawer])

  useLayoutEffect(() => {
    let targetElement = document.getElementById('chat-toolbar')

    const observer = new MutationObserver(setBounding)
    if (targetElement) {
      observer.observe(targetElement, {
        attributes: true,
        childList: true,
        subtree: true,
      })
    }

    function iosSetBounding() {
      // ios端需要等键盘弹出后再重置宽高, 执行times.length次, 防止因卡顿导致没有重置成功
      for (const time of times) {
        setTimeout(() => {
          setBounding()
        }, time)
      }
    }

    if (isIOS) {
      window.addEventListener('focusin', iosSetBounding)
      window.addEventListener('focusout', iosSetBounding)
    } else {
      window.addEventListener('resize', setBounding)
    }

    return () => {
      targetElement = null
      observer.disconnect()
      if (isIOS) {
        window.removeEventListener('focusin', iosSetBounding)
        window.removeEventListener('focusout', iosSetBounding)
      } else {
        window.removeEventListener('resize', setBounding)
      }
    }
  }, [setBounding])

  if (!IS_H5 || !isShowMatchedParamDrawer) {
    return null
  }

  return (
    <div
      className={clsx(
        'fixed overscroll-contain bg-white text-slate-800 md:rounded-md md:border md:p-[32px]',
        visible ? 'visible' : 'invisible',
      )}
      ref={drawerRef}
      style={{
        top: IS_H5 ? bottom : bottom - 15,
        width: IS_H5 ? '100%' : width,
        left: left,
        maxHeight: IS_H5 ? `calc(min(${height}, ${innerHeight}px) - ${bottom}px)` : 400,
        height: IS_H5 ? `calc(min(${height}, ${innerHeight}px) - ${bottom}px)` : 400,
        zIndex: 10,
      }}
    >
      {/* 防止码值随着页面滚动 */}
      <div className={clsx('overflow-auto px-1', 'h-full pt-3')} ref={scrollContainerRef}>
        {children}
      </div>
    </div>
  )
}
