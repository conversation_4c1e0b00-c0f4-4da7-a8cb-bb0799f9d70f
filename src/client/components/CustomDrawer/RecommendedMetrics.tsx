import { use<PERSON><PERSON>, useSet<PERSON>tom } from 'jotai/react'
import React, { useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import clsx from 'clsx'
import { recommendedSafeAreaClass } from 'src/shared/constants'
import {
  agentStructuredMessageAtom,
  isShowRecommendedMetricsAtom,
  makeAgentRequestAtom,
  recommendedMetricsAtom,
  updateMessageAndHtmlAtom,
} from 'src/client/pages/AskBI/askBIAtoms'
import { JsonContentItem } from 'src/shared/common-types'
import { useSafeClick } from 'src/client/hooks/useSafeClick'
import { convertToHtml } from '../MessageInputEditable/MessageInputEditable'
import { recommendedEditIcon, SvgIcon } from '../SvgIcon'

const typeMap = {
  digit: '问数',
  trend: '趋势',
  topN: 'topN',
}

export default function RecommendedMetrics(props: { isMiniMode: boolean; className?: string }) {
  const [datasource] = useAtom(recommendedMetricsAtom)
  const [isShowRecommendedMetrics, setIsShowRecommendedMetrics] = useAtom(isShowRecommendedMetricsAtom)
  const setStructuredMessage = useSetAtom(agentStructuredMessageAtom)
  const updateMessageAndHtml = useSetAtom(updateMessageAndHtmlAtom)
  const makeAgentRequest = useSetAtom(makeAgentRequestAtom)
  const navigate = useNavigate()

  // 安全点击区域
  useSafeClick(recommendedSafeAreaClass, () => setIsShowRecommendedMetrics(false))

  const onEdit = (data: JsonContentItem[]) => {
    updateMessageAndHtml(data)
  }

  const handleSubmit = useCallback(
    (newMessage?: string) => {
      makeAgentRequest({
        navigate,
        isMiniMode: props.isMiniMode,
        newMessage: newMessage,
      })
    },
    [makeAgentRequest, navigate, props.isMiniMode],
  )

  const onSubmit = (jsonContent: JsonContentItem[]) => {
    setStructuredMessage(jsonContent)
    const newMessage = jsonContent.map((item) => item['data-content']).join('')
    handleSubmit(newMessage)
  }

  const getType = (type: keyof typeof typeMap) => {
    return typeMap[type]
  }
  if (!isShowRecommendedMetrics) {
    return null
  }
  return (
    <div
      className={clsx(
        'absolute bottom-0 left-0 right-0 m-1 flex flex-col gap-2 bg-white',
        recommendedSafeAreaClass,
        props.className,
      )}
    >
      {datasource.map((item) => {
        return (
          <div
            key={item.id}
            onClick={() => onSubmit(item.jsonContent)}
            className="flex w-full flex-row flex-wrap items-center justify-between gap-1 overflow-auto rounded-md border py-2 pl-3 pr-4 text-[#171717] shadow-sm"
          >
            <div
              className="w-2/3 overflow-hidden text-ellipsis whitespace-nowrap"
              dangerouslySetInnerHTML={{ __html: convertToHtml(item.jsonContent) }}
            />
            <div className="flex flex-row items-center">
              <div className="mr-4 inline-block rounded-md bg-[#F5F5F5] p-2">{getType(item.type)}</div>
              <span
                onClick={(e) => {
                  e.stopPropagation()
                  onEdit(item.jsonContent)
                }}
              >
                <SvgIcon icon={recommendedEditIcon} className="h-6 w-6 cursor-pointer" />
              </span>
            </div>
          </div>
        )
      })}
    </div>
  )
}
