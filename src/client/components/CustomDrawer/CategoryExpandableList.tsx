import clsx from 'clsx'
import React, { useRef, useState } from 'react'
import { Dimension, Metric } from 'src/shared/metric-types'
import { JsonContentItem, STRUCTURED_MESSAGE_DATA_TYPE } from 'src/shared/common-types'
import { downOutlined, metricKeyPointIcon, SvgIcon, upOutlined } from '../SvgIcon'
import { OverviewItem } from './OverviewItem'

const foldLen = 7
const unfoldLen = 85

export default function CategoryExpandableList<T extends Metric | Dimension>({
  title,
  showTitleIcon = true,
  type,
  safeAreaClass,
  datasource = [],
  selectedList = [],
  rightNode,
  searchValue,
  isMetric = true,
  minLen = foldLen,
  maxLen = unfoldLen,
}: {
  title?: string
  type: STRUCTURED_MESSAGE_DATA_TYPE
  safeAreaClass?: string
  datasource: T[]
  selectedList: JsonContentItem[]
  rightNode?: React.ReactNode | JSX.Element
  searchPlaceholder?: string
  searchValue?: string
  isMetric: boolean
  showTitleIcon?: boolean
  minLen?: number
  maxLen?: number
}) {
  const listRef = useRef<HTMLDivElement>(null)
  const [isFolded, setIsFolded] = useState(true)

  const onFoldBtnClick = () => {
    setIsFolded(!isFolded)
  }

  return (
    <div className="ExpandableList mb-4 flex flex-col gap-2">
      {title ? (
        <div className="flex flex-row items-center justify-between px-3 md:px-0">
          <div className="flex flex-row items-center">
            <h3 className="flex items-center text-[#575757]">
              {showTitleIcon && <SvgIcon icon={metricKeyPointIcon} className="mr-1 size-4" />} {title}
            </h3>
          </div>
          <div>{rightNode ? rightNode : null}</div>
        </div>
      ) : null}
      <div className={clsx('flex flex-row flex-wrap gap-2')} ref={listRef}>
        {datasource.length > 0 ? (
          <>
            {datasource.map((data: T, index: number) => {
              if ((isFolded && index >= minLen) || index >= maxLen) {
                return null
              }
              return (
                <OverviewItem<T>
                  key={data.name}
                  data={data}
                  type={type}
                  safeAreaClass={safeAreaClass}
                  isMetric={isMetric}
                  selectedList={selectedList}
                  searchValue={searchValue}
                />
              )
            })}
            {datasource.length > minLen ? (
              <OverviewItem<T>
                key={isFolded ? 'folded' : 'unfolded'}
                data={{
                  type: 'fold',
                  label: (
                    <div className="px-3">
                      {isFolded ? (
                        <SvgIcon className="size-5" icon={downOutlined} />
                      ) : (
                        <SvgIcon className="size-5" icon={upOutlined} />
                      )}
                    </div>
                  ),
                }}
                type={type}
                safeAreaClass={safeAreaClass}
                isMetric={isMetric}
                selectedList={selectedList}
                onClick={onFoldBtnClick}
              />
            ) : null}
          </>
        ) : null}
      </div>
    </div>
  )
}
