import React, { useState, useMemo, useRef } from 'react'
import { Input, Table, type TableProps } from 'antd'
import { useAntdTable } from 'ahooks'
import clsx from 'clsx'
type TableRequestType = {
  [key: string]: any
  current: number
  pageSize: number
  sorter?: any
  filter?: any
  extra?: any
}
export type ActionType = { reload: (args: ApiArgsType) => void; refresh: () => void } | undefined
export type ApiArgsType = { search: string } & TableRequestType
type PropsType<T = object> = {
  api: (args: ApiArgsType) => Promise<{ list: T[]; total: number }>
  isFullList: boolean
  searchKey?: string | string[]
  placeholder?: string
  tableProps: TableProps<T>
  className?: string
  extra?: React.ReactNode
  onSearch?: (search: string) => void
  onError?: () => void
  onSuccess?: () => void
  actionRef?: React.MutableRefObject<ActionType>
}
export default function CountSearchTable<T>(props: PropsType<T>) {
  const { api, searchKey, placeholder, tableProps, extra, isFullList } = props
  const [showData, setShowData] = useState<T[]>([])
  const searchRef = useRef('')

  const {
    tableProps: responseTableProps,
    run,
    refresh,
  } = useAntdTable(
    (args) => {
      return api({ ...args, search: searchRef.current })
    },
    {
      refreshDeps: [api],
      onError() {
        props.onError && props.onError()
      },
      onSuccess(data: { list: T[]; total: number }) {
        if (props.actionRef) {
          props.actionRef.current = {
            reload: run,
            refresh: refresh,
          }
        }
        setShowData(data.list || [])
        props.onSuccess && props.onSuccess()
      },
    },
  )

  function onSearch(value: string) {
    searchRef.current = value
    props.onSearch && props.onSearch(value)
    if (isFullList) {
      const list = responseTableProps?.dataSource || []
      const filterProjects = list?.filter((item) => {
        if (typeof item === 'string') {
          return item.includes(value)
        }
        const searchKeys = Array.isArray(searchKey) ? searchKey : [searchKey || '']
        return searchKeys.some((key: string) => (item?.[key as keyof typeof item]?.toString() || '').includes(value))
      })
      setShowData(filterProjects)
    } else {
      run({ current: 1, pageSize: 10, search: value })
    }
  }
  const resolvedTableProps = useMemo(() => {
    if (isFullList) {
      return {
        dataSource: showData,
        loading: responseTableProps.loading,
        pagination: { total: showData.length },
      }
    }
    return responseTableProps
  }, [responseTableProps, showData, isFullList])
  return (
    <div className={clsx(props.className)}>
      <div className="count-search-table-header flex items-center justify-between pb-3 pt-2">
        <div>{`共${resolvedTableProps?.pagination?.total || 0}个`}</div>
        <div className="flex items-center">
          <div className="mr-1 w-[280px]">
            <Input.Search placeholder={placeholder || '请输入'} allowClear onSearch={onSearch} />
          </div>
          {extra}
        </div>
      </div>

      <Table className="count-search-table" scroll={{ x: 940 }} {...tableProps} {...resolvedTableProps} />
    </div>
  )
}
