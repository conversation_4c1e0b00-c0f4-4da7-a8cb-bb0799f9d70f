import { CloseCircleOutlined, PlusCircleOutlined } from '@ant-design/icons'
import { ColumnRef, Expr, Param, Parser, Value } from 'node-sql-parser'
import { FunnelIcon } from '@heroicons/react/24/outline'
import {
  Cascader,
  CascaderProps,
  Form,
  FormListFieldData,
  FormListOperation,
  InputNumber,
  Radio,
  Select,
  SelectProps,
} from 'antd'
import { useAtomValue } from 'jotai'
import { DefaultOptionType } from 'antd/es/cascader'
import React, { useMemo, useEffect } from 'react'
import { codeValueSplitChar } from 'src/shared/constants'
import { Dimension, Metric, QueryParams } from 'src/shared/metric-types'
import { metricConfigAtom } from '../pages/AskBI/askBIAtoms'
import { ChatHistoryItemContext } from './ChatHistoryItem'

type WhereItem = { left: string; right: string; operator: string }
/** node-sql-parser 当中 where 表达式的 type 取值类型 */
type WhereNodeType =
  | 'binary_expr'
  | 'column_ref'
  | 'param'
  | 'null'
  | 'expr_list'
  | 'number' // Value 的 type 为 string，Value 的一种
  | 'double_quote_string' // Value 的 type 为 string，Value 的一种
  | 'single_quote_string' // Value 的 type 为 string，Value 的一种
  | 'unary_expr' // NOT
  | 'function'
  | 'interval'
// 解析where为ast
export function parseWhere(where?: string): { whereList: WhereItem[]; andOr: 'AND' | 'OR' } | null {
  if (!where) return null
  const sql = 'SELECT * FROM `table1` WHERE ' + where
  const sqlParser = new Parser()
  const ast = sqlParser.astify(sql)
  if (Array.isArray(ast) || ast.type !== 'select' || !ast.where || ast.where.type !== 'binary_expr') return null
  const andOr = (ast.where.operator === 'AND' || ast.where.operator === 'OR' ? ast.where.operator : 'AND') as
    | 'AND'
    | 'OR'
  const arr: WhereItem[] = []
  // 第一期不支持嵌套，把所有数据打散平铺，后期规整
  function dfs(node: Expr | ColumnRef | Param | Value): any {
    switch (node.type as WhereNodeType) {
      case 'binary_expr':
        node = node as Expr
        // 第一期不支持嵌套
        if (node.operator === 'AND' || node.operator === 'OR') {
          if (node.operator !== andOr) {
            throw 'AndOr异常'
          } else {
            dfs(node.left)
            dfs(node.right)
          }
        } else {
          arr.push({
            left: dfs(node.left),
            right: dfs(node.right),
            operator: node.operator,
          })
        }
        break
      case 'number':
      case 'single_quote_string':
        node = node as Value
        return node.value
      case 'column_ref':
        node = node as ColumnRef
        return node.column
      default:
        throw new Error('Unknown where')
    }
  }
  try {
    dfs(ast.where)
    return { whereList: arr, andOr }
  } catch (err) {
    console.info(err)
    return null
  }
}

const emptyHint = (
  <div className="flex flex-row gap-1 rounded-lg bg-gray-100 px-2 py-1 text-xs">
    <div className="text-gray-500">空</div>
  </div>
)

export interface EditWhereItemProps {
  value?: {
    left?: string[]
    right?: string | number
    operator?: string
  }

  onChange?: (v: EditWhereItemProps['value']) => void
  fieldOps: FormListOperation
  fieldData: FormListFieldData
  index: number
  isEdit: boolean
}

function EditWhereItem(props: EditWhereItemProps) {
  const { fieldOps, index, value, onChange, isEdit } = props
  const metricConfig = useAtomValue(metricConfigAtom)
  const cascaderOptions: CascaderProps['options'] = useMemo(() => {
    const arr: CascaderProps['options'] = []
    arr.push(
      {
        label: '维度',
        value: 'dimension',
        children: metricConfig?.allDimensions
          .filter((item) => Array.isArray(item.values))
          .map((item) => ({
            label: item.label,
            value: item.name,
            isLeaf: true,
          })),
      },
      {
        label: '指标',
        value: 'metric',
        children: metricConfig?.allMetrics.map((item) => ({
          label: item.label,
          value: item.name,
          isLeaf: true,
        })),
      },
    )
    return arr
  }, [metricConfig])
  const nameToLabelRecord = useMemo(() => {
    const record: Record<string, string> = {}
    for (const item of ([] as (Dimension | Metric)[])
      .concat(metricConfig?.allDimensions ?? [])
      .concat(metricConfig?.allMetrics ?? [])) {
      record[item.name] = item.label
    }
    return record
  }, [metricConfig])
  const isDim = typeof value === 'object' && value?.left?.[0] === 'dimension'
  const operatorOptions: SelectProps['options'] = useMemo(() => {
    if (typeof value !== 'string') {
      if (isDim) {
        return [
          { label: '等于', value: '=' },
          { label: '不等于', value: '!=' },
          { label: '大于', value: '>' },
          { label: '大于等于', value: '>=' },
          { label: '小于', value: '<' },
          { label: '小于等于', value: '<=' },
        ]
      } else {
        return [
          { label: '等于', value: '=' },
          { label: '不等于', value: '!=' },
          { label: '大于', value: '>' },
          { label: '大于等于', value: '>=' },
          { label: '小于', value: '<' },
          { label: '小于等于', value: '<=' },
        ]
      }
    }
    return []
  }, [value, isDim])
  const dimOptions: SelectProps['options'] = useMemo(() => {
    const dimVal = Array.isArray(value?.left) ? value?.left?.at(-1) : value?.left
    const data = metricConfig?.allDimensions.find((v) => v.name === dimVal)
    return (
      data?.values?.map((v) => {
        const data = v.split(codeValueSplitChar)[0]
        return { label: data, value: data, isLeaf: true }
      }) ?? []
    )
  }, [metricConfig?.allDimensions, value?.left])

  const rightNode = useMemo(() => {
    if (isDim && (!value?.operator || value.operator === '=' || value.operator === '!=')) {
      return (
        <Select
          size="small"
          className="!w-[140px] shrink-0"
          value={value?.right}
          options={dimOptions}
          onChange={(e) => {
            onChange?.({ ...value, right: e })
          }}
        />
      )
    } else {
      return (
        <InputNumber
          size="small"
          className="!w-[80px] shrink-0"
          value={value?.right}
          onChange={(e) => {
            onChange?.({ ...value, right: e as number })
          }}
        />
      )
    }
  }, [dimOptions, isDim, onChange, value])
  const editNode =
    typeof value === 'string' ? (
      value
    ) : (
      <>
        <Cascader
          size="small"
          value={value?.left}
          className="!w-[120px] shrink-0"
          options={cascaderOptions}
          onChange={(e) => {
            if (e[0] === value?.left?.[0]) {
              onChange?.({ ...value, left: e as string[] })
            } else {
              onChange?.({ left: e as string[] })
            }
          }}
          placeholder="维度/指标"
          showSearch={{
            filter: (inputValue: string, path: DefaultOptionType[]) =>
              path.some((option) => (option.label as string).toLowerCase().indexOf(inputValue.toLowerCase()) > -1),
          }}
        />
        <Select
          size="small"
          className="!w-[100px] shrink-0"
          options={operatorOptions}
          value={value?.operator}
          onChange={(e) => {
            onChange?.({ ...value, operator: e })
          }}
        />
        {rightNode}
      </>
    )
  const displayNode =
    value &&
    Array.isArray(value.left) &&
    nameToLabelRecord[value.left.at(-1)!] &&
    value.right !== undefined &&
    value.operator !== undefined ? (
      <>
        {nameToLabelRecord[value.left.at(-1)!]} {value.operator} {value.right}
      </>
    ) : null
  return (
    <div className="flex flex-row items-center gap-1 rounded-lg bg-blue-100 px-2 py-1 text-xs text-gray-700">
      <FunnelIcon className="h-4 w-4 shrink-0 text-blue-500" />
      {isEdit ? editNode : displayNode}
      {isEdit && (
        <CloseCircleOutlined
          className="ml-1 h-5 w-5 shrink-0 cursor-pointer text-blue-500"
          onClick={() => {
            fieldOps.remove(index)
          }}
        />
      )}
    </div>
  )
}

export interface WhereEditProps {
  isEdit: boolean
  where?: string
  queryParams: QueryParams
}

export function WhereEdit(props: WhereEditProps) {
  const { isEdit, where, queryParams } = props
  const { whereEditForm } = React.useContext(ChatHistoryItemContext)
  const metricConfig = useAtomValue(metricConfigAtom)
  const nameRecord = useMemo(() => {
    const record: Record<string, any> = {}
    for (const item of metricConfig?.allDimensions ?? []) {
      record[item.name] = 'dimension'
    }
    for (const item of metricConfig?.allMetrics ?? []) {
      record[item.name] = 'metric'
    }
    return record
  }, [metricConfig])

  const whereAst = useMemo(() => {
    return parseWhere(queryParams?.where)
  }, [queryParams])
  useEffect(() => {
    if (whereAst && whereEditForm) {
      const whereArr: EditWhereItemProps['value'][] = []
      for (const where of whereAst.whereList) {
        if (nameRecord[where.left]) {
          whereArr.push({ ...where, left: [nameRecord[where.left], where.left] })
        }
      }
      whereEditForm.setFieldsValue({ operator: whereAst.andOr, where: whereArr })
    }
  }, [nameRecord, whereAst, whereEditForm])

  if (!where || where.length === 0) {
    return emptyHint
  }
  if (!whereAst || !whereEditForm) {
    return (
      <div className="flex flex-row gap-1 rounded-lg bg-blue-100 px-2 py-1 text-xs text-gray-700">
        <FunnelIcon className="h-4 w-4 text-blue-500" />
        <div>{where}</div>
      </div>
    )
  }
  return (
    <Form form={whereEditForm} className="flex flex-wrap gap-[4px]">
      <Form.List name="where">
        {(fields, fieldOps) => {
          return (
            <>
              {fields.map((data, index) => {
                const { key, ...restFields } = data
                return (
                  <Form.Item className="mb-0" key={key} {...restFields}>
                    <EditWhereItem isEdit={isEdit} fieldData={data} index={index} fieldOps={fieldOps} />
                  </Form.Item>
                )
              })}
              {isEdit && (
                <Form.Item className="mb-0">
                  <PlusCircleOutlined className="cursor-pointer" onClick={() => fieldOps.add()} />
                </Form.Item>
              )}
            </>
          )
        }}
      </Form.List>
      {isEdit && (
        <Form.Item className="mb-0" name="operator">
          <Radio.Group
            options={[
              { label: 'AND', value: 'AND' },
              { label: 'OR', value: 'OR' },
            ]}
            optionType="button"
            size="small"
          />
        </Form.Item>
      )}
    </Form>
  )
}
