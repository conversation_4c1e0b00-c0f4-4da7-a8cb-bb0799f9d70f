/* eslint-disable no-case-declarations */
/**
 * @description 卡片上面的指标和分组选择器
 */
import React from 'react'
import { BarsArrowUpIcon } from '@heroicons/react/24/outline'
import { produce } from 'immer'
import { App, Button, DatePicker, Popover, Select } from 'antd'
import type { CustomTagProps } from 'rc-select/lib/BaseSelect'
import dayjs from 'dayjs'
import {
  MetricConfigResponse,
  QueryParams,
  QueryParamsVerified,
  TimeFunction,
  TimeQueryParams,
  getAvailableTimeGranularityOptions,
} from 'src/shared/metric-types'
import { convertTimeToSpecificDate, getDateFormat } from 'src/shared/time-utils'
import { TimeGranularityOptions, TimeGranularityType } from 'src/shared/common-types'
import { DimensionPopupContent, DimensionView, MetricPopupContent, MetricView } from './MetricTinyViews'
import { EditWhereItemProps, WhereEdit } from './WhereEdit'
import { ChatHistoryItemContext } from './ChatHistoryItem'

const { RangePicker } = DatePicker

const emptyHint = (
  <div className="flex flex-row gap-1 rounded-lg bg-gray-100 px-2 py-1 text-xs">
    <div className="text-gray-500">空</div>
  </div>
)

function getMetricLabel(metricName: string, metricConfig: MetricConfigResponse | null) {
  const metric = metricConfig?.allMetrics?.find((m) => m.name === metricName)
  return metric ? metric.label : metricName
}

function getDimensionLabel(dimensionName: string, metricConfig: MetricConfigResponse | null) {
  const dimension = metricConfig?.allDimensions?.find((d) => d.name === dimensionName)
  return dimension ? dimension.label : dimensionName
}

export const renderRangePicker = (
  queryParams: QueryParams,
  isEdit: boolean,
  handleRangeChange?: (dates: any) => void,
) => {
  // console.log('renderRangePicker', queryParams)
  const timeQueryParams = queryParams.timeQueryParams
  const timeGranularity = queryParams.timeQueryParams?.timeGranularity
  let pickerType: 'date' | 'month' | 'year' = 'date'
  switch (timeGranularity) {
    case 'total':
      pickerType = 'date'
      break
    case 'month':
      pickerType = 'month'
      break
    case 'year':
      pickerType = 'year'
      break
    default:
      pickerType = 'date'
  }
  if (timeQueryParams == null && !isEdit) {
    return emptyHint
  }
  // 如果 timeQueryParams 为空，在编辑状态，RangePicker 默认不选择任何日期
  let startTime: dayjs.Dayjs | null = null
  let endTime: dayjs.Dayjs | null = null
  // update later
  let dateFormat = 'YYYY-MM-DD'
  if (timeQueryParams != null) {
    dateFormat = getDateFormat(timeQueryParams.timeGranularity)
    const timeStartDate = convertTimeToSpecificDate(timeQueryParams.timeStartFunction, 'start')
    const timeEndDate = convertTimeToSpecificDate(timeQueryParams.timeEndFunction, 'end')

    startTime = dayjs(new Date(timeStartDate.year, timeStartDate.month - 1, timeStartDate.day))
    endTime = dayjs(new Date(timeEndDate.year, timeEndDate.month - 1, timeEndDate.day))
  }

  if (isEdit) {
    return (
      <RangePicker
        value={[startTime, endTime]}
        format={dateFormat}
        picker={pickerType}
        onChange={(dates) => handleRangeChange?.(dates)}
        separator="至"
        allowClear={false}
        className="font-default"
      />
    )
  } else {
    if (startTime && endTime) {
      // 如果开始和结束刚好是一个月的开始结束，那么就展示 xxxx年xx月
      if (startTime.startOf('month').isSame(startTime, 'date') && startTime.endOf('month').isSame(endTime, 'date')) {
        return `${startTime.format('YYYY年MM月')}`
      }
      // 如果开始和结束刚好是一个月的开始结束，那么就展示 xxxx年xx月
      if (startTime.startOf('year').isSame(startTime, 'date') && startTime.endOf('year').isSame(endTime, 'date')) {
        return `${startTime.format('YYYY年')}`
      }
      const formattedStartTime = startTime.format(dateFormat)
      const formattedEndTime = endTime.format(dateFormat)
      return formattedStartTime === formattedEndTime
        ? formattedStartTime
        : `${formattedStartTime} 至 ${formattedEndTime}`
    } else {
      return emptyHint
    }
  }
}

export default function QueryParamsSelector({
  queryParamsVerified,
  metricConfig,
  onChange,
  isViewMode,
}: {
  queryParamsVerified?: QueryParamsVerified
  metricConfig?: MetricConfigResponse | null
  onChange?: (value: QueryParamsVerified) => void
  isViewMode?: boolean
}) {
  const { whereEditForm } = React.useContext(ChatHistoryItemContext)
  // 是否处于编辑状态
  const [isEdit, setIsEdit] = React.useState(false)
  const [queryParams, setQueryParams] = React.useState<QueryParams | undefined>(queryParamsVerified?.queryParams)

  const { message } = App.useApp()

  // 处理日期范围变化
  const handleRangeChange = (dates: any) => {
    if (dates && dates.length === 2) {
      const startDay: dayjs.Dayjs = dates[0]
      const endDay: dayjs.Dayjs = dates[1]
      if (queryParams?.metricNames == null) {
        message.warning('请先选择指标')
        return
      }

      const timeStartFunction: TimeFunction = {
        type: 'specificDate',
        year: startDay.year(),
        month: startDay.month() + 1,
        day: startDay.date(),
      }
      const timeEndFunction: TimeFunction = {
        type: 'specificDate',
        year: endDay.year(),
        month: endDay.month() + 1,
        day: endDay.date(),
      }
      if (metricConfig?.timeDimensionDatum == null) {
        message.error('timeDimensionDatum is null')
        return
      }
      console.info('=== setQueryParams', {
        ...queryParams,
        timeQueryParams: {
          timeGranularity: queryParams.timeQueryParams?.timeGranularity || 'total',
          timeStartFunction: timeStartFunction,
          timeEndFunction: timeEndFunction,
        },
      })
      setQueryParams({
        ...queryParams,
        timeQueryParams: {
          /** TODO: 这里的 timeDimensionName 应该从用户的选择来赋值 */
          timeDimensionName:
            queryParams.timeQueryParams?.timeDimensionName || metricConfig.timeDimensionDatum?.timeDimensionName,
          timeGranularity: queryParams.timeQueryParams?.timeGranularity || 'total',
          timeStartFunction: timeStartFunction,
          timeEndFunction: timeEndFunction,
        },
      })
    }
  }

  if (queryParamsVerified == null || queryParams == null) {
    return false
  }

  if (metricConfig == null) {
    return <div>正在加载指标信息中</div>
  }

  const renderMetrics = () => {
    const { metricNames } = queryParams

    if (isEdit) {
      const options = metricConfig.allMetrics.map((d) => ({
        label: getMetricLabel(d.name, metricConfig),
        value: d.name,
      }))

      const tagRender = (tagProps: CustomTagProps) => {
        const { label, onClose } = tagProps
        return (
          <MetricView
            label={label as string}
            showClose={true}
            className="mr-2"
            onClose={(event) => {
              event.preventDefault()
              event.stopPropagation()
              // 判断长度是否为1，如果为1，不允许删除
              if (metricNames.length === 1) {
                message.warning('至少需要一个指标')
                return
              }
              onClose(event)
            }}
          />
        )
      }

      return (
        <Select
          mode="multiple"
          className="min-w-[150px]"
          placeholder="选择指标"
          tagRender={tagRender}
          style={{ width: '100%' }}
          options={options}
          value={metricNames}
          onChange={(values) => {
            setQueryParams({ ...queryParams, metricNames: values })
          }}
        />
      )
    } else {
      if (!metricNames?.length) {
        return emptyHint
      }
      return (
        <>
          {metricNames.map((metricName) => {
            const metric = metricConfig?.allMetrics?.find((m) => m.name === metricName)
            if (!metric) {
              return null
            }
            return (
              <Popover key={metricName} title={metricName} content={<MetricPopupContent metric={metric} />}>
                <div>
                  <MetricView label={getMetricLabel(metricName, metricConfig)} />
                </div>
              </Popover>
            )
          })}
        </>
      )
    }
  }

  const renderGroupBy = () => {
    const { groupBys } = queryParams

    if (isEdit) {
      const options = metricConfig.allDimensions
        .filter((n) => n.type === 'categorical')
        .map((d) => ({
          label: getDimensionLabel(d.name, metricConfig),
          value: d.name,
        }))

      const tagRender = (tagProps: CustomTagProps) => {
        const { value, onClose } = tagProps
        return (
          <DimensionView
            dimension={metricConfig.allDimensions.find((d) => d.name === value)}
            showClose={true}
            className="mr-2"
            onClose={onClose}
          />
        )
      }

      return (
        <Select
          mode="multiple"
          className="min-w-[150px]"
          placeholder="选择维度来分组"
          tagRender={tagRender}
          style={{ width: '100%' }}
          options={options}
          value={groupBys}
          onChange={(values) => {
            setQueryParams({ ...queryParams, groupBys: values })
          }}
        />
      )
    } else {
      if (!groupBys?.length) {
        return emptyHint
      }
      return (
        <>
          {groupBys.map((groupBy) => {
            const dimension = metricConfig.allDimensions.find((d) => d.name === groupBy)
            if (!dimension) {
              return null
            }
            return (
              <Popover key={groupBy} title={dimension.label} content={<DimensionPopupContent dimension={dimension} />}>
                <div>
                  <DimensionView dimension={metricConfig.allDimensions.find((d) => d.name === groupBy)} />
                </div>
              </Popover>
            )
          })}
        </>
      )
    }
  }

  const renderLimit = () => {
    const { limit } = queryParams

    if (isEdit) {
      return (
        <Select
          className="min-w-[80px]"
          placeholder="前多少条"
          style={{ width: '100%' }}
          options={[1, 2, 3, 4, 5, 10, 20, 50].map((d) => ({
            label: d.toString(),
            value: d,
          }))}
          value={limit}
          onChange={(value) => {
            setQueryParams({ ...queryParams, limit: value })
          }}
        />
      )
    } else {
      if (limit == null) {
        return emptyHint
      }

      return (
        <div className="flex flex-row gap-1 rounded-lg bg-blue-100 px-2 py-1 text-xs text-gray-700">
          <div>{limit}</div>
        </div>
      )
    }
  }

  const renderOrderBys = () => {
    const { orderBys, metricNames, groupBys } = queryParams
    const options: { label: string; value: string }[] = []
    metricNames?.forEach((metricName) => {
      options.push({
        label: getMetricLabel(metricName, metricConfig) + '-升序',
        value: metricName + ' asc',
      })
      options.push({
        label: getMetricLabel(metricName, metricConfig) + '-降序',
        value: metricName + ' desc',
      })
    })

    groupBys?.forEach((groupBy) => {
      options.push({
        label: getDimensionLabel(groupBy, metricConfig) + '-升序',
        value: groupBy + ' asc',
      })

      options.push({
        label: getDimensionLabel(groupBy, metricConfig) + '-降序',
        value: groupBy + ' desc',
      })
    })

    if (isEdit) {
      return (
        <Select
          className="min-w-[150px]"
          placeholder="指定排序"
          style={{ width: '100%' }}
          options={options}
          value={orderBys && orderBys[0]}
          onChange={(value) => {
            setQueryParams({ ...queryParams, orderBys: [value] })
          }}
        />
      )
    } else {
      if (!orderBys?.length) {
        return emptyHint
      }
      return (
        <>
          {orderBys.map((orderBy) => {
            return (
              <div
                key={orderBy}
                className="flex flex-row gap-1 rounded-lg bg-gray-100 px-2 py-1 text-xs dark:bg-gray-800"
              >
                {orderBy.endsWith('asc') ? (
                  <BarsArrowUpIcon className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                ) : (
                  <BarsArrowUpIcon className="h-4 w-4 rotate-180 transform text-gray-500" />
                )}
                <div className="text-gray-700 dark:text-gray-200">
                  {options.find((o) => o.value === orderBy)?.label || orderBy}
                </div>
              </div>
            )
          })}
        </>
      )
    }
  }

  const renderTimeGranularity = () => {
    if (metricConfig.timeDimensionDatum == null) {
      return null
    }

    const timeQueryParams = queryParams.timeQueryParams
    // 只展示比 timeDimensionDatum 更大的日期粒度

    if (isEdit) {
      const today = dayjs(Date.now())
      // 如果没有选择开始结束时间，默认选择现在
      const newTimeQueryParams: TimeQueryParams =
        timeQueryParams != null
          ? timeQueryParams
          : {
              timeDimensionName:
                queryParams.timeQueryParams?.timeDimensionName || metricConfig.timeDimensionDatum.timeDimensionName,
              timeStartFunction: {
                type: 'specificDate',
                year: today.year(),
                month: today.month() + 1,
                day: today.date(),
              },
              timeEndFunction: {
                type: 'specificDate',
                year: today.year(),
                month: today.month() + 1,
                day: today.date(),
              },
              timeGranularity: 'total',
            }

      return (
        <Select
          className="min-w-[80px]"
          placeholder="选择维度来分组"
          style={{ width: '100%' }}
          options={getAvailableTimeGranularityOptions(metricConfig.timeDimensionDatum.timeGranularityMin).map((d) => ({
            label: d.label,
            value: d.value,
          }))}
          value={timeQueryParams?.timeGranularity}
          onChange={(value) => {
            setQueryParams({
              ...queryParams,
              timeQueryParams: { ...newTimeQueryParams, timeGranularity: value as TimeGranularityType },
            })
          }}
        />
      )
    } else {
      if (!timeQueryParams?.timeGranularity) {
        return emptyHint
      }
      return (
        <div className="flex flex-row gap-1 rounded-lg bg-gray-100 px-2 py-1 text-xs text-gray-700">
          {TimeGranularityOptions.find((d) => d.value === timeQueryParams.timeGranularity)?.label ?? '未知'}
        </div>
      )
    }
  }

  const renderExtraMessage = () => {
    if (queryParamsVerified.extraParams == null) {
      return null
    }
    const { extraGroupBys, extraMetricNames, extraOrderBys } = queryParamsVerified.extraParams
    return (
      <div className="flex flex-row text-red-500">
        {extraGroupBys.length > 0 ? `可能有分组列缺失，模型的提参为：${extraGroupBys.join(', ')}` : ''}
        {extraMetricNames.length > 0 ? `可能有指标缺失，模型的指标提参为：${extraMetricNames.join(', ')}` : ''}
        {extraOrderBys.length > 0 ? `可能有排序字段缺失，模型的排序提参为：${extraOrderBys.join(', ')}` : ''}
      </div>
    )
  }

  const handleReset = () => {
    setIsEdit(false)
    if (queryParamsVerified && onChange) {
      setQueryParams(queryParamsVerified.queryParams)
      onChange(queryParamsVerified)
    }
  }
  const handleSubmit = async () => {
    setIsEdit(false)
    let finalQueryParams = queryParams
    if (whereEditForm) {
      const whereData = await whereEditForm.validateFields()
      finalQueryParams = produce(queryParams, (queryParams) => {
        if (whereData.where?.length) {
          queryParams.where = (whereData.where as EditWhereItemProps['value'][])
            .map((item) => {
              if (item && item.left !== undefined && item.right !== undefined && item.operator !== undefined) {
                const left = typeof item.left === 'string' ? item.left : item.left?.at(-1)
                const right = typeof item.right === 'string' ? `'${item.right}'` : item.right
                return `${left} ${item.operator} ${right}`
              }
              return null
            })
            .filter(Boolean)
            .join(` ${whereData.operator} `)
        }
      })
    }
    onChange?.({
      ...queryParamsVerified,
      queryParams: finalQueryParams,
    })
  }

  return (
    <div className="mb-2 flex flex-row flex-wrap gap-4">
      <div className="flex flex-row flex-wrap items-center">
        <div className="font-bold">指标：</div>
        <div className="flex flex-row flex-wrap items-center gap-2">{renderMetrics()}</div>
      </div>
      <div className="flex flex-row flex-wrap items-center">
        <div className="font-bold">分组：</div>
        <div className="flex flex-row flex-wrap items-center gap-2">{renderGroupBy()}</div>
      </div>
      {/** 只有启用了时间维度才显示时间的过滤 */}
      {metricConfig.timeDimensionDatum != null && (
        <>
          <div className="flex flex-row flex-wrap items-center">
            <div className="font-bold">时间区间：</div>
            <div className="flex flex-row flex-wrap items-center gap-2">
              {renderRangePicker(queryParams, isEdit, handleRangeChange)}
            </div>
          </div>
          {(isEdit ||
            (queryParamsVerified.queryParams.timeQueryParams &&
              queryParamsVerified.queryParams.timeQueryParams.timeGranularity !== 'total')) && (
            <div className="flex flex-row flex-wrap items-center">
              <div className="font-bold">时间聚合：</div>
              <div className="flex flex-row flex-wrap items-center gap-2">{renderTimeGranularity()}</div>
            </div>
          )}
        </>
      )}
      {(isEdit || queryParamsVerified.queryParams.where) && (
        <div className="flex flex-row flex-wrap items-center">
          <div className="font-bold">过滤：</div>
          <div className="flex flex-row flex-wrap items-center gap-2">
            <WhereEdit isEdit={isEdit} queryParams={queryParams} where={queryParams?.where} />
          </div>
        </div>
      )}
      {(isEdit ||
        (queryParamsVerified.queryParams.orderBys && queryParamsVerified.queryParams.orderBys.length > 0)) && (
        <div className="flex flex-row flex-wrap items-center">
          <div className="font-bold">排序：</div>
          <div className="flex flex-row flex-wrap items-center gap-2">{renderOrderBys()}</div>
        </div>
      )}
      {(isEdit || queryParamsVerified.queryParams.limit != null) && (
        <div className="flex flex-row flex-wrap items-center">
          <div className="font-bold">前：</div>
          <div className="flex flex-row flex-wrap items-center gap-2">{renderLimit()}</div>
        </div>
      )}
      {renderExtraMessage()}
      {isViewMode ? null : isEdit ? (
        <div className="ml-auto flex flex-row gap-2">
          <Button size="small" onClick={handleReset}>
            重置
          </Button>
          <Button size="small" type="primary" onClick={handleSubmit}>
            查询
          </Button>
        </div>
      ) : (
        <Button size="small" onClick={() => setIsEdit(true)}>
          调整
        </Button>
      )}
    </div>
  )
}
