import { markdownTable } from 'markdown-table'
import { ChatResponseErrorTypes, Chat, Message, AnsChatItem, isMetric, OlapRow } from 'src/shared/common-types'
import { formatOrderBy, sortByOrderBy } from 'src/shared/common-utils'
import { Metric, MetricConfigResponse } from 'src/shared/metric-types'
import { renderRangePicker } from '../QueryParamsSelector'

export const DEEPSEEK_ERROR_LIST: string[] = [
  ChatResponseErrorTypes.METRICS_NOT_EXIST,
  ChatResponseErrorTypes.DIMENSIONS_TREE_NOT_EXIST,
  ChatResponseErrorTypes.METRIC_TREE_NOT_EXIST,
  ChatResponseErrorTypes.ATTR_ANALYSIS_NOT_SUPPORT,
  ChatResponseErrorTypes.ATTR_ANALYSIS_DATA_NOT_EXIST,
  ChatResponseErrorTypes.PARAM_EXTRACT_EMPTY_RESULT,
  ChatResponseErrorTypes.QUESTION_NOT_SUPPORTED,
  ChatResponseErrorTypes.CHIT_CHAT,
]

function toMessageContentAnsItemList(ansItemList: AnsChatItem[], metricConfig: MetricConfigResponse) {
  let content = `<think>\n\n</think>\n`
  for (const ansItem of ansItemList) {
    if (ansItem.role === 'assistant') {
      for (const contentItem of ansItem.content) {
        if (contentItem.type === 'chat-error' && contentItem.tryQueryToSqlData.ansChatItemList) {
          content = toMessageContentAnsItemList(contentItem.tryQueryToSqlData.ansChatItemList, metricConfig)
        } else if (
          (contentItem.type === 'chart' && contentItem.queryParamsVerified) ||
          (contentItem.type === 'chat-error' && contentItem.queryParamsVerified)
        ) {
          const { queryParamsVerified } = contentItem
          const table: string[][] = []
          if (contentItem.type === 'chart') {
            const { rowsMetadata, rows } = contentItem
            const metrics = rowsMetadata
              .filter((item) => isMetric(item) && item.value.type !== 'periodOverPeriod')
              .map((item) => item.value as Metric)
            const orderByList = formatOrderBy({
              queryParamsVerified: queryParamsVerified,
              defaultValue: {
                metricName: metrics[0].name,
                orderBy: 'desc',
              },
            })
            const sortedData = sortByOrderBy({
              rows: rows as OlapRow[],
              orderByList,
            })
            table.push(contentItem.rowsMetadata.map((v) => v.value.label))
            table.push(
              ...sortedData.map((v) => contentItem.rowsMetadata.map((meta) => (v as any)[meta.value.name] ?? '')),
            )
          }
          content += `
## 提参结果

- 指标: ${queryParamsVerified.queryParams.metricNames
            .map((name) => metricConfig.allMetrics.find((v) => v.name === name)?.label)
            .filter(Boolean)
            .join(',')}
- 时间: ${renderRangePicker(queryParamsVerified.queryParams, false)}
- GroupBY: ${
            queryParamsVerified.queryParams.groupBys
              ?.map(
                (name) =>
                  contentItem.type === 'chart' &&
                  contentItem.rowsMetadata.find((v) => v.value.name === name)?.value.label,
              )
              .filter(Boolean)
              .join(',') ?? ''
          }
- Limit: ${queryParamsVerified.queryParams.limit ?? ''}

## 查数结果

${markdownTable(table)}
`
        } else if (contentItem.type === 'deepseek' && contentItem.data.trigger === 'error') {
          content = contentItem.data.content
        }
      }
    }
  }
  return content
}

export function toMessageContent(chat: Chat, metricConfig?: MetricConfigResponse | null): string {
  if (!metricConfig) return ''
  return toMessageContentAnsItemList(chat.ans, metricConfig)
}

export function isDeepseekErrorTriggerChatItem(chat?: Chat): boolean {
  const content = chat?.ans.at(0)?.content.at(0)
  return !!(chat && !chat.ask && content && content.type === 'deepseek' && content.data.trigger === 'error')
}

export function loadAllMessages({
  chats,
  relativeIdx = chats.length,
  metricConfig,
}: {
  chats: Chat[]
  relativeIdx?: number
  metricConfig?: MetricConfigResponse | null
}): Message[] {
  const messages: Message[] = []
  for (let i = 0; i < relativeIdx; i++) {
    const chat = chats[i]

    if (chat.ask && chat.ask.role === 'user') {
      messages.push({
        content: chat.ask.content,
        role: 'user',
      })
      messages.push({ role: 'assistant', content: toMessageContent(chat, metricConfig) })
    }
    // DeepSeek 特判
    if (isDeepseekErrorTriggerChatItem(chat)) {
      messages.pop()
      messages.push({ role: 'assistant', content: toMessageContent(chat, metricConfig) })
    }
  }
  if (relativeIdx < chats.length) {
    messages.push({ role: 'user', content: chats[relativeIdx].ask.content })
  }
  return messages
}
