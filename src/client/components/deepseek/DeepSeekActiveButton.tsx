import clsx from 'clsx'
import { nanoid } from 'nanoid'
import { useAtomValue, useSetAtom } from 'jotai'
import { produce } from 'immer'
import React, { ReactNode, useState } from 'react'
import { chatsAtom, currentDatasetAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { Chat } from 'src/shared/common-types'
import { DeepSeekData } from 'src/shared/deepseek'
import { DeepSeekIcon } from './DeepSeekIcon'

export function DeepSeekActiveButton({
  containerProps = {},
  textProps = {},
  chatId,
  updateDeepSeekData = (v) => v,
  icon,
}: {
  icon?: ReactNode
  chatId: string
  containerProps?: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>
  textProps?: React.DetailedHTMLProps<React.HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>
  updateDeepSeekData?: (data: DeepSeekData) => DeepSeekData
}) {
  const currentDataset = useAtomValue(currentDataset<PERSON>tom)
  const setChats = useSetAtom(chatsAtom)
  const [activeDeepSeekAssistantId, setActiveDeepSeekAssistantId] = useState<string | null>(null)
  return (
    <div
      {...containerProps}
      className={clsx('flex cursor-pointer items-center', containerProps.className)}
      onClick={() => {
        setChats(
          produce((chats) => {
            const id = activeDeepSeekAssistantId ?? nanoid()
            setActiveDeepSeekAssistantId(id)
            const item: Chat = {
              id,
              isSystemPrompt: !true,
              askTime: new Date(),
              ask: null as any,
              selectedSceneId: currentDataset?.sceneId ?? '',
              ans: [
                {
                  role: 'assistant',
                  sceneId: currentDataset?.sceneId ?? '',
                  status: 'success',
                  content: [
                    {
                      type: 'deepseek',
                      relativeChatId: chatId,
                      data: updateDeepSeekData(new DeepSeekData()),
                    },
                  ],
                },
              ],
              docAns: {
                role: 'assistant',
                status: 'pending',
                content: [],
              },
            }
            const relativeChatIdx = chats.findIndex((chat) => chat.id === chatId)
            if (relativeChatIdx === -1) return chats
            const exist = chats[relativeChatIdx + 1]?.id === activeDeepSeekAssistantId
            chats.splice(relativeChatIdx + 1, exist ? 1 : 0, item)
          }),
        )
      }}
    >
      {icon === null ? null : icon === undefined ? <DeepSeekIcon className="h-[22px] w-[22px]" /> : icon}
      <span {...textProps} className={clsx('ml-[8px] text-primary', textProps.className)} />
    </div>
  )
}
