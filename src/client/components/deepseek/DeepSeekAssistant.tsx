import { use<PERSON>tomValue } from 'jotai'
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react'
import rehypeKatex from 'rehype-katex'
import { nanoid } from 'nanoid'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import clsx from 'clsx'
import { throttle } from 'lodash'
import { useBoolean } from 'ahooks'
import ReactMarkdown from 'react-markdown'
import { CaretDownOutlined, CaretUpOutlined, SyncOutlined } from '@ant-design/icons'
import {
  chatsAtom,
  conversationIdAtom,
  currentDatasetAtom,
  currentFollowUpQuestionAtom,
  currentParamsExtractApiAtom,
  deepSeekEnableNetWorkAtom,
  metricConfigAtom,
} from 'src/client/pages/AskBI/askBIAtoms'
import { DeepSeekData } from 'src/shared/deepseek'
import { AssistantDeepSeekItem } from 'src/shared/common-types'
import { scrollToBottom } from 'src/client/utils'
import { askBIApiUrls } from 'src/shared/url-map'
import { createTraceId } from 'src/shared/common-utils'
import { TOKEN_BI, TOKEN_RANGER, U_TOKEN_RANGER } from 'src/shared/constants'
import { ChatHistoryItemContext } from '../ChatHistoryItem'
import { loadAllMessages, toMessageContent } from './utils'
import { DeepSeekIcon } from './DeepSeekIcon'

function Markdown(props: { content: string; className: string }) {
  return (
    <ReactMarkdown
      className={clsx(`askbi-markdown mt-[20px] text-[16px] font-[400] leading-[28px]`, props.className)}
      remarkPlugins={[remarkGfm, remarkMath]}
      rehypePlugins={[rehypeKatex]}
    >
      {props.content}
    </ReactMarkdown>
  )
}

export function DeepSeekAssistant() {
  const loadingRef = useRef(false)
  const { chat } = useContext(ChatHistoryItemContext)
  const metricConfig = useAtomValue(metricConfigAtom)
  const currentDataset = useAtomValue(currentDatasetAtom)
  const defaultConversationId = useAtomValue(conversationIdAtom)
  const currentConversationId = defaultConversationId || nanoid()
  const currentFollowUpQuestion = useAtomValue(currentFollowUpQuestionAtom)
  const content = chat.ans[0].content[0] as AssistantDeepSeekItem
  const [loadingData, setLoadingData] = useState(false)
  const chats = useAtomValue(chatsAtom)
  const deepSeekEnableNetWork = useAtomValue(deepSeekEnableNetWorkAtom)
  const containerRef = useRef<HTMLDivElement | null>(null)
  const relativeIdx = chats.findIndex((v) => v.id === content.relativeChatId)
  const [thinking, thinkingOps] = useBoolean(true)
  const currentParamsExtractApi = useAtomValue(currentParamsExtractApiAtom)
  const [data, setData] = useState(content.data)
  const { content: rawContent, status } = data
  useEffect(() => {
    content.data.setStatus(data.status).setContent(data.content).setTrigger(data.trigger).setReader(data.reader)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data])

  const { thinkContent, resultContent } = useMemo(() => {
    const thinkStartReg = /<think>/g
    const thinkEndReg = /<\/think>/g
    const tagReg = /<think>[\s\S]*<\/think>/g
    let thinkContent = ''
    let resultContent = ''
    const startIdx = Array.from(rawContent.matchAll(thinkStartReg))[0]?.index
    const endIdx = Array.from(rawContent.matchAll(thinkEndReg))[0]?.index
    if (startIdx >= 0 && endIdx >= 0) {
      const data = Array.from(rawContent.matchAll(tagReg))
      thinkContent = data[0][0]
      resultContent = rawContent.replaceAll(thinkContent, '')
    } else if (endIdx === undefined && startIdx >= 0) {
      thinkContent = rawContent.substring(startIdx) + '</think>'
    } else if (startIdx === undefined && endIdx >= 0) {
      thinkContent = '<think>' + rawContent.substring(0, endIdx + `</think>`.length)
      resultContent = rawContent.substring(endIdx + `</think>`.length)
    } else {
      resultContent = rawContent
    }
    const updateKatex = (s: string) =>
      s.replaceAll(/\\\[[\s\S]*\\\]/g, (val) => `\n$$\n${val.substring(2, val.length - 2)}\n$$\n`)
    return {
      thinkContent: updateKatex(thinkContent.substring(`<think>`.length, thinkContent.length - `</think>`.length)),
      resultContent: updateKatex(resultContent),
    }
  }, [rawContent])
  // useEffect(() => {
  //   console.group('DS Content')
  //   console.log(JSON.stringify(rawContent))
  //   console.log(JSON.stringify(thinkContent))
  //   console.log(JSON.stringify(resultContent))
  //   console.groupEnd()
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [rawContent])
  const toBottom = throttle(scrollToBottom, 50)
  async function loadData() {
    if (!content.data.reader) {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        traceId: createTraceId(),
      }
      const token = localStorage.getItem(TOKEN_BI)
      const rangerToken = localStorage.getItem(TOKEN_RANGER)
      const rangerUToken = localStorage.getItem(U_TOKEN_RANGER)
      if (token) headers['Authorization'] = `Bearer ${token}`
      if (rangerToken) headers[TOKEN_RANGER] = rangerToken
      if (rangerUToken) headers[U_TOKEN_RANGER] = rangerUToken
      const response =
        data.trigger === 'deep-think'
          ? await fetch(askBIApiUrls.resultAnalysis, {
              method: 'POST',
              body: JSON.stringify({
                parentId: currentFollowUpQuestion ? currentFollowUpQuestion.id : null,
                sceneId: currentDataset?.sceneId ?? '',
                conversationId: currentConversationId,
                message: chats[relativeIdx].ask.content,
                deepThinkContent: toMessageContent(chats[relativeIdx], metricConfig),
                currentParamsExtractApi,
              }),
              headers,
            })
          : await fetch(askBIApiUrls.chitchat, {
              method: 'POST',
              body: JSON.stringify({
                messages: loadAllMessages({ chats, relativeIdx, metricConfig }),
                currentParamsExtractApi,
                webSearch: deepSeekEnableNetWork,
              }),
              headers,
            })
      if (!response.ok) throw new Error('请求失败')
      if (response.body) {
        const reader = response.body.getReader()
        setData((old) => DeepSeekData.from(old).setReader(reader).setStatus('success'))
        let done = false
        // 在循环外部实例化 TextDecoder，提高性能。
        const decoder = new TextDecoder()
        while (!done) {
          const { done: isDone, value } = await reader.read()
          done = isDone
          if (!done) {
            const text = decoder.decode(value)
            const regex = /\{"code":\d+,"data":"(.*?)"\}/g
            const match = text.match(regex)

            match?.map((item) => {
              const everyStream = JSON.parse(item)
              if (everyStream['code'] === 0) {
                const data = everyStream['data']
                setData((old) => DeepSeekData.from(old).setContent(old.content + data))
                if (containerRef.current) containerRef.current.scrollIntoView(false)
                else toBottom()
              } else {
                console.error('接口返回异常:', everyStream)
                throw new Error('接口返回异常')
              }
            })
          }
        }
      }
    }
  }
  useEffect(() => {
    if (status === 'loading' && !loadingRef.current) {
      setLoadingData(true)
      loadingRef.current = true
      loadData()
        ?.catch(() => {
          setData((old) => DeepSeekData.from(old).setContent('请求失败，请稍后再试。').setStatus('fail'))
        })
        .finally(() => {
          setLoadingData(false)
          loadingRef.current = false
        })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [status])
  useEffect(() => {
    setTimeout(() => toBottom(), 200)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return status === 'loading' ? (
    <div className="flex" ref={containerRef}>
      deepseek正在尝试解答您的问题...
      <SyncOutlined spin className="ml-4 flex flex-none text-xl text-gray-400 dark:text-gray-500" />
    </div>
  ) : status === 'fail' ? (
    <div className="flex" ref={containerRef}>
      {data.content}
    </div>
  ) : (
    <div ref={containerRef}>
      <div className="flex h-[24px] items-center justify-between">
        <div className="flex h-full w-full items-center justify-start">
          <DeepSeekIcon className="h-[22px] w-[22px]" />
          <span className="ml-[8px] text-[14px] font-[400] text-[#0F172A]">该问题由DeepSeek尝试解答</span>
        </div>
        <div className="flex h-full shrink-0 cursor-pointer items-center justify-end" onClick={thinkingOps.toggle}>
          <span className="text-[14px] font-[400] text-[#575757]">收起</span>
          {thinking ? (
            <CaretUpOutlined className="ml-[4px] h-[20px] w-[20px] text-[#858585]" />
          ) : (
            <CaretDownOutlined className="ml-[4px] h-[20px] w-[20px] text-[#858585]" />
          )}
        </div>
      </div>
      {thinking && <Markdown content={thinkContent} className="text-[#8B8B8B]" />}
      <Markdown content={resultContent} className="text-[#404040]" />
      {loadingData && (
        <div className="mt-[16px] rounded-[4px] bg-[#E5F7F8] px-[16px] py-[12px]">
          deepseek正在思考回答中...请继续等待...
        </div>
      )}
    </div>
  )
}
