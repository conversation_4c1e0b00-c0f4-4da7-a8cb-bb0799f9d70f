/**
 * @description 聊天历史中的一次聊天记录，包含一问一答
 */
import React, { Fragment, useCallback, useEffect, useRef, useState } from 'react'
import {
  ConsoleSqlOutlined,
  CopyOutlined,
  InfoCircleOutlined,
  InteractionOutlined,
  ReadOutlined,
} from '@ant-design/icons'
import { Light as SyntaxHighlighter } from 'react-syntax-highlighter'
import sql from 'react-syntax-highlighter/dist/cjs/languages/hljs/sql'
import { format as sqlFormatter } from 'sql-formatter'
import { Prisma } from '@prisma/client'
import { github as githubTheme, dark as darkTheme } from 'react-syntax-highlighter/dist/esm/styles/hljs'
import { produce } from 'immer'
import { App, Divider, Drawer, Dropdown, Form, FormInstance, Input, Popconfirm, Tabs } from 'antd'
import clsx from 'clsx'
import { ArrowUpOnSquareIcon } from '@heroicons/react/24/outline'
import axios from 'axios'
import { useAtomValue, useSetAtom } from 'jotai'
import {
  AssistantChartChatItem,
  AssistantRelatedQuestionItem,
  ChartCreateInput,
  ChartType,
  Chat,
  ThemeType,
  BlobWithRatio,
  OlapRow,
  AnsChatItem,
  AssistantHelloTextChatItem,
  AssistantChatError,
  DatasetDatum,
  ChatStatus,
  ChatResponseErrorTypes,
} from '@shared/common-types'
import { copyToClipboard, isSecureEnvironment, scrollToBottom } from '@client/utils'
import { assertExhaustive, isKeXueCheng } from '@shared/common-utils'
import {
  DISABLE_INSIGHT_PROJECT_NAME_LIST,
  IS_H5,
  MULTI_SCENE_CHAT_MOCK_SCENE_ID,
  DISPLAY_INSIGHT,
} from '@shared/constants'
import { CalculatorData } from 'src/shared/calculator'
import { askBIApiUrls, askBIPageUrls } from 'src/shared/url-map'
import { isBaoWu } from 'src/shared/baowu-share-utils'
import { ProjectType, SceneType } from 'src/shared/metric-types'
import ChartWrapper from '../charts/ChartWrapper'
import Card, { getVisibleChartIconMap } from '../charts/Card'
import ChartCopy from '../charts/ChartCopy'
import ChartDownload from '../charts/ChartDownload'
import {
  currentChartThemeTypeAtom,
  llmTypeAtom,
  makeAgentRequestAtom,
  chatsAtom,
  currentFollowUpQuestionAtom,
  chatQueryParamsChangeAtom,
  semanticProjectInfoAtom,
  currentDatasetAtom,
  isProjectChosenAtom,
  currentFollowUpSceneIdAtom,
  metricConfigOfProjectAtom,
  metricConfigAtom,
  brandInfoAtom,
  intervalId,
} from '../pages/AskBI/askBIAtoms'
import DataOverview from '../pages/AskBI/Chat/MetaDataCards/DataOverview'
import DimensionList from '../pages/AskBI/Chat/MetaDataCards/DimensionList'
import MetricList from '../pages/AskBI/Chat/MetaDataCards/MetricList'
import TableList from '../pages/AskBI/Chat/MetaDataCards/TableList'
import MetricDetail from '../pages/AskBI/Chat/MetaDataCards/MetricDetail'
import { useChatProgress } from '../hooks/useChatProgress'
import AnswerView from '../pages/AskDoc/DocDetail/AnswerView'
import DimensionDetail from '../pages/AskBI/Chat/MetaDataCards/DimensionDetail'
import { docPreviewOpenTagAtom } from '../pages/AskDoc/askDocAtoms'
import DocReportAnalyze from '../pages/AskBI/DocReport/DocReportAnalyze'
import MetricForceMatchComp from './MetricForceMatchComp'
import { Calculator } from './Calculator'
import ChatBoxFeedback from './ChatBoxFeedback'
import IconButtonGroup from './IconButtonGroup'
import {
  SvgIcon,
  downloadReportIcon,
  circleInfoIcon,
  followUpAskIcon,
  scenesListIcon,
  agentAskDocIcon,
  agentAskBIIcon,
} from './SvgIcon'
import './ChatHistoryItem.css'
import {
  renderChatAnsHelloTextItem,
  renderChatAnsTextItem,
  renderChatAnsMetaItem,
  renderNotExistMetric,
} from './ChatHistoryRenderComp'
import MetricStoreOverview from './MetricStoreOverview'
import ChatDataErrorFeedback from './ChatDataErrorFeedback'
import MetricForceMatch from './MetricForceMatch'
import ChatError from './ChatError/ChatError'
import PartialDataNotice from './PartialDataNotice/PartialDataNotice'
import ExternalReport from './ExternalReport/ExternalReport'
import SeriesQuestionNotice from './SeriesQuestionNotice/SeriesQuestionNotice'
import ChatHistoryItemAskEdit from './ChatHistoryItemAskEdit/ChatHistoryItemAskEdit'
import { DeepSeekAssistant } from './deepseek'
import DataInsight from './DataInsight'

SyntaxHighlighter.registerLanguage('sql', sql)

// 每一个区域内的小标题 [SQL, 分析报告]
const titleClassName = 'block text-base font-semibold mb-1'
type TabType = 'Ask-BI' | 'Ask-Doc'

interface Props {
  chat: Chat
  theme: ThemeType
}

export const ChatHistoryItemContext = React.createContext(
  {} as {
    whereEditForm: FormInstance
    chatProgress: ReturnType<typeof useChatProgress>
    chat: Chat
  },
)

function ChatHistoryItem({ chat, theme }: Props) {
  const [whereEditForm] = Form.useForm()
  const semanticProjectInfo = useAtomValue(semanticProjectInfoAtom)
  const currentDataset = useAtomValue(currentDatasetAtom)
  const metricConfig = useAtomValue(metricConfigAtom)
  const setDocPreviewOpenTag = useSetAtom(docPreviewOpenTagAtom)
  const setChats = useSetAtom(chatsAtom)
  const brandInfo = useAtomValue(brandInfoAtom)
  const chartWrapperRef = useRef<{
    downloadPNG: () => void
    copyPNG: () => void
    getBlob: () => Promise<BlobWithRatio | string>
  }>(null)

  const isProjectChosen = useAtomValue(isProjectChosenAtom)

  const currentScene = semanticProjectInfo
    .find((p) => p.id === currentDataset?.projectId)
    ?.scenes?.find((s) => s.id === currentDataset?.sceneId)
  const agentStr = currentScene?.agent || ''
  const isDocAgent = agentStr.includes('Doc')
  const isBIAgent = agentStr.includes('BI')
  const onlyBIAgent = isBIAgent && agentStr === 'BI'
  const chatProgress = useChatProgress({ chat })
  const { chatProgressList, runFetchChatProgress, nl2IntentMsg, close, fetchingChatProgress } = chatProgress
  const defaultActiveTab: TabType = (() => {
    if (isKeXueCheng(currentDataset)) {
      const data = nl2IntentMsg
      if (data?.askbi && !data.askdoc) {
        return 'Ask-BI'
      } else {
        return 'Ask-Doc'
      }
    } else if (isBIAgent) {
      return 'Ask-BI'
    } else {
      return 'Ask-Doc'
    }
  })()
  const [activeTab, setActiveTab] = useState<TabType>(defaultActiveTab)

  useEffect(() => {
    if (isKeXueCheng(currentDataset)) {
      const defaultAgentFlag = { askbi: true, askdoc: true, intent: '' }
      const agentFlag = chatProgressList.length === 0 ? defaultAgentFlag : (nl2IntentMsg ?? defaultAgentFlag)
      if (agentFlag.askbi && !agentFlag.askdoc) {
        setActiveTab('Ask-BI')
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [nl2IntentMsg])

  useEffect(() => {
    if (!close && isBIAgent && chat.ask && !fetchingChatProgress && !isBaoWu(metricConfig?.metricTableName)) {
      runFetchChatProgress()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [close, runFetchChatProgress, isBIAgent, chat.ask])

  const defaultSceneId = chat.ans[0]?.sceneId ?? currentDataset?.sceneId ?? ''
  const [currentSelectedSceneId, setCurrentSelectedSceneId] = useState<string>(defaultSceneId)

  useEffect(() => {
    if (isProjectChosen && defaultSceneId !== MULTI_SCENE_CHAT_MOCK_SCENE_ID) {
      setCurrentSelectedSceneId(defaultSceneId)
    }
  }, [defaultSceneId, isProjectChosen])

  // 此为该回答的后续提问，点击事件
  const handleAskForSourceAnswerClick = (id: string | null) => {
    if (id) {
      const targetDiv = document.getElementById(id)
      if (targetDiv) {
        targetDiv.classList.add('animate-pulse-border')
        targetDiv.scrollIntoView({ behavior: 'smooth', block: 'center' })
        setTimeout(() => {
          targetDiv.classList.remove('animate-pulse-border')
        }, 2000) // 这里的时间应与动画时间保持一致
      }
    }
  }

  const BiResultWrapper = (props: { children: JSX.Element | React.ReactNode; contentClassName?: string }) => {
    const { children, contentClassName } = props
    return (
      <div className="chat-item chat-item-ans group flex flex-col md:flex-row">
        <div
          id={chat.id}
          className={clsx(
            'left-content flex w-full max-w-full flex-col items-start gap-2 self-start rounded-lg bg-white dark:bg-slate-800 md:ml-11',
            contentClassName,
          )}
        >
          {children}
        </div>
      </div>
    )
  }

  const scenesChange = (
    <div className="multi-scene-list flex flex-wrap gap-2 px-6 py-4">
      {!chat.isSystemPrompt &&
        chat.ans[0]?.sceneId !== MULTI_SCENE_CHAT_MOCK_SCENE_ID &&
        chat.ans.map((e) => {
          return (
            <div
              className={clsx('flex w-fit cursor-pointer items-center gap-2 rounded-lg border px-3 py-2', {
                'border-[#E1DEF5] bg-[#E1DEF5]': currentSelectedSceneId === e.sceneId,
                'border-[#DEDEDE]': currentSelectedSceneId !== e.sceneId,
              })}
              key={e.sceneId}
              onClick={() => {
                setCurrentSelectedSceneId(e.sceneId)
              }}
            >
              <SvgIcon icon={scenesListIcon} className="h-4 w-4" />
              {currentDataset?.projectName}_
              {
                semanticProjectInfo
                  .find((c) => c.id === currentDataset?.projectId)
                  ?.scenes.find((m) => m.id === e.sceneId)?.label
              }
            </div>
          )
        })}
    </div>
  )
  const chatAnsItem = chat.ans.find((e) => e.sceneId === currentSelectedSceneId) || chat.ans[0]

  const tabItems = [
    {
      key: 'Ask-BI',
      label: 'BI',
      children: (
        <div className="ask-bi-result-content">
          {/* 宝武不展示场景切换按钮 */}
          {isProjectChosen &&
            !chat.isSystemPrompt &&
            chat.ans[0]?.sceneId !== MULTI_SCENE_CHAT_MOCK_SCENE_ID &&
            !isBaoWu(metricConfig?.metricTableName) &&
            scenesChange}
          {IS_H5 && chat?.stateMap?.currentConfidenceSelection && (
            <MetricForceMatchComp
              setCurrentSelectedSceneId={setCurrentSelectedSceneId}
              currentConfidenceSelection={chat.stateMap.currentConfidenceSelection}
              chatId={chat.id}
            />
          )}
          <BiResultContent
            theme={theme}
            currentSelectedSceneId={currentSelectedSceneId}
            currentChat={chat}
            chatAnsItem={chatAnsItem}
            projectId={currentDataset?.projectId ?? ''}
            chartWrapperRef={chartWrapperRef}
          />
        </div>
      ),
      show: isBIAgent,
    },
    {
      key: 'Ask-Doc',
      label: 'Doc',
      children: (
        <div className={clsx('chat-item chat-item-ans group flex w-full flex-col md:ml-7 md:mr-1 md:flex-row md:pr-8')}>
          <div
            id={chat.id + 'doc'}
            className="left-content flex w-full flex-col items-start gap-2 self-start rounded-lg bg-white px-2.5 py-4 dark:bg-slate-800"
          >
            {(chat.docAns.content || []).map((content, idx) => {
              switch (content.type) {
                case 'hello-text':
                  return renderChatAnsHelloTextItem(content.text, idx)
                case 'text':
                  return renderChatAnsTextItem(chat.docAns as AnsChatItem, content, idx)
                case 'doc-result':
                  return <AnswerView key={`${chat.id}-${idx}`} content={content} status={chat.docAns.status} />
                default:
                  return assertExhaustive(content)
              }
            })}
          </div>
        </div>
      ),
      show: isDocAgent,
    },
  ]

  const RenderCustomTabBar = (props: any) => {
    const tabInfo: { key: string; title: string }[] = []
    props.panes.forEach((item: any) => {
      tabInfo.push({ key: item.key, title: item.props.tab })
    })
    if (onlyBIAgent) {
      return <React.Fragment />
    }
    return (
      <>
        <div className="flex gap-3 px-6 py-3">
          {tabInfo.map((item) => {
            if (item.key === 'Ask-BI') {
              return (
                <div
                  key={item.key}
                  className="relative flex cursor-pointer items-center gap-1 px-1"
                  onClick={() => {
                    setActiveTab('Ask-BI')
                  }}
                >
                  <SvgIcon icon={agentAskBIIcon} className="h-4 w-4" />
                  <span className="tracking-wider">AskBI</span>
                  {activeTab === 'Ask-BI' && (
                    <div className="absolute -bottom-[13px] w-full border-b-2 border-[#171717]" />
                  )}
                </div>
              )
            } else if (item.key === 'Ask-Doc') {
              return (
                <div
                  key={item.key}
                  className="relative flex cursor-pointer items-center gap-1 px-1"
                  onClick={() => {
                    setActiveTab('Ask-Doc')
                  }}
                >
                  <SvgIcon icon={agentAskDocIcon} className="h-4 w-4" />
                  <span className="tracking-wider">AskDoc</span>
                  {activeTab === 'Ask-Doc' && (
                    <div className="absolute -bottom-[13px] w-full border-b-2 border-[#171717]" />
                  )}
                </div>
              )
            }
          })}
        </div>
        <Divider className="my-0" />
      </>
    )
  }
  useEffect(() => {
    if (['占比', '同环比', '预测'].includes(chatProgress.nl2IntentMsg?.intent ?? '')) {
      if (intervalId) clearInterval(intervalId)
      setChats(
        produce((draft) => {
          const theChat = draft.find((item) => item.id === chat.id)
          if (theChat) {
            const newAns = theChat.ans.map((ans) => {
              return {
                ...ans,
                content: [{ type: 'calculator' as const, data: new CalculatorData() }],
                status: ChatStatus.failure,
              }
            })
            theChat.ans = newAns
          }
        }),
      )
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chatProgress.nl2IntentMsg?.intent])

  return (
    <ChatHistoryItemContext.Provider value={{ whereEditForm, chat, chatProgress }}>
      <Fragment>
        {chat.ask && (
          <div className="chat-item-ask flex items-center self-start">
            <img
              className="user-avatar mr-3.5 hidden h-8 w-8 cursor-pointer md:block"
              src={brandInfo.chatUserIcon}
              alt=""
            />
            <div className="rounded-b-xl rounded-tr-xl bg-primary px-5 py-2 text-white">
              <div className="flex items-center justify-between gap-3">
                <div className="text-base leading-7">{chat.ask.content}</div>
                <ChatHistoryItemAskEdit chat={chat} />
              </div>
              {chat.ask.parentId != null && (
                <p className="mt-1 text-xs tracking-wider">
                  此为
                  <span
                    className="cursor-pointer px-1 italic tracking-wider underline"
                    style={{ fontSynthesis: 'style' }} // 斜体
                    onClick={() => {
                      handleAskForSourceAnswerClick(chat.ask.parentId || '')
                    }}
                  >
                    该回答
                  </span>
                  的后续提问
                </p>
              )}
            </div>
          </div>
        )}
        {chat.isSystemPrompt ? (
          <>
            {!IS_H5 && (
              <>
                <BiResultWrapper contentClassName="px-6 py-4">
                  {renderChatAnsHelloTextItem((chat.ans[0].content[0] as AssistantHelloTextChatItem).text, 9999)}
                </BiResultWrapper>
                {/* FIXME: 宝武不需要维度列表，临时去掉 */}
                {!isBaoWu(metricConfig?.metricTableName) && isBIAgent && (
                  <BiResultWrapper contentClassName="px-6 py-4">
                    <MetricStoreOverview metricConfig={metricConfig} />
                  </BiResultWrapper>
                )}
              </>
            )}
          </>
        ) : chat.ans.length > 0 ? (
          <BiResultWrapper>
            <Tabs
              className="w-full"
              renderTabBar={RenderCustomTabBar}
              items={tabItems.filter((tabItem) => tabItem.show === true)}
              activeKey={activeTab}
              onChange={(active) => {
                setActiveTab(active as TabType)
                active === 'ask-bi' && setDocPreviewOpenTag(false)
              }}
            />
          </BiResultWrapper>
        ) : null}
      </Fragment>
    </ChatHistoryItemContext.Provider>
  )
}

export const BiResultContent = React.memo(function BiResultContent(props: {
  theme: ThemeType
  currentChat: Chat
  chatAnsItem: AnsChatItem
  projectId: string
  currentSelectedSceneId: string
  chartWrapperRef: React.RefObject<{
    downloadPNG: () => void
    copyPNG: () => void
    getBlob: () => Promise<BlobWithRatio | string>
  }>
  // 是否是查看模式, 目前只有实体-提问历史详情用到,不需要底部操作栏
  isViewMode?: boolean
  className?: string
}) {
  const {
    theme,
    currentChat,
    chatAnsItem,
    projectId,
    currentSelectedSceneId,
    chartWrapperRef,
    isViewMode = false,
    className,
  } = props

  const { message: antdMessage } = App.useApp()
  const setChats = useSetAtom(chatsAtom)

  const defaultMetricConfig = useAtomValue(metricConfigAtom)
  const metricConfigResponseForProject = useAtomValue(metricConfigOfProjectAtom)
  const metricConfig =
    currentSelectedSceneId === MULTI_SCENE_CHAT_MOCK_SCENE_ID || metricConfigResponseForProject === null
      ? defaultMetricConfig
      : metricConfigResponseForProject?.find((e) => e.sceneId === currentSelectedSceneId)?.data || null
  const currentChartThemeType = useAtomValue(currentChartThemeTypeAtom)

  const semanticProjectInfo = useAtomValue(semanticProjectInfoAtom)
  const currentDataset = extractDatasetInfo({ semanticProjectInfo, projectId, sceneId: currentSelectedSceneId })

  const llmType = useAtomValue(llmTypeAtom)
  const chatQueryParamsChange = useSetAtom(chatQueryParamsChangeAtom)
  const setCurrentFollowUpQuestion = useSetAtom(currentFollowUpQuestionAtom)
  const setCurrentFollowUpSceneId = useSetAtom(currentFollowUpSceneIdAtom)
  const brandInfo = useAtomValue(brandInfoAtom)

  // 用户手动输入图表标题 默认值为[用户的问题]
  const [chartTitle, setChartTitle] = useState<string>(currentChat.ask?.content || '')

  // 如果有type=“chart”的元素 就展示buttons
  const isShowOperationButtons = chatAnsItem.content.some(({ type }) => type === 'chart')
  const mainChart = chatAnsItem.content.find((obj) => obj.type === 'chart') as AssistantChartChatItem
  const mainChartInsight = chatAnsItem.content.find((obj) => obj.type === 'chart-insight')

  const infoTexts =
    currentChat.ans[0].content[0].type === 'chart' ? (currentChat.ans[0].content[0].infoTexts as string[]) : []

  // 检查当前项目名称是否在禁用列表中
  const isProjectNameDisabled = DISABLE_INSIGHT_PROJECT_NAME_LIST.some((name) =>
    currentDataset?.projectName.includes(name),
  )

  const [showChartInsight, setShowChartInsight] = useState(
    brandInfo.brandName === 'China Telecom' ||
      mainChart?.chartType === 'AttrMetricAnalysis' ||
      !!mainChartInsight?.text,
  )
  const [showSql, setShowSql] = useState(false)

  // 当满足单个按钮的展示条件时 才展示
  const showFollowUpButton = mainChart && mainChart.chartType !== 'AttrAnalysis'
  const showSwitchChartButton = mainChart && mainChart.recommendChartTypes.length > 1
  const showCharInsightButton = chatAnsItem.content.some(({ type }) => type === 'chart-insight')
  const showSqlButton =
    !isBaoWu(metricConfig?.metricTableName) && chatAnsItem.content.some(({ type }) => type === 'sql')

  const makeAgentRequest = useSetAtom(makeAgentRequestAtom)
  const handleSubmit = (message: string) => {
    makeAgentRequest({
      isMiniMode: false,
      newMessage: message,
    })
  }

  /** 更改图表类型 */
  const handleChartTypeChange = useCallback(
    (newChartType: ChartType, idx: number) => {
      console.info('change chart type to', newChartType)
      const newChat = produce(currentChat, (draft) => {
        const currentAns = draft.ans.find((m) => m.sceneId === currentSelectedSceneId)
        if (!currentAns) {
          return
        }
        const data = currentAns.content[idx] as AssistantChartChatItem
        data.chartType = newChartType
      })
      setChats(produce((draft) => draft.map((item) => (item.id === currentChat.id ? newChat : item))))
    },
    [currentChat, currentSelectedSceneId, setChats],
  )

  const onChartTypeChange = (value: ChartType) => {
    handleChartTypeChange(value, 0)
  }

  const handleGetPNGBlob = useCallback(async () => {
    return await chartWrapperRef.current?.getBlob()
  }, [chartWrapperRef])

  const handleDownloadPNG = useCallback(() => {
    chartWrapperRef.current?.downloadPNG()
  }, [chartWrapperRef])

  // 这里由于H5对复制图片有限制，所以H5移动端时，走下载图片的逻辑
  const handleCopyPNG = useCallback(() => {
    if (IS_H5) {
      handleDownloadPNG()
    } else {
      chartWrapperRef.current?.copyPNG()
    }
  }, [chartWrapperRef, handleDownloadPNG])

  const handleFeedbackChange = useCallback(
    (newChat: Chat, oldChat: Chat) => {
      setChats(produce((draft) => draft.map((item) => (item.id === oldChat.id ? newChat : item))))
      antdMessage.success('感谢您的反馈，我们会努力做得更好！')
    },
    [antdMessage, setChats],
  )

  // 处理排序的数据 将排序的数据存到chat中
  const handleChangTableData = useCallback(
    (sortedData: OlapRow[]) => {
      setChats(
        produce((draft) => {
          const item = draft.find((item) => item.id === currentChat.id)
          if (!item) return
          const currentAnsIndex = item.ans.findIndex((m) => m.sceneId === currentSelectedSceneId)
          if (currentAnsIndex === -1) return
          const chartIndex = item.ans[currentAnsIndex].content.findIndex((e) => e.type === 'chart')
          if (chartIndex !== -1) {
            const chartItem = item.ans[currentAnsIndex].content[chartIndex] as AssistantChartChatItem
            chartItem.rows = sortedData
          }
        }),
      )
    },
    [currentChat.id, currentSelectedSceneId, setChats],
  )

  const renderFormattedSql = (idx: number, formattedSql: string) =>
    IS_H5 ? (
      <Drawer
        title="返回"
        placement="bottom"
        width="100%"
        height="100%"
        open={showSql}
        onClose={() => {
          setShowSql(false)
        }}
        extra={
          <div
            className="text-link"
            onClick={() => {
              copyToClipboard(formattedSql)
                ? antdMessage.success('SQL 已复制到粘贴板')
                : antdMessage.error('SQL 复制失败，请手动复制')
              setShowSql(false)
            }}
          >
            复制
          </div>
        }
      >
        <SyntaxHighlighter
          language="sql"
          style={theme === 'dark' ? darkTheme : githubTheme}
          customStyle={{ background: 'bg-slate-50 dark:bg-slate-700', padding: 0 }}
        >
          {formattedSql}
        </SyntaxHighlighter>
      </Drawer>
    ) : (
      <div className="max-w-full" key={idx}>
        <span className={titleClassName}>SQL语句</span>
        <div className="card-show-sql group relative max-w-full rounded-lg bg-slate-50 p-4 pr-10 text-left text-xs dark:bg-slate-700">
          <div
            className="invisible absolute bottom-1 right-2 flex cursor-pointer flex-row items-center hover:text-primary group-hover:visible"
            title="复制SQL"
            onClick={() => {
              copyToClipboard(formattedSql)
                ? antdMessage.success('SQL 已复制到粘贴板')
                : antdMessage.error('SQL 复制失败，请手动复制')
            }}
          >
            <CopyOutlined className="ml-4 h-4 w-4" />
            复制
          </div>
          <SyntaxHighlighter
            language="sql"
            style={theme === 'dark' ? darkTheme : githubTheme}
            customStyle={{ background: 'bg-slate-50 dark:bg-slate-700', padding: 0 }}
          >
            {formattedSql}
          </SyntaxHighlighter>
        </div>
      </div>
    )

  const renderRelatedQuestionItem = (content: AssistantRelatedQuestionItem, idx: number) => {
    const { suggestionQuestions, reason } = content
    const questionClassName =
      'w-fit cursor-pointer rounded-lg border bg-white px-2 py-1 hover:bg-slate-50 dark:hover:bg-slate-700'
    return (
      <div className="related-question-wrapper" key={idx}>
        <div className="mb-2 flex w-fit items-center gap-2 rounded-lg bg-yellow-100 px-2 py-1 text-base font-semibold dark:bg-yellow-700">
          <SvgIcon icon={circleInfoIcon} className="h-5 w-5" />
          <span>{reason}</span>
        </div>
        <div className="mx-6 flex flex-col gap-2">
          {suggestionQuestions &&
            suggestionQuestions.map((question, index) => (
              <div key={index} onClick={() => handleSubmit(question)} className={questionClassName}>
                {question}
              </div>
            ))}
        </div>
      </div>
    )
  }

  // 数据解读
  const handleChartInsight = () => {
    setShowChartInsight(!showChartInsight)
  }

  // SQL语句
  const handleSQL = () => {
    setShowSql(!showSql)
  }

  // 图表嵌入点击事件
  const handleClickEmbed = useCallback(async () => {
    if (!mainChart) {
      return
    }
    // FIXME: 这里有安全问题，这里面的 sql 需要从后端来设置
    const chartToCreate: Omit<ChartCreateInput, 'username'> = {
      chartType: mainChart.chartType,
      chartTitle,
      recommendChartTypes: mainChart.recommendChartTypes.join(','),
      sql: mainChart.sql || '',
      rowsMetadata: JSON.stringify(mainChart.rowsMetadata),
      chartThemeType: currentChartThemeType,
      llmType: llmType,
      taskType: mainChart.taskType,
      semanticSceneId: mainChart.sceneId,
      queryParams: mainChart.queryParamsVerified?.queryParams as Prisma.NullTypes.JsonNull | Prisma.InputJsonValue,
      ask: currentChat.ask?.content || '',
    }

    await axios
      .post(askBIApiUrls.charts.create, { ...chartToCreate })
      .then((createResponse) => {
        console.info('保存图表的返回为 =>', createResponse.data)
        const editChartId = createResponse.data.data.id
        setTimeout(() => {
          const hostPart = window.location.href.match(/^(https?:\/\/[^/]+)/) || ['']
          const copyResult = copyToClipboard(hostPart[0] + askBIPageUrls.metricStore.charts.embed(editChartId))
          antdMessage.info(`成功保存图表，复制嵌入链接到粘贴板${copyResult ? '成功' : '失败'}`, 0.5)
        }, 0)
      })
      .catch((error) => {
        antdMessage.error('创建图表失败！' + error.message, 0.5)
      })
  }, [mainChart, chartTitle, currentChartThemeType, llmType, currentChat, antdMessage])

  const handleExportReport = useCallback(async () => {
    try {
      const { generateDocxContentFromChatAns, generateDocxFromContent } = await import('./reportExport')
      const options =
        mainChart.chartType === 'SimpleTable'
          ? { rows: mainChart.rows.slice(0, 20) as { [key: string]: string }[] }
          : {
              rows:
                mainChart.chartType === 'AttrMetricAnalysis'
                  ? []
                  : (mainChart.rows.slice(0, 20) as { [key: string]: string }[]),
              blob: await handleGetPNGBlob(),
            }

      const docxContent = generateDocxContentFromChatAns(
        currentChat.ask,
        currentChat.ans.find((i) => i.sceneId === currentSelectedSceneId)?.content || [],
        options,
      )

      generateDocxFromContent(docxContent)
    } catch (error) {
      console.error('An error occurred when export report:', error)
      antdMessage.error('导出报告失败！')
    }
  }, [mainChart, handleGetPNGBlob, currentChat, currentSelectedSceneId, antdMessage])

  const renderChatChartItem = (content: AssistantChartChatItem, idx: number, isViewMode: boolean = false) => {
    return (
      <div className="flex w-full flex-col items-start" key={idx}>
        <Card
          isViewMode={isViewMode}
          // 默认撑满 大屏幕最小宽度为532px
          className="w-full md:min-w-[532px]"
          title={content.chartTitle}
          queryParamsVerified={content.queryParamsVerified}
          assistantChartChatItem={content}
          chartType={content.chartType}
          metricConfig={metricConfig}
          onQueryParamsChange={(queryParamsVerified) => {
            chatQueryParamsChange({
              queryParamsVerified,
              infoTexts: content.infoTexts,
              chatId: currentChat.id,
              sceneId: currentSelectedSceneId,
            })
          }}
          currentChat={currentChat}
          originalChartType={content.originalChartType}
          recommendChartTypes={content.recommendChartTypes}
          theme={theme}
          data={content.rows}
          onDownloadPNG={handleDownloadPNG}
          onCopyPNG={handleCopyPNG}
          onChartTypeChange={(newValue: ChartType) => {
            handleChartTypeChange(newValue, idx)
          }}
        >
          <ChartWrapper ref={chartWrapperRef} data={content} theme={theme} onTableDataChange={handleChangTableData} />
        </Card>
      </div>
    )
  }
  const renderSwitchChartDropdown = () => {
    return (
      <IconButtonGroup<ChartType>
        options={getVisibleChartIconMap(mainChart?.recommendChartTypes)}
        value={mainChart?.chartType}
        onChange={onChartTypeChange}
      />
    )
  }

  /** 渲染消息窗口底部操作按钮 */
  const renderOperationContent = () => {
    const iconStyle = 'h-4 w-4'
    const commonStyle =
      'flex cursor-pointer items-center rounded-md px-2 py-2 ring-1 ring-inset ring-gray-300 hover:bg-primary/60 hover:text-white dark:text-gray-400 dark:ring-gray-500 dark:hover:text-white'
    return (
      <>
        <div className="operation-list flex w-full items-center justify-between">
          {IS_H5 ? (
            <div className="flex w-full flex-nowrap gap-1">
              {showSwitchChartButton && (
                <Dropdown placement="top" dropdownRender={renderSwitchChartDropdown}>
                  <div className="rounded-lg p-2 text-link">切换图表</div>
                </Dropdown>
              )}
              {showSqlButton && (
                <div
                  className={clsx('rounded-lg p-2 text-link', {
                    'bg-[#DDD9F2]': showSql,
                  })}
                  onClick={handleSQL}
                >
                  查看SQL
                </div>
              )}
            </div>
          ) : (
            <div className="flex w-full flex-nowrap gap-1">
              {showFollowUpButton && (
                <div
                  className={commonStyle}
                  onClick={() => {
                    setCurrentFollowUpQuestion(currentChat)
                    setCurrentFollowUpSceneId(currentSelectedSceneId)
                    setTimeout(scrollToBottom, 100)
                  }}
                >
                  <SvgIcon icon={followUpAskIcon} className="mr-0.5 h-4 w-4" />
                  跟进提问
                </div>
              )}
              {showSwitchChartButton && (
                <Dropdown placement="top" dropdownRender={renderSwitchChartDropdown}>
                  <div className={commonStyle}>
                    <InteractionOutlined className={iconStyle} />
                    切换图表
                  </div>
                </Dropdown>
              )}
              {showSqlButton && (
                <div className={commonStyle} onClick={handleSQL}>
                  <ConsoleSqlOutlined className={iconStyle} />
                  SQL
                </div>
              )}
              {DISPLAY_INSIGHT && !isProjectNameDisabled && showCharInsightButton && (
                <>
                  <div className={commonStyle} onClick={handleChartInsight}>
                    <ReadOutlined className={iconStyle} />
                    {/* 这里的数据解读 重命名为[分析报告] */}
                    分析报告
                  </div>
                  <div className={commonStyle} onClick={handleExportReport}>
                    <SvgIcon icon={downloadReportIcon} className="h-4 w-4" />
                    下载报告
                  </div>
                </>
              )}
              {mainChart && (
                <>
                  {isSecureEnvironment() && (
                    <ChartCopy
                      chartType={mainChart.chartType}
                      tableData={mainChart.rows}
                      rowsMetadata={mainChart.rowsMetadata}
                      onCopyPNG={handleCopyPNG}
                    />
                  )}
                  <ChartDownload
                    onDownloadPNG={handleDownloadPNG}
                    chartType={mainChart.chartType}
                    tableData={mainChart.rows}
                  />
                  {mainChart.chartType !== 'AttrAnalysis' && mainChart.chartType !== 'AttrMetricAnalysis' && (
                    <Popconfirm
                      title="确认图表标题"
                      description={
                        <Input
                          value={chartTitle}
                          onChange={(e) => {
                            setChartTitle(e.target.value)
                          }}
                        />
                      }
                      onConfirm={handleClickEmbed}
                      okText="确认"
                      cancelText="取消"
                    >
                      <div className={commonStyle}>
                        <ArrowUpOnSquareIcon className={iconStyle} />
                        保存图表
                      </div>
                    </Popconfirm>
                  )}
                </>
              )}
            </div>
          )}
          {!isBaoWu(metricConfig?.metricTableName) &&
            chatAnsItem.status === 'success' &&
            !currentChat.isSystemPrompt && (
              <div className="right-actions flex w-12 shrink-0 flex-col justify-end">
                <ChatBoxFeedback<Chat> chat={currentChat} onChange={handleFeedbackChange} />
              </div>
            )}
        </div>
        <div className="operation-list flex w-full items-center justify-end">
          <ChatDataErrorFeedback chatId={currentChat.id} />
        </div>
      </>
    )
  }

  return (
    <div
      className={clsx(
        'BI-result-content flex flex-col gap-4 break-all py-4',
        { 'px-6': !chatAnsItem.content.some((chatItem) => chatItem.type === 'query-external-report') },
        className,
      )}
    >
      {infoTexts.length > 0 && (
        <div className="flex w-full items-center rounded-[8px] bg-[#DBF6F1] p-4">
          <InfoCircleOutlined className="mr-1 h-4 w-4" />
          {infoTexts.map((infoText) => (
            <span key={infoText} className="mr-2">
              {infoText}
            </span>
          ))}
        </div>
      )}

      {/* 宝武连续提问, 增加只返回第一个问题的答案提示↓ */}
      {isBaoWu(metricConfig?.metricTableName) && chatAnsItem.content?.[0] && (
        <SeriesQuestionNotice data={chatAnsItem.content[0]} />
      )}
      {/* 数据不全提示↓ */}
      {chatAnsItem.content?.[0] && <PartialDataNotice data={chatAnsItem.content[0]} />}
      {(chatAnsItem.content || []).map((content, idx) => {
        switch (content.type) {
          case 'hello-text':
            return renderChatAnsHelloTextItem(content.text, idx)
          case 'text':
            return renderChatAnsTextItem(chatAnsItem, content, idx)
          case 'chart-insight':
            if (showChartInsight) {
              return <DataInsight content={content} idx={idx} />
            }
            return null
          case 'chart':
            return renderChatChartItem(content, idx, isViewMode)
          case 'sql':
            if (showSql) {
              try {
                return renderFormattedSql(idx, sqlFormatter(content.sql, { indentStyle: 'tabularLeft' }))
              } catch (error) {
                console.error('sql format失败', error)
                return renderFormattedSql(idx, content.sql)
              }
            }
            return null
          case 'related-question':
            return renderRelatedQuestionItem(content, idx)
          case 'not-exist-metric':
            return renderNotExistMetric(content, idx)
          case 'data-overview':
            return <DataOverview key={idx} traceId={chatAnsItem.traceId || ''} />
          case 'dimension-list':
            return <DimensionList key={idx} />
          case 'metric-list':
            return <MetricList key={idx} />
          case 'table-list':
            return <TableList key={idx} />
          case 'metric-detail':
            return <MetricDetail key={idx} metricName={content.rows} />
          case 'dimension-detail':
            return <DimensionDetail key={idx} metricConfig={metricConfig} dimensionName={content.rows} />
          case 'metric-tree': {
            return renderChatAnsMetaItem(chatAnsItem, content, idx)
          }
          case 'llm-error':
          case 'chitchat': {
            return renderChatAnsTextItem(chatAnsItem, content, idx)
          }
          case 'doc-report': {
            return (
              <DocReportAnalyze key={idx} currentChatId={currentChat.id} chatAnsItem={chatAnsItem} content={content} />
            )
          }
          case 'chat-error': {
            if (
              (chatAnsItem.content[0] as AssistantChatError).errType === ChatResponseErrorTypes.LOOKUP_FAILED ||
              (chatAnsItem.content[0] as AssistantChatError).errType === ChatResponseErrorTypes.CALCULATE_FAILED
            ) {
              return <Calculator key={idx} chat={currentChat} />
            }
            return (
              <div key={idx}>
                <ChatError content={content} biResultContentProps={props} currentDataset={currentDataset} />
              </div>
            )
          }
          case 'query-external-report':
            return (
              <ExternalReport
                key={idx}
                externalReports={content.externalReports}
                metricConfig={metricConfig}
                where={content.where}
                timeQueryParams={content.timeQueryParams}
                chatId={currentChat.id}
              />
            )
          case 'metric-force-match': {
            return (
              <MetricForceMatch
                key={idx}
                chatId={currentChat.id}
                isViewMode={isViewMode}
                metricConfig={metricConfig}
                onQueryParamsChange={(queryParamsVerified, sceneId) => {
                  chatQueryParamsChange({ queryParamsVerified, chatId: currentChat.id, sceneId })
                }}
              />
            )
          }
          case 'calculator': {
            return <Calculator key={idx} chat={currentChat} />
          }
          case 'deepseek':
            return <DeepSeekAssistant key={idx} />
          default:
            return assertExhaustive(content)
        }
      })}
      {!isViewMode && isShowOperationButtons && renderOperationContent()}
    </div>
  )
})

function extractDatasetInfo({
  semanticProjectInfo,
  projectId,
  sceneId,
}: {
  semanticProjectInfo: ProjectType[]
  projectId: string
  sceneId: string
}): DatasetDatum | null {
  const projectInfo = semanticProjectInfo.find((p) => p.id === projectId) as ProjectType
  const scene = projectInfo.scenes?.find((s) => s.id === sceneId) as SceneType

  return {
    projectId,
    sceneId,
    projectName: projectInfo.name,
    sceneLabel: scene?.label,
    tableName: scene?.tableName,
    enableFollowUpQuestion: scene?.enableFollowUpQuestion,
    enableMetricExactMatch: scene?.enableMetricExactMatch,
    enableTryQueryUp: scene?.enableTryQueryUp,
    enableSelectToastWhenEmptyData: scene?.enableSelectToastWhenEmptyData,
    enableAccMetricToastWhenEmptyData: scene?.enableAccMetricToastWhenEmptyData,
  }
}

export default React.memo(ChatHistoryItem)
