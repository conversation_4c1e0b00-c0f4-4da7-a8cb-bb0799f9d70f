import React, { useEffect, useRef, useState } from 'react'
import { Upload, message, Typography, type UploadFile, Modal, ModalProps, Button, Result } from 'antd'
import { InboxOutlined, EyeOutlined } from '@ant-design/icons'
import { useRequest } from 'ahooks'
import axios from 'axios'
import UploadErrorModal from '@components/UploadErrorModal'
import { TOKEN_RANGER } from 'src/shared/constants'

function viewFile(file: File | UploadFile) {
  const f = file instanceof File ? file : file?.originFileObj
  if (!(f instanceof File)) return
  const fileUrl = URL.createObjectURL(f)
  window.open(fileUrl)
}

function validateFileExt(file: File, exts?: string[]): boolean {
  if (!file) return false
  if (!exts || exts.length <= 0) return true
  const fileExt = file.name.match(/\.[^.]+$/)?.pop()
  if (!fileExt || !exts?.includes(fileExt)) {
    return false
  }
  return true
}

async function downloadFile(fileUrl: string, filename: string) {
  const response = await axios.get(fileUrl, {
    headers: { TOKEN_RANGER: `${localStorage.getItem(TOKEN_RANGER)}` },
    responseType: 'blob',
  })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(response.data)
  link.download = filename
  link.click()
  URL.revokeObjectURL(link.href)
  link.remove()
  void message.success('下载成功')
}

function validateBeforeUpload(file: File, size?: number, acceptTypes?: string[]) {
  const valid = [
    () => {
      const v = validateFileExt(file, acceptTypes)
      if (!v) {
        message.error('上传文件后缀名不符')
      }
      return v
    },
    () => {
      if (typeof size !== 'number') {
        return true
      }
      const MAX_FILE_SIZE = size * 1024 * 1024
      const fileSize = file.size
      const v = fileSize <= MAX_FILE_SIZE
      if (!v) {
        message.error(`上传文件大小超${size}MB`)
      }
      return v
    },
  ].every((bool) => bool())
  if (!valid) {
    return Upload.LIST_IGNORE
  }
}

type PropsType = {
  className?: string
  size?: number
  acceptTypes?: string[]
  samples?: { fileName: string; fileUrl: string }[]
  uploadApi: (file: File) => unknown
  modalProps: ModalProps
  onSuccess?: () => void
  children?: React.ReactNode
}
// 自定义上传，上传之后显示小眼睛，走自己上传的路线
export default function ValidatedUploadFile({
  uploadApi,
  size,
  acceptTypes,
  className,
  samples,
  onSuccess,
  modalProps,
  children,
}: PropsType) {
  const [fileList, setFileList] = useState<UploadFile<any>[]>([])
  const [uploadErrorModalOpen, setUploadErrorModalOpen] = useState(false)
  const [process, setProcess] = useState<'upload' | 'result'>('upload')
  const currentRemoveUid = useRef('')
  const {
    loading,
    error,
    run: upload,
  } = useRequest(
    async () => {
      const file = fileList[0]
      if (file) {
        await uploadApi(file.originFileObj as File)
      }
    },
    {
      manual: true,
      onSuccess() {
        onSuccess?.()
      },
      onError() {
        setProcess('result')
        setUploadErrorModalOpen(true)
      },
    },
  )
  useEffect(() => {
    if (!modalProps.open) {
      setFileList([])
      setProcess('upload')
      setUploadErrorModalOpen(false)
    }
  }, [modalProps.open])
  return (
    <Modal
      className={className}
      {...modalProps}
      okButtonProps={{
        ...(modalProps?.okButtonProps || {}),
        loading,
      }}
      onOk={(e) => {
        modalProps.onOk && modalProps.onOk(e)
        upload()
      }}
    >
      {process === 'upload' && (
        <Upload.Dragger
          fileList={fileList}
          className={className}
          listType="text"
          maxCount={1}
          accept={acceptTypes?.join(',')}
          showUploadList={{
            showDownloadIcon: true,
            downloadIcon: <EyeOutlined />,
          }}
          customRequest={() => false}
          onDownload={viewFile}
          beforeUpload={(file) => validateBeforeUpload(file, size)}
          onChange={({ file }) => {
            if (file.uid === currentRemoveUid.current) {
              setFileList([])
              currentRemoveUid.current = ''
            } else {
              setFileList([Object.assign(file, { status: 'done' })])
            }
          }}
          onRemove={(removeFile) => {
            currentRemoveUid.current = removeFile.uid
          }}
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">点击或将文件拖拽到此区域进行上传</p>
          <p className="ant-upload-hint">支持扩展名为：{acceptTypes?.map((t) => `*${t}`).join('、')}文件</p>
          {size && (
            <p>
              <Typography.Text mark>tips：单次上传数量限制在{size}MB以内</Typography.Text>
            </p>
          )}

          {samples && samples.length > 0 && (
            <p className="ant-upload-hint">
              示例文件下载：
              {samples.map((sample, index: number) => (
                <a
                  key={index}
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    downloadFile(sample.fileUrl, sample.fileName)
                  }}
                >
                  {`${sample.fileName}${index < samples.length - 1 ? '、' : ''}`}
                </a>
              ))}
            </p>
          )}
          {children}
        </Upload.Dragger>
      )}
      {process === 'result' && (
        <div className={`flex min-h-[180px] items-center justify-center py-8`}>
          {!loading && error && (
            <Result
              status="error"
              title="数据导入失败"
              extra={[
                <Button key="errMsg" onClick={() => setUploadErrorModalOpen(true)}>
                  查看错误原因
                </Button>,
                <Button
                  type="primary"
                  key="console"
                  onClick={() => {
                    setProcess('upload')
                  }}
                  loading={loading}
                >
                  重新上传文件
                </Button>,
              ]}
            />
          )}
        </div>
      )}
      <UploadErrorModal
        open={uploadErrorModalOpen}
        onClose={() => setUploadErrorModalOpen(false)}
        errMsg={(error as Error & { msg: string })?.msg ?? error?.message ?? '暂无更多信息'}
      />
    </Modal>
  )
}
