import React, { useState, useMemo, useCallback, useRef } from 'react'
import { App, Modal, Steps, Form, Spin, type FormInstance, Result, But<PERSON>, Popconfirm } from 'antd'
import { CheckCircleOutlined, CloseOutlined } from '@ant-design/icons'
import { useRequest } from 'ahooks'
import { produce } from 'immer'
import { clsx } from 'clsx'
import UploadErrorModal from '@components/UploadErrorModal'
import request from 'src/shared/xengineAxios'
import { askBIApiUrls } from 'src/shared/url-map'
import { objectToFormData, CancelTokenControl } from 'src/shared/common-utils'
import UploadFile from './UploadFile'
import ValidateData from './ValidateData'

type InferColumnType = {
  auth: boolean
  columnPrecision: number
  columnScale: number
  columnType: string
  name: string
  notAllowNull: boolean
}
type ColumnType = { name: string; columnType: string; columnScale?: number; columnPrecision?: number }
function formatDataToCreatePTableParams(
  form: FormInstance<any>,
  selectedColumns: {
    name: string
    columnType: string
  }[],
) {
  const values = form.getFieldsValue()
  const params = {
    table: {
      primaryKeys: [values.primaryKeys],
      distributedByHash: {
        keys: [values.distributedKeys],
      },
      name: values.name,
      catalogName: 'dipeak',
      databaseName: 'csv',
      tableEngine: 'MergeTree',
      tableType: 'MANAGED_TABLE',
      columns: selectedColumns,
    },
    cascadeCreateVTable: true,
  }
  return params
}

const StepItems = [
  {
    title: '上传文件',
    description: '请上传csv文件',
  },
  {
    title: '校验数据',
    description: '检查数据格式是否正确',
  },
  {
    title: '导入数据',
    description: '正在执行导入数据',
  },
]

const StepMap = {
  UPLOAD_FILE: 0,
  VALIDATE_DATA: 1,
  IMPORT_DATA: 2,
}
// 上传成功状态组件
function SuccessStatus({
  successNumber,
  viewDataAfterSuccessUpload,
}: {
  successNumber: number | string
  viewDataAfterSuccessUpload: () => void
}) {
  return (
    <div className="text-center">
      <CheckCircleOutlined className="my-4 text-9xl text-[#04a76f]" />
      <div className="text-lg font-medium">数据导入成功</div>
      <div className="my-2 text-slate-500">{`成功导入数据数量${successNumber || '-'}条，点击完成`}</div>
      <div className="cursor-pointer text-[#9355f6]" onClick={viewDataAfterSuccessUpload}>
        请到 数据目录dipeak - 数据库csv 里查看表格{' '}
      </div>
    </div>
  )
}

const cancelTokenControl = new CancelTokenControl()

export default function CSVUpload({
  open,
  setOpen,
  viewDataAfterSuccessUpload,
}: {
  open: boolean
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
  viewDataAfterSuccessUpload: () => void
}) {
  const [uploadFile, setUploadFile] = useState<Blob>()
  const [currentStep, setCurrentStep] = useState(StepMap['UPLOAD_FILE'])
  const [columns, setColumns] = useState<ColumnType[]>([])
  const [nextBtnLoading, setNextBtnLoading] = useState(false)
  const [preBtnLoading, setPreBtnLoading] = useState(false)
  const [returnFirstBtnLoading, setReturnFirstBtnLoading] = useState(false)
  const [uploadErrorModalOpen, setUploadErrorModalOpen] = useState(false)
  const currentAlreadyPTable = useRef('')

  const [form] = Form.useForm()
  const { message } = App.useApp()

  // 清除数据
  const clearData = useCallback(() => {
    setCurrentStep(0)
    setColumns([])
    form.resetFields()
  }, [form])

  const {
    run: uploadCSV,
    loading,
    error,
    data,
  } = useRequest(
    async () => {
      return request.put<null, number>(
        askBIApiUrls.xengine.uploadCSV,
        objectToFormData({
          file: uploadFile as Blob,
          ptable: form.getFieldValue('name'),
        }),
        {
          cancelToken: cancelTokenControl.getToken(),
          headers: { 'Content-Type': 'multipart/form-data' },
          timeout: 30 * 60 * 1000,
        },
      )
    },
    {
      manual: true,
      onError() {
        setUploadErrorModalOpen(true)
      },
      onSuccess() {
        currentAlreadyPTable.current = ''
      },
    },
  )

  // 删除虚拟表
  const deleteVTable = useCallback((name: string) => {
    return request
      .delete(askBIApiUrls.xengine.deleteVTable, {
        params: {
          catalog: 'dipeak',
          database: 'csv',
          name: `vt_${name}`,
        },
      })
      .then(() => {
        currentAlreadyPTable.current = ''
      })
  }, [])

  const StepsProcess = useMemo(
    () => [
      {
        validate() {
          if (!uploadFile) {
            message.error('请选择对应文件上传')
            return false
          }
          return true
        },
        async next() {
          if (uploadFile) {
            const { name } = uploadFile as File
            const formData = new FormData()
            uploadFile && formData.append('file', uploadFile)
            const inferColumns = await request.put<null, InferColumnType[]>(
              askBIApiUrls.xengine.inferColumnType,
              formData,
              {
                cancelToken: cancelTokenControl.getToken(),
                timeout: 30 * 60 * 1000,
                headers: {
                  'Content-Type': 'multipart/form-data',
                },
              },
            )
            form.resetFields()
            form.setFieldValue('name', name.split('.')[0])
            const nextColumns = inferColumns.map((col) => {
              const column: Partial<InferColumnType> = { name: col.name, columnType: col.columnType || 'CHAR' }
              if (col.columnType === 'DECIMAL') {
                column.columnScale = col.columnScale
                column.columnPrecision = col.columnPrecision
              }
              return column
            })

            setColumns(nextColumns as InferColumnType[])
          }
        },
      },
      {
        validate: () =>
          form.validateFields().then(
            () => true,
            () => false,
          ),
        next: async () => {
          return request
            .post(askBIApiUrls.xengine.createPTable, formatDataToCreatePTableParams(form, columns), {
              timeout: 30 * 60 * 1000,
              cancelToken: cancelTokenControl.getToken(),
            })
            .then(
              () => {
                currentAlreadyPTable.current = form.getFieldValue('name')
                message.success('数据校验成功')
                uploadCSV()
              },
              (error) => {
                message.error(error?.msg || '数据校验失败')
                throw error
              },
            )
        },
      },
      {
        async pre() {
          return deleteVTable(form.getFieldValue('name')).then(() => {
            setUploadErrorModalOpen(false)
          })
        },
        next: async () => {
          setOpen(false)
          clearData()
        },
      },
    ],
    [uploadFile, form, message, uploadCSV, columns, setOpen, clearData, deleteVTable],
  )

  // 处理下一步
  async function handleNextStep() {
    const currentStepItem = StepsProcess[currentStep]
    let valid = true
    if (currentStepItem.validate) {
      valid = await currentStepItem.validate()
    }
    if (valid) {
      if (currentStepItem.next) {
        setNextBtnLoading(true)
        currentStepItem
          .next()
          .then(() => {
            currentStep < StepItems.length - 1 && setCurrentStep(currentStep + 1)
          })
          .finally(() => {
            setNextBtnLoading(false)
          })
      } else {
        currentStep < StepItems.length - 1 && setCurrentStep(currentStep + 1)
      }
    }
  }

  // 处理上一步
  async function handlePreStep() {
    if (currentStep > 0) {
      const currentStepItem = StepsProcess[currentStep]
      if (currentStepItem.pre) {
        setPreBtnLoading(true)
        currentStepItem
          .pre()
          .then(() => {
            setCurrentStep(currentStep - 1)
          })
          .finally(() => {
            setPreBtnLoading(false)
          })
      } else {
        setCurrentStep(currentStep - 1)
      }
    }
  }

  async function returnFirstStep() {
    setReturnFirstBtnLoading(true)
    await deleteVTable(form.getFieldValue('name'))
      .then(() => {
        setCurrentStep(0)
        form.resetFields()
      })
      .finally(() => {
        setReturnFirstBtnLoading(false)
      })
  }
  // 关闭弹窗
  const handleClose = useCallback(() => {
    if (currentAlreadyPTable.current) {
      deleteVTable(currentAlreadyPTable.current)
    }
    cancelTokenControl.cancel()
    cancelTokenControl.update()
    setOpen(false)
    clearData()
  }, [clearData, deleteVTable, setOpen])

  return (
    <Modal
      width="780px"
      open={open}
      destroyOnClose
      okButtonProps={{
        loading: nextBtnLoading,
      }}
      cancelButtonProps={{
        style: {
          display: currentStep <= 0 ? 'none' : '',
        },
        onClick: handlePreStep,
        loading: preBtnLoading,
      }}
      closeIcon={
        <Popconfirm
          title="关闭窗口"
          description="点击关闭窗口，将会中止正在进行的请求，是否关闭窗口？"
          onConfirm={handleClose}
        >
          <CloseOutlined />
        </Popconfirm>
      }
      onOk={handleNextStep}
      cancelText="上一步"
      okText={currentStep === StepItems.length - 1 ? '完成' : '下一步'}
    >
      <div className="mt-8" />
      <Steps className="mb-6" size="small" current={currentStep} items={StepItems} />
      <UploadFile
        onChange={(file) => {
          setUploadFile(file as Blob)
        }}
        className={currentStep !== 0 ? `hidden` : ''}
      />
      <ValidateData
        form={form}
        className={clsx(currentStep !== 1 && 'hidden')}
        columns={columns}
        handleChange={(key, columnItem) => {
          setColumns(
            produce(columns, (draft) => {
              const changeItem = draft.find((col) => col.name === columnItem.name)
              if (changeItem) {
                const value = columnItem[key]
                if (value) {
                  changeItem[key] = value as never
                }
              }
            }),
          )
        }}
      />
      <div className={clsx(currentStep !== 2 && 'hidden', 'flex min-h-[180px] items-center justify-center py-8')}>
        {loading && (
          <Spin spinning={true} size="large" tip="正在上传..." className="">
            <div className="h-[80px] w-[100px]" />
          </Spin>
        )}
        {!loading && !error && (
          <SuccessStatus
            successNumber={data ?? '-'}
            viewDataAfterSuccessUpload={() => {
              viewDataAfterSuccessUpload()
              handleClose()
            }}
          />
        )}
        {!loading && error && (
          <Result
            status="error"
            title="数据导入失败"
            extra={[
              <Button key="errMsg" onClick={() => setUploadErrorModalOpen(true)}>
                查看错误原因
              </Button>,
              <Button type="primary" key="console" onClick={returnFirstStep} loading={returnFirstBtnLoading}>
                重新上传文件
              </Button>,
            ]}
          />
        )}
      </div>
      <UploadErrorModal
        open={uploadErrorModalOpen}
        onClose={() => setUploadErrorModalOpen(false)}
        errMsg={(error as Error & { msg: string })?.msg || ''}
      />
    </Modal>
  )
}
