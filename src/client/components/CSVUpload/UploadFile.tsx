import React, { useRef } from 'react'
import { Upload, message, Typography, type UploadFile } from 'antd'
import { InboxOutlined, EyeOutlined } from '@ant-design/icons'
import axios from 'axios'
import { TOKEN_RANGER } from 'src/shared/constants'

function viewFile(file: File | UploadFile) {
  const f = file instanceof File ? file : file?.originFileObj
  if (!(f instanceof File)) return
  const fileUrl = URL.createObjectURL(f)
  window.open(fileUrl)
}

function validateFileExt(file: File, exts?: string[]): boolean {
  if (!file) return false
  const { name: fileName } = file
  const fileExt = fileName.match(/\.[^.]+$/)?.pop()
  if (!fileExt || !exts?.includes(fileExt)) {
    return false
  }
  return true
}

async function downloadFile(fileUrl: string, filename: string) {
  const response = await axios.get(fileUrl, {
    headers: { TOKEN_RANGER: `${localStorage.getItem(TOKEN_RANGER)}` },
    responseType: 'blob',
  })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(response.data)
  link.download = filename
  link.click()
  URL.revokeObjectURL(link.href)
  void message.success('下载成功')
}

const sampleList = [
  {
    fileName: '示例.csv',
    // @ts-expect-error 使用vite中的import.meta，原因是tsconfig.json中配置了"module": "commonJS"
    fileUrl: new URL('./sample.csv', import.meta.url).href,
  },
]

function validateBeforeUpload(file: File) {
  const valid = [
    () => {
      const v = validateFileExt(file, ['.csv'])
      if (!v) {
        message.error('上传文件后缀名不符')
      }
      return v
    },
    () => {
      const MAX_FILE_SIZE = 500 * 1024 * 1024
      const size = file.size
      const v = size <= MAX_FILE_SIZE
      if (!v) {
        message.error('上传文件大小超过50MB')
      }
      return v
    },
  ].every((bool) => bool())
  if (!valid) {
    return Upload.LIST_IGNORE
  }
}

type PropsType = {
  onChange?: (file: Blob | string) => void
  className?: string
}
export default function UploadFile({ onChange, className }: PropsType) {
  const currentRemoveFileUId = useRef('')
  return (
    <Upload.Dragger
      className={className}
      listType="text"
      maxCount={1}
      accept=".csv"
      showUploadList={{
        showDownloadIcon: true,
        downloadIcon: <EyeOutlined />,
      }}
      onDownload={viewFile}
      beforeUpload={validateBeforeUpload}
      onChange={({ file }) => {
        onChange && onChange(file.uid === currentRemoveFileUId.current ? '' : (file.originFileObj as Blob))
        return {
          file: Object.assign(file, {
            status: 'done',
            response: '{"status": "success"}',
          }),
        }
      }}
      onRemove={(f) => {
        currentRemoveFileUId.current = f.uid
      }}
    >
      <p className="ant-upload-drag-icon">
        <InboxOutlined />
      </p>
      <p className="ant-upload-text">点击或将文件拖拽到此区域进行上传</p>
      <p className="ant-upload-hint">支持扩展名为：*.csv文件</p>
      <p>
        <Typography.Text mark>tips：单次上传数量限制在500MB以内</Typography.Text>
      </p>
      {sampleList && sampleList.length > 0 && (
        <p className="ant-upload-hint">
          示例文件下载：
          {sampleList.map((sample, index: number) => (
            <a
              key={index}
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                downloadFile(sample.fileUrl, sample.fileName)
              }}
            >
              {`${sample.fileName}${index < sampleList.length - 1 ? '、' : ''}`}
            </a>
          ))}
        </p>
      )}
    </Upload.Dragger>
  )
}
