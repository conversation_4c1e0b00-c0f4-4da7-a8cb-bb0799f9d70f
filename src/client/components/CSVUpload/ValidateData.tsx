import React from 'react'
import { Form, Input, Select, type FormInstance, Table, Row, Col, Space, InputNumber } from 'antd'
import { useRequest } from 'ahooks'
import { isAlphaNumberAndAlphaStart } from '@shared/common-utils'
import { askBIApiUrls } from 'src/shared/url-map'
import request from 'src/shared/xengineAxios'

type PropsType = {
  columns: { name: string; columnType: string; columnScale?: number; columnPrecision?: number }[]
  form: FormInstance<any>
  className?: string
  handleChange: (
    key: 'columnType' | 'columnScale' | 'columnPrecision',
    args: { name: string; columnType?: string; columnScale?: number; columnPrecision?: number },
  ) => void
}

export default function ValidateData({ columns, className, form, handleChange }: PropsType) {
  const { loading, data: columnTypes } = useRequest(() => request.get<null, string[]>(askBIApiUrls.xengine.columnType))
  return (
    <div className={`${className} mt-8`}>
      <Form form={form} labelCol={{ flex: '130px' }} className="mb-2">
        <Row gutter={[10, 0]}>
          <Col span={12}>
            <Form.Item
              validateFirst
              label="表名称"
              name="name"
              rules={[
                {
                  required: true,
                  validator(_, value) {
                    const valid = isAlphaNumberAndAlphaStart(value)
                    return valid ? Promise.resolve() : Promise.reject('表名称需以数字字母下划线组成，并以字母开头')
                  },
                },
              ]}
            >
              <Input placeholder="请输入表名" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Primary Key" tooltip="请选择表主键" name="primaryKeys" rules={[{ required: true }]}>
              <Select placeholder="请选择" options={columns.map((v) => ({ label: v.name, value: v.name }))} />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              label="Distributed By"
              name="distributedKeys"
              tooltip="建议选择经常作为查询条件的列为分桶键，提高查询效率。请选择字段类型为DECIMAL、DATE、DATETIME、CHAR、VARCHAR、STRING的字段"
              rules={[{ required: true }]}
            >
              <Select placeholder="请选择" options={columns.map((v) => ({ label: v.name, value: v.name }))} />
            </Form.Item>
          </Col>
        </Row>
      </Form>

      <Table
        rowKey="name"
        columns={[
          {
            title: '字段名称',
            dataIndex: 'name',
          },
          {
            title: '数据类型',
            dataIndex: 'primitiveType',
            render(_, record) {
              return (
                <Select
                  onChange={(columnType) => {
                    handleChange('columnType', { name: record.name, columnType })
                  }}
                  value={record.columnType}
                  loading={loading}
                  className="w-[180px]"
                  options={columnTypes?.map((v) => ({
                    label: v,
                    value: v,
                  }))}
                />
              )
            },
          },
          {
            title: '(精度,标度)',
            dataIndex: 'columnScale',
            width: 200,
            render(_, record) {
              if (record.columnType === 'DECIMAL') {
                return (
                  <div className="flex items-center">
                    <span>(</span>
                    <Space.Compact>
                      <InputNumber
                        style={{ width: '50%' }}
                        value={record.columnPrecision}
                        onChange={(columnPrecision) => {
                          handleChange('columnPrecision', { name: record.name, columnPrecision: columnPrecision || 0 })
                        }}
                      />
                      <InputNumber
                        style={{ width: '50%' }}
                        value={record.columnScale}
                        onChange={(columnScale) => {
                          handleChange('columnScale', {
                            name: record.name,
                            columnScale: columnScale || 0,
                          })
                        }}
                      />
                    </Space.Compact>
                    <span>)</span>
                  </div>
                )
              }
              return <>-</>
            },
          },
        ]}
        pagination={{
          defaultPageSize: 8,
        }}
        dataSource={columns}
      />
    </div>
  )
}
