import React from 'react'
import {
  Form,
  Table,
  DatePicker,
  Input,
  InputNumber,
  Select,
  Button,
  Space,
  Row,
  Col,
  Mentions,
  Card,
  type FormItemProps,
  type InputProps,
  type SelectProps,
  type InputNumberProps,
  type DatePickerProps,
  type TimeRangePickerProps,
  type MentionProps,
  type TableProps,
  type FormProps,
} from 'antd'
import { useAntdTable } from 'ahooks'
import DBSelect from 'src/client/components/DBSelect/DBSelect'

export type SearchActionType = { submit: () => void; reset: () => void; refresh: () => void }
export interface SearchProps<T = object> {
  items: ItemType[]
  request: () => Promise<any>
  table: TableProps<T>
  formProps?: FormProps
  className?: string
  actionRef?: React.MutableRefObject<SearchActionType | null>
}

type ItemTypeEnum = 'Input' | 'Select' | 'InputNumber' | 'DatePicker' | 'DateRangePicker' | 'Mentions' | 'DBSelect'

type ItemTypeFieldPropsInfer<T extends ItemTypeEnum> = T extends 'Input'
  ? InputProps
  : T extends 'Select'
    ? SelectProps
    : T extends 'InputNumber'
      ? InputNumberProps
      : T extends 'DatePicker'
        ? DatePickerProps
        : T extends 'DateRangePicker'
          ? TimeRangePickerProps
          : T extends 'Mentions'
            ? MentionProps
            : T extends 'DBSelect'
              ? SelectProps
              : never

export type ItemType = {
  tag: ItemTypeEnum
  name: string
  label: string
  formItemProps?: FormItemProps
  fieldProps?: ItemTypeFieldPropsInfer<ItemType['tag']>
}

function Search<T>(props: SearchProps<T>) {
  const [form] = Form.useForm()
  const { items, table, request, formProps, actionRef, className } = props
  const renderForm = (items: ItemType[]) => {
    const colSpan = 6
    const renderTag = (_items: ItemType[]) => {
      const tagHtml: JSX.Element[] = []
      _items?.map((value: ItemType, index: number) => {
        const { tag, name, label, formItemProps, fieldProps } = value
        switch (tag) {
          case 'DBSelect': {
            html.push(
              <Col span={colSpan} key={index}>
                <DBSelect span={colSpan} setFieldValue={form.setFieldValue} />
              </Col>,
            )
            break
          }
          case 'Input': {
            tagHtml.push(
              <Col span={colSpan} key={index}>
                <Form.Item name={name} label={label} {...formItemProps}>
                  <Input placeholder="请输入" {...(fieldProps as InputProps)} />
                </Form.Item>
              </Col>,
            )
            break
          }
          case 'InputNumber': {
            tagHtml.push(
              <Col span={colSpan} key={index}>
                <Form.Item name={name} label={label} {...formItemProps}>
                  <InputNumber placeholder="请输入" {...(fieldProps as InputNumberProps)} className="w-full" />
                </Form.Item>
              </Col>,
            )
            break
          }

          case 'Select': {
            tagHtml.push(
              <Col span={colSpan} key={index}>
                <Form.Item name={name} label={label} {...formItemProps}>
                  <Select placeholder="请选择" {...(fieldProps as SelectProps)} />
                </Form.Item>
              </Col>,
            )
            break
          }

          case 'DatePicker': {
            tagHtml.push(
              <Col span={colSpan} key={index}>
                <Form.Item name={name} label={label} {...formItemProps}>
                  <DatePicker {...(fieldProps as DatePickerProps)} className="w-full" />
                </Form.Item>
              </Col>,
            )
            break
          }

          case 'DateRangePicker': {
            tagHtml.push(
              <Col span={colSpan} key={index}>
                <Form.Item name={name} label={label} {...formItemProps}>
                  <DatePicker.RangePicker {...(fieldProps as TimeRangePickerProps)} />
                </Form.Item>
              </Col>,
            )
            break
          }

          case 'Mentions': {
            tagHtml.push(
              <Col span={24} offset={0} key={index}>
                <Form.Item name={name} label={label} {...formItemProps}>
                  <Mentions {...(fieldProps as MentionProps)} className="w-full" />
                </Form.Item>
              </Col>,
            )
            break
          }
          default:
            break
        }
      })
      return tagHtml
    }

    const html = renderTag(items || [])
    html.push(
      <Col span={24} className="text-right" key="buttons">
        <Space>
          <Button onClick={search.reset}>重置</Button>
          <Button type="primary" onClick={search.submit}>
            搜索
          </Button>
        </Space>
      </Col>,
    )
    return html
  }
  const {
    tableProps,
    refresh: tableRefresh,
    search,
  } = useAntdTable(request, {
    form,
    defaultCurrent: 1,
    defaultPageSize: 10,
    onSuccess() {
      if (actionRef) {
        actionRef.current = {
          submit: search.submit,
          reset: search.reset,
          refresh: tableRefresh,
        }
      }
    },
  })

  return (
    <div className={className}>
      <Card className="mb-6">
        <Form form={form} layout="vertical" labelCol={{ span: 24 }} {...formProps}>
          <Row gutter={[16, 0]}>{renderForm(items)}</Row>
        </Form>
      </Card>
      <Card bordered={false}>
        <Table {...table} {...tableProps} />
      </Card>
    </div>
  )
}

export default Search
