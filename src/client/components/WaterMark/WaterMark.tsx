import React, { useMemo } from 'react'
import { Watermark as AntWaterMark } from 'antd'
import { useAtomValue } from 'jotai/react'
import dayjs from 'dayjs'
import qs from 'query-string'
import { currentLoginUserAtom, metricConfigAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { getCookieValue } from 'src/client/utils'
import { isBaoWu } from 'src/shared/baowu-share-utils'

export default function WaterMark() {
  const metricConfig = useAtomValue(metricConfigAtom)
  const { watermarkUsername } = qs.parse(location.search)
  const cookieUsername = getCookieValue('u_info')

  const currentUser = useAtomValue(currentLoginUserAtom)
  const waterMarkContent = useMemo(() => {
    return `${currentUser?.username || cookieUsername || watermarkUsername} ${dayjs().format('YYYY/MM/DD')}`
  }, [currentUser, watermarkUsername, cookieUsername])

  if (!isBaoWu(metricConfig?.metricTableName)) {
    return null
  }
  return (
    <AntWaterMark
      className="pointer-events-none bottom-0 left-0 right-0 top-0"
      style={{ position: 'fixed' }}
      gap={[80, 140]}
      font={{ color: 'rgba(0,0,0,.05)' }}
      content={waterMarkContent}
    />
  )
}
