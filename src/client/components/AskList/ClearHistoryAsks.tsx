import React, { useState } from 'react'
import axios from 'axios'
import { useAtomValue, useSetAtom } from 'jotai'
import { message, Modal } from 'antd'
import { TrashIcon } from '@heroicons/react/24/outline'
import { askBIApiUrls } from 'src/shared/url-map'
import {
  askHistoryAtom,
  conversationIdAtom,
  currentDatasetAtom,
  initChatsAtom,
} from 'src/client/pages/AskBI/askBIAtoms'

const ClearHistoryAsks = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const currentDataset = useAtomValue(currentDatasetAtom)
  const setAskHistory = useSetAtom(askHistoryAtom)

  const setConversationId = useSetAtom(conversationIdAtom)
  const initChats = useSetAtom(initChatsAtom)

  // 清空历史记录
  const clearHistory = () => {
    // 清空 conversation & chart ID , 初始化chats数组
    setConversationId(null)
    initChats()
  }

  const handleClearHistory = async () => {
    setIsLoading(true)
    await axios
      .put(askBIApiUrls.convers.updateAll(currentDataset?.sceneId || ''), {
        isDraft: true,
      })
      .then(() => {
        setAskHistory([])
        clearHistory()
        message.success('清除历史记录成功')
      })
      .catch(() => {
        message.info('清除历史记录失败')
      })
      .finally(() => {
        setIsModalOpen(false)
        setIsLoading(false)
      })
  }

  return (
    <>
      <div
        className="flex cursor-pointer gap-1"
        onClick={() => {
          setIsModalOpen(true)
        }}
      >
        <TrashIcon className="h-5 w-5" />
      </div>
      <Modal
        centered
        title="删除历史记录"
        loading={isLoading}
        open={isModalOpen}
        onOk={handleClearHistory}
        onCancel={() => {
          setIsModalOpen(false)
        }}
      >
        <p>删除历史记录后，无法恢复，请确认</p>
      </Modal>
    </>
  )
}

export default ClearHistoryAsks
