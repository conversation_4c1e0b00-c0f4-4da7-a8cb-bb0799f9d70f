import React from 'react'
import { useAtomValue, useSetAtom } from 'jotai'
import { editAskHistoryIcon, SvgIcon } from 'src/client/components/SvgIcon'
import { updateMessageAndHtmlAtom, showAskHistoryListAtom, askHistory<PERSON>tom } from '../../pages/AskBI/askBIAtoms'
import otherErrorImg from '/img/error/default/other-error.webp'
import ClearHistoryAsks from './ClearHistoryAsks'

const HistoryAsks = () => {
  const setMessage = useSetAtom(updateMessageAndHtmlAtom)
  const setAskHistoryOpen = useSetAtom(showAskHistoryListAtom)
  const askList = useAtomValue(askHistoryAtom)

  if (askList?.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center gap-2">
        <img src={otherErrorImg} className="h-[120px]" alt="错误图片" />
        <span>暂无最近提问</span>
      </div>
    )
  }

  return (
    <div className="flex flex-col gap-2 pl-2">
      <div className="mb-4 flex justify-between px-5">
        <p>最近提问</p>
        <ClearHistoryAsks />
      </div>
      {askList?.slice(0, 10).map((e, index) => {
        return (
          <div key={index} className="flex justify-between gap-2 rounded bg-white px-5 py-4">
            <span className="font-medium">{e}</span>
            <div
              onClick={() => {
                setMessage(e)
                setAskHistoryOpen(false)
              }}
            >
              <SvgIcon icon={editAskHistoryIcon} className="h-6 w-6" />
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default HistoryAsks
