import { useAtomValue, useSet<PERSON>tom } from 'jotai/react'
import React from 'react'
import { askHistoryAtom, updateMessageAndHtmlAtom } from 'src/client/pages/AskBI/askBIAtoms'
import TextTruncate from '../TextTruncate'
import { editAskHistoryIcon, SvgIcon } from '../SvgIcon'

export default function RecentAsks() {
  const setMessage = useSetAtom(updateMessageAndHtmlAtom)
  const askList = useAtomValue(askHistoryAtom)

  if (!askList || askList.length === 0) {
    return null
  }

  return askList.length > 0 ? (
    <div className="pb-2 text-[#575757]">
      <div className="mb-2 px-5">最近提问</div>
      <div className="flex flex-col gap-2">
        {askList.slice(0, 3).map((message, index) => {
          return (
            <div key={index} className="flex justify-between rounded border bg-white px-5 py-4">
              <TextTruncate className="w-4/5" placement="topLeft">
                {message}
              </TextTruncate>
              <div
                onClick={() => {
                  setMessage(message)
                }}
              >
                <SvgIcon icon={editAskHistoryIcon} className="h-6 w-6" />
              </div>
            </div>
          )
        })}
      </div>
    </div>
  ) : null
}
