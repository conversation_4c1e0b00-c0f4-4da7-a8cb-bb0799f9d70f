import React from 'react'
import { useAtomValue, useSetAtom } from 'jotai'
import { updateMessageAndHtmlAtom, askHistoryAtom } from '../../pages/AskBI/askBIAtoms'
import { SvgIcon, editAskHistoryIcon } from '../../components/SvgIcon'
import TextTruncate from '../TextTruncate'
import ClearHistoryAsks from './ClearHistoryAsks'

const H5HomeRecentAsks = () => {
  const setMessage = useSetAtom(updateMessageAndHtmlAtom)
  const askList = useAtomValue(askHistoryAtom)

  if (!askList || askList.length === 0) {
    return null
  }

  return (
    <div>
      {askList && askList.length > 0 && (
        <div className="mb-10 p-4">
          <div className="mb-2 flex items-center justify-between">
            最近提问
            <ClearHistoryAsks />
          </div>
          <div className="flex flex-col gap-2">
            {askList.slice(0, 3).map((message, index) => {
              return (
                <div key={index} className="flex justify-between rounded bg-white px-5 py-4">
                  <TextTruncate className="w-4/5" placement="topLeft">
                    {message}
                  </TextTruncate>
                  <div
                    onClick={() => {
                      setMessage(message)
                    }}
                  >
                    <SvgIcon icon={editAskHistoryIcon} className="h-6 w-6" />
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}

export default H5HomeRecentAsks
