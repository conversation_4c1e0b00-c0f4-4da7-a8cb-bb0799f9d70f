/**
 * 文档数据 和 场景管理下的文档
 */
import { CloudUploadOutlined, DeleteOutlined, ExportOutlined, MoreOutlined, SelectOutlined } from '@ant-design/icons'
import { useRequest } from 'ahooks'
import { App, Button, Form, Input, Modal, PaginationProps, Popconfirm, Popover, Select, Table, Tooltip } from 'antd'
import axios from 'axios'
import dayjs from 'dayjs'
import { nanoid } from 'nanoid'
import React, { useEffect, useState } from 'react'
import debounce from 'lodash/debounce'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useAtom, useAtomValue } from 'jotai'
import { Doc, DocumentSearchType } from 'src/shared/askdoc-types'
import { askBIApiUrls, askBIPageUrls, askDocApiUrls } from 'src/shared/url-map'
import { FileMimeTypeInfo } from 'src/shared/common-types'
import { mimeTypeList } from 'src/shared/constants'
import { docStatusMap, getIconComponent } from '../utils'
import DocumentData from '../pages/AskBI/Scenario/DocumentData'
import { selectFileIdsAtom } from '../pages/AskDoc/askDocAtoms'
import { currentLoginUserAtom } from '../pages/AskBI/askBIAtoms'
import useIsAllowedRanger from '../hooks/useIsAllowedRanger'
import { SvgIcon } from './SvgIcon'
import CustomUploadModal from './CustomUploadModal'

const DefaultPagination = {
  pageSize: 10,
  current: 1,
  total: 0,
}

interface ScenarioType {
  id: string
  label: string
  tableName: string
  description: string
  agent: string
  projectName: string
  creationUser: string
  iconType: number
  creationTime: string
  projectId: string
  modelNames: string
  aggTimeDimension: string
  timeDimensionType: string
  timeDimensionFormat: string
  timeGranularityMin: string
}

const width = 150

export default function DocumentDataTable() {
  const { message: antdMessage } = App.useApp()
  const [searchParams, _setSearchParams] = useSearchParams()
  const [form] = Form.useForm()
  const navigate = useNavigate()
  const [editForm] = Form.useForm()
  const [pagination, setPatination] = useState<PaginationProps>(DefaultPagination)
  const [fileList, setFileList] = useState<Doc[]>([]) // 显示数据
  const [listTotal, setListTotal] = useState<number>(0)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false)
  const [currentClickItem, setCurrentClickItem] = useState<Doc>()
  const [scenesList, setSceneList] = useState<ScenarioType[]>([])
  const filterName = decodeURIComponent(searchParams.get('filterName') || '')
  const scenarioId = searchParams.get('scenarioId') || ''
  const [filterParams, setFilterParams] = useState<DocumentSearchType | {}>({
    fileName: filterName,
    sceneId: scenarioId,
  })
  const [removedScenes, setRemovedScenes] = useState<string[]>([])
  const [addedScenes, setAddedScenes] = useState<string[]>([])
  const [isShowUploadModal, setIsShowUploadModal] = useState(false)
  const [selectFileIds, setSelectFileIds] = useState<number[]>([])
  const [isShowSelectAllFileModalOpen, setIsShowSelectAllFileModalOpen] = useState<boolean>(false)
  const [showAddSceneLoading, setShowAddSceneLoading] = useState<boolean>(false)
  const [selectFileIdsFromAll, setSelectFileIdsFromAll] = useAtom(selectFileIdsAtom)
  const currentLoginUser = useAtomValue(currentLoginUserAtom)
  const isAdmin = useIsAllowedRanger()

  useRequest(
    async () => {
      const response = await axios.get(askBIApiUrls.auth.scene.list, {
        params: { current: 1, pageSize: 1000 },
      })
      setSceneList(response.data?.data?.list || [])
    },
    {
      onError: (error) => {
        console.error('获取场景信息失败', error)
      },
    },
  )

  // 删除文件 和 从场景里移除文件
  const deleteSceneFile = async (fileIds: number[], sceneId?: string) => {
    if (fileIds.length === 0) {
      antdMessage.warning('请选择要操作的文档')
      return
    }
    try {
      await axios.post(askDocApiUrls.deleteSceneFile, { fileIds, sceneId })
      antdMessage.success('操作成功')
      getDocumentList(filterParams, pagination)
    } catch (error) {
      antdMessage.success('操作失败')
      console.info('deleteSceneFile', error)
    }
  }

  // 编辑
  const editSceneFile = async (data: { name: string; sceneIdList: string[] }) => {
    try {
      const dataBody = {
        fileId: currentClickItem?.id,
        fileName: data.name,
        removedScene: removedScenes,
        addedScene: addedScenes,
      }
      await axios.post(askDocApiUrls.editSceneFile, dataBody)
      antdMessage.success('修改成功')
      getDocumentList(filterParams, DefaultPagination)
    } catch (error) {
      console.info('deleteSceneFile', error)
      antdMessage.success('修改失败')
    }
  }

  // 获取文档数据列表
  const { run: getDocumentList } = useRequest(
    async ({ fileName, fileType, sceneId }: DocumentSearchType, pagination: PaginationProps) => {
      const queryData = {
        sceneId: sceneId || '',
        fileName: fileName || '',
        fileType: fileType || '',
        creator: !isAdmin ? currentLoginUser?.username : '',
        page: pagination.current,
        pageSize: pagination.pageSize,
      }
      const response = await axios.get(askDocApiUrls.fileListV2, { params: queryData })
      const res = response.data.data.files

      setFileList(res || [])
      setListTotal(response.data.data?.total || 0)
      setIsLoading(false)
      setPatination({
        ...pagination,
        total: response.data.data?.total,
      })
    },
    {
      manual: true,
      onError: (error: any) => {
        setIsLoading(false)
        console.error('获取文档数据失败 =', error)
        antdMessage.error(error?.response.data.msg || '获取文档数据失败')
      },
    },
  )

  useEffect(() => {
    getDocumentList(filterParams, DefaultPagination)
  }, [filterParams, getDocumentList])

  // Table 分页
  const handlePageChange = (pagination: PaginationProps) => {
    const { current, pageSize } = pagination
    setPatination({ ...pagination, current, pageSize })
    getDocumentList(filterParams, pagination)
  }

  // 上传文档点击事件
  const handleUploadFile = () => {
    setIsShowUploadModal(true)
  }

  // 编辑 - 确定
  const handleOk = () => {
    setIsModalOpen(false)
    editForm.getFieldsValue()
    editSceneFile(editForm.getFieldsValue())
  }

  // 编辑 - 取消
  const handleCancel = () => {
    editForm.resetFields()
    setIsModalOpen(false)
  }

  const handleValuesChange = (_changedValues: DocumentSearchType, allValues: DocumentSearchType) => {
    // 这里调用接口查询，传入当前表单的所有值
    const params = scenarioId ? { ...allValues, sceneId: scenarioId } : allValues
    setFilterParams(params)
    getDocumentList(params, pagination)
  }

  const debounceHandleValuesChange = debounce(handleValuesChange, 500)

  const onFinishUpload = () => {
    getDocumentList(filterParams, DefaultPagination)
  }

  const handleEditSceneChange = (newValues: string[]) => {
    if (currentClickItem) {
      const oldValues = currentClickItem.sceneIdList

      const added = newValues.filter((value: any) => !oldValues.includes(value))
      const removed = oldValues.filter((value: any) => !newValues.includes(value))
      setAddedScenes(added)
      setRemovedScenes(removed)
    }
  }

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectFileIds(newSelectedRowKeys.map(Number))
  }

  const rowSelection = {
    selectedRowKeys: selectFileIds,
    onChange: onSelectChange,
  }

  const handleDeleteFiles = () => {
    if (selectFileIds) {
      deleteSceneFile(selectFileIds, scenarioId)
    }
  }

  // 选择已有文件加入到此场景中
  const handleSelectAllFileAddScene = () => {
    setSelectFileIdsFromAll([])
    setIsShowSelectAllFileModalOpen(true)
  }

  const handleSelectAllFileModalCancel = () => {
    setSelectFileIdsFromAll([])
    setIsShowSelectAllFileModalOpen(false)
  }

  const handleSelectAllFileModalOk = async () => {
    try {
      if (selectFileIdsFromAll.length === 0) return antdMessage.warning('请选择要关联的文档')
      setShowAddSceneLoading(true)
      await axios.post(askDocApiUrls.addSceneFile, { sceneId: scenarioId, fileIds: selectFileIdsFromAll })
      antdMessage.success('关联场景成功！')
      setSelectFileIdsFromAll([])
      setShowAddSceneLoading(false)
      setIsShowSelectAllFileModalOpen(false)
      getDocumentList(filterParams, DefaultPagination)
    } catch (error) {
      console.error(error)
      antdMessage.error('关联场景失败！')
      setShowAddSceneLoading(false)
    }
  }

  /**
   * 处理文件下载
   *
   * @param item 包含文件源URL的对象
   */
  const handleDownloadFile = (item: { sourceUrl: string }) => {
    if (item && item.sourceUrl) {
      window.location.href = askDocApiUrls.downloadFileProxy(item.sourceUrl)
    } else {
      antdMessage.error('下载失败')
    }
  }

  const columns = [
    { title: 'ID', dataIndex: 'id', key: 'id' },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 300,
      render: (value: string, record: Doc) => {
        return (
          <div className="my-2 flex items-center">
            <SvgIcon icon={getIconComponent(record.mimeType as FileMimeTypeInfo)} className="mr-2 h-5 w-5" />
            <Tooltip title={value}>
              <p className="w-64 truncate text-xs font-medium text-link">{value}</p>
            </Tooltip>
          </div>
        )
      },
    },
    {
      title: '文档状态',
      dataIndex: 'fileStatus',
      key: 'fileStatus',
      render: (value: string) => {
        /** 文档 embed 的状态 Pending异常 Ready解析中 Done解析完成 Fail失败 */
        const status = docStatusMap[value] || { label: value, colorClass: 'text-gray-900' } // 默认颜色
        return <p className={`text-xs ${status.colorClass} font-medium`}>{status.label}</p>
      },
    },
    {
      title: ' 类型',
      dataIndex: 'mimeType',
      key: 'mimeType',
      render: (value: string) => {
        return <p className="text-xs text-[#171717]">{mimeTypeList.find((item) => item.value === value)?.label}</p>
      },
    },
    {
      title: '来源',
      dataIndex: 'source',
      key: 'source',
      render: (value: string) => {
        return <p className="text-xs text-[#171717]">{value}</p>
      },
    },
    {
      title: '关联场景',
      dataIndex: 'sceneIdList',
      key: 'sceneIdList',
      render: (value: string[]) => {
        if (value && scenesList) {
          const selectedSceneNames = (scenesList || [])
            .filter((scene) => value.includes(scene.id.toString()))
            .map((scene) => scene.label)
            .join(', ')
          return <p className="text-xs text-link">{selectedSceneNames}</p>
        }
        return <p className="text-xs text-link">{''}</p>
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdTime',
      key: 'createdTime',
      render: (value: string) => {
        return <p className="text-xs text-[#171717]"> {dayjs(Number(value) * 1000).format('YYYY-MM-DD HH:mm:ss')}</p>
      },
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      key: 'creator',
      render: (value: string) => {
        return <div>{value}</div>
      },
    },
    {
      title: '操作',
      render: (_: any, record: Doc) => {
        return (
          <>
            <Popover
              placement="top"
              content={
                <div>
                  {scenarioId ? (
                    <div>
                      <Button
                        type="text"
                        className="flex w-full items-center px-1 font-medium"
                        icon={<ExportOutlined />}
                        // iconPosition={'end'}
                        onClick={() => {
                          const queryObject = new URLSearchParams({
                            filterName: encodeURIComponent(record.name),
                          })
                          navigate(`${askBIPageUrls.metricStore.document.list}?${queryObject.toString()}`)
                        }}
                      >
                        文档管理
                      </Button>

                      <Popconfirm
                        placement="top"
                        title={'确认'}
                        description={'请确认从场景下移除该文档，仍可在文档列表内查看'}
                        okText="确定"
                        cancelText="取消"
                        onConfirm={() => {
                          deleteSceneFile([Number(record.id)], scenarioId)
                        }}
                      >
                        <Button type="text" className="w-full px-1 text-left font-medium">
                          从场景移除
                        </Button>
                      </Popconfirm>
                    </div>
                  ) : (
                    <div className="w-36">
                      <Button
                        type="text"
                        className="w-full px-1 text-left font-medium"
                        onClick={() => {
                          setCurrentClickItem(record)
                          setIsModalOpen(true)
                          editForm.setFieldsValue({ name: record.name, sceneIdList: record.sceneIdList })
                        }}
                      >
                        编辑
                      </Button>
                      <br />
                      <Popconfirm
                        placement="top"
                        title={'删除提示'}
                        description={`所有场景内都将同步删除该文档，请确认`}
                        okText="确定"
                        cancelText="取消"
                        onConfirm={() => {
                          deleteSceneFile([Number(record.id)], undefined)
                        }}
                      >
                        <Button type="text" className="w-full px-1 text-left font-medium">
                          删除
                        </Button>
                      </Popconfirm>
                    </div>
                  )}
                  <Button
                    type="text"
                    className="w-full px-1 text-left font-medium"
                    onClick={() => {
                      handleDownloadFile(record)
                    }}
                  >
                    下载
                  </Button>
                </div>
              }
            >
              <MoreOutlined className="cursor-pointer" />
            </Popover>
          </>
        )
      },
    },
  ]

  return (
    <div>
      <div className="mb-6 mt-4 flex h-9 items-center justify-between">
        <div className="text-black">全部 ({listTotal})</div>
        <div className="flex">
          <Form
            layout={'inline'}
            form={form}
            onValuesChange={debounceHandleValuesChange}
            autoComplete="off"
            initialValues={{ fileName: filterName || '' }}
          >
            <Form.Item label="文件名" name="fileName">
              <Input placeholder={'文件名'} style={{ width }} />
            </Form.Item>
            <Form.Item label="类型" name="fileType">
              <Select options={mimeTypeList} placeholder={'类型'} style={{ width }} />
            </Form.Item>
            {!scenarioId && (
              <Form.Item label="场景" name="sceneId">
                <Select
                  placeholder={'场景'}
                  style={{ minWidth: width }}
                  options={(scenesList || []).map((item) => ({ label: item.label, value: item.id }))}
                />
              </Form.Item>
            )}
            {/* <Form.Item label="创建人" name="creator">
              <Input placeholder={'创建人'} style={{ width }} />
            </Form.Item> */}
          </Form>
          {location.pathname !== askBIPageUrls.metricStore.document.list && (
            <Button icon={<SelectOutlined />} onClick={() => handleSelectAllFileAddScene()} className="mr-2">
              选择已有文件
            </Button>
          )}
          <Button danger icon={<DeleteOutlined />} onClick={() => handleDeleteFiles()} className="mr-2">
            批量删除
          </Button>
          <Button type="primary" icon={<CloudUploadOutlined />} onClick={() => handleUploadFile()}>
            上传文档
          </Button>
        </div>
      </div>

      <Table
        rowSelection={rowSelection}
        loading={isLoading}
        columns={columns}
        dataSource={fileList}
        size="small"
        rowKey={(record) => record.id || nanoid()} // 确保每一行都有唯一的 key
        pagination={{ ...pagination, hideOnSinglePage: true }}
        scroll={{ x: 'max-content' }}
        onChange={handlePageChange}
      />
      <Modal title="编辑" open={isModalOpen} onOk={handleOk} onCancel={handleCancel}>
        <Form form={editForm} name="validateOnly" layout="vertical" autoComplete="off">
          <Form.Item name="name" label="文档名称">
            <Input placeholder={'文档名称'} />
          </Form.Item>
          <Form.Item name="sceneIdList" label="所属场景">
            <Select
              mode="tags"
              placeholder={'所属场景'}
              style={{ width: '100%' }}
              onChange={handleEditSceneChange}
              options={(scenesList || []).map((item) => ({ label: item.label, value: item.id }))}
            />
          </Form.Item>
        </Form>
      </Modal>

      <CustomUploadModal
        isShowUploadModal={isShowUploadModal}
        setIsShowUploadModal={setIsShowUploadModal}
        onGetFoldersList={onFinishUpload}
        isDragUpload={true}
        multiple={true}
        accept={'.pdf,.docx,.xlsx'}
        folderId={scenarioId || ''}
        uploadType={'doc'}
      />

      <Modal
        title={`选择已有文件关联至${scenesList.find((scene) => scene.id === scenarioId)?.label}中`}
        width={1200}
        open={isShowSelectAllFileModalOpen}
        onOk={handleSelectAllFileModalOk}
        onCancel={handleSelectAllFileModalCancel}
        footer={[
          <Button key="back" onClick={handleSelectAllFileModalCancel}>
            取消
          </Button>,
          <Button key="submit" type="primary" loading={showAddSceneLoading} onClick={handleSelectAllFileModalOk}>
            确定
          </Button>,
        ]}
      >
        {isShowSelectAllFileModalOpen && <DocumentData isModalInstance={true} />}
      </Modal>
    </div>
  )
}
