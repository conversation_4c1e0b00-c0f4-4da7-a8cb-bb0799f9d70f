import React, { useRef, useState } from 'react'
import { useAtomValue } from 'jotai'
import SpeechRecognitionWrapper from '/public/img/speech-recognition-wrapper.svg'
import useLocalSpeechRecognition from 'src/client/hooks/useLocalSpeechRecognition'
import { isIOS } from 'src/client/utils'
import { metricConfigAtom } from 'src/client/pages/AskBI/askBIAtoms'
import useIflytekSpeechRecognition from 'src/client/hooks/useIflytekSpeechRecognition'
import { isBaoWu } from 'src/shared/baowu-share-utils'
import './CustomSpeechRecognition.css'
import { microphoneEmpty, SvgIcon } from '../SvgIcon'
import WaveAnime from './WaveAnime'

export default function CustomSpeechRecognition({ onSubmit }: { onSubmit: (message?: string) => void }) {
  const [isPressed, setIsPressed] = useState(false)
  const metricConfig = useAtomValue(metricConfigAtom)
  const isStartedRef = useRef(false)

  const { isRecognitionActive, startRecognition, stopRecognition } = useLocalSpeechRecognition(onSubmit)
  const {
    isRecognitionActive: isBaowuRecognitionActive,
    startRecognition: startBaowuRecognition,
    stopRecognition: stopBaowuRecognition,
  } = useIflytekSpeechRecognition(onSubmit)

  const startPress = () => {
    if (isStartedRef.current) {
      return
    }
    isStartedRef.current = true
    setTimeout(() => {
      // 防止短时间内多次点击, 导致出现音频闭包问题(快速点击时, 获取音频权限部分有延时,会导致本不应继续执行的代码被继续执行, 从而无法关闭音频流)
      isStartedRef.current = false
    }, 500)
    console.info('Start Press =>', isBaoWu(metricConfig?.metricTableName))
    // 宝武微聊ios app内调用本地语音识别,防止闪退
    if (isBaoWu(metricConfig?.metricTableName)) {
      setIsPressed(true)
      startBaowuRecognition()
      return
    } else if (isIOS) {
      // 其他场景的ios设备,调用本地的
      setIsPressed(true)
      startRecognition()
      return
    } else {
      return
    }
  }

  const endPress = () => {
    console.info('Stop Press =>', isBaoWu(metricConfig?.metricTableName))
    // 宝武微聊ios app内调用本地语音识别,防止闪退
    if (isBaoWu(metricConfig?.metricTableName)) {
      setIsPressed(false)
      stopBaowuRecognition()
    } else if (isIOS) {
      setTimeout(() => {
        // 本地语音识别需要点时间,所以手指移开后, 过一小会再停止, 增加一点识别率
        stopRecognition()
      }, 800)
      setIsPressed(false)
      return
    } else {
      return
    }
  }

  return (
    <div
      className="custom-speech-recognition-wrapper z-1 w-full"
      onMouseDown={startPress}
      onMouseUp={endPress}
      onMouseLeave={endPress}
      onTouchStart={startPress}
      onTouchEnd={endPress}
    >
      <div className="custom-speech-recognition-button w-full select-none rounded-xl text-center text-base text-white">
        按住说话
      </div>
      {/* 按住,且语音识别就绪后再显示识别中样式 */}
      {isPressed && (isBaowuRecognitionActive || isRecognitionActive) ? (
        <div>
          <div className="fixed bottom-0 left-0 right-0 top-0 z-0 bg-black opacity-70" />
          <div className="fixed left-0 right-0 top-1/2 z-10 mx-auto mt-[-25%] w-[186px]">
            <WaveAnime />
            <img src={SpeechRecognitionWrapper} alt="" />
          </div>
          <div className="custom-speech-recognition-pressed-block fixed bottom-0 left-0 right-0 z-10 flex h-[179px] select-none flex-col items-center rounded-t-[50px] bg-white py-5">
            <span className="text-base text-white">松开 发送</span>
            <span className="mt-10">
              <SvgIcon className="size-9" icon={microphoneEmpty} />
            </span>
          </div>
        </div>
      ) : null}
    </div>
  )
}
