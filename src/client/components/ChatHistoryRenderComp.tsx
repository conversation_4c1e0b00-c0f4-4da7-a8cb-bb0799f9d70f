/**
 * @description 聊天历史中的一次聊天记录，包含一问一答
 */
import React from 'react'
import { InfoCircleOutlined, SyncOutlined } from '@ant-design/icons'
import { Light as SyntaxHighlighter } from 'react-syntax-highlighter'
import sql from 'react-syntax-highlighter/dist/cjs/languages/hljs/sql'
import { Button, Divider } from 'antd'
import {
  AssistantTextChatItem,
  ChatStatus,
  AssistantMetricNotExistChatItem,
  AssistantDataOverview,
  AssistantTableList,
  AssistantD<PERSON><PERSON>ionD<PERSON><PERSON>,
  AssistantDimensionList,
  AssistantMetricList,
  Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Assistant<PERSON><PERSON><PERSON><PERSON>,
  AssistantMetric<PERSON>ree,
  AssistantMetricDetail,
  AnsChatItem,
} from '@shared/common-types'
import { assertExhaustive } from '@shared/common-utils'
import { askBIPageUrls } from 'src/shared/url-map'
import { SvgIcon, agentAskBIIcon, agentAskDocIcon } from './SvgIcon'

SyntaxHighlighter.registerLanguage('sql', sql)

const renderChatAnsHelloTextItem = (text: string, idx: number) => {
  return (
    <div key={idx} className="flex w-full flex-col">
      {text.split('\n').map((line, index) => (
        <React.Fragment key={index}>{line}</React.Fragment>
      ))}
      <Divider style={{ margin: '16px 0px' }} />
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="flex max-w-[300px] flex-col gap-2">
          <div className="flex items-center gap-2">
            <SvgIcon icon={agentAskBIIcon} className="h-6 w-6" />
            <span className="font-bold tracking-wider">AskBI</span>
          </div>
          <div>帮你处理数据分析需求，你只需针对可用指标和维度提问</div>
        </div>
        <div className="flex max-w-[300px] flex-col gap-2">
          <div className="flex items-center gap-2">
            <SvgIcon icon={agentAskDocIcon} className="h-6 w-6" />
            <span className="font-bold tracking-wider">AskDoc</span>
          </div>
          <div>根据你提出的问题搜索企业相关文档，帮助你获取更多信息，领先一步</div>
        </div>
      </div>
    </div>
  )
}

// 用于普通text展示
const renderChatAnsTextItem = (
  chatAnsItem: AnsChatItem | AnsChatItem,
  content: AssistantChitchat | AssistantLlmError | AssistantTextChatItem,
  idx: number,
) => {
  const { text } = content
  const lines = text.split('\n')
  switch (chatAnsItem.status) {
    case ChatStatus.success:
      return (
        <div key={idx}>
          {lines.map((line, index) => (
            <React.Fragment key={index}>
              {line}
              <br />
            </React.Fragment>
          ))}
        </div>
      )
    case ChatStatus.pending:
      return (
        <div key={idx} className="flex">
          {text}
          <SyncOutlined spin className="ml-4 flex flex-none text-xl text-gray-400 dark:text-gray-500" />
        </div>
      )
    case ChatStatus.failure:
      return (
        <div key={idx} className="text-red-400 dark:text-red-500">
          {text}
        </div>
      )
    default:
      return assertExhaustive(chatAnsItem.status)
  }
}

// TODO: 每个元数据类型单独实现
const renderChatAnsMetaItem = (
  chatAnsItem: AnsChatItem,
  content:
    | AssistantDataOverview
    | AssistantTableList
    | AssistantDimensionDetail
    | AssistantDimensionList
    | AssistantMetricList
    | AssistantMetricDetail
    | AssistantMetricTree,
  idx: number,
) => {
  const { rows } = content
  const lines = rows.split('\n')
  switch (chatAnsItem.status) {
    case ChatStatus.success:
      return (
        <div key={idx}>
          {lines.map((line, index) => (
            <React.Fragment key={index}>
              {line}
              <br />
            </React.Fragment>
          ))}
        </div>
      )
    case ChatStatus.pending:
      return (
        <div key={idx} className="flex">
          {lines}
          <SyncOutlined spin className="ml-4 flex flex-none text-xl text-gray-400 dark:text-gray-500" />
        </div>
      )
    case ChatStatus.failure:
      return (
        <div key={idx} className="text-red-400 dark:text-red-500">
          {lines}
        </div>
      )
    default:
      return assertExhaustive(chatAnsItem.status)
  }
}

const renderNotExistMetric = (data: AssistantMetricNotExistChatItem, idx: number) => {
  return (
    <div key={idx} className="flex w-full items-center gap-4 rounded-md bg-yellow-200 px-2 py-1 dark:bg-yellow-700">
      <div>
        <InfoCircleOutlined className="mr-1 h-4 w-4" />有{data.names.length}个指标不存在：
      </div>
      <div className="flex gap-2">
        {data.names.map((name) => {
          return (
            <div key={name} className="flex rounded-lg bg-purple-100 px-2 py-1 text-sm">
              {name}
            </div>
          )
        })}
      </div>
      <Button
        size="small"
        className="ml-4"
        onClick={() => {
          window.open(askBIPageUrls.metricStore.metrics.create, '_blank')
        }}
      >
        开始创建
      </Button>
    </div>
  )
}

export { renderNotExistMetric, renderChatAnsMetaItem, renderChatAnsTextItem, renderChatAnsHelloTextItem }
