import React from 'react'
import { MessageFilled } from '@ant-design/icons'
import { AssistantChatItem } from 'src/shared/common-types'

export default function SeriesQuestionNotice({ data }: { data: AssistantChatItem }) {
  if (
    !('queryParamsVerified' in data) ||
    ('queryParamsVerified' in data && !data.queryParamsVerified?.queryParams?.extraInfo?.sequential_first_question)
  ) {
    return null
  }
  return (
    <div className="flex border-b border-dashed pb-5 text-base">
      <div className="mr-2.5">
        <MessageFilled className="text-xl text-primary" />
      </div>
      <div className="test-[#171717]">
        请使用单个问题的方式向小宝提问哦～小宝这边先帮您找到了第一个问题
        <span className="font-bold">
          “{data.queryParamsVerified?.queryParams?.extraInfo?.sequential_first_question}”
        </span>
        的答案
      </div>
    </div>
  )
}
