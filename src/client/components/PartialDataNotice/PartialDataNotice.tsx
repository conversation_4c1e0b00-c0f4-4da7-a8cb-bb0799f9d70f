import React from 'react'
import { AssistantChatItem } from 'src/shared/common-types'
import { noticeBellIcon, SvgIcon } from '../SvgIcon'

export default function PartialDataNotice({ data }: { data: AssistantChatItem }) {
  if (!('isPartialRow' in data) || ('isPartialRow' in data && !data.isPartialRow)) {
    return null
  }
  return (
    <div className="flex rounded-lg bg-[#dbf6f1] px-4 py-3">
      <div className="mr-2.5">
        <SvgIcon className="text-[#42CEE1]" icon={noticeBellIcon} />
      </div>
      <div className="test-[#171717]">{data.partialRowMsg}</div>
    </div>
  )
}
