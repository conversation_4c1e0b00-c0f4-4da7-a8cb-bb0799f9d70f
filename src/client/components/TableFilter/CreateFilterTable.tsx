import React, { useCallback, useEffect, useRef, useState } from 'react'
import { App, Form, Input, Modal } from 'antd'
import { useRequest } from 'ahooks'
import axios from 'axios'
import { useNavigate } from 'react-router-dom'
import DBSelect from '@model/DBSelect'
import { askBIApiUrls } from 'src/shared/url-map'
import { routerMap } from 'src/client/x-engine/widget/router/routerMap'
import { DataModelDescFilterType } from 'src/shared/xengine-types'
import TableFilterDrawer, {
  type OriginColumnsType,
  formatTableFilterDrawerColumnOptionsData,
} from './TableFilterDrawer'
export type DataModelDescType = {
  dataModelDescType: string
  measures: []
  factTable: string
  joinDag: {
    edges: []
    vertices: {
      dummy: false
      id: string
      table: string
      kind: 'FACT'
      timeColumn: null
      tableType: string
      dimensionsColumns: {
        name: string
      }[]
    }[]
  }
  filter: DataModelDescFilterType
}

export default function CreateFilterTable({
  open,
  onClose,
  tableData,
}: {
  open: boolean
  onClose?: () => void
  tableData?: OriginColumnsType
}) {
  const [form] = Form.useForm()
  const [drawerForm] = Form.useForm()
  const navigate = useNavigate()
  const [tableCfgModalOpen, setTableCfgModalOpen] = useState(false)
  const { message } = App.useApp()
  const filterRef = useRef<DataModelDescFilterType>()

  const { loading, run } = useRequest((data) => axios.post(askBIApiUrls.xengineVTable.create, data), {
    manual: true,
    onSuccess(_, params) {
      message.success('数据清洗成功')
      setTableCfgModalOpen(false)
      form.resetFields()
      onClose?.()
      navigate(
        `${routerMap.dataModel.businessVirtualTable.path}?catalog=${params[0].table.catalogName}&database=${params[0].table.databaseName}&name=${params[0].table.name}`,
      )
    },
    onError() {
      message.error('数据清洗失败')
    },
  })

  const handleOk = useCallback(async () => {
    if (tableData) {
      const filter = filterRef.current
      const { catalogName, databaseName, name, computeType, columns, id } = tableData
      const completeTable = `${catalogName}.${databaseName}.${name}`
      const { catalog, database, name: modelName } = await form.validateFields()
      const data = {
        table: {
          catalogName: catalog,
          databaseName: database,
          name: modelName,
          dataModelDesc: {
            catalog,
            modelName: name,
            dataModelDescType: computeType,
            measures: [],
            factTable: completeTable,
            joinDag: {
              edges: [],
              vertices: [
                {
                  dummy: false,
                  id,
                  table: completeTable,
                  kind: 'FACT',
                  timeColumn: null,
                  dimensionsColumns: columns.map((col: { name: string }) => `${completeTable}.${col.name}`),
                  tableType: computeType,
                },
              ],
            },
            filter,
          },
        },
      }
      run(data)
    }
  }, [run, form, tableData])

  // todo：编辑情况下的initValue
  useEffect(() => {
    if (!open) {
      drawerForm.resetFields()
    }
  }, [open, drawerForm])

  return (
    <>
      <TableFilterDrawer
        open={open}
        columnOptionsData={tableData ? formatTableFilterDrawerColumnOptionsData([tableData]) : []}
        onClose={() => onClose?.()}
        onConfirm={(filter) => {
          filterRef.current = filter
          setTableCfgModalOpen(true)
        }}
        form={drawerForm}
      />
      <Modal
        open={tableCfgModalOpen}
        okButtonProps={{
          loading,
        }}
        title={'选择业务虚拟表配置'}
        width={600}
        destroyOnClose
        onCancel={() => {
          setTableCfgModalOpen(false)
          form.resetFields()
        }}
        onOk={handleOk}
      >
        <Form layout="vertical" form={form}>
          <DBSelect span={24} required={{ catalog: true, database: true }} setFieldValue={form.setFieldValue} />
          <Form.Item
            label="虚拟表名称"
            name="name"
            required
            rules={[{ required: true, message: '请输入业务虚拟表名称' }]}
          >
            <Input placeholder={'请输入虚拟表的名称'} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}
