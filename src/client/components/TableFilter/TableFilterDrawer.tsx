import React, { ReactNode } from 'react'
import { Drawer, Form, Input, Select, Cascader, Typography, Popover, Space, Button, FormInstance } from 'antd'
import { PlusOutlined, MinusOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import clsx from 'clsx'
import axios from 'axios'
import { useRequest } from 'ahooks'
import { askBIApiUrls } from 'src/shared/url-map'
import { DataModelDescFilterType } from 'src/shared/xengine-types'

export type ColumnOption = {
  value: string
  label: string
  children?: ColumnOption[]
}

export type OriginColumnsType = {
  id: string
  catalogName: string
  databaseName: string
  name: string
  columns: { name: string }[]
  [key: string]: any
}

export function formatTableFilterDrawerColumnOptionsData(
  originData: {
    // 表示表的id值，对应vertexId的值，请在id有其他取值意思的时候移除id值。
    id: string
    catalogName: string
    databaseName: string
    name: string
    columns: { name: string }[]
  }[],
): ColumnOption[] {
  const nameChildrenMap: Record<string, ColumnOption> = {}
  const results: ColumnOption[] = []
  ;(originData || []).forEach((item) => {
    const { name, catalogName, databaseName, columns = [], id } = item
    const catalogKey = `catalog_${catalogName}`
    const databaseKey = `database_${catalogName}_${databaseName}`
    const tableKey = `%table%~${catalogName}~${databaseName}~${name}~${id}`
    if (!nameChildrenMap[catalogKey]) {
      const catalogOption = { label: catalogName, value: catalogName, children: [] }
      results.push(catalogOption)
      nameChildrenMap[catalogKey] = catalogOption
    }
    if (!nameChildrenMap[databaseKey]) {
      const databaseOption = { label: databaseName, value: databaseName, children: [] }
      nameChildrenMap[databaseKey] = databaseOption
      nameChildrenMap[catalogKey]?.children?.push(databaseOption)
    }
    if (!nameChildrenMap[tableKey]) {
      const tableOption = {
        label: name,
        value: tableKey,
        children: columns.map((col) => ({
          value: col.name,
          label: col.name,
        })),
      }
      nameChildrenMap[tableKey] = tableOption
      nameChildrenMap[databaseKey].children?.push(tableOption)
    }
  })
  return results
}

export function transformFormToRequestBody({
  filterExpr = '',
  conditions = [],
}: {
  filterExpr: string
  conditions: { column: string[]; operator: string; [key: string]: string | string[] }[]
}) {
  const resolvedConditions = conditions.map(
    (condition: { column: string[]; operator: string; [key: string]: string | string[] }, index: number) => {
      const params = []
      const tableInfo = condition.column[2].split('~')
      if (tableInfo[0] === '%table%') {
        params.push({
          type: 'COLUMN',
          value: `${tableInfo.slice(1, 4).join('.')}.${condition.column[3]}`,
          // todo：跟后端协商暂时不传递，后面有需要再传递
          // valueType: 'STRING',
          vertexId: tableInfo[4],
        })
        Object.keys(condition).forEach((key) => {
          switch (key) {
            case 'COMMON': {
              params.push({
                type: 'CONSTANT',
                value: condition[key] as string,
              })
              break
            }
            case 'BETWEEN_START': {
              params.push(
                {
                  type: 'CONSTANT',
                  value: condition[key] as string,
                },
                {
                  type: 'CONSTANT',
                  value: condition['BETWEEN_END'] as string,
                },
              )
              break
            }
            case 'IN_VALUES': {
              params.push(
                ...(condition['IN_VALUES'] as string[]).map((value: string) => ({
                  type: 'CONSTANT',
                  value,
                })),
              )
              break
            }
            default: {
              break
            }
          }
        })
      }
      return {
        params,
        operator: condition.operator,
        alias: `filter${index + 1}`,
      }
    },
  )
  return {
    filterExpr,
    conditions: resolvedConditions,
  } as DataModelDescFilterType
}

function FilterItemWrap({
  className,
  children,
  extra,
}: {
  className?: string
  children: ReactNode
  extra?: ReactNode
}) {
  return (
    <div className={clsx('m-auto my-4 flex justify-center', className)}>
      <div className="w-[800px] flex-none rounded-lg bg-white p-5">{children}</div>
      {extra && <div className="ml-2 flex-none">{extra}</div>}
    </div>
  )
}

export default function TableFilterDrawer({
  open,
  columnOptionsData,
  onClose,
  onConfirm,
  form,
  loading,
}: {
  columnOptionsData: ColumnOption[]
  open: boolean
  loading?: boolean
  onClose?: () => void
  onConfirm?: (value: any) => void
  form: FormInstance<any>
}) {
  const { data: compareOperators, loading: compareOperatorsLoading } = useRequest(() =>
    axios.get(askBIApiUrls.xengineVTable.compareOperator)?.then((res) => res?.data.data),
  )

  // todo：后端示例有待完整
  const renderParamsItemByOperator = (key: number, operator: string) => {
    switch (operator) {
      case 'NOT_IN':
      case 'IN': {
        return (
          <Form.Item label="值" required>
            <Form.List name={[key, 'IN_VALUES']} initialValue={['']}>
              {(fields, { add, remove }) => {
                return fields.map((field) => (
                  <div className="flex items-center" key={field.key}>
                    <Form.Item
                      name={field.key}
                      className="flex-1"
                      rules={[
                        {
                          required: true,
                          message: '请输入值',
                        },
                      ]}
                    >
                      <Input placeholder="请输入" />
                    </Form.Item>

                    <PlusOutlined className="mx-1 mb-[24px] flex-none cursor-pointer" onClick={() => add()} />
                    <Typography.Text disabled={fields.length <= 1} className="cursor-pointer">
                      <MinusOutlined
                        className="mb-[24px] flex-none"
                        onClick={() => fields.length > 1 && remove(field.key)}
                      />
                    </Typography.Text>
                  </div>
                ))
              }}
            </Form.List>
          </Form.Item>
        )
      }
      case 'BETWEEN': {
        return (
          <>
            <Form.Item name={[key, 'BETWEEN_START']} label="开始的值" rules={[{ required: true }]}>
              <Input allowClear placeholder="请输入" />
            </Form.Item>
            <Form.Item name={[key, 'BETWEEN_END']} label="结束的值" rules={[{ required: true }]}>
              <Input allowClear placeholder="请输入" />
            </Form.Item>
          </>
        )
      }
      default: {
        return (
          <Form.Item name={[key, 'COMMON']} label="值" rules={[{ required: true }]}>
            <Input allowClear placeholder="请输入" />
          </Form.Item>
        )
      }
    }
  }

  async function handleConfirm() {
    const data = await form.validateFields()
    onConfirm?.(transformFormToRequestBody(data))
  }
  return (
    <Drawer
      loading={loading}
      title="数据处理"
      open={open}
      placement="bottom"
      height="calc(100vh - 20px)"
      classNames={{
        body: 'bg-[#F8F8F8]',
      }}
      closeIcon={null}
      extra={
        <Space>
          <Button
            onClick={() => {
              onClose?.()
            }}
          >
            取消
          </Button>
          <Button type="primary" onClick={handleConfirm}>
            确定
          </Button>
        </Space>
      }
    >
      <Form
        form={form}
        initialValues={{
          conditions: [{}],
        }}
        labelCol={{
          flex: '80px',
        }}
        labelAlign="left"
      >
        <FilterItemWrap>
          <div className="flex items-center">
            <Form.Item className="flex-grow" name="filterExpr" label="关系设置">
              <Input placeholder="请输入关系设置" allowClear />
            </Form.Item>
            <div className="mb-[24px] ml-2 cursor-pointer text-[#503CE4]">
              <Popover
                content={
                  <div className="w-[380px]">
                    <div className="mb-2 rounded-md bg-[#f5f5f5] px-3 py-1">(filter01 or filter02) and filter03</div>
                    数据过滤的逻辑：先处理filter01和filter02的关系，满足两者任一后再与符合filter01的取交集作为最终过滤条件
                  </div>
                }
                placement="bottom"
              >
                <QuestionCircleOutlined className="mr-1" />
                <span>示例</span>
              </Popover>
            </div>
          </div>
        </FilterItemWrap>
        <Form.List name="conditions">
          {(fields, { add, remove }) => (
            <>
              {fields.map((field) => {
                return (
                  <FilterItemWrap
                    className="pl-[22px]"
                    key={field.key}
                    extra={
                      <Typography.Text
                        disabled={fields.length <= 1}
                        onClick={() => fields.length > 1 && remove(field.name)}
                        className={clsx('cursor-pointer text-[#503CE4]')}
                      >
                        <MinusOutlined />
                      </Typography.Text>
                    }
                  >
                    <div className="mb-2 leading-5 text-[#575757]">{`filter${field.key + 1}`}</div>
                    <Form.Item label="表&字段" name={[field.name, 'column']} rules={[{ required: true }]}>
                      <Cascader options={columnOptionsData} placeholder="请选择" showSearch />
                    </Form.Item>
                    <Form.Item label="过滤条件" name={[field.name, 'operator']} rules={[{ required: true }]}>
                      <Select
                        filterSort={(optionA, optionB) =>
                          (`${optionA?.desc}` ?? '')
                            .toLowerCase()
                            .localeCompare((`${optionB?.desc}` ?? '').toLowerCase())
                        }
                        loading={compareOperatorsLoading}
                        placeholder="请选择"
                        optionFilterProp="desc"
                        showSearch
                        fieldNames={{ label: 'desc' }}
                        options={compareOperators}
                      />
                    </Form.Item>
                    <Form.Item noStyle shouldUpdate>
                      {({ getFieldValue }) => {
                        return renderParamsItemByOperator(
                          field.name,
                          getFieldValue(['conditions', field.name, 'operator']),
                        )
                      }}
                    </Form.Item>
                  </FilterItemWrap>
                )
              })}
              <div className="m-auto my-5 w-[800px] text-[#503CE4]">
                <div className="inline-block cursor-pointer" onClick={add}>
                  <PlusOutlined />
                  <span className="ml-1">Filter</span>
                </div>
              </div>
            </>
          )}
        </Form.List>
      </Form>
    </Drawer>
  )
}
