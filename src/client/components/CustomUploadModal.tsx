import { App, UploadProps, Modal, UploadFile, Button } from 'antd'
import React, { useState } from 'react'
import type { UploadRequestOption } from 'rc-upload/lib/interface'
import axios from 'axios'
import Dragger from 'antd/es/upload/Dragger'
import { PlusOutlined } from '@ant-design/icons'
import { askDocApiUrls } from '@shared/url-map'
import { MAX_COUNT_UPLOAD_LIMIT } from 'src/shared/constants'

type UploadType = 'doc' | 'report' // doc是doc的文件，report是标识是上传报告的
interface Props {
  className?: string
  folderId?: string
  accept?: string // 接受文档的类型 默认pdf
  multiple?: boolean // 文档上传是否支持一次上传多个
  onGetFoldersList?: (fileInfo: { fileName: string; fileId: string }) => void
  children?: JSX.Element // upload下的内容，可为button或icon等
  maxCount?: number // 最大上传数量
  isDragUpload?: boolean // 是否支持拖拽上传
  uploadType: UploadType // doc是doc的文件，report是标识是上传报告的
  isShowUploadModal: boolean
  setIsShowUploadModal: (value: boolean) => void
}

export const getCustomUploadFormData = async (
  options: UploadRequestOption,
  uploadType: UploadType,
  folderId?: string,
) => {
  const { file } = options
  const formData = new FormData()
  formData.append('files', file)
  folderId && formData.append('folderId', folderId as string)
  formData.append('parserType', 'text')
  formData.append('uploadType', uploadType)
  return formData
}

/**
 * 自定义上传文件 - 支持同步上传文件id以及pdf的第一页缩略图展示
 * @param props
 * @returns
 */
function CustomUploadModal(props: Props) {
  const {
    folderId,
    onGetFoldersList,
    maxCount = MAX_COUNT_UPLOAD_LIMIT,
    accept,
    multiple = false,
    uploadType,
    isShowUploadModal,
    setIsShowUploadModal,
  } = props
  const { message } = App.useApp()
  const [fileList, setFileList] = useState<UploadFile[]>([])
  const [showLoading, setShowLoading] = useState<boolean>(false)

  const uploadProps: UploadProps = {
    name: 'files',
    multiple,
    accept,
    maxCount: maxCount,
    fileList,
    onChange: (info) => {
      let newFileList = [...info.fileList]
      newFileList = newFileList.slice(-maxCount)
      setFileList(newFileList)
    },
    beforeUpload: () => {
      return false
    },
  }

  /**
   *  调用上传文件接口
   */
  const handleUploadFiles = async (formData: FormData, file: UploadFile) => {
    try {
      const response = await axios.post(askDocApiUrls.uploadFile, formData, {
        timeout: 2 * 60 * 1000, // 可能会有大文件上传，所以设置2分钟
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
      const result = response.data.data
      onGetFoldersList && onGetFoldersList(result || null)
      setFileList((prevFileList) =>
        prevFileList.map((item) =>
          item.uid === file.uid ? { ...item, status: 'done', response: response.data } : item,
        ),
      )
      message.success(`${file.name} 上传成功！`)
      setShowLoading(false)
      setFileList([])
      setIsShowUploadModal(false)
    } catch (error: any) {
      console.error(error)
      setShowLoading(false)
      message.error(`上传失败！${error.message}`)
      setFileList((prevFileList) =>
        prevFileList.map((item) => (item.uid === file.uid ? { ...item, status: 'error' } : item)),
      )
    }
  }

  /**
   * 上传文件
   * @returns
   */
  const customUpload = async (options: UploadRequestOption) => {
    try {
      const tempFolderId = folderId
      const resFormData = await getCustomUploadFormData(options, uploadType, tempFolderId)
      handleUploadFiles(resFormData, options.file as UploadFile)
    } catch (error) {
      setShowLoading(false)
    }
  }

  const handleOk = async () => {
    setShowLoading(true)
    for (const file of fileList) {
      const options = {
        file: file.originFileObj,
        onProgress: (e: any) => console.info(e),
      } as unknown as UploadRequestOption
      await customUpload(options)
    }
  }

  const handleCancel = () => {
    setFileList([])
    setShowLoading(false)
    setIsShowUploadModal(false)
  }

  return (
    <Modal
      title="上传文档"
      open={isShowUploadModal}
      onOk={handleOk}
      onCancel={handleCancel}
      footer={[
        <Button key="back" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={showLoading} onClick={handleOk}>
          确定
        </Button>,
      ]}
    >
      <Dragger {...uploadProps}>
        <div className="ant-upload-drag-icon mt-3 text-5xl">
          <PlusOutlined />
        </div>
        <p className="font-medium dark:text-white">点击或拖拽文件到此处上传</p>
        <div className="w-full pt-4 text-center text-sm dark:text-[#b8bcc5]">
          {/* <p>文件类型：支持.pdf，.docx，.html，.pptx，.xlsx文件</p> */}
          <p>文件类型：支持.pdf，.docx，.xlsx文件</p>
        </div>
      </Dragger>
    </Modal>
  )
}

export default CustomUploadModal
