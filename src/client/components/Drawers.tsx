import React from 'react'
import { Drawer } from 'antd'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import {
  chatsAtom,
  currentDatasetAtom,
  metricConfigAtom,
  showAskHistoryListAtom,
  confidenceDrawerConfigAtom,
  chatQueryParamsChangeAtom,
} from '../pages/AskBI/askBIAtoms'
import HistoryAsks from './AskList/HistoryAsks'
import MetricForceMatch from './MetricForceMatch'

const Drawers = () => {
  const [askHistoryOpen, setAskHistoryOpen] = useAtom(showAskHistoryListAtom)
  const [confidenceDrawerConfig, setConfidenceDrawerConfig] = useAtom(confidenceDrawerConfigAtom)
  const chats = useAtomValue(chatsAtom)
  const currentDataset = useAtomValue(currentDatasetAtom)
  const metricConfig = useAtomValue(metricConfigAtom)
  const chatQueryParamsChange = useSet<PERSON>tom(chatQueryParamsChange<PERSON>tom)

  return (
    <>
      <Drawer
        placement="bottom"
        width="100%"
        height="100%"
        style={{ backgroundColor: '#F5F5F7' }}
        styles={{
          header: { border: 'none' },
          body: { padding: 0 },
        }}
        open={askHistoryOpen}
        onClose={() => {
          setAskHistoryOpen(false)
        }}
      >
        <HistoryAsks />
      </Drawer>

      <Drawer
        placement="bottom"
        width="100%"
        height="50%"
        style={{ backgroundColor: '#F5F5F7' }}
        styles={{
          header: { border: 'none' },
          body: { padding: '0px 16px 16px 16px' },
        }}
        open={confidenceDrawerConfig.open}
        onClose={() => {
          setConfidenceDrawerConfig({
            open: false,
            chatId: null,
          })
        }}
        destroyOnClose
      >
        {confidenceDrawerConfig.chatId ? (
          <MetricForceMatch
            metricConfig={metricConfig}
            onQueryParamsChange={(queryParamsVerified, selectedSceneId) => {
              chatQueryParamsChange({
                queryParamsVerified,
                chatId: confidenceDrawerConfig.chatId || '',
                sceneId: selectedSceneId || currentDataset?.sceneId || '',
              })
            }}
            isViewMode={false}
            chatId={confidenceDrawerConfig.chatId || ''}
            currentConfidenceSelection={
              chats.find((i) => i.id === confidenceDrawerConfig.chatId)?.stateMap?.currentConfidenceSelection
            }
            onClose={() => {
              setConfidenceDrawerConfig({
                open: false,
                chatId: null,
              })
            }}
            onSubmit={() => {
              setConfidenceDrawerConfig({
                open: false,
                chatId: null,
              })
            }}
          />
        ) : null}
      </Drawer>
    </>
  )
}

export default Drawers
