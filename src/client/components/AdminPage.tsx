/**
 * 管理页面的 Page 组件
 */
import React from 'react'
import clsx from 'clsx'

interface Props {
  className?: string
  title: React.ReactNode
  children: React.ReactNode
}

export default function AdminPage(props: Props) {
  return (
    <div className={clsx('admin-page flex w-full flex-col gap-4', props.className)}>
      <h1 className="text-[22px] font-bold text-[#101828] dark:text-gray-200">{props.title}</h1>
      {props.children}
    </div>
  )
}
