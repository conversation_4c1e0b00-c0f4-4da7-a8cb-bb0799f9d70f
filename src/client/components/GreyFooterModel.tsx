import React from 'react'
import { Button, Space, Modal, type ModalProps, type ButtonProps } from 'antd'

export default function GreyFooterModel({
  title,
  children,
  cancelButtonProps,
  cancelText,
  okButtonProps,
  okText,
  okType,
  onOk,
  onCancel,
  ...resProps
}: ModalProps) {
  function renderFooterButtons() {
    return (
      <Space>
        <Button
          className="font-[PingFang SC] mr-[6px] w-[74px] rounded text-[13px] font-medium leading-5"
          onClick={(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
            onCancel && onCancel(e)
          }}
          {...cancelButtonProps}
        >
          {cancelText || '取消'}
        </Button>
        <Button
          type={(okType || 'primary') as ButtonProps['type']}
          className="font-[PingFang SC] w-[74px] rounded text-[13px] font-medium leading-5"
          onClick={(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
            onOk && onOk(e)
          }}
          {...okButtonProps}
        >
          {okText || '确认'}
        </Button>
      </Space>
    )
  }
  return (
    <Modal
      className="grey-modal-footer"
      title={<div className="pl-5">{title || '确认'}</div>}
      footer={<div className="rounded-b bg-[#F4F4F4] px-5 py-3">{renderFooterButtons()}</div>}
      {...resProps}
      onCancel={(e) => onCancel && onCancel(e)}
    >
      {children}
    </Modal>
  )
}
