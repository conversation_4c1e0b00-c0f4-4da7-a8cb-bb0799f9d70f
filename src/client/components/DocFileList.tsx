import { Empty, Input, Tooltip } from 'antd'
import React, { useEffect, useState } from 'react'
import { SearchOutlined } from '@ant-design/icons'
import debounce from 'lodash/debounce'
import axios from 'axios'
import { useRequest } from 'ahooks'
import { useAtomValue } from 'jotai'
import clsx from 'clsx'
import { DatasetDatum, FileMimeTypeInfo } from 'src/shared/common-types'
import { askDocApiUrls } from 'src/shared/url-map'
import { Doc } from 'src/shared/askdoc-types'
import { docStatusMap, getIconComponent } from '../utils'
import { currentDatasetAtom, currentLoginUserAtom } from '../pages/AskBI/askBIAtoms'
import { currentSelectFileBySceneAtom } from '../pages/AskDoc/askDocAtoms'
import useIsAllowedRanger from '../hooks/useIsAllowedRanger'
import { SvgIcon, checkDatasetIcon } from './SvgIcon'

interface Props {
  selectDataset: DatasetDatum | null
  onSelectFile: (data: Doc) => void
}

/**
 * 折叠面板右侧 -- 文档列表
 * @returns
 */
const DocFileList = (props: Props) => {
  const { selectDataset, onSelectFile } = props
  const [fileList, setFileList] = useState<Doc[]>([]) // 显示数据
  const [sourceFileList, setSourceFileList] = useState<Doc[]>([]) // 源数据
  const currentDataset = useAtomValue(currentDatasetAtom)
  const currentSelectFileByScene = useAtomValue(currentSelectFileBySceneAtom)
  const [selectFile, setSelectFile] = useState<Doc | null>(currentSelectFileByScene) // 当前选中的item
  const currentLoginUser = useAtomValue(currentLoginUserAtom)
  const isAdmin = useIsAllowedRanger()

  // 获取文件list
  const { run: getFileListByScene } = useRequest(
    async () => {
      const queryData = {
        creator: !isAdmin ? currentLoginUser?.username : '',
        sceneId: selectDataset?.sceneId || currentDataset?.sceneId,
      }
      const response = await axios.get(askDocApiUrls.fileListV2, { params: queryData })
      const res = response.data.data.files
      setFileList(res || [])
      setSourceFileList(res || [])
    },
    {
      manual: true,
      onError: (error: any) => {
        console.error('获取页面权限list =', error)
      },
    },
  )

  useEffect(() => {
    getFileListByScene()
  }, [selectDataset, getFileListByScene])

  // 阻止事件冒泡 - 防止底部输入框获取到焦点
  const handlePopoverInputClick = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
    e.stopPropagation()
  }

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target
    const lowerCaseValue = value.toLowerCase()
    const matchingList: Doc[] = []
    fileList.forEach((item) => {
      if (item.name?.toLowerCase().includes(lowerCaseValue)) {
        matchingList.push(item)
      }
    })
    // 说明没搜索到
    if (matchingList.length === 0 && value) {
      setFileList([])
    } else {
      setFileList(value ? matchingList : sourceFileList)
    }
  }

  const handleFileItemClick = (data: Doc) => {
    setSelectFile(data)
    onSelectFile(data)
  }

  const debouncedOnChange = debounce(onChange, 300)

  return (
    <div className="chat-dataset-popover-doc-file ml-6 w-[341px] flex-1 overflow-auto">
      <Input
        className="my-4"
        placeholder="文档名"
        prefix={<SearchOutlined />}
        onClick={handlePopoverInputClick}
        onChange={debouncedOnChange}
      />
      <div className="flex h-[300px] flex-col overflow-y-scroll">
        {fileList.length > 0 ? (
          fileList.map((item) => {
            return (
              <div
                key={item.id}
                className={clsx(
                  'flex justify-between px-2 py-2 dark:bg-slate-700',
                  selectFile?.id === item.id && 'bg-[#F2F1FA] dark:bg-slate-800',
                  item.fileStatus === 'Done' && 'cursor-pointer hover:bg-slate-200 dark:hover:bg-slate-800',
                  item.fileStatus !== 'Done' && 'bg-gray-400',
                )}
                onClick={() => {
                  if (item.fileStatus !== 'Done') return
                  handleFileItemClick(item)
                }}
              >
                <div className="mr-2 flex items-center truncate">
                  <SvgIcon
                    icon={getIconComponent(item.mimeType as FileMimeTypeInfo)}
                    className="mr-1 h-6 w-6 shrink-0"
                  />
                  <Tooltip title={item.name}>
                    <p className="truncate">{item.name}</p>
                  </Tooltip>
                </div>

                {item.fileStatus === 'Done' && selectFile?.id === item.id ? (
                  <SvgIcon icon={checkDatasetIcon} className="h-5 w-5" />
                ) : (
                  item.fileStatus !== 'Done' && (
                    <p className="pt-1 text-xs font-medium text-red-500">
                      {docStatusMap[item.fileStatus]?.label || '解析中'}
                    </p>
                  )
                )}
              </div>
            )
          })
        ) : (
          <Empty className="mt-12" description={'暂无文档'} />
        )}
      </div>
    </div>
  )
}

export default DocFileList
