import { useAtom, useSet<PERSON>tom } from 'jotai/react'
import React from 'react'
import { isShowSpeechRecognitionAtom, updateMessageAndHtmlAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { Chat } from 'src/shared/common-types'
import { generalEditIcon, SvgIcon } from '../SvgIcon'

export default React.memo(function ChatHistoryItemAskEdit({ chat }: { chat: Chat }) {
  const setMessageAndHtml = useSetAtom(updateMessageAndHtmlAtom)
  const [isShowSpeechRecognition, setIsShowSpeechRecognition] = useAtom(isShowSpeechRecognitionAtom)

  return (
    <div
      onClick={() => {
        const jsonContent = chat.ask.jsonContent
        const content = chat.ask.content
        setIsShowSpeechRecognition(false)
        // 处于语音识别选项时, 编辑问题, 需要加一段延迟,否则无法改成显示弹窗指标
        setTimeout(
          () => {
            if (jsonContent?.length > 0) {
              try {
                setMessageAndHtml(JSON.parse(jsonContent))
              } catch (error) {
                setMessageAndHtml(content)
              }
            } else {
              setMessageAndHtml(content)
            }
          },
          isShowSpeechRecognition ? 100 : 0,
        )
      }}
    >
      <SvgIcon icon={generalEditIcon} className="h-6 w-6 cursor-pointer" />
    </div>
  )
})
