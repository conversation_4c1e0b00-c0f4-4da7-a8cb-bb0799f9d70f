import React from 'react'
import { Button, Modal, type ModalProps, type ButtonProps } from 'antd'
import './style.css'
export default function GreyBgModel({
  title,
  children,
  cancelButtonProps,
  cancelText,
  okButtonProps,
  okText,
  okType,
  onOk,
  onCancel,
  ...resProps
}: ModalProps) {
  function renderFooterButtons() {
    return (
      <div className="flex">
        <Button
          block
          className="font-[PingFang SC] mr-[6px] flex-1 rounded bg-white text-[13px] font-medium leading-5"
          onClick={(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
            onCancel && onCancel(e)
          }}
          {...cancelButtonProps}
        >
          {cancelText || '取消'}
        </Button>
        <Button
          block
          type={(okType || 'primary') as ButtonProps['type']}
          className="font-[PingFang SC] flex-1 rounded text-[13px] font-medium leading-5"
          onClick={(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
            onOk && onOk(e)
          }}
          {...okButtonProps}
        >
          {okText || '确认'}
        </Button>
      </div>
    )
  }
  return (
    <Modal
      className="grey-bg-model"
      title={title || '确认'}
      footer={<div className="rounded-b bg-[#F4F4F4]">{renderFooterButtons()}</div>}
      {...resProps}
      onCancel={(e) => onCancel && onCancel(e)}
    >
      {children}
    </Modal>
  )
}
