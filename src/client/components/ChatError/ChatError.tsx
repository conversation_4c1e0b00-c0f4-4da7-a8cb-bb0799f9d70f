import { debounce } from 'lodash'
import React, { useMemo, useEffect } from 'react'
import { useAtomValue, useSetAtom } from 'jotai/react'
import qs from 'query-string'
import { produce } from 'immer'
import { nanoid } from 'nanoid'
import {
  AssistantChatError,
  AssistantChatItem,
  Chat,
  ChatResponseErrorTypes,
  DatasetDatum,
} from 'src/shared/common-types'
import { DeepSeekData } from 'src/shared/deepseek'
import { metricConfigAtom, chatsAtom } from 'src/client/pages/AskBI/askBIAtoms'
import MetricItems from 'src/client/charts/MetricItems'
import { isBaoWu } from 'src/shared/baowu-share-utils'
import { DEEPSEEK_ERROR_LIST, DeepSeekActiveButton } from '../deepseek'
import { BiResultContent } from '../ChatHistoryItem'
import { TryQueryToSql } from '../TryQueryToSql/TryQueryToSql'
import errorImgConfig from './errorImgConfig'
import ExpandableCompany from './ExpandableCompany'
import { baowuConfig, universalConfig } from './errorInfoConfig'

type Props = {
  content: AssistantChatError
  currentDataset: DatasetDatum | null
  biResultContentProps?: Parameters<typeof BiResultContent>[0]
}

export function useChatError({ content }: { content?: AssistantChatItem }) {
  const metricConfig = useAtomValue(metricConfigAtom)

  const { metricErrorImg, metricTreeErrorImg, modelErrorImg, notAnalyzeErrorImg, otherErrorImg, noSceneErrorImg } =
    useMemo(() => {
      if (isBaoWu(metricConfig?.metricTableName)) {
        return errorImgConfig.baowuErrorImgMap
      }
      return errorImgConfig.defaultErrorImgMap
    }, [metricConfig])

  const { getImgUrl, getTitle, getDesc } = useMemo(() => {
    if (isBaoWu(metricConfig?.metricTableName)) {
      return baowuConfig
    }
    return universalConfig
  }, [metricConfig])

  if (!content || content.type !== 'chat-error') return null

  return {
    imgUrl: getImgUrl({
      metricErrorImg,
      metricTreeErrorImg,
      modelErrorImg,
      notAnalyzeErrorImg,
      otherErrorImg,
      noSceneErrorImg,
      content: content,
    }),
    title: getTitle({ content }),
    desc: getDesc({ content, metricConfig }),
  }
}

export default function ChatError({ content, currentDataset, biResultContentProps }: Props) {
  const data = useChatError({ content })
  const metricConfig = useAtomValue(metricConfigAtom)
  const setChats = useSetAtom(chatsAtom)

  const search = useMemo(() => {
    if (location.search) {
      return qs.parse(location.search)
    }
  }, [])

  const updateDeepSeek = debounce(() => {
    setChats(
      produce((chats) => {
        const chatId = biResultContentProps?.currentChat.id ?? ''
        const id = nanoid()
        const data = new DeepSeekData()
        data.trigger = 'error'
        const item: Chat = {
          id,
          isSystemPrompt: !true,
          askTime: new Date(),
          selectedSceneId: currentDataset?.sceneId ?? '',
          ask: null as any,
          ans: [
            {
              role: 'assistant',
              sceneId: currentDataset?.sceneId ?? '',
              status: 'success',
              content: [
                {
                  type: 'deepseek',
                  relativeChatId: chatId,
                  data,
                },
              ],
            },
          ],
          docAns: {
            role: 'assistant',
            status: 'pending',
            content: [],
          },
        }
        const relativeChatIdx = chats.findIndex((chat) => chat.id === chatId)
        chats[relativeChatIdx].ans = []
        if (relativeChatIdx === -1) return chats
        chats.splice(relativeChatIdx + 1, 0, item)
      }),
    )
  })

  // TODO: 当报错时清空报错并直接展示DeepSeek弹窗，先按照需求写，如果后期固化，可以放到chats中直接展示
  useEffect(() => {
    if (DEEPSEEK_ERROR_LIST.includes(content.errType) && isBaoWu(metricConfig?.metricTableName)) {
      updateDeepSeek()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [content.errType])

  if (DEEPSEEK_ERROR_LIST.includes(content.errType) && isBaoWu(metricConfig?.metricTableName)) {
    return null
  }

  if (!data) return null

  const { imgUrl, title, desc } = data
  const infoRender = (
    <div className="flex flex-col items-center justify-center">
      {content.whereCompany ? (
        <div className="text-sm text-[#A3A3A3]">
          <ExpandableCompany datasource={content.whereCompany} />
        </div>
      ) : null}
      {desc ? <p className="mt-1 text-center text-sm text-[#A3A3A3]">{desc}</p> : null}
      {content.metricNames ? (
        <div className="mt-1">
          <MetricItems metricNames={content.metricNames} needWrap={true} />
        </div>
      ) : null}
      {search?.isDebugMode && content.llmResponse?.data?.originResponse && (
        <span className="text-red-500">原始错误：{JSON.stringify(content.llmResponse?.data?.originResponse)}</span>
      )}
    </div>
  )
  if (
    (content.errType === ChatResponseErrorTypes.LATEST_DATA_NOT_EXIST ||
      content.errType === ChatResponseErrorTypes.FUTURE_DATA_NOT_EXIST) &&
    currentDataset?.enableTryQueryUp &&
    biResultContentProps
  ) {
    return (
      <TryQueryToSql
        content={content}
        biResultContentProps={biResultContentProps}
        noDataTitle={content.unreadyReason}
        noDataRender={() => infoRender}
      />
    )
  }

  return (
    <div className="flex flex-col items-center justify-center pb-6 pt-8">
      {imgUrl ? (
        <div className="img mb-3 h-[90px]">
          <img src={imgUrl} className="h-[90px]" alt="错误图片" />
        </div>
      ) : null}
      {title ? <h4 className="text-center text-lg font-bold">{title}</h4> : null}

      {currentDataset?.enableTryQueryUp && biResultContentProps ? (
        <TryQueryToSql content={content} biResultContentProps={biResultContentProps} noDataRender={() => infoRender} />
      ) : (
        infoRender
      )}

      <DeepSeekActiveButton
        icon={null}
        textProps={{ children: '用deepseek回答', className: 'font-[600]' }}
        chatId={biResultContentProps?.currentChat.id ?? ''}
        containerProps={{ className: 'mt-[20px]' }}
      />
    </div>
  )
}
