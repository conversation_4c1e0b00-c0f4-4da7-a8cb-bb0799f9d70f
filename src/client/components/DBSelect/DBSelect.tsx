import { Form, Col, Select, message } from 'antd'
import { useRequest } from 'ahooks'
import { useEffect, useState } from 'react'
import * as React from 'react'
import { askBIApiUrls } from 'src/shared/url-map'
import request from 'src/shared/xengineAxios'
import type { CatalogType, DatabaseType } from 'src/shared/common-types'

export interface BaseOptionType {
  disabled?: boolean
  [name: string]: any
}
export interface DefaultOptionType extends BaseOptionType {
  label: React.ReactNode
  value?: string | number | null
  children?: Omit<DefaultOptionType, 'children'>[]
}

/**
 * @param props setFieldVaule : form.setFieldValue , in order to clear selection.
 * span: to configuration the col span
 * @returns
 */
const DBSelect = (props: {
  params?: { catalog: string; database: string }
  setParams?: (data: any) => void
  required?: {
    catalog: boolean
    database: boolean
  }
  setFieldValue: (name: string, value: any) => void
  span: number
  offset?: number
  getCatalogName?: (catalogList: { id: number; name: string }[], val: any) => string // 返回catalogName 用来请求 databases
  formatCatalogOptions?: (val: any) => DefaultOptionType // getCatalogName必须和 getCatalogName同时出现
  formatDatabaseOptions?: (val: any) => DefaultOptionType
}) => {
  const {
    params,
    setParams,
    required,
    setFieldValue,
    span,
    offset,
    formatCatalogOptions,
    formatDatabaseOptions,
    getCatalogName,
  } = props
  const [catalogList, setCatalogList] = useState<Array<{ id: number; name: string }>>([])
  const [databaseList, setDatabaseList] = useState<Array<{ id: number; catalogName: string; name: string }>>()
  const { runAsync: runCatalog } = useRequest(
    (params) =>
      request.get<{ current: number; pageSize: number }, CatalogType[]>(askBIApiUrls.xengine.catalogList, { params }),
    {
      manual: true,
    },
  )
  const { runAsync: runDatabase } = useRequest(
    (params) =>
      request.get<{ current: number; pageSize: number }, DatabaseType[]>(askBIApiUrls.xengine.databaseList, { params }),
    {
      manual: true,
    },
  )
  useEffect(() => {
    runCatalog({ current: 1, pageSize: -1 })
      .then((l) => {
        const data = l.filter((item: any) => item.type === 'INTERNAL')
        setCatalogList(data)
      })
      .catch(() => message.error('获取catalog 出错'))
  }, [runCatalog])
  const catalogOnchange = (e: string | number) => {
    let catalogName = e
    if (typeof catalogName === 'number') {
      catalogName = getCatalogName?.(catalogList, e) || ''
    }
    runDatabase({ catalog: catalogName, current: 1, pageSize: -1 })
      .then((res) => {
        setDatabaseList(res)
        setFieldValue('database', null)
      })
      .catch(() => message.error('获取database 出错'))
    setParams &&
      setParams({
        ...params,
        catalog: catalogName,
        database: '',
      })
  }
  return (
    <>
      <Col span={span} offset={offset}>
        <Form.Item
          name="catalog"
          label="虚拟表目录"
          rules={[{ required: required ? required.catalog : true, message: '请选择虚拟表目录' }]}
        >
          <Select
            showSearch
            placeholder="请选择虚拟表目录"
            value={params && params.catalog}
            onChange={catalogOnchange}
            options={
              catalogList
                ? catalogList.map(
                    formatCatalogOptions ? formatCatalogOptions : (e) => ({ label: e.name, value: e.name }),
                  )
                : []
            }
          />
        </Form.Item>
      </Col>
      <Col span={span} offset={offset}>
        <Form.Item
          name="database"
          label="虚拟表库"
          rules={[{ required: required ? required.database : true, message: '请选择虚拟表库' }]}
        >
          <Select
            showSearch
            placeholder="请选择虚拟表库"
            value={params && params.database}
            onChange={(e: string) => {
              setParams &&
                setParams({
                  ...params,
                  database: e,
                })
            }}
            options={
              databaseList
                ? databaseList.map(
                    formatDatabaseOptions ? formatDatabaseOptions : (e) => ({ label: e.name, value: e.name }),
                  )
                : []
            }
          />
        </Form.Item>
      </Col>
    </>
  )
}
export default DBSelect
