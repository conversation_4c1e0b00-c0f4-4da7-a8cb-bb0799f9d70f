/**
 * 折叠面板 --  数据源
 * @returns
 */

import { App, Button, Collapse, Input, Modal, Popover, Switch } from 'antd'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import React, { useCallback, useEffect, useState } from 'react'
import { CheckCircleFilled, CloseOutlined, SearchOutlined } from '@ant-design/icons'
import clsx from 'clsx'
import debounce from 'lodash/debounce'
import { useNavigate } from 'react-router-dom'
import { IS_H5 } from 'src/shared/constants'
import { sceneToDataset } from 'src/shared/common-utils'
import { DatasetDatum } from 'src/shared/common-types'
import { ProjectType, SceneType } from 'src/shared/metric-types'
import { askBIPageUrls } from 'src/shared/url-map'
import { Doc } from 'src/shared/askdoc-types'
import {
  conversationIdAtom,
  currentDatasetAtom,
  initChatsAtom,
  isProjectChosen<PERSON>tom,
  makeAgentRequest<PERSON>tom,
  scenePopoverIsCheckedDocAtom,
  semanticProjectInfoAtom,
} from '../pages/AskBI/askBIAtoms'
import { currentSelectFileBySceneAtom } from '../pages/AskDoc/askDocAtoms'
import useIsAllowedRanger from '../hooks/useIsAllowedRanger'
import TextTruncate from './TextTruncate'
import './ChatScenePopover.css'
import { SvgIcon, checkDatasetIcon, datasetIcon, datasetInMetricIcon, reportTagIcon } from './SvgIcon'
import DocFileList from './DocFileList'

const { confirm } = Modal

interface DatasetCollapseType {
  key: string
  label: JSX.Element // 使用 JSX.Element 而不是 Element
  children: JSX.Element[] // 同样地，使用 JSX.Element[]
}

interface Props {
  onCurrentDatasetChange?: (data: DatasetDatum) => void
  showDatasetText?: boolean // 展示项目/场景的文字信息
  isMetricOverview?: boolean // 此组件有两种展示方式 为true时 用在指标列表中展示
}

/**
 * 折叠面板 --  数据源
 * @returns
 */
const ChatScenePopover = (props: Props) => {
  const { onCurrentDatasetChange, showDatasetText = true, isMetricOverview = false } = props
  const { message } = App.useApp()
  const navigate = useNavigate()
  const initChats = useSetAtom(initChatsAtom)
  const [_conversationId, setConversationId] = useAtom(conversationIdAtom)
  const [isCheckedDoc, setIsCheckedDoc] = useAtom(scenePopoverIsCheckedDocAtom)
  const [isShowPopover, setIsShowPopover] = useState<boolean>(false)
  const semanticProjectInfo = useAtomValue(semanticProjectInfoAtom)
  const [currentDataset, setCurrentDataset] = useAtom(currentDatasetAtom)
  const [selectDataset, setSelectDataset] = useState<DatasetDatum | null>(currentDataset)
  const [currentSelectFileByScene, setCurrentSelectFileByScene] = useAtom(currentSelectFileBySceneAtom)
  const [isProjectChosen, setIsProjectChosen] = useAtom(isProjectChosenAtom)
  const [isChooseProject, setIsChooseProject] = useState<boolean>(isProjectChosen)
  const [hoveredSceneId, setHoveredSceneId] = useState<string>('')

  // 判断当ranger中有report生成报告的权限的时候才显示报告生成按钮，否则不显示
  const isShowReportBtn = useIsAllowedRanger()
  const makeAgentRequest = useSetAtom(makeAgentRequestAtom)

  const handleTitleClick = useCallback(
    (e: React.MouseEvent<HTMLParagraphElement, MouseEvent>, semanticProjectItem: ProjectType) => {
      e.stopPropagation() // 防止触发 collapse 展开折叠
      currentSelectFileByScene && setCurrentSelectFileByScene(null)
      setIsChooseProject(true)
      setSelectDataset(sceneToDataset({ project: semanticProjectItem, scene: semanticProjectItem.scenes[0] }))
    },
    [currentSelectFileByScene, setCurrentSelectFileByScene],
  )

  const makeRequestChat = useCallback(
    (sceneLabel: string, modelNames: string[]) => {
      makeAgentRequest({
        navigate,
        isMiniMode: location.pathname === askBIPageUrls.home,
        newMessage: `请基于${sceneLabel}场景生成一份数据分析报告`,
        isReportModelNameTag: modelNames[0],
      })
    },
    [makeAgentRequest, navigate],
  )

  // 确认
  const handleConfirm = useCallback(
    (e: React.MouseEvent<HTMLElement, MouseEvent>, localDataset: DatasetDatum | null, modelNames: string[]) => {
      e.stopPropagation()
      if (!selectDataset) {
        return message.error('当前选择的场景为空，请重新选择！')
      }

      const tempDataset = localDataset ? localDataset : selectDataset
      if (location.pathname === askBIPageUrls.chatNew && tempDataset.sceneId !== currentDataset?.sceneId) {
        confirm({
          title: '修改当前的场景',
          icon: <CheckCircleFilled />,
          content: (
            <div>
              修改当前的场景，会清空当前的问答历史记录
              <br />
              请确认！
            </div>
          ),
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            setConversationId(null)
            initChats()
            setIsProjectChosen(isChooseProject)
            setCurrentDataset(tempDataset)
            setIsShowPopover(false)
            onCurrentDatasetChange && onCurrentDatasetChange(tempDataset)
            modelNames.length > 0 && makeRequestChat(tempDataset.sceneLabel, modelNames)
          },
        })
      } else {
        setConversationId(null)
        initChats()
        setIsProjectChosen(isChooseProject)
        setCurrentDataset(tempDataset)
        setIsShowPopover(false)
        modelNames.length > 0 && makeRequestChat(tempDataset.sceneLabel, modelNames)
      }
    },
    [
      currentDataset?.sceneId,
      initChats,
      isChooseProject,
      makeRequestChat,
      message,
      onCurrentDatasetChange,
      selectDataset,
      setConversationId,
      setCurrentDataset,
      setIsProjectChosen,
    ],
  )

  const handleScenesItemClick = useCallback(
    (
      scene: SceneType,
      semanticProjectItem: ProjectType,
      isReport: boolean,
      e: React.MouseEvent<HTMLElement, MouseEvent>,
    ) => {
      setIsChooseProject(false)
      currentSelectFileByScene && setCurrentSelectFileByScene(null)
      const localDataset: DatasetDatum = {
        projectId: semanticProjectItem.id,
        projectName: semanticProjectItem.name,
        sceneId: scene.id,
        sceneLabel: scene.label,
        tableName: scene.tableName,
        enableFollowUpQuestion: !!scene.enableFollowUpQuestion,
        enableMetricExactMatch: !!scene.enableMetricExactMatch,
        enableTryQueryUp: !!scene.enableTryQueryUp,
        enableSelectToastWhenEmptyData: !!scene.enableSelectToastWhenEmptyData,
        enableAccMetricToastWhenEmptyData: !!scene.enableAccMetricToastWhenEmptyData,
      }
      setSelectDataset(localDataset)
      // 如果是点击了报告生成，就调用handleConfirm方法
      if (isReport) {
        handleConfirm(e, localDataset, scene.modelNames)
      }
      setHoveredSceneId(scene.id)
    },
    [currentSelectFileByScene, handleConfirm, setCurrentSelectFileByScene],
  )

  const highlightText = (text: string, highlight: string) => {
    if (!highlight.trim()) {
      return text
    }
    const regex = new RegExp(`(${highlight})`, 'gi')
    return text.split(regex).map((part, i) =>
      regex.test(part) ? (
        <span key={i} style={{ backgroundColor: 'yellow' }}>
          {part}
        </span>
      ) : (
        part
      ),
    )
  }

  const renderProjectLabel = useCallback(
    (project: ProjectType, lowerCaseValue: string) => {
      return (
        <div
          className={clsx(
            'flex justify-between px-2 py-2 hover:bg-slate-200 dark:hover:bg-slate-800',
            selectDataset?.projectId === project.id && isChooseProject && 'bg-[#F2F1FA] dark:bg-slate-800',
          )}
          onClick={(e) => handleTitleClick(e, project)}
        >
          <p>{highlightText(project.name, lowerCaseValue)}</p>
          <div className="w-5">
            {selectDataset?.projectId === project.id && isChooseProject && (
              <SvgIcon icon={checkDatasetIcon} className="h-5 w-5" />
            )}
          </div>
        </div>
      )
    },
    [selectDataset, isChooseProject, handleTitleClick],
  )

  const renderSceneLabel = useCallback(
    (scene: SceneType, project: ProjectType, lowerCaseValue: string) => {
      return (
        <div
          key={`${scene.projectId}.${scene.id}`}
          className={clsx(
            'scene-content flex max-w-[340px] cursor-pointer justify-between px-2 py-2 hover:bg-slate-200 dark:hover:bg-slate-800',
            selectDataset?.sceneId === scene.id && !isChooseProject && 'bg-[#F2F1FA] dark:bg-slate-800',
          )}
          onClick={(e) => handleScenesItemClick(scene, project, false, e)}
        >
          <p className="mr-1 truncate">{highlightText(scene.label, lowerCaseValue)}</p>
          <div className="flex shrink-0">
            {!IS_H5 &&
              scene.modelNames &&
              scene.modelNames.length > 0 &&
              (location.pathname === askBIPageUrls.chatNew || location.pathname === askBIPageUrls.home) &&
              isShowReportBtn && (
                <div
                  className={clsx(
                    'doc-report-btn mr-1 flex items-center justify-center rounded border border-gray-400 px-1 py-0.5 text-xs transition-opacity duration-200',
                    (hoveredSceneId === scene.id || (selectDataset?.sceneId === scene.id && !isChooseProject)) &&
                      scene.agent?.includes('BI')
                      ? 'opacity-100'
                      : 'opacity-0',
                  )}
                  onClick={(e) => handleScenesItemClick(scene, project, true, e)}
                >
                  <SvgIcon icon={reportTagIcon} className="h-5 w-5" />
                  报告生成
                </div>
              )}
            {selectDataset?.sceneId === scene.id && !isChooseProject && (
              <SvgIcon icon={checkDatasetIcon} className="h-5 w-5" />
            )}
          </div>
        </div>
      )
    },
    [selectDataset?.sceneId, isChooseProject, isShowReportBtn, hoveredSceneId, handleScenesItemClick],
  )

  const getFinalScenesData = useCallback(
    (semanticProjectInfo: ProjectType[], lowerCaseValue: string = '') => {
      return semanticProjectInfo
        .flatMap((semanticProjectItem) => {
          const projectMatches = semanticProjectItem.name.toLowerCase().includes(lowerCaseValue)
          const matchingScenes = semanticProjectItem.scenes.filter((scene) =>
            scene.label.toLowerCase().includes(lowerCaseValue),
          )

          if (projectMatches) {
            return {
              key: semanticProjectItem.id,
              label: renderProjectLabel(semanticProjectItem, lowerCaseValue),
              children: semanticProjectItem.scenes.map((scene) => renderSceneLabel(scene, semanticProjectItem, '')),
            }
          }

          if (matchingScenes.length > 0) {
            return {
              key: semanticProjectItem.id,
              label: renderProjectLabel(semanticProjectItem, ''),
              children: semanticProjectItem.scenes.map((scene) =>
                renderSceneLabel(scene, semanticProjectItem, lowerCaseValue),
              ),
            }
          }
          return null
        })
        .filter((item) => item !== null) as DatasetCollapseType[]
    },
    [renderProjectLabel, renderSceneLabel],
  )

  const [scenesData, setScenesData] = useState<DatasetCollapseType[]>([])
  const [expandedKeys, setExpandedKeys] = useState<string[]>([])

  useEffect(() => {
    setScenesData(getFinalScenesData(semanticProjectInfo))
    setExpandedKeys(
      getFinalScenesData(semanticProjectInfo).map((item) => {
        return item.key
      }),
    )
  }, [semanticProjectInfo, selectDataset, getFinalScenesData, currentDataset])

  // 选定文档提问 switch
  const onSwitchChange = (checked: boolean) => {
    setIsCheckedDoc(checked)
    setCurrentSelectFileByScene(null)
  }

  // 关闭Popover
  const handleCancelPopover = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
    e.stopPropagation()
    setIsShowPopover(false)
  }

  // 阻止事件冒泡 - 防止底部输入框获取到焦点
  const handlePopoverInputClick = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
    e.stopPropagation()
  }

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target
    const lowerCaseValue = value.toLowerCase()
    const matchingList: string[] = []
    const filteredScenesData = getFinalScenesData(semanticProjectInfo, lowerCaseValue)

    filteredScenesData.forEach((item) => {
      matchingList.push(item.key)
    })
    setExpandedKeys(matchingList)
    setScenesData(filteredScenesData)
  }

  // 切换面板的回调
  const onCollapseChange = (keys: string | string[]) => {
    setExpandedKeys(Array.isArray(keys) ? keys : [keys])
  }

  const handleSelectFile = (data: Doc) => {
    setCurrentSelectFileByScene(data)
  }

  const debouncedOnChange = debounce(onChange, 300)

  // 渲染Popover中DatasetContent
  const renderDatasetContent = () => {
    return (
      <div
        className={clsx(
          'transition-width flex flex-col duration-500 ease-in-out',
          isCheckedDoc && !IS_H5 ? 'w-[741px]' : 'md:w-[400px]',
        )}
      >
        <div className="mx-5 mt-5 flex items-center justify-between">
          <p className="text-base font-medium">选择场景</p>
          <div
            className="cursor-pointer"
            onClick={() => {
              setIsShowPopover(false)
            }}
          >
            <CloseOutlined />
          </div>
        </div>
        <div className="mx-5 flex max-h-96">
          <div className="flex-1">
            <Input
              className="my-4"
              placeholder="场景名称/描述"
              prefix={<SearchOutlined />}
              onClick={handlePopoverInputClick}
              onChange={debouncedOnChange}
            />
            <Collapse
              className="h-[300px] overflow-y-scroll"
              items={scenesData}
              expandIconPosition="end"
              activeKey={expandedKeys}
              onChange={onCollapseChange}
            />
          </div>
          {isCheckedDoc && <DocFileList selectDataset={selectDataset} onSelectFile={handleSelectFile} />}
        </div>
        <div className="flex h-[56px] flex-shrink-0 items-center justify-end bg-[#F4F4F4] px-5 dark:bg-slate-700 md:justify-between">
          <div className="hidden md:block">
            {(location.pathname === askBIPageUrls.home || location.pathname === askBIPageUrls.chatNew) && (
              <div className="flex items-center">
                <Switch value={isCheckedDoc} onChange={onSwitchChange} />
                <p className="ml-2">选定文档提问</p>
              </div>
            )}
          </div>
          <div className="flex">
            <Button
              className="mr-3 border-none bg-white font-medium dark:bg-slate-500"
              onClick={(e) => {
                handleCancelPopover(e)
              }}
            >
              取消
            </Button>
            <Button
              className="border-none bg-primary font-medium text-white"
              onClick={(e) => {
                handleConfirm(e, null, [])
              }}
            >
              确定
            </Button>
          </div>
        </div>
      </div>
    )
  }

  const renderSimpleDataset = (
    <>
      <SvgIcon icon={datasetIcon} className="h-6 w-6 text-[#333038] dark:text-gray-500" />
      {currentDataset && showDatasetText && (
        <TextTruncate className="max-w-[150px] cursor-pointer truncate font-normal dark:text-gray-500 sm:max-w-[300px]">
          {isProjectChosen ? (
            <>{currentDataset.projectName}</>
          ) : (
            <>
              {currentDataset.projectName}-{currentDataset.sceneLabel}
            </>
          )}
        </TextTruncate>
      )}
    </>
  )

  const renderDatasetInMetricList = (
    <>
      <SvgIcon icon={datasetInMetricIcon} className="h-4 w-4" />
      {currentDataset && showDatasetText && (
        <TextTruncate className="max-w-[250px] cursor-pointer truncate font-normal dark:text-gray-500 sm:max-w-[300px]">
          {isProjectChosen ? <>{currentDataset.projectName}</> : <>{currentDataset.sceneLabel}</>}
        </TextTruncate>
      )}
    </>
  )

  return (
    <div className="chat-dataset-popover w-fit">
      <Popover content={renderDatasetContent} getPopupContainer={(triggerNode) => triggerNode} open={isShowPopover}>
        <div
          className="chat-dataset-popover-content flex items-center gap-2"
          onClick={() => {
            setIsShowPopover(true)
          }}
        >
          {isMetricOverview ? <>{renderDatasetInMetricList}</> : <>{renderSimpleDataset}</>}
        </div>
      </Popover>
    </div>
  )
}

export default ChatScenePopover
