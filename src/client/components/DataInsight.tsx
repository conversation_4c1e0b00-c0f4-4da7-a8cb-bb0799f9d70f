import React, { useEffect, useRef, useState } from 'react'
import { SyncOutlined } from '@ant-design/icons'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm' // 划线、表、任务列表和直接url等的语法扩展
import { throttle } from 'lodash'
import { produce } from 'immer'
import { useAtomValue, useSetAtom } from 'jotai'
import { AssistantChartInsightChatItem, Chat } from 'src/shared/common-types'
import { TOKEN_BI, TOKEN_RANGER, U_TOKEN_RANGER } from 'src/shared/constants'
import { askBIApiUrls } from 'src/shared/url-map'
import { createTraceId } from 'src/shared/common-utils'
import { chatsAtom, currentParamsExtractApiAtom } from '../pages/AskBI/askBIAtoms'
import { scrollToBottom } from '../utils'

const ChartInsightLoadingText = '正在生成分析报告...'

const DataInsight = ({ content, idx }: { content: AssistantChartInsightChatItem; idx: number }) => {
  const chatId = content.chatId
  const currentParamsExtractApi = useAtomValue(currentParamsExtractApiAtom)
  const setChats = useSetAtom(chatsAtom)
  const [data, setData] = useState<string>(content.text || '')
  const [status, setStatus] = useState(content.status)
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  const [reader, setReader] = useState<ReadableStreamDefaultReader<Uint8Array<ArrayBufferLike>> | null>(null)
  const [allDone, setAllDone] = useState(false)
  const hasLoaded = useRef(false)

  async function loadData() {
    if (!reader) {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        traceId: content.traceId ? content.traceId : createTraceId(),
      }
      const token = localStorage.getItem(TOKEN_BI)
      const rangerToken = localStorage.getItem(TOKEN_RANGER)
      const rangerUToken = localStorage.getItem(U_TOKEN_RANGER)
      if (token) headers['Authorization'] = `Bearer ${token}`
      if (rangerToken) headers[TOKEN_RANGER] = rangerToken
      if (rangerUToken) headers[U_TOKEN_RANGER] = rangerUToken
      const url =
        content.respData.taskType === 'attribution-metric-analysis'
          ? askBIApiUrls.attrInsightStream
          : askBIApiUrls.chartInsightStream
      const response = await fetch(url, {
        method: 'POST',
        body: JSON.stringify({
          responseData: JSON.stringify(content.respData),
          llmType: content.llmType,
          sceneId: content.sceneId,
          currentParamsExtractApi,
        }),
        headers,
      })
      if (!response.ok) throw new Error('请求失败')
      if (response.body) {
        const reader = response.body.getReader()
        setStatus('success')
        setReader(reader)
        let done = false
        const decoder = new TextDecoder()
        while (!done) {
          const { done: isDone, value } = await reader.read()
          done = isDone
          if (!done) {
            const text = decoder.decode(value)
            const regex = /\{"code":\d+,"data":"(.*?)"\}/g
            const match = text.match(regex)

            match?.map((item) => {
              const everyStream = JSON.parse(item)
              if (everyStream['code'] === 0) {
                const data = everyStream['data']
                setData((old) => old + data)
                toBottom()
              } else {
                console.error('接口返回异常:', everyStream)
                throw new Error('接口返回异常')
              }
            })
          }
        }
      }
    }
  }

  useEffect(() => {
    if (data && allDone) {
      setChats(
        produce((draft: Chat[]) => {
          const theChat = draft.find((chatItem) => chatItem.id === chatId)
          if (!theChat) return

          const currentAns = theChat.ans.find((m) => m.sceneId === content.sceneId)
          if (!currentAns) return

          const chartInsightContentIndex = currentAns.content.findIndex((item) => item.type === 'chart-insight')
          if (chartInsightContentIndex === -1) return

          const chartInsightItem = currentAns.content[chartInsightContentIndex] as AssistantChartInsightChatItem
          currentAns.content[chartInsightContentIndex] = {
            ...chartInsightItem,
            text: data,
            status,
          }
          currentAns.ansTime = new Date()
        }),
      )
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, allDone])

  useEffect(() => {
    if (status === 'pending' && !hasLoaded.current && !content.text) {
      hasLoaded.current = true
      loadData()
        ?.catch(() => {
          setData((old) => old + ' 获取数据解读失败')
          setStatus('failure')
        })
        .finally(() => {
          setAllDone(true)
        })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [status])

  const toBottom = throttle(scrollToBottom, 50)

  useEffect(() => {
    setTimeout(() => toBottom(), 200)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  if (status === 'pending') {
    return (
      <div key={idx} className="flex">
        <span>{ChartInsightLoadingText}</span>
        <SyncOutlined spin className="ml-4 flex flex-none text-xl text-gray-400 dark:text-gray-500" />
      </div>
    )
  }

  return (
    <div key={idx} className="askbi-markdown rounded-md bg-slate-50 p-2 dark:bg-slate-700">
      <ReactMarkdown remarkPlugins={[remarkGfm]}>{data}</ReactMarkdown>
    </div>
  )
}

export default DataInsight
