import { Document, ImageRun, Packer, Paragraph, Table, TableCell, TableRow, TextRun } from 'docx'
import { FileChild } from 'docx/build/file/file-child'
import { saveAs } from 'file-saver'
import {
  AssistantChatItem,
  AskChatItem,
  BlobWithRatio,
  AssistantChartChatItem,
  RowsMetadata,
} from '@shared/common-types'

interface TextForDocx {
  type: 'text'
  title: string
  text: string
}

interface TableForDocx {
  type: 'table'
  rows: { [key: string]: string }[]
  rowsMetadata: RowsMetadata
}

interface ImageForDocx {
  type: 'image'
  blob: BlobWithRatio | string
}

type DocxContent = (TextForDocx | TableForDocx | ImageForDocx)[]

/**
 * 通过DocxContent内容生成docx文件
 * @param {DocxContent} content - 包含要生成的文本内容的数组
 */
export async function generateDocxFromContent(content: DocxContent) {
  const sectionsChildRen: FileChild[] = []

  for (const item of content) {
    if (item.type === 'table') {
      sectionsChildRen.push(generateTable(item.rows, item.rowsMetadata))
    } else if (item.type === 'image') {
      if (typeof item.blob !== 'string') {
        const graph = await generateImage(item.blob)
        sectionsChildRen.push(graph)
      }
    } else if (item.type === 'text') {
      sectionsChildRen.push(generateTitleParagraph(item.title))
      sectionsChildRen.push(generateTextParagraph(item.text))
    }
  }

  const doc = new Document({
    sections: [{ children: sectionsChildRen }],
  })

  Packer.toBlob(doc).then((blob) => {
    saveAs(blob, '分析报告.docx')
    console.info('Document created successfully.')
  })
}

/**
 * 从会话中生成DocxContent
 */
export function generateDocxContentFromChatAns(
  chatAskContent: AskChatItem,
  chatAnsContent: AssistantChatItem[],
  options: { rows?: { [key: string]: string }[]; blob?: BlobWithRatio | string },
): DocxContent {
  const { rows = [], blob } = options
  const docxContent: DocxContent = []
  const chartAnsContent = chatAnsContent.find((item) => item.type === 'chart') as AssistantChartChatItem
  docxContent.push({
    type: 'text',
    title: '问题',
    text: chartAnsContent ? chartAnsContent.chartTitle : chatAskContent.content,
  })
  // 先把table和image push 进去
  if (blob) {
    docxContent.push({ type: 'image', blob })
  }
  if (rows.length > 0) {
    docxContent.push({ type: 'table', rows, rowsMetadata: chartAnsContent?.rowsMetadata })
  }
  chatAnsContent.forEach((content) => {
    switch (content.type) {
      case 'sql':
        docxContent.push({ type: 'text', title: 'SQL语句', text: content.sql })
        break
      case 'chart-insight':
        docxContent.push({ type: 'text', title: '分析报告', text: content.text || '分析报告暂未生成' })
        break
      default:
        break
    }
  })

  return docxContent
}

/**
 * 生成标题的段落
 * @param {string} title - 标题文本
 * @returns {Paragraph} - 生成的段落
 */
function generateTitleParagraph(title: string): Paragraph {
  return new Paragraph({
    children: [
      new TextRun({
        text: title,
        color: '000000',
        font: '宋体',
        bold: true,
      }),
    ],
    heading: 'Heading3',
  })
}

/**
 * 生成文本的段落
 * @param {string} text - 文本内容
 * @returns {Paragraph} - 生成的段落
 */
function generateTextParagraph(text: string): Paragraph {
  const lines = text.split('\n') // 切分成多行，导出时保留换行符
  const children = lines.map(
    (line, index) =>
      new TextRun({
        text: line,
        font: '宋体',
        break: index === 0 ? 0 : 1, // 第一个元素不需要换行，否则会多出一个空行
      }),
  )
  return new Paragraph({
    children,
    alignment: 'left',
  })
}

/**
 * 生成表格的段落
 * @param {{ [key: string]: string }[]} data - SimpleTable的数据
 * @returns {Paragraph} - 生成的表格段落
 */
function generateTable(data: { [key: string]: string }[], rowsMetadata: RowsMetadata): Table {
  const metadataMap: { [key: string]: string } = {}
  if (rowsMetadata) {
    rowsMetadata.forEach((item) => {
      metadataMap[item.value.name] = item.value.label
    })
  }

  return new Table({
    width: {
      type: 'auto',
      size: 100,
    },
    rows: [
      new TableRow({
        children: Object.keys(data[0]).map((header) => {
          const headerToShow = typeof header === 'number' ? (header as number).toFixed(2) : header.toString()
          return new TableCell({ children: [new Paragraph(metadataMap[header] || headerToShow)] })
        }),
      }),
      ...data.map(
        (row) =>
          new TableRow({
            children: Object.values(row).map((value: string) => {
              const valueToShow = typeof value === 'number' ? (value as number).toFixed(2) : value.toString()
              return new TableCell({ children: [new Paragraph(valueToShow)] })
            }),
          }),
      ),
    ],
  })
}

/** 将Blob转换成base64string */
function blobToBase64(blob: BlobWithRatio): Promise<string> {
  return new Promise((resolve, reject) => {
    const fileReader = new FileReader()
    fileReader.onload = (e) => {
      if (e.target && typeof e.target?.result === 'string') {
        resolve(e.target.result)
      }
      reject(new Error('Base64 type error'))
    }
    fileReader.readAsDataURL(blob)
    fileReader.onerror = () => {
      reject(new Error('BlobToBase64 error'))
    }
  })
}

/**
 * 生成Image的段落
 * @param {BlobWithRatio} blob - blob格式的图片
 * @returns {Paragraph} - 生成的表格段落
 */
async function generateImage(blob: BlobWithRatio): Promise<Paragraph> {
  const ratio = blob?.ratio || 1.5
  const height = 180
  const base64Image = await blobToBase64(blob)

  return new Paragraph({
    children: [
      new ImageRun({
        data: base64Image,
        transformation: {
          width: height * ratio,
          height,
        },
      }),
    ],
  })
}
