import clsx from 'clsx'
import * as echarts from 'echarts'
import React, { useEffect, useRef } from 'react'
import { assertExhaustive } from 'src/shared/common-utils'
import { IS_H5 } from 'src/shared/constants'

type SparkChartType = 'bar' | 'line'
export default function SparkChart({
  className,
  xAxisData,
  width,
  height,
  data,
  chartType,
}: {
  className?: string
  width: number
  height: number
  xAxisData: string[]
  data: number[] | string[]
  chartType: SparkChartType
}) {
  // 使用 echarts 的 line chart 来绘制
  const chartRef = React.useRef<HTMLDivElement>(null)
  const myChartInstance = useRef<echarts.ECharts>()

  useEffect(() => {
    if (chartRef.current == null) {
      return
    }
    const handleResize = () => {
      myChartInstance.current?.resize()
    }
    myChartInstance.current = echarts.init(chartRef.current)
    myChartInstance.current.setOption(getChartOptions(chartType, xAxisData, data))
    window.addEventListener('resize', handleResize)
    return () => {
      myChartInstance.current?.dispose()
      window.removeEventListener('resize', handleResize)
    }
  })

  return (
    <div className={clsx('flex', `h-[${height}px]`, `w-[${width}px]`, className)}>
      <div className="flex h-full w-full" ref={chartRef} />
    </div>
  )
}

function getChartOptions(chartType: SparkChartType, xAxisData: string[], data: number[] | string[]) {
  switch (chartType) {
    case 'bar':
      return {
        tooltip: {
          show: !IS_H5,
          trigger: 'axis',
          axisPointer: {
            animation: false,
          },
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            show: false,
            margin: 0,
          },
          axisLine: {
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            show: false,
            margin: 0,
          },
          splitLine: {
            show: false,
          },
        },
        grid: {
          left: '0%',
          right: '0%',
          top: '0%',
          bottom: '0%',
          containLabel: true,
        },
        series: [
          {
            data,
            type: 'bar',
            itemStyle: {
              color: '#2458FE',
            },
          },
        ],
      }
    case 'line':
      return {
        tooltip: {
          show: !IS_H5,
          trigger: 'axis',
          axisPointer: {
            animation: false,
          },
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData,
          axisLabel: {
            show: false,
            margin: 0,
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            show: false,
            margin: 0,
          },
          splitLine: {
            show: false,
          },
        },
        grid: {
          left: '0%', // 根据实际情况调整
          right: '0%', // 根据实际情况调整
          top: '0%',
          bottom: '0%', // 如果是X轴标签被隐藏，调整这个
          containLabel: false,
        },
        series: [
          {
            data,
            type: 'line',
            smooth: false,
            symbol: 'none',
            itemStyle: {
              color: '#2458FE',
            },
            lineStyle: {
              width: 1,
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(174, 195, 255, 0.95)',
                },
                {
                  offset: 1,
                  color: 'rgba(174, 195, 255, 0.1)',
                },
              ]),
            },
          },
        ],
      }
    default:
      return assertExhaustive(chartType)
  }
}
