/**
 * @description Llm 切换器
 */
import { ExclamationCircleFilled } from '@ant-design/icons'
import { Dropdown, MenuProps, Modal, Tooltip } from 'antd'
import clsx from 'clsx'
import React from 'react'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import { LlmType } from '@shared/common-types'
import { SvgIcon, llmDefaultIcon, newLlmIcon } from '@components/SvgIcon'
import {
  llmTypeAtom,
  conversationIdAtom,
  isSubmittingAtom,
  llmListAtom,
  brandInfoAtom,
  initChatsAtom,
} from '../pages/AskBI/askBIAtoms'
import './LlmToggle.css'

interface Props {
  /** 切换模型的时候，是否弹窗确认框 */
  needConfirm: boolean
  /** 显示模型简称 移动端不需要展示模型简称*/
  showLlmAbbrName?: boolean
}

function LlmToggle({ needConfirm, showLlmAbbrName = true }: Props) {
  const [llmType, setLlmType] = useAtom(llmTypeAtom)
  const [_conversationId, setConversationId] = useAtom(conversationIdAtom)
  const [_isSubmitting, setIsSubmitting] = useAtom(isSubmittingAtom)
  const llmList = useAtomValue(llmListAtom)
  const brandInfo = useAtomValue(brandInfoAtom)
  const initChats = useSetAtom(initChatsAtom)
  const currentLlm = (llmList || []).find((o) => o.type === llmType)

  const clearHistory = () => {
    setConversationId(null)
    initChats()
  }

  const handleLlmChange = (type: LlmType) => {
    {
      !needConfirm
        ? setLlmType(type)
        : Modal.confirm({
            title: `切换模型将会清空当前聊天记录，是否确认？`,
            icon: <ExclamationCircleFilled />,
            okText: '确认',
            cancelText: '取消',
            centered: true,
            className: 'llm-toggle-confirm bg-white dark:bg-slate-900 dark:text-slate-100',
            onOk() {
              setLlmType(type)
              clearHistory()
              setIsSubmitting(false)
            },
          })
    }
  }

  const items: MenuProps['items'] = (llmList || []).map((o, index) => {
    const selected = llmType === o.type
    return {
      key: index,
      label: (
        <div
          className={clsx(
            'flex cursor-pointer items-center px-2 py-1',
            selected && 'text-sky-500',
            'hover:bg-slate-50 hover:dark:bg-slate-600/30',
          )}
          onClick={() => {
            handleLlmChange(o.type)
          }}
        >
          <SvgIcon icon={llmDefaultIcon} className="mr-2 h-4 w-4" />
          {brandInfo.brandName === 'DIPEAK' ? o.name : o.name.replace(/Dipeak/g, brandInfo.brandName || '')}
        </div>
      ),
    }
  })

  if (items == null || items.length === 0) {
    return (
      <div className="mx-4 mr-2 flex shrink-0 flex-row items-center gap-2 text-xl text-gray-400 dark:text-gray-500">
        {/* <SvgIcon icon={llmDefaultIcon} className="flex h-4 w-4 flex-none text-xl text-gray-400 dark:text-gray-500" /> */}
        <div className="whitespace-no-wrap border-r pr-2 text-xs dark:border-gray-500 md:text-sm">
          <Tooltip placement="bottom" title="模型加载中或无模型使用权限">
            -
          </Tooltip>
        </div>
      </div>
    )
  }

  const renderCurrentLlm = () => {
    return (
      <div className="flex shrink-0 flex-row items-center gap-2 text-xl dark:text-gray-500">
        <SvgIcon icon={newLlmIcon} className="flex h-6 w-6 flex-none text-xl dark:text-gray-500" />
        {showLlmAbbrName && brandInfo && currentLlm && (
          <div className="whitespace-no-wrap text-xs dark:border-gray-500 md:text-sm">
            {brandInfo.brandName === 'China Telecom'
              ? 'china-telecom-llm'
              : brandInfo.brandName === 'DIPEAK'
                ? currentLlm.abbrName
                : currentLlm.abbrName.replace(/Dipeak/g, brandInfo.brandName)}
          </div>
        )}
      </div>
    )
  }

  return (
    <>
      {llmList.length === 1 ? (
        renderCurrentLlm()
      ) : (
        <Dropdown menu={{ items }} placement="bottomLeft">
          <div className="flex items-center">{renderCurrentLlm()}</div>
        </Dropdown>
      )}
    </>
  )
}

export default LlmToggle
