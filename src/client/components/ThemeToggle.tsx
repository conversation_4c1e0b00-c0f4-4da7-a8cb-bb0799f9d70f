/**
 * @description Theme ColorSchema 切换器
 * 需要同步3部分的存储：prefers-color-scheme, localStorage, html.dark
 * 数据流向1：点击按钮 -> 更新 localStorage -> 更新 html.dark -> 更新 atom
 * 数据流向2：useEffect 监听切换 prefers-color-scheme -> 更新 html.dark -> 更新 atom
 */
import { ComputerDesktopIcon, MoonIcon, SunIcon } from '@heroicons/react/24/outline'
import { Dropdown, MenuProps } from 'antd'
import clsx from 'clsx'
import React, { useCallback, useEffect, useReducer } from 'react'
import { useAtom } from 'jotai'
import { CHROME_ACTIONS, IS_CHROME_EXTENSION } from '@shared/constants'
import { themeAtom } from '../pages/AskBI/askBIAtoms'

function getPreferredTheme() {
  return localStorage.theme === 'dark' ||
    (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)
    ? 'dark'
    : 'light'
}

function syncHtmlClass() {
  const currentTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'light'
  const nextTheme = getPreferredTheme()
  if (currentTheme !== nextTheme) {
    document.documentElement.classList.add('changing-theme')

    // 在 chrome 插件中，把当前的 theme 同步出去
    if (IS_CHROME_EXTENSION) {
      console.info('sync theme in chrome extension to', nextTheme)
      const parentWindow = window.parent as Window
      parentWindow.postMessage({ action: CHROME_ACTIONS.changeTheme, theme: nextTheme }, '*')
    }

    if (nextTheme === 'dark') {
      document.documentElement.classList.add('dark')
      document.querySelector('meta[name="theme-color"]')?.setAttribute('content', '#0B1120')
    } else {
      document.documentElement.classList.remove('dark')
      document.querySelector('meta[name="theme-color"]')?.setAttribute('content', '#f8fafc')
    }
    window.setTimeout(() => {
      document.documentElement.classList.remove('changing-theme')
    })
  }
}

const themeOptions = [
  {
    value: 'light',
    label: '浅色模式',
    icon: SunIcon,
  },
  {
    value: 'dark',
    label: '深色模式',
    icon: MoonIcon,
  },
  {
    value: 'system',
    label: '跟随系统',
    icon: ComputerDesktopIcon,
  },
]

function ThemeToggle() {
  const [theme, setTheme] = useAtom(themeAtom)
  // 每次 dropdown 变化后都要重新渲染本组件才行，这样才可以重新render
  const [, forceUpdate] = useReducer((x) => x + 1, 0) // 与 useState 类似，我们并不使用这个 state

  // useEffect 监听切换 prefers-color-scheme -> 更新 html.dark -> 更新 atom
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')

    mediaQuery.addEventListener('change', syncHtmlClass)
    const nextTheme = getPreferredTheme()
    setTheme(nextTheme)

    return () => {
      mediaQuery.removeEventListener('change', syncHtmlClass)
    }
  }, [setTheme])

  const handleThemeChange = useCallback(
    (val: string) => {
      // 点击按钮 -> 更新 localStorage -> 更新 html.dark -> 更新 atom
      if (val === 'system') {
        localStorage.removeItem('theme')
      } else {
        localStorage.setItem('theme', val)
      }
      syncHtmlClass()
      const nextTheme = getPreferredTheme()
      setTheme(nextTheme)
      forceUpdate() // 强制重新渲染本组件，因为 system 的时候 theme 并没有改变
    },
    [setTheme],
  )

  const items: MenuProps['items'] = themeOptions.map((o, index) => {
    let selected = theme === o.value
    if (localStorage.getItem('theme') == null) {
      selected = 'system' === o.value
    }

    return {
      key: index,
      label: (
        <div
          className={clsx(
            'flex cursor-pointer items-center px-2 py-1',
            selected && 'text-link',
            'hover:bg-slate-50 hover:dark:bg-slate-600/30',
          )}
          onClick={() => {
            handleThemeChange(o.value)
          }}
        >
          <o.icon
            className={clsx('mr-2 h-6 w-6', selected ? 'stroke-primary' : 'stroke-slate-400 dark:stroke-slate-500')}
          />
          {o.label}
        </div>
      ),
    }
  })

  const SelectedIcon = themeOptions.find((o) => o.value === theme)?.icon as React.JSX.ElementType
  return (
    <Dropdown menu={{ items }} placement="bottom">
      <a onClick={(e) => e.preventDefault()} className="cursor-pointer">
        <div>
          <SelectedIcon className={clsx('h-6 w-6', localStorage.getItem('theme') != null && 'text-[#919FBD]')} />
        </div>
      </a>
    </Dropdown>
  )
}

export default ThemeToggle
