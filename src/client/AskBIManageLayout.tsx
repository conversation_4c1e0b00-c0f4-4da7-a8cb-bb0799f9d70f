/**
 * 后台管理页面的布局
 */
import React, { Suspense, useEffect } from 'react'
import { UserIcon, RectangleStackIcon, UserGroupIcon } from '@heroicons/react/24/outline'
import clsx from 'clsx'
import { ConfigProvider, App } from 'antd'
import { Outlet, useLocation } from 'react-router-dom'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import zhCN from 'antd/lib/locale/zh_CN'
import { IS_CHROME_EXTENSION, IS_H5 } from '@shared/constants'
import { askBIPageUrls } from 'src/shared/url-map'
import { MenuItem } from 'src/shared/common-types'
import { currentDatasetAtom, envAtom, requestDefaultDatasetAtom, themeAtom } from './pages/AskBI/askBIAtoms'
import AdminSidebar from './components/AdminSidebar'
import LayoutHeader from './LayoutHeader'
import { getAntdConfigProviderTheme } from './utils'
import {
  SvgIcon,
  menuDataSourceIcon,
  menuIndexSceneIcon,
  menuProjectManagementIcon,
  menuSceneIcon,
  menuSqlSearchIcon,
  menuVirtualTableIcon,
  menuMetricmodelIcon,
  menuOperationIcon,
} from './components/SvgIcon'
import useIsAllowedRanger from './hooks/useIsAllowedRanger'
import { useAuth } from './hooks/useAuth'

const SidebarMenuItemsKeyNotInXEngine = ['scene', 'project', 'scenario']

const hideSidebarPaths = [askBIPageUrls.manage.testOverview, askBIPageUrls.manage.executeSql]
export function AskBIManageLayout() {
  const [theme, _setTheme] = useAtom(themeAtom)
  const requestDefaultDataset = useSetAtom(requestDefaultDatasetAtom)
  const currentDataset = useAtomValue(currentDatasetAtom)
  const isAllowedRanger = useIsAllowedRanger()
  const { data: canAccessResource } = useAuth({ resource: 'page:/manage/admin/resource', action: 'read' })
  const { data: canAccessRole } = useAuth({ resource: 'page:/manage/admin/role', action: 'read' })
  const { data: canAccessUser } = useAuth({ resource: 'page:/manage/admin/user', action: 'read' })

  const getAdminSidebarMenuItems = (): MenuItem[] => {
    const items: MenuItem[] = [
      {
        key: 'scene',
        label: '场景/项目管理',
        type: 'group',
      },
      {
        key: 'project',
        label: '项目管理',
        icon: <SvgIcon icon={menuProjectManagementIcon} className="h-6 w-6 dark:text-white" />,
        path: askBIPageUrls.manage.manageProject.list,
      },
      {
        key: 'scenario',
        path: askBIPageUrls.manage.scenarios.manage,
        label: '场景管理',
        icon: <SvgIcon icon={menuSceneIcon} className="h-6 w-6 dark:text-white" />,
      },

      // {
      //   key: 'xengine-management',
      //   label: 'XEngine数据管理',
      //   type: 'group',
      // },
      // {
      //   key: 'external-datasource',
      //   label: '外部数据源',
      //   icon: <SvgIcon icon={menuDataSourceIcon} className="h-6 w-6 dark:text-white" />,
      //   children: [
      //     { key: 'catalog-list', path: askBIPageUrls.manage.externalDatasource.catalogList, label: '数据源管理' },
      //     {
      //       key: 'table-list',
      //       path: askBIPageUrls.manage.externalDatasource.tableList,
      //       label: '表列表',
      //     },
      //   ],
      // },
      // {
      //   key: 'sql-query',
      //   path: askBIPageUrls.manage.xengine.sqlQuery,
      //   label: <a rel="noopener noreferrer">SQL查询</a>,
      //   icon: <SvgIcon icon={menuSqlSearchIcon} className="h-6 w-6 dark:text-white" />,
      // },

      // {
      //   key: 'data-model',
      //   label: '虚拟表',
      //   icon: <SvgIcon icon={menuVirtualTableIcon} className="h-6 w-6 dark:text-white" />,
      //   children: [
      //     { label: '贴源虚拟表', key: 'virtual-table', path: askBIPageUrls.manage.xengine.virtualTable },
      //     { label: '业务虚拟表', key: 'business-data-model', path: askBIPageUrls.manage.xengine.businessDataModel },
      //     { label: '虚拟表目录管理', key: 'catalog-manager', path: askBIPageUrls.manage.xengine.catalogManager },
      //   ],
      // },
      // {
      //   key: 'metric-model',
      //   label: '指标模型管理',
      //   icon: <SvgIcon icon={menuMetricmodelIcon} className="h-6 w-6 dark:text-white" />,
      //   path: askBIPageUrls.manage.metricModel.list,
      // },
      // {
      //   key: 'materialization',
      //   label: '物化管理',
      //   icon: <SvgIcon icon={menuIndexSceneIcon} className="h-6 w-6 dark:text-white" />,
      //   children: [
      //     { label: '智能物化', key: 'material-view-list', path: askBIPageUrls.manage.xengine.materialViewList },
      //     { label: '任务列表', key: 'job-list', path: askBIPageUrls.manage.xengine.jobList },
      //     { label: '智能物化发现', key: 'material-view-scan', path: askBIPageUrls.manage.xengine.materialViewScan },
      //     { label: '关联物化查询', key: 'query-related-mv', path: askBIPageUrls.manage.xengine.queryRelatedMv },
      //   ],
      // },
      // {
      //   key: 'operation',
      //   label: '运维管理',
      //   icon: <SvgIcon icon={menuOperationIcon} className="h-6 w-6 dark:text-white" />,
      //   children: [
      //     {
      //       key: 'data-scene',
      //       path: askBIPageUrls.manage.xengine.dataSceneList,
      //       label: '数据场景',
      //     },
      //     { label: '日志', key: 'log-file-list', path: askBIPageUrls.manage.xengine.logFileList },
      //     { label: '工具箱', key: 'tools-box', path: askBIPageUrls.manage.xengine.toolsBox },
      //     { label: 'SQL查询历史', key: 'sql-history', path: askBIPageUrls.manage.xengine.sqlHistory },
      //     { label: '大查询管理', key: 'sql-query-manage', path: askBIPageUrls.manage.xengine.sqlQueryManage },
      //   ],
      // },
    ]
    // if (canAccessResource || canAccessRole || canAccessUser) {
    //   items.push({
    //     key: 'admin-management',
    //     label: '权限管理',
    //     type: 'group',
    //   })
    // }
    // if (canAccessUser) {
    //   items.push({
    //     key: 'user',
    //     label: '用户管理',
    //     icon: <UserIcon className="h-5 w-5 dark:text-white" />,
    //     path: askBIPageUrls.manage.admin.user,
    //   })
    // }
    // if (canAccessRole) {
    //   items.push({
    //     key: 'role',
    //     label: '角色管理',
    //     icon: <UserGroupIcon className="h-5 w-5 dark:text-white" />,
    //     path: askBIPageUrls.manage.admin.role,
    //   })
    // }
    // if (canAccessResource) {
    //   items.push({
    //     key: 'resource',
    //     label: '资源管理',
    //     icon: <RectangleStackIcon className="h-5 w-5 dark:text-white" />,
    //     path: askBIPageUrls.manage.admin.resource,
    //   })
    // }

    // // {
    // //   key: 'group',
    // //   label: '用户组管理',
    // //   icon: <HomeIcon className="h-6 w-6 dark:text-white" />,
    // //   path: askBIPageUrls.manage.admin.group,
    // // },
    return items
  }
  const location = useLocation()
  const [env] = useAtom(envAtom)

  let AdminSidebarMenuItems = getAdminSidebarMenuItems()

  // 如果 xengine 单独部署对菜单进行过滤
  if (env?.VITE_PRODUCTS?.trim() === 'X-Engine') {
    AdminSidebarMenuItems = AdminSidebarMenuItems.filter((item) => {
      return SidebarMenuItemsKeyNotInXEngine.indexOf(item.key) === -1
    })
  }

  // 加载默认的 dataset
  useEffect(() => {
    // 先判断 currentDatasetAtom 是否为空，当为空的时候才去请求
    if (currentDataset == null) {
      requestDefaultDataset()
    }
  }, [currentDataset, requestDefaultDataset])

  return (
    <ConfigProvider locale={zhCN} theme={getAntdConfigProviderTheme(theme)}>
      <App>
        <div
          className={clsx(
            'layout-root relative flex h-screen flex-col overflow-hidden text-black dark:bg-slate-900 dark:text-slate-100 md:overflow-visible',
            IS_CHROME_EXTENSION && 'chrome-extension',
          )}
          style={IS_H5 ? { height: window.innerHeight + 'px' } : {}}
        >
          <LayoutHeader isFullWidth={true} />
          <section className="flex flex-grow flex-row items-stretch overflow-auto border-t border-slate-200 pb-0 dark:border-slate-700">
            {hideSidebarPaths.some((url) => location.pathname.includes(url)) ? null : (
              <AdminSidebar
                menuItems={
                  isAllowedRanger
                    ? AdminSidebarMenuItems
                    : AdminSidebarMenuItems.filter((item) => !item.key?.startsWith('admin'))
                }
              />
            )}
            <div className="relative flex-1 overflow-auto bg-white p-[28px] dark:bg-slate-800">
              <Suspense fallback={<div>Loading...</div>}>
                <Outlet />
              </Suspense>
            </div>
          </section>
        </div>
      </App>
    </ConfigProvider>
  )
}
