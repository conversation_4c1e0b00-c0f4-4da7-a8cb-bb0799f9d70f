/**
 * @description 转发代理函数
 */

import { type Request, type Response } from 'express'
import axios from 'axios'
import FormData from 'form-data'
import { omit } from 'lodash'

export async function commonProxy(req: Request, res: Response, url: string) {
  try {
    const { headers = {}, query, body, method } = req

    // 处理 multipart/form-data 请求
    if (headers['content-type']?.startsWith('multipart/form-data')) {
      const formData = new FormData()
      Object.keys(body).forEach((key) => {
        formData.append(key, body[key])
      })

      const response = await axios({
        method,
        url,
        data: formData,
        params: query,
        headers: {
          ...omit(headers, ['content-length']),
          ...formData.getHeaders(),
        },
      })

      if (response.headers?.['content-type']) {
        res.set('Content-Type', response.headers['content-type'])
      }
      return res.send(response.data)
    } else {
      // 处理其他类型的请求
      const response = await axios({
        method,
        url,
        data: body,
        params: query,
        headers,
      })

      if (response.headers?.['content-type']) {
        res.set('Content-Type', response.headers['content-type'])
      }
      return res.send(response.data)
    }
  } catch (error: any) {
    console.error(`出现错误，请求地址${req.url}`, error)
    const response = error?.response || {}
    if (response.code !== undefined) {
      if (response.headers?.['content-type']) {
        res.set('Content-Type', response.headers['content-type'])
      }
      return res.send(response)
    }
    return res.status(500).json({ code: 500, msg: '服务端错误' })
  }
}
