import { Request } from 'express'
import axios from 'axios'
import chalk from 'chalk'
import { ChatResponseErrorTypes, LlmType, Message, OlapRow } from '@shared/common-types'
import { DEFAULT_TIMEOUT } from '@shared/constants'
import { TaskType, ResultOfParamsExtract, TaskTypes, TimeQueryParams } from 'src/shared/metric-types'
import { PlanNode } from 'src/shared/chat-progress'
import { assertExhaustive, filterRowsByTimeRange } from 'src/shared/common-utils'
import { PROCESS_ENV } from '../server-constants'
import { saveAskBotBusinessLogs } from '../winston-log'
import MetricConfig from '../MetricStore/metric-config'
import { handleQueryMetricResult, transformChatResponseError } from './utils'

// const fake_passthrough_prompts_q = {
//   nl2metric_group_bys: { groupBys: ['bch_nme'], notExistGroupBys: [] },
//   nl2metric_time_query: {
//     timeStartFunction: { year: 2023, month: 1, day: 1 },
//     timeEndFunction: { year: 2025, month: 1, day: 24 },
//     timeGranularity: 'month',
//   },
//   nl2metric_metrics: {
//     metrics: ['demo_7000_i7000'],
//     notExistMetricNames: [],
//   },
//   nl2metric_order_bys: { limit: 1, orderBys: ['demo_7000_i7000 desc'] },
//   nl2metric_where: {
//     limit: 1,
//     where: '',
//     select: 'bch_nme, demo_7000_i7000',
//     groupby: 'bch_nme',
//     orderby: 'demo_7000_i7000 desc',
//     where_time: "dt >= '2023-01-01'",
//   },
//   nl2intent_by_tag_v2: {
//     intent_list: ['与召回相关', '问码值', '需同环比'],
//   },
//   period_on_period: {
//     a: ["lookup_data('23年2月各分行的经营利润')", null],
//     b: ["lookup_data('23年1月各分行的经营利润')", null],
//     c: ["calculator('a - b / a')", ['a', 'b']],
//   },
// }
// const fake_passthrough_prompts_jan = {
//   nl2metric_group_bys: { groupBys: ['bch_nme'], notExistGroupBys: [] },
//   nl2metric_time_query: {
//     timeStartFunction: { year: 2023, month: 1, day: 1 },
//     timeEndFunction: { year: 2023, month: 1, day: 31 },
//     timeGranularity: 'total',
//   },
//   nl2metric_metrics: {
//     metrics: ['demo_7000_i7000'],
//     notExistMetricNames: [],
//   },
//   nl2metric_where: {
//     limit: 0,
//     where: '',
//     select: 'bch_nme, demo_7000_i7000',
//     groupby: 'bch_nme',
//     orderby: '',
//     where_time: "dt >= '2023-01-01' and dt <= '2023-01-31'",
//   },
// }
// const fake_passthrough_prompts_feb = {
//   nl2metric_group_bys: { groupBys: ['bch_nme'], notExistGroupBys: [] },
//   nl2metric_time_query: {
//     timeStartFunction: { year: 2023, month: 2, day: 1 },
//     timeEndFunction: { year: 2023, month: 2, day: 28 },
//     timeGranularity: 'total',
//   },
//   nl2metric_metrics: {
//     metrics: ['demo_7000_i7000'],
//     notExistMetricNames: [],
//   },
//   nl2metric_where: {
//     limit: 0,
//     where: '',
//     select: 'bch_nme, demo_7000_i7000',
//     groupby: 'bch_nme',
//     orderby: '',
//     where_time: "dt >= '2023-01-01' and dt <= '2023-01-31'",
//   },
// }
/**
 * 处理python返回的参数
 * @param {Object} props - 函数参数
 * @param {any} props.resp - 提参返回值
 * @param {string} props.resp.message - 用户的问题
 * @param {boolean} props.config.enableMetricExactMatch - 开启指标置信度 后续需要改成场景的所有配置
 * @returns {ResultOfParamsExtract} - 处理后的聊天响应 返回给前端
 */
export const parseParamsExtractResponse = async (props: {
  resp: any
  config: {
    enableMetricExactMatch: boolean
    metricConfig: MetricConfig
  }
}): Promise<ResultOfParamsExtract> => {
  const { resp, config } = props
  if (resp.data?.ready === false) {
    return {
      type: 'chat-error',
      errType: resp.data.errType,
      content: resp.data.unreadyReason,
    }
  }
  if (resp.error) {
    return {
      type: 'llm-error',
      content: resp.error,
    }
  }
  const taskType: TaskType = TaskTypes.includes(resp.data.type) === true ? resp.data.type : 'llm-error'
  switch (taskType) {
    case 'query-metric': {
      return await handleQueryMetricResult(resp, config)
    }
    case 'attribution-analysis': {
      const attributionAnalysis = resp.data.attribution_analysis
      if (attributionAnalysis.error) {
        return {
          type: 'llm-error',
          content: attributionAnalysis.error,
        }
      } else {
        return {
          type: 'attribution-analysis',
          base_compare: attributionAnalysis.base_compare,
          tree: attributionAnalysis.tree,
          dimension: attributionAnalysis.dimension,
          attr_params: attributionAnalysis.attr_params,
          metric_sql: attributionAnalysis.metric_sql,
        }
      }
    }
    case 'attribution-metric-analysis': {
      const attributionAnalysis = resp.data.attribution_analysis
      return {
        type: 'attribution-metric-analysis',
        reportPrompt: attributionAnalysis.reportPrompt,
        metricName: attributionAnalysis.metricName,
        metricValue: attributionAnalysis.metricValue,
        metricExpr: attributionAnalysis.metricExpr,
        dimensionDetail: attributionAnalysis.dimensionDetail,
      }
    }
    case 'data-overview': {
      return {
        type: 'data-overview',
        content: '数据概览，开发中',
      }
    }
    case 'table-list': {
      return {
        type: 'table-list',
        content: '场景列表，开发中',
      }
    }
    case 'dimension-list': {
      return {
        type: 'dimension-list',
        content: '维度列表，开发中',
      }
    }
    case 'dimension-detail': {
      return {
        type: 'dimension-detail',
        content: resp?.data?.meta_result?.param ?? 'DIMENSION_ID_ERROR',
      }
    }
    case 'metric-list': {
      return {
        type: 'metric-list',
        content: '指标列表，开发中',
      }
    }
    case 'metric-detail': {
      return {
        type: 'metric-detail',
        content: resp?.data?.meta_result?.param ?? 'METRIC_ID_ERROR',
      }
    }
    case 'metric-tree': {
      return {
        type: 'metric-tree',
        content: '指标树，敬请期待',
      }
    }
    case 'chitchat': {
      return {
        type: 'chitchat',
        content: '非数据分析类问题',
      }
    }
    case 'llm-error': {
      return {
        type: 'chitchat',
        // llm-error 归类为意图识别出错
        content: '未知的意图类型',
      }
    }
    case 'percentage': {
      return {
        type: taskType,
        content: '占比类型',
      }
    }
    case 'period_on_period': {
      return {
        type: taskType,
        content: '环比类型',
      }
    }
    case 'predict': {
      const calculator = resp.data.calculator as PlanNode[]
      const queryMetric = calculator.find((i) => i.param_key === 'a')?.meta?.query_metric as any
      const timeItem = calculator.find((i) => i.param_key === 'time')!
      const resultItem = calculator.find((i) => i.param_key === 'result')! as any
      const schema = resultItem.data.schema as any
      const data = resultItem.data.table as any
      const timeQueryParams = timeItem?.meta?.time_range as TimeQueryParams

      return {
        type: 'predict',
        groupBys: [],
        metricNames: queryMetric.metricNames,
        orderBys: queryMetric.orderBys,
        where: queryMetric.where,
        originWhere: queryMetric.where,
        limit: queryMetric.limit || undefined,
        extraInfo: resp.data?.extra_info,
        timeQueryParams: timeQueryParams,
        predictRows: filterRowsByTimeRange(data, schema, timeQueryParams) as OlapRow[],
        isMetricNamesExactMatch: queryMetric.isMetricNamesExactMatch,
        isWhereExactMatch: queryMetric.isWhereExactMatch,
        notExistGroupBys: queryMetric.notExistGroupBys,
        notExistMetricNames: queryMetric.notExistMetricNames,
        notExistOrderBys: queryMetric.notExistOrderBys,
        infoTexts: [],
      }
    }
    default: {
      assertExhaustive(taskType)
    }
  }
}

export const paramsExtractChat = async (
  messages: Message[],
  sceneId: string,
  projectId: string,
  llmType: LlmType,
  enableMetricExactMatch: boolean,
  followUpQuestionParams: any,
  currentParamsExtractApi: string,
  paramsForLog: {
    host: string
    conversationId: string
    chatId: string
    username: string
    traceId: string
    req: Request
  },
  taskId: string,
): Promise<any> => {
  const startTime = Date.now()
  const { host, username, traceId, conversationId, chatId, req } = paramsForLog
  const headers = {
    'Content-Type': 'application/json',
  }
  const bodyForPython = {
    messages: messages,
    model_id: sceneId,
    force_exact_match: enableMetricExactMatch,
    history_params_extract_data: followUpQuestionParams,
    task_id: taskId,
    model_type: llmType,
    additional_info: {
      original_history_params_extract_data: req.body.prevLlmResponse,
    },
    // model_type: 'fake_passthrough',
    // additional_info: {
    //   fake_passthrough_prompts: {
    //     [messages[0].content]: fake_passthrough_prompts_q,
    //     '23年1月各分行的经营利润': fake_passthrough_prompts_jan,
    //     '23年2月各分行的经营利润': fake_passthrough_prompts_feb,
    //   },
    //   fake_passthrough_prompts_mode: 'by_question',
    // },
  }
  const baseUrl = currentParamsExtractApi || PROCESS_ENV.PARAMS_EXTRACT_URL_HOST
  const paramsExtractUrl = `${baseUrl}/api/v1/params_extract`

  console.info(chalk.yellow('Params Extract URL: ', paramsExtractUrl))
  console.info('Body for Params Extract: ', JSON.stringify(bodyForPython))

  return axios
    .post(paramsExtractUrl, bodyForPython, { headers, timeout: DEFAULT_TIMEOUT })
    .then((result) => {
      console.info(chalk.green('Params Extract Result:', JSON.stringify(result.data)))
      saveAskBotBusinessLogs({
        serviceType: 'web_service',
        moduleType: 'params_extract',
        host,
        username,
        traceId,
        startTime,
        resultCode: result.data.code,
        input: messages,
        output: result.data,
        semanticProjectId: projectId,
        semanticSceneId: sceneId,
        debug: {
          conversationId,
          sceneId,
          chatId,
          llmType,
          url: paramsExtractUrl,
          method: 'POST',
          payload: {
            messages: messages,
            model_type: llmType,
            model_id: sceneId,
            enableMetricExactMatch,
            followUpQuestionParams,
          },
          response: result.data,
        },
        req,
      })
      if (result.data.code === 0 && result.data.data != null) {
        return result.data
      } else {
        return transformChatResponseError(result.data)
      }
    })
    .catch((error) => {
      console.info(chalk.red('Params Extract error:', JSON.stringify(error)))
      saveAskBotBusinessLogs({
        serviceType: 'web_service',
        moduleType: 'params_extract',
        host,
        username,
        traceId,
        startTime,
        resultCode: 500,
        input: messages,
        output: error,
        semanticProjectId: projectId,
        semanticSceneId: sceneId,
        debug: {
          conversationId,
          sceneId,
          chatId,
          llmType,
          url: paramsExtractUrl,
          method: 'POST',
          payload: {
            messages: messages,
            model_type: llmType,
            model_id: sceneId,
            enableMetricExactMatch,
            followUpQuestionParams,
          },
          response: error,
        },
        req,
      })
      if (error.response) {
        return transformChatResponseError(error.response)
      }
      return {
        code: -1,
        data: {
          taskType: 'chat-error',
          errType: ChatResponseErrorTypes.E_UNKNOWN,
          unreadyReason: '超纲了，大模型正在努力学习中...',
          ready: false,
          originResponse: { response: error, type: '兜底错误' },
        },
      }
    })
}

export const projectParamsExtractChat = async (
  messages: Message[],
  projectId: string,
  llmType: LlmType,
  enableMetricExactMatch: boolean,
  followUpQuestionParams: any,
  currentParamsExtractApi: string,
  paramsForLog: {
    traceId: string
    username: string
    host: string
    conversationId: string
    chatId: string
    req: Request
  },
): Promise<{ code: number; data: any }> => {
  const startTime = Date.now()
  const { host, username, traceId, conversationId, chatId, req } = paramsForLog

  const headers = {
    'Content-Type': 'application/json',
  }
  const baseUrl = currentParamsExtractApi || PROCESS_ENV.PARAMS_EXTRACT_URL_HOST
  const projectParamsExtractUrl = `${baseUrl}/api/v1/project_params_extract`

  const bodyForPython = {
    project_id: projectId,
    messages,
    model_type: llmType,
    force_exact_match: enableMetricExactMatch,
    history_params_extract_data: followUpQuestionParams,
  }
  console.info(chalk.yellow('Project Params Extract URL: ', projectParamsExtractUrl))
  console.info('Body for Project Params Extract: ', JSON.stringify(bodyForPython))

  return axios
    .post(projectParamsExtractUrl, bodyForPython, { headers, timeout: DEFAULT_TIMEOUT })
    .then((result) => {
      console.info(chalk.green('Project Params Extract Result:', JSON.stringify(result.data)))
      saveAskBotBusinessLogs({
        serviceType: 'web_service',
        moduleType: 'project_params_extract',
        host,
        username,
        traceId,
        startTime,
        resultCode: result.data.code,
        input: messages,
        output: result.data,
        semanticProjectId: projectId,
        debug: {
          conversationId,
          projectId,
          chatId,
          llmType,
          url: projectParamsExtractUrl,
          method: 'POST',
          payload: {
            project_id: projectId,
            messages,
            model_type: llmType,
          },
          response: result.data,
        },
        req,
      })
      if (result.data.code === 0 && result.data.data != null) {
        return result.data
      } else {
        throw new Error('大模型提参错误，提参返回: ' + JSON.stringify(result.data))
      }
    })
    .catch((error) => {
      console.error('Project Params Extract with Error: ', error)
      saveAskBotBusinessLogs({
        serviceType: 'web_service',
        moduleType: 'params_extract',
        host,
        username,
        traceId,
        startTime,
        resultCode: 500,
        input: messages,
        output: error,
        semanticProjectId: projectId,
        debug: {
          conversationId,
          projectId,
          chatId,
          llmType,
          url: projectParamsExtractUrl,
          method: 'POST',
          payload: {
            project_id: projectId,
            messages,
            model_type: llmType,
          },
          response: error,
        },
        req,
      })
      if (error.response) {
        return transformChatResponseError(error.response)
      }
    })
}
