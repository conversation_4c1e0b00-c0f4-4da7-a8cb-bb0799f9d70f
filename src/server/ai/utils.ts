/* eslint-disable @typescript-eslint/naming-convention */
import {
  ChatResponse,
  ChatResponseError,
  ChatResponseErrorMap,
  ChatResponseErrorStatus,
  ChatResponseErrorTypes,
  OlapRow,
} from '@shared/common-types'
import {
  DATE_ALIAS,
  ParamsExtractExactMatch,
  ParamsExtractExternalReport,
  ParamsExtractQueryMetricResult,
} from 'src/shared/metric-types'
import { detectTimeType, extractDimensionAndValues } from 'src/shared/common-utils'
import { convertTimeToSpecificDate } from 'src/shared/time-utils'
import { isBaoWu, isBaoWuCost, isBaoWuFinancial, isValidCodeValuesForOneCompany } from 'src/shared/baowu-share-utils'
import { isQueryBaoWuSubCompany } from '../custom/baowu/baowu-utils'
import MetricConfig from '../MetricStore/metric-config'
import Metric2Sql from '../MetricStore/metric2sql/metric2sql'
import { addLimitToSql } from '../dao/db'
import { executeAllXengineSql } from '../dao/xengine-query'
import { genNewSubCompanyQueryWhere, getBaowuQueryLevel } from '../custom/baowu/baowu-sub-company'
import { getCostSubCompanies } from '../custom/baowu/baowu-cost-scene'

/**
 * 处理queryMetric的python返回值，转换成node需要的类型，并且加入一些rewrite操作
 */
export async function handleQueryMetricResult(
  resp: any,
  config: {
    enableMetricExactMatch: boolean
    metricConfig: MetricConfig
  },
): Promise<ParamsExtractQueryMetricResult | ParamsExtractExactMatch | ParamsExtractExternalReport> {
  const { enableMetricExactMatch, metricConfig } = config
  const infoTexts: string[] = []
  const queryMetric = resp.data.query_metric
  let confidenceOriginData = undefined

  const matchedReports = metricConfig.allExternalReports
    .filter((report) => queryMetric.metricNames.includes(report.name))
    .map((i) => i.name)

  // 从 metricNames 中去除已匹配的报告名称
  const cleanMetricNames = queryMetric.metricNames.filter((name: string) => !matchedReports.includes(name))

  console.info(
    'originMetricNames, CleanMetricNames, MatchedReports',
    queryMetric.metricNames,
    cleanMetricNames,
    matchedReports,
  )

  // FIXME: 移除baowu定制逻辑
  if (isBaoWuFinancial(metricConfig.name)) {
    // 当只有一个metric时，改写orderby，防止错误 [ 'SUMT6200' ] [ 'BAOWU_LIRUN_REPORT desc' ]
    if (queryMetric.metricNames.length === 1 && queryMetric.orderBys && queryMetric.orderBys.length > 0) {
      queryMetric.orderBys = [
        queryMetric.orderBys[0]
          .split(/\s+/)
          .map((item: string, index: number) => (index === 0 ? queryMetric.metricNames[0] : item))
          .join(' '),
      ]
    }

    if (queryMetric.groupBys?.length) {
      queryMetric.isWhereExactMatch = true
    }

    // 如果是对子公司数量进行提问 groupbys_with_level设置为[]
    if (
      queryMetric.metricNames.some((item: string) => {
        return ['SUMZZ10002', 'SUMZZ10001'].includes(item)
      })
    ) {
      console.info(
        '宝武对子公司数量进行提问，将groupbys_with_level改成[], 即认为不是对子公司进行提问, 防止触发下面的改写 --->',
      )
      resp.data.extra_info = {
        ...resp.data?.extra_info,
        groupbys_with_level: [],
      }
      queryMetric.isWhereExactMatch = false
    }

    // todo 暂时由前端简单判断是否是问基地的,然后把子公司相关的参数去掉, 后续删掉这部分判断,改由大模型判断
    if (
      /基地/.test(resp.message) &&
      !/各公司/.test(resp.message) &&
      resp.data?.extra_info?.groupbys_with_level?.length > 0
    ) {
      console.info('----问的是,不是子公司, 改写groupbys_with_level为[]----')
      resp.data.extra_info.groupbys_with_level = []
    }

    // 如果是对子公司提问 改写where条件 不触发维度置信度
    const queryParamsForSubCompany = { ...queryMetric, extraInfo: resp.data?.extra_info }
    if (isQueryBaoWuSubCompany(queryParamsForSubCompany)) {
      queryMetric.isWhereExactMatch = true
      const queryLevel = getBaowuQueryLevel(queryParamsForSubCompany)
      console.info('queryLevel----->>>>>', queryLevel)

      const startTime = new Date().getTime()
      const hasOrderBy = queryMetric?.orderBys?.length > 0
      queryMetric.where = await genNewSubCompanyQueryWhere({
        metricConfig,
        where: queryMetric.where,
        queryLevel,
        hasOrderBy,
      })
      console.info('子公司处理时间: ', new Date().getTime() - startTime)

      console.info(
        '宝武母公司匹配子公司, 改写公司查询where后isWhereExactMatch, queryMetric.where --->',
        queryMetric.isWhereExactMatch,
        queryMetric.where,
      )
    }

    // 默认选择 管理合并=>管理合并=>法人
    if (queryMetric.isWhereExactMatch === false && (queryMetric.where || '').includes('COMPANY_INNER_CODE_DES')) {
      const whereRewrite = queryMetric.where
      const { codeValues } = extractDimensionAndValues(whereRewrite)
      if (isValidCodeValuesForOneCompany(codeValues)) {
        const priorityOrder = ['管理合并', '资产合并', '法人']
        const sortedValues = codeValues.sort((a, b) => {
          const aPriority = priorityOrder.findIndex((priority) => a.includes(priority))
          const bPriority = priorityOrder.findIndex((priority) => b.includes(priority))
          return aPriority - bPriority
        })
        // 如果是外部报表 默认选择
        if (matchedReports.length > 0) {
          const defaultSelection = sortedValues[0]
          console.info('[BAOWU]命中默认选择(报表+默认推荐):', sortedValues, defaultSelection)
          const defaultCondition = `COMPANY_INNER_CODE_DES = '${defaultSelection}'`
          queryMetric.where = defaultCondition
          queryMetric.isWhereExactMatch = true
          confidenceOriginData = {
            originWhere: whereRewrite,
            isWhereExactMatch: false,
            where: defaultCondition,
          }
        } else {
          const metric2Sql = new Metric2Sql(metricConfig)
          const verifiedMetricParams = metric2Sql.verifyQueryParams({
            metricNames: cleanMetricNames,
            externalReportNames: matchedReports,
            groupBys: queryMetric.groupBys,
            where: queryMetric.where,
            orderBys: queryMetric.orderBys,
            limit: queryMetric.limit,
            timeQueryParams: queryMetric.timeQueryParams,
            isMetricNamesExactMatch: queryMetric.isMetricNamesExactMatch,
            isWhereExactMatch: queryMetric.isWhereExactMatch,
            extraInfo: resp.data?.extra_info,
            notExistGroupBys: queryMetric.notExistGroupBys,
            notExistMetricNames: queryMetric.notExistMetricNames,
            notExistOrderBys: queryMetric.notExistOrderBys,
          })

          // 跨度为月份，可以启动追溯
          if (
            detectTimeType(verifiedMetricParams.queryParams) === 'month' &&
            verifiedMetricParams.queryParams.timeQueryParams
          ) {
            const specificDate = convertTimeToSpecificDate(
              verifiedMetricParams.queryParams.timeQueryParams.timeStartFunction,
              'start',
            )
            const { sql } = metric2Sql.toSql({
              ...verifiedMetricParams.queryParams,
              timeQueryParams: {
                ...verifiedMetricParams.queryParams.timeQueryParams,
                timeGranularity: 'month',
                timeStartFunction: {
                  type: 'specificDate',
                  year: specificDate.year - 1,
                  month: specificDate.month,
                  day: specificDate.day,
                },
              },
            })
            const rows = await executeAllXengineSql(addLimitToSql(sql, 100))
            const defaultSelection = detectHasData(rows, sortedValues)
            const missingData = sortedValues.slice(0, sortedValues.indexOf(defaultSelection)).map((item) => {
              const parts = item.split('-')
              return parts.length > 1 ? `${parts[1].length === 4 ? parts[1].slice(0, 2) : parts[1]}口径` : `${item}`
            })
            if (missingData.length > 0) {
              infoTexts.push(
                `未找到${missingData.join('、')}的数据，当前展示的是${defaultSelection.split('-').length > 1 ? defaultSelection.split('-')[1].split('-') : defaultSelection}口径的数据`,
              )
            }
            console.info('[BAOWU]命中默认选择(指标+月跨度+追溯+推荐):', sortedValues, defaultSelection, missingData)
            const defaultCondition = `COMPANY_INNER_CODE_DES = '${defaultSelection}'`
            queryMetric.where = defaultCondition
            queryMetric.isWhereExactMatch = true
            confidenceOriginData = {
              originWhere: whereRewrite,
              isWhereExactMatch: false,
              where: defaultCondition,
            }
          } else {
            const defaultSelection = sortedValues[0]
            console.info('[BAOWU]命中默认选择(指标+默认推荐):', sortedValues, defaultSelection)
            const defaultCondition = `COMPANY_INNER_CODE_DES = '${defaultSelection}'`
            queryMetric.where = defaultCondition
            queryMetric.isWhereExactMatch = true
            confidenceOriginData = {
              originWhere: whereRewrite,
              isWhereExactMatch: false,
              where: defaultCondition,
            }
          }
        }
      }
    }
  }

  // 宝武成本场景子公司问题处理
  if (isBaoWuCost(metricConfig.name)) {
    // 如果是对子公司提问 改写where条件 不触发维度置信度
    const queryParamsForSubCompany = { ...queryMetric, extraInfo: resp.data?.extra_info }
    if (isQueryBaoWuSubCompany(queryParamsForSubCompany)) {
      queryMetric.isWhereExactMatch = true

      queryMetric.where = getCostSubCompanies({ where: queryMetric.where })

      console.info(
        '宝武成本场景母公司匹配子公司, 改写公司查询where后isWhereExactMatch, queryMetric.where --->',
        queryMetric.isWhereExactMatch,
        queryMetric.where,
      )
    }
  }

  // 开启了指标置信度开关 && 提取到维度码值（宝武允许提到的码值为空，前端做兜底处理） && 提取到超过1个指标
  const forceExactMatch =
    enableMetricExactMatch &&
    queryMetric.metricNames.length > 0 &&
    (queryMetric.isMetricNamesExactMatch === false ||
      (queryMetric.isWhereExactMatch === false &&
        (isBaoWu(metricConfig.name) || (!isBaoWu(metricConfig.name) && queryMetric.where.length > 0)))) // 非宝武：where 长度必须大于 0
  console.info('forceExactMatch result is ---> ', forceExactMatch)

  // 置信度选择为false & python提取到外部报表 => 直接展示报表
  if (!forceExactMatch && matchedReports.length > 0) {
    return {
      type: 'query-external-report',
      where: queryMetric.where,
      timeQueryParams: queryMetric.timeQueryParams,
      externalReports: matchedReports,
      confidenceOriginData,
    }
  }

  // python 返回的timeQueryParams 必须包含起始&结束时间
  const { timeQueryParams } = queryMetric
  const localTimeQueryParams =
    timeQueryParams?.timeStartFunction && timeQueryParams?.timeEndFunction ? timeQueryParams : null

  return {
    type: forceExactMatch ? 'metric-exact-match' : 'query-metric',
    groupBys: queryMetric.groupBys || [],
    metricNames: cleanMetricNames,
    externalReportNames: matchedReports,
    orderBys: queryMetric.orderBys,
    where: queryMetric.where,
    originWhere: queryMetric.originWhere,
    limit: queryMetric.limit,
    timeQueryParams: localTimeQueryParams,
    isMetricNamesExactMatch: queryMetric.isMetricNamesExactMatch,
    isWhereExactMatch: queryMetric.isWhereExactMatch,
    extraInfo: resp.data?.extra_info,
    confidenceOriginData,
    notExistGroupBys: queryMetric.notExistGroupBys,
    notExistMetricNames: queryMetric.notExistMetricNames,
    notExistOrderBys: queryMetric.notExistOrderBys,
    infoTexts,
  }
}

export function transformChatResponseError(data: {
  code: keyof typeof ChatResponseErrorMap
  data: string | ChatResponse
}): {
  code: string
  data: Omit<ChatResponseError, 'conversationId' | 'sceneId'>
} {
  let errType: ChatResponseErrorStatus = ChatResponseErrorTypes.E_UNKNOWN
  const errorType = ChatResponseErrorMap[data.code]
  if (errorType) {
    errType = ChatResponseErrorTypes[ChatResponseErrorMap[data.code]]
  }
  return {
    ...data,
    data: {
      ...(!data?.data || typeof data.data === 'string' ? {} : data.data),
      originResponse: data,
      taskType: 'chat-error',
      errType,
      ready: false,
      // 暂时测试看有哪些参数
      unreadyReason:
        errType === ChatResponseErrorTypes.E_UNKNOWN
          ? '超纲了，大模型正在努力学习中...'
          : typeof data?.data === 'string'
            ? data.data
            : '超纲了，大模型正在努力学习中...',
    },
  }
}

function detectHasData(rows: OlapRow[], sortedValues: string[]) {
  if (rows.length === 0) {
    return sortedValues[0]
  }
  const companyCodes = rows
    .filter((item) => {
      return Object.keys(item)
        .filter(
          (key) =>
            !key.includes('yoy') && !key.includes('mom') && key !== 'COMPANY_INNER_CODE_DES' && key !== DATE_ALIAS,
        )
        .some((key) => item[key] !== null && item[key] !== '')
    })
    .map((item) => item.COMPANY_INNER_CODE_DES)

  for (const val of sortedValues) {
    if (companyCodes.includes(val)) {
      return val
    }
  }
  return sortedValues[0]
}
