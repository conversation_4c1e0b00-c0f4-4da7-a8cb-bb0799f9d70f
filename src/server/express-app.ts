import path from 'path'
import express from 'express'
import session from 'express-session'
import bodyParser from 'body-parser'
import cors from 'cors'
import axios from 'axios'
import cookieParser from 'cookie-parser'
import { askBIApiUrlsWithoutBaseUrl } from 'src/shared/url-map'
import { APIResponse } from 'src/shared/common-types'
import { setAxiosDefaults } from 'src/shared/config'
import { SEMANTIC_PROJECT_ID_PLACEHOLDER, SEMANTIC_SCENE_ID_PLACEHOLDER } from 'src/shared/constants'
import charts from './AskBI/charts/api'
import dashboards from './AskBI/dashboards/api'
import chats from './AskBI/chats/api'
import convers from './AskBI/convers/api'
import converChats from './AskBI/conver_chats/api'
import datasets from './AskBI/datasets/api'
import datasources from './AskBI/datasources/api'
import systemInfo from './AskBI/system-info/api'
import users from './AskBI/users/api'
import roles from './AskBI/roles/api'
import metrics from './AskBI/metrics/api'
import model from './MetricStore/models/api'
import llms from './AskBI/llms/api'
import permissionDatasource from './AskBI/permissions/datasource'
import permissionLlm from './AskBI/permissions/llm'
import permissionProject from './AskBI/permissions/project'
import rolesPermissionDatasource from './AskBI/roles-permissions/datasource'
import rolesPermissionLlm from './AskBI/roles-permissions/llm'
import rolesPermissionProject from './AskBI/roles-permissions/project'
import rolesPermissionPageList from './AskBI/roles-permissions/page-list'
import operationProject from './AskBI/operation-project/api'
import semanticModel from './MetricStore/semanticModel/api'
import semanticMetricTree from './MetricStore/semanticMetricTree/api'
import semanticProject from './MetricStore/semanticProject/api'
import dimensions from './MetricStore/dimensions/api'
import externalReports from './MetricStore/externalReport/api'
import executeSql from './AskBI/execute-sql/api'
import docs from './AskDoc/docs/api'
import login from './login/api'
import rangerLogin from './ProxyLogin/api'
import baowuApi from './AskBI/baowu-outer-api'
import iFlyProxy from './iFlyProxy/index'
import xengine from './MetricStore/xengine/api'
import envList from './AskBI/env-list/api'
import reportGenerate from './ReportGenerate/api'
import pptGenerate from './PPTGenerate/api'
import ai from './ai/api'
import elk from './elk/api'
import { router as authRouter, passport, passportMiddleware } from './auth'
import { globalLimiter, level1Limiter, level2Limiter, level3Limiter } from './limiter/limiter'
import { createStaticCompression, setAxiosUserId } from './utils'
import { saveAskBotBusinessLogs } from './winston-log'
import alipaySdk from './AskBI/alipaySdk/api'
import { getProjectIdBySceneId } from './AskBI/datasets/dao'

declare module 'express-session' {
  interface SessionData {
    username?: string // 登录成功后，会在 session 中添加 username 属性
    extra?: {
      tianhongToken?: string
    }
  }
}

const app = express.Router()

app.use((req, _, next) => {
  // eslint-disable-next-line @typescript-eslint/no-extra-semi, no-extra-semi
  ;(axios as any).server = { req }
  next()
})

app.use((req, res, next) => {
  if (req.protocol === 'http') {
    next()
    return
  }
  const oldSetHeader = res.setHeader.bind(res)
  function formatValue(val: string) {
    // 添加 Secure 属性
    if (!val.includes('Secure')) val += '; Secure'
    if (!val.includes('SameSite')) val += '; SameSite=None'
    return val
  }
  const newSetHeader: typeof oldSetHeader = function (key, val) {
    if (key === 'Set-Cookie') {
      if (typeof val === 'string') {
        val = formatValue(val)
      } else if (Array.isArray(val)) {
        val = val.map((item) => formatValue(item))
      }
    }
    return oldSetHeader(key, val)
  }
  res.setHeader = newSetHeader
  next()
})

app.use(bodyParser.json({ limit: '512mb' }))
app.use(passport.initialize())
app.use(express.urlencoded({ extended: true }))
app.use(cors())
app.use(cookieParser())
app.use(createStaticCompression())

// 配置 express-session
app.use(
  session({
    secret: 'askbi-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: {
      httpOnly: true,
      maxAge: 3 * 24 * 60 * 60 * 1000, // 默认过期时间 3天
    },
    // cookie: { secure: true }, // TODO: 生产环境应该使用 HTTPS
  }),
)

app.use(passportMiddleware)

// 限流
const limitApi: string[] = ['/api/chats/chat', '/api/chats/chatMultiScenes', '/api/chats/insight-streaming']
for (let i = 0; i < limitApi.length; i++) {
  console.info('add rate limiter for api' + limitApi[i])
  app.use(limitApi[i], globalLimiter)
  app.use(limitApi[i], level1Limiter)
  app.use(limitApi[i], level2Limiter)
  app.use(limitApi[i], level3Limiter)
}

type ResponseWithBody = express.Response & { responseBody?: APIResponse<any> }

// 记录请求信息的中间件
app.use(async (req, res: ResponseWithBody, next) => {
  try {
    const startAt = Date.now()
    const username = req.user?.username || ''
    const traceId = req.header('traceId') as string
    const userIp = req.header('X-Real-IP') || req.socket.remoteAddress
    const host = req.header('Host') ?? ''
    // FIXME: 有风险，因为 node.js 是单线程的，如果同时有多个请求，会覆盖 username
    username && setAxiosUserId(username)
    traceId && setAxiosDefaults(traceId)
    const originalJson = res.json
    res.json = function (body) {
      res.responseBody = body
      return originalJson.call(this, body)
    }
    // 在请求完成后记录日志
    res.on('finish', async () => {
      // 不要记录 img、css、js 等静态资源的请求
      if (
        !req.url.startsWith('/assets') &&
        !req.url.startsWith('/img') &&
        !req.url.startsWith(askBIApiUrlsWithoutBaseUrl.login.login) &&
        !req.url.startsWith(askBIApiUrlsWithoutBaseUrl.login.logout)
      ) {
        // fix TypeError: Cannot read properties of undefined (reading 'substring')
        const responseBody =
          res && res.responseBody != null && typeof res.responseBody === 'object'
            ? JSON.stringify(res.responseBody).substring(0, 2000)
            : ''
        const responseCode = res.responseBody?.code != null ? res.responseBody.code : res.statusCode
        saveAskBotBusinessLogs({
          serviceType: 'web_service',
          moduleType: 'express_app_middleware',
          host,
          username,
          traceId,
          startTime: startAt,
          input: req.body,
          output: responseBody,
          resultCode: responseCode,
          semanticProjectId: req.body?.sceneId
            ? await getProjectIdBySceneId(req.body.sceneId)
            : SEMANTIC_PROJECT_ID_PLACEHOLDER,
          semanticSceneId: req.body?.sceneId ? req.body.sceneId : SEMANTIC_SCENE_ID_PLACEHOLDER,
          debug: {
            url: req.originalUrl,
            headers: req.header,
            userIp: userIp,
          },
          req,
        })
      }
    })
    next()
  } catch (error) {
    console.error('记录日志出错', error)
    next()
  }
})

app.use('/api/datasources', datasources)
app.use('/api/charts', charts)
app.use('/api/chats', chats)
app.use('/api/convers', convers)
app.use('/api/conver-chats', converChats)
app.use('/api/dashboards', dashboards)
app.use('/api/datasets', datasets)
app.use('/api/llms', llms)
app.use('/api/users', users)
app.use('/api/roles', roles)
app.use('/api/metrics', metrics)
app.use('/api/dimensions', dimensions)
app.use('/api/external-reports', externalReports)
app.use('/api/model', model)
app.use('/api/system-info', systemInfo)
app.use('/api/askdoc/docs', docs)
app.use('/api/sso', login)
app.use('/api/ditest', rangerLogin)
app.use('/api/permission/datasource', permissionDatasource)
app.use('/api/permission/llm', permissionLlm)
app.use('/api/permission/project', permissionProject)
app.use('/api/roles-permission/page-list', rolesPermissionPageList)
app.use('/api/roles-permission/datasource', rolesPermissionDatasource)
app.use('/api/roles-permission/llm', rolesPermissionLlm)
app.use('/api/roles-permission/project', rolesPermissionProject)
app.use('/api/semantic-model', semanticModel)
app.use('/api/semantic-metric-tree', semanticMetricTree)
app.use('/api/semantic-project', semanticProject)
app.use('/api/execute-sql', executeSql)
app.use('/api/engine', xengine)
app.use('/api/operation', operationProject)
app.use('/api/operation', xengine)
app.use('/api/ai', ai)
app.use('/api/env', envList)
app.use('/api/report-generate', reportGenerate)
app.use('/api/ppt-generate', pptGenerate)
app.use('/api/', baowuApi)
app.use('/api/', iFlyProxy)
app.use('/api/elk', elk)
app.use('/api/auth', authRouter)
app.use('/api/alipaySdk', alipaySdk)

app.use('/', express.static(path.join(__dirname, '../../dist-client/')))
app.use(bodyParser.text({ type: 'text/event-stream' }))

app.get('/*', (_req, res) => {
  res.sendFile(path.join(__dirname, '../../dist-client', 'index.html'))
})

export default app
