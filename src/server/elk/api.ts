import axios from 'axios'
import express, { Router, Request, Response } from 'express'
import { ElkResponse } from 'src/shared/common-types'
import { PROCESS_ENV } from '../server-constants'

const router: Router = express.Router()

/**
 * 根据traceId从ELK系统中搜寻日志
 * @param traceId
 * @returns {Array<{ _id: string; _source: ElkResponse }>}
 */
async function fetchLogsByTraceId(traceId: string): Promise<Array<{ _id: string; _source: ElkResponse }>> {
  if (!traceId || PROCESS_ENV.ELK_LOG_URL.length === 0) {
    return []
  }
  return await axios
    .post(
      PROCESS_ENV.ELK_LOG_URL + '/_search',
      {
        size: PROCESS_ENV.ELK_LOG_SIZE,
        query: { bool: { must: [{ match: { request_id: traceId } }] } },
        _source: [
          'timestamp',
          'user_id',
          'request_id',
          'host',
          'service_type',
          'duration',
          'result_code',
          'module_type',
          'input',
          'output',
          'debug',
          'url',
          'cluster_id',
        ],
      },
      { timeout: 20 * 1000 },
    )
    .then((result) => {
      return result.data.hits.hits
    })
}

router.get('/log/:traceId', async (req: Request, res: Response) => {
  const traceId = req.params.traceId
  try {
    const logs = await fetchLogsByTraceId(traceId)
    return res.json({ code: 0, data: logs })
  } catch (error: any) {
    console.error('Error in /log/:traceId route:', error)
    return res.status(500).json({ code: 500, error: error.message, msg: '获取ELK日志失败' })
  }
})

export default router
