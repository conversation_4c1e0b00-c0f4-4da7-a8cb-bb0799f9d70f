import { NextFunction, Request, Response } from 'express'
// import axios from 'axios'
// import { askBIApiUrls, askBIPageUrls, loginWhitelist } from 'src/shared/url-map'
// import { getQueryState } from 'src/shared/common-utils'
// import memoryCache from '../dao/memory-cache'
import { prisma } from '../dao/prisma-init'
import { PROCESS_ENV } from '../server-constants'
// const COOKIE_NAME = 'set-cookie'

export const SESSION_ID = 'JSESSIONID'
export const JWT_ID = 'jwt_token'
export const COOKIE_MAX_AGE = 12 * 60 * 60 * 1000

const rangerEnable = PROCESS_ENV.RANGER_LOGIN_ENABLE

// const LLM_SERVICE_NAME = 'askbi_llm'
// const DATASOURCE_SERVICE_NAME = 'askbi_datasource'
// const PROJECT_SERVICE_NAME = 'askbi_project'

/** 检查是否登录 */
// export async function verifyLogin(req: Request, res: Response, next: NextFunction) {
//   // 忽略 /assets（js和css） 和 /img（图片）开头、白名单内的 URL
//   const queryState = getQueryState(false, req.query as Record<string, string>)
//   if (
//     req.url.startsWith('/assets') ||
//     req.url.startsWith('/img') ||
//     loginWhitelist.some((whitelistedUrl) => req.url.startsWith(whitelistedUrl)) ||
//     req.url.startsWith(askBIApiUrls.diProxyLogin.namespace) ||
//     req.url.startsWith(askBIApiUrls.diProxyLogin.service) ||
//     req.url.startsWith(askBIApiUrls.diProxyLogin.pod) ||
//     req.url.startsWith(askBIApiUrls.diProxyLogin.diDefaultProxy) ||
//     queryState.enableAutoLogin
//   ) {
//     return next()
//   }

//   // 如果没有 username 表示没有登录过
//   if (!req.cookies.u_info && !req.cookies.JSESSIONID) {
//     // 如果是 /api 的请求，返回  401
//     if (req.url.startsWith('/api')) {
//       // 如果验证失败，直接return 401结束
//       const domain = getSecondDomainOrIP(req.hostname)
//       clearAuthCookies(req, res, domain)
//       return res.json({ code: 401, data: {}, msg: '登录已过期，请重新登录' })
//     } else {
//       // 其他的情况为页面请求，重定向到登录页
//       return res.redirect(askBIPageUrls.login)
//     }
//   } else {
//     const { username: sessionUsername } = req.session
//     const { u_info: cookieUsername, JSESSIONID: jsessionid } = req.cookies

//     // 本地运行server重启的时候 session和memoryCache的数据会清空，所以需要在赋值一遍
//     if (!sessionUsername) {
//       req.user?.username = cookieUsername
//       memoryCache.set(String(jsessionid), cookieUsername, COOKIE_MAX_AGE)
//     }

//     const cachedUsername = memoryCache.get(String(cookieUsername))
//     if (cachedUsername) {
//       return next()
//     }

//     try {
//       const checkResult = await axios.get(`${checkToken}/${jsessionid}`)
//       if (checkResult.data.data.userName) {
//         memoryCache.set(String(cookieUsername), jsessionid, COOKIE_MAX_AGE)
//         return next()
//       } else {
//         return res.json({ code: 401, data: {}, msg: '登录态校验失败' })
//       }
//     } catch (error: any) {
//       const domain = getSecondDomainOrIP(req.hostname)
//       clearAuthCookies(req, res, domain)
//       console.error('Error verifying session:', error.message)
//       return res.json({ code: 401, data: {}, msg: '服务器内部错误，校验token失败' })
//     }
//   }
// }

/** 检查是否登录，且必须为管理员 */
export async function verifyAdminLogin(req: Request, res: Response, next: NextFunction) {
  try {
    if (rangerEnable) {
      //TODO(zhaoyang) Check if the user's role is administrator.
      next()
    } else {
      const { username } = req.session

      if (!username) {
        return res.json({ code: 401, data: {}, msg: '未检测到用户名，请重新登录' })
      }

      // 关联查询用户和角色
      const userInfo = await prisma.user.findUnique({
        where: {
          username,
        },
        include: {
          userRoles: {
            where: {
              roleId: 'admin',
            },
          },
        },
      })

      if (!userInfo || !userInfo.userRoles.length) {
        return res.json({ code: 403, data: {}, msg: '非管理员禁止访问，请联系管理员开通权限' })
      }

      next()
    }
  } catch (error) {
    console.error('verifyAdminLogin-error', error)
    return res.json({ code: 500, data: {}, msg: '服务器错误，请联系管理员' })
  }
}

// /**
//  * If ranger login enabled login with ranger.
//  * @param username the username.
//  * @param password the password
//  * @param res http response.
//  */
// export async function loginWithRanger(
//   username: string,
//   password: string,
//   isEncryption: boolean,
//   req: Request,
//   res: Response,
// ) {
//   try {
//     const userInfo = await loginGetToken(username, password)
//     console.info('Login with ranger', userInfo)
//     const { token, jwtToken } = userInfo
//     //Set cookie in memory cache.
//     req.user?.username = username
//     const domain = getSecondDomainOrIP(req.body.hostname)
//     const expiresTime = new Date().getTime() + COOKIE_MAX_AGE
//     const cookieOptions: CookieOptions = {
//       domain: domain,
//       expires: new Date(expiresTime), // Cookie will expires 12 hours,
//       path: '/',
//     }
//     // isEncryption当有这个标识为true的时候，表示是免登，需要设置cookie，不影响其他业务逻辑
//     if (req.protocol === 'https' || isEncryption) {
//       cookieOptions.sameSite = 'none'
//       cookieOptions.secure = true
//     }

//     memoryCache.set(String(token), username, COOKIE_MAX_AGE)
//     memoryCache.set(String(jwtToken), username, COOKIE_MAX_AGE)
//     res.cookie('u_token', `${token}|${expiresTime}`, Object.assign({ httpOnly: true }, cookieOptions))
//     res.cookie('u_info', username, cookieOptions)
//     res.cookie(SESSION_ID, token, Object.assign({ httpOnly: true }, cookieOptions))
//     res.cookie(JWT_ID, jwtToken, Object.assign({ httpOnly: true }, cookieOptions))
//     return res.json({
//       code: 0,
//       data: { ...userInfo, lastLoginAt: new Date() },
//       msg: 'Login success.',
//     })
//   } catch (error) {
//     return res.json({
//       code: 500,
//       msg: (error as Error)?.message,
//     })
//   }
// }

// export async function authLogOut(token: string) {
//   console.info('Logout token=' + token)
//   try {
//     await axios.get(logoutUrl, { headers: { Cookie: 'JSESSIONID=' + token } })
//   } catch (error: any) {
//     console.error('Logout error', error.message)
//   }
// }

// export async function getLLMList(user: string) {
//   return await getAccessResource(LLM_SERVICE_NAME, user)
// }

// 验证域名
const isValidDomain = (domain: string) => {
  const domainRegex = /^(?!-)[A-Za-z0-9-]{1,63}(?<!-)\.(?!-)(?:[A-Za-z0-9-]{1,63}\.)*[A-Za-z]{2,6}$/
  return domainRegex.test(domain)
}

/**
 * 获取二级域名或者IP地址
 * @param {String} host IP|HostName
 * @returns secondHost| IP
 */
export const getSecondDomainOrIP = (host: string) => {
  // 特殊处理 localhost 和 0.0.0.0
  if (host === 'localhost' || host === '0.0.0.0') {
    return 'localhost'
  }

  const isIP = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(host)
  if (isIP) {
    return host
  }

  // 检查是否是有效的域名
  if (isValidDomain(host)) {
    const domainArr = host.split('.')
    if (domainArr.length > 2) {
      return domainArr.slice(1).join('.')
    }
    return host
  }
  // 如果都不是，返回一个默认值或者抛出错误
  throw new Error('无效的hostname')
}

// export async function getProjectList(user: string) {
//   const allProjectList = await getAllResourceListByAdmin(String(user), 'Project')
//   const projectIds = allProjectList.map((item) => item.semanticProjectId)
//   const projectList = await prisma.semanticProject.findMany({
//     where: {
//       id: {
//         in: projectIds,
//       },
//     },
//     select: {
//       id: true,
//       name: true,
//     },
//   })

//   const userProjectList = await getAccessResource(PROJECT_SERVICE_NAME, user)
//   const filed = projectList.filter((item) => userProjectList.includes(item.name)).map((item) => item.id)
//   return allProjectList.filter((item) => filed.includes(item.id))
// }

// export async function getDatasourceList(user: string) {
//   const allDatasourceList = await getAllResourceListByAdmin(user, 'Datasource')
//   const datasourceIds = allDatasourceList.map((item) => item.id)
//   const datasourceList = prisma.datasource.findMany({
//     where: {
//       id: {
//         in: datasourceIds,
//       },
//     },
//     select: {
//       id: true,
//       name: true,
//     },
//   })
//   const userDatasourceList = await getAccessResource(DATASOURCE_SERVICE_NAME, user)
//   const fileted = (await datasourceList).filter((item) => userDatasourceList.includes(item.name)).map((item) => item.id)
//   return allDatasourceList.filter((item) => fileted.includes(item.datasourceId))
// }

// export async function registerRangerUser(thUserId: string) {
//   const addUserData = {
//     password: thUserId,
//     username: thUserId,
//   }
//   const response = await axios.post(registerUserUrl, addUserData, {
//     headers: {
//       'Content-Type': 'application/json',
//     },
//   })
//   const httpStatus = response.status
//   if (httpStatus !== 200) {
//     console.error(`Request ranger register error user = ${thUserId}.`)
//     throw new Error(`Request ranger login failed, status = ${httpStatus} user = ${thUserId}.`)
//   }
//   return await loginGetToken(thUserId, thUserId)
// }

// async function loginGetToken(username: string, password: string) {
//   const buffer = Buffer.from(password)
//   const base64Encoded = buffer.toString('base64')
//   const jsonData = {
//     userName: username,
//     password: base64Encoded,
//   }
//   // Call ranger user login.
//   return await axios
//     .post(rangerLoginUrl, jsonData)
//     .then((response) => {
//       const res = response.data
//       if (res.code === 0) {
//         return res.data
//       } else {
//         console.error('Login with range does not have cookie.')
//         return null
//       }
//     })
//     .catch((error) => {
//       if (error.response) {
//         if (error.response.code === 273) {
//           throw new Error('账号或密码错误')
//         }
//         throw new Error(error.response.data.msgDesc)
//       } else {
//         throw new Error('Server error.')
//       }
//     })
// }

// export async function getAccessResource(serviceName: string, user: string) {
//   try {
//     const urlFormatted = getAccessResourceUrl + user + '/' + serviceName
//     console.info(chalk.yellow('GET Access Resource URL: ', urlFormatted))
//     const response = await axios.get(urlFormatted)
//     const allowedResources: string[] = []
//     const pageAccessResult = response.data.data
//     if (pageAccessResult == null || pageAccessResult.length === 0) {
//       return allowedResources
//     }
//     pageAccessResult.forEach((item: { allowed: boolean; path: string }) => {
//       if (item.allowed && item.path !== '/') {
//         allowedResources.push(item.path.split('/')[1])
//       }
//     })
//     return allowedResources
//   } catch (error: any) {
//     console.error('Get access resource error:', error.message, error.response?.data)
//     throw Error('获取权限列表失败:' + error.message)
//   }
// }

// 封装清除Cookies的函数
export async function clearAuthCookies(_: Request, res: Response, domain: string) {
  try {
    // const jsessionid = req.cookies.JSESSIONID
    // req.user?.username = undefined
    const cookieOptions = {
      domain: domain,
      path: '/',
      httpOnly: true,
    }

    res.clearCookie('u_token', cookieOptions)
    res.clearCookie('u_info', cookieOptions)
    res.clearCookie('bw_org_code', cookieOptions)
    res.clearCookie(SESSION_ID, cookieOptions)
    res.clearCookie(JWT_ID, cookieOptions)
    // jsessionid && (await authLogOut(jsessionid))
  } catch (error: any) {
    console.error('clearAuthCookies - error', error.message)
  }
}
