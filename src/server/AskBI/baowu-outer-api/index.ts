import dayjs from 'dayjs'
import express, { Router, Request, Response } from 'express'
import { getBaowuSubCompanies } from 'src/server/custom/baowu/baowu-sub-company'
import { processBaoWuCompanyFilter } from 'src/server/custom/baowu/baowu-utils'
import { prisma } from 'src/server/dao/db'
import { executeAllXengineSql } from 'src/server/dao/xengine-query'
import { decryptSignature } from 'src/server/login/api'
import { allDimensions } from './mock'

const router: Router = express.Router()

// 查询某公司某指标对应时间的数据, 只提供给宝武现场使用, 其他环境无法使用
router.get('/datasets/baowu/dataset-list', async (req: Request, res: Response) => {
  try {
    console.info('/baowu/dataset-list--req=====----->>>>>', req.query)
    const startDate = req.query.startDate as string
    const endDate = req.query.endDate as string
    const companyCodeDes = req.query.companyCodeDes as string
    const reportItem = req.query.reportItem as string
    if (!startDate || !endDate || !companyCodeDes || !reportItem) {
      return res.json({ code: 400, data: [], msg: 'startDate,endDate,companyCodeDes及reportItem必传' })
    }
    const reportItems = reportItem
      .split(/,/)
      .map((item) => `'${item}'`)
      .join(',')
    const companyCodeDesGroup = companyCodeDes
      .split(/,/)
      .map((item) => `'${item}'`)
      .join(',')
    // 表路径写死为宝武现场的db2路径, 其他环境不可用
    const tableNamePrefix = 'db2_baowu.DBPROD13'
    // const tableNamePrefix = 'dipeak.baowu_test'
    const sql = `SELECT * FROM ${tableNamePrefix}.T_ADS_FACT_WSSJ_TOTAL_INDEX 
        WHERE
         REPORT_ITEM in (${reportItems}) 
         AND COMPANY_INNER_CODE_DES in (${companyCodeDesGroup}) 
         AND ACCT_PERIOD_NO >= '${startDate}' 
         AND ACCT_PERIOD_NO <= '${endDate}' 
         ORDER BY ACCT_PERIOD_NO DESC
      `
    const result = (await executeAllXengineSql(sql)) || []
    console.info('/baowu/dataset-list--->>>result', result)
    res.json({ code: 0, data: result })
  } catch (error) {
    console.info('Get metric baowu dataset list with error ', error)
    return res.json({ code: 500, data: [], msg: '获取数据失败' })
  }
})

// 查询推荐问题 只提供给宝武现场使用, 其他环境无法使用
router.get('/metrics/baowu/recommend-question', async (req: Request, res: Response) => {
  try {
    console.info('/baowu/recommend-question--req----->>>>>', req.query)

    // 表路径写死为宝武现场的db2路径, 其他环境不可用
    const tableNamePrefix = 'db2_baowu.DBPROD13'
    const sql = `SELECT * FROM ${tableNamePrefix}.T_BWWS_RECOMMENDED_QUESTION_LIST`
    const result = (await executeAllXengineSql(sql)) || []
    console.info('/baowu/recommend-question--->>>result', result)
    res.json({ code: 0, data: result })
  } catch (error) {
    console.info('Get metric baowu recommend question with error ', error)
    return res.json({ code: 500, data: [], msg: '获取指标推荐问题失败' })
  }
})

// 查询指定id的用户是否存在, 只提供给宝武现场使用, 其他环境无法使用
router.get('/auth/baowu/check-user-auth', async (req: Request, res: Response) => {
  try {
    console.info('/auth/baowu/check-user-auth--req----->>>>>', req.query)
    const userId = req.query.userId as string
    // 表路径写死为宝武现场的db2路径, 其他环境不可用
    const tableNamePrefix = 'db2_baowu.DBPROD13'
    const sql = `SELECT * FROM ${tableNamePrefix}.T_ADS_WH_WSSJ_USER_PERMISSION WHERE USER_ID = '${userId}' `
    const result = (await executeAllXengineSql(sql)) || []
    console.info('/auth/baowu/check-user-auth--->>>result', result)
    res.json({ code: 0, data: result?.length > 0 })
  } catch (error) {
    console.info('Get metric baowu check user auth with error ', error)
    return res.json({ code: 500, data: false, msg: '获取角色权限失败' })
  }
})

// 提供给宝武, app登录后存登录时间及用户
// 写数据的接口, 做一层校验
router.post('/auth/baowu/login-log', async (req: Request, res: Response) => {
  try {
    console.info('/auth/baowu/login-log-req----->>>>>', req.body)
    const signature = req.body?.signature as string
    const loginAt = req.body?.loginAt as string
    const action = req.body?.action as string

    if (!signature) {
      throw new Error('参数格式错误')
    }

    const appSecret = 'jG7rPfbbPZEK-4ucC7'
    // 解密 signature
    const userData = decryptSignature(appSecret, signature)
    if (typeof userData === 'string') {
      console.info(`解密失败，失败信息为 ${userData}`)
      throw new Error('解密失败')
    }

    const { userId, username, timestamp } = userData
    if (!userId || !username || !timestamp) {
      throw new Error('解密后的数据格式错误')
    }
    // 默认使用传入时间, 无传入时间,则使用服务器当前时间
    const defaultLoginAt = dayjs().format('YYYY-MM-DD HH:mm:ss')
    const defaultAction = 'login'

    prisma.loginLog
      .create({
        data: { userId, username, loginAt: loginAt || defaultLoginAt, action: action || defaultAction },
      })
      .then(() => {
        res.json({ code: 0, data: true })
      })
      .catch(() => {
        res.json({ code: 500, data: false, message: '保存登录记录失败' })
      })
  } catch (error: any) {
    return res.json({ code: 500, data: false, msg: error?.message || '保存登录记录失败' })
  }
})

router.get('/baowu/test-auth', async (req: Request, res: Response) => {
  try {
    const { username, userId, where } = req.query as any
    console.info('username-userId-orgCode-where=====>>>', username, userId, where)

    const authData = {
      username: username || 'YG4107',
      userId: userId || 'YG4107',
    }
    const response = {
      data: {
        type: 'query-metric',
        query_metric: {
          groupBys: [],
          metricNames: ['SUMI6602130'],
          isMetricNamesExactMatch: true,
          orderBys: [],
          where: where || '',
          where_json: {
            sub_where_link: 'AND',
            metric_sub_wheres: [],

            dimension_sub_wheres: [
              {
                dimension_name: 'COMPANY_INNER_CODE_DES',
                operator: 'positive',
                dimension_values: [
                  {
                    dimension_value_name: '马钢(集团)控股有限公司-管理合并',
                    score: 1,
                  },
                ],
              },
            ],
          },
          isWhereExactMatch: true,
          limit: null,
          timeQueryParams: {
            timeStartFunction: {
              type: 'specificDate',
              year: 2024,
              month: 3,
              day: 1,
              quarter: null,
            },
            timeEndFunction: {
              type: 'specificDate',
              year: 2024,
              month: 3,
              day: 31,
              quarter: null,
            },
            timeGranularity: 'total',
            timeDimensionName: null,
          },
          notExistMetricNames: null,
          notExistGroupBys: null,
          notExistOrderBys: null,
        },
        query_metric_list: [],
        attribution_analysis: null,
        chit_chat: null,
        meta_result: null,
        extra_info: {
          is_sub: true,
          intent_tags: ['与召回相关', '问指标'],
          metric_scores: {
            SUMI6602130: 1,
          },
        },
      },
      code: 0,
    }
    const metricConfig: any = {
      name: 'dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX',
    }
    const baowuCompanyFilter = await processBaoWuCompanyFilter({
      authData,
      extractedResponse: response,
      metricConfig,
    })
    console.info('baowu-auth-test-baowuCompanyFilter', baowuCompanyFilter)
    res.json({ code: 0, data: baowuCompanyFilter })
  } catch (error) {
    console.error('baowu-auth-test-error', error)
  }
})

router.get('/baowu/test-subCompany', async (req: Request, res: Response) => {
  try {
    const { username, userId, where } = req.query as any
    console.info('username-userId-orgCode-where=====>>>', username, userId, where)

    const metricConfig: any = {
      name: 'dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX',
      allDimensions,
    }
    const subCompanies = await getBaowuSubCompanies({
      metricConfig,
      parentCompanies: ['上海宝信软件股份有限公司', '宝武碳业科技股份有限公司'],
      queryLevel: 1,
    })
    console.info('baowu-test-subCompany-subCompanies', subCompanies)
    res.json({ code: 0, data: subCompanies })
  } catch (error) {
    console.error('baowu-auth-test-error', error)
  }
})

export default router
