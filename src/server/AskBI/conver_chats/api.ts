import express, { Router, Request, Response } from 'express'
import { prisma } from 'src/server/dao/prisma-init'
import { ConverChatErrorTypesMap } from 'src/shared/common-types'
import { validateUserConversationOwnership } from '../convers/dao'
import { getChatConverErrorType, updateChatLlmResponse } from './utils'

const router: Router = express.Router()

// 获取历史会话
router.get('/history-list', async (req: Request, res: Response) => {
  console.info('get /history-list list with query: ', req.query)
  const errorType = req.query?.errorType?.toString() as keyof typeof ConverChatErrorTypesMap
  const content = req.query?.content?.toString()
  const page = Number(req.query?.page || 0)
  const pageSize = Number(req.query?.pageSize || 0)
  const projectId = req.query.projectId?.toString()
  const isProjectChosen = req.query.isProjectChosen?.toString() as 'true' | 'false' | undefined
  const sceneId = req.query.sceneId?.toString()
  const startDate = req.query?.startDate ? Number(req.query?.startDate) : null
  const endDate = req.query?.endDate ? Number(req.query?.endDate) : null
  const filter: NonNullable<Parameters<typeof prisma.converChat.findMany>[0]>['where'] = {
    conver: {
      semanticProjectId: projectId,
    },
    ask: {
      contains: content,
    },
  }
  if (isProjectChosen !== 'true' && filter.conver) {
    filter.conver.semanticSceneId = sceneId
  }
  if (errorType) {
    // errorType由于新增了自定义内容,并且内容与原来的不太一样,所以要加模糊匹配
    filter.OR = [
      {
        errorType: {
          contains: errorType,
        },
      },
      {
        errorType: {
          startsWith: ConverChatErrorTypesMap[errorType],
        },
      },
    ]
  }
  if (endDate || startDate) {
    filter.createdAt = {
      lte: endDate ? new Date(endDate) : new Date(),
      gte: startDate ? new Date(startDate) : new Date(),
    }
  }

  const findManyParameters: Parameters<typeof prisma.converChat.findMany>[0] = {
    where: filter,
    include: {
      conver: {
        select: {
          username: true,
          semanticProjectId: true,
          semanticSceneId: true,
        },
      },
    },
  }

  const findIdsParameters: Parameters<typeof prisma.converChat.findMany>[0] = {
    select: { id: true },
    where: filter,
    orderBy: { createdAt: 'desc' },
  }

  if (page && pageSize) {
    findIdsParameters.skip = (page - 1) * pageSize
    findIdsParameters.take = pageSize
  }

  try {
    const idsResult = await prisma.converChat.findMany(findIdsParameters)
    const ids = idsResult.map((i) => i.id)

    let converChatsList = await prisma.converChat.findMany({
      ...findManyParameters,
      where: {
        id: { in: ids },
        ...findManyParameters.where,
      },
    })

    converChatsList = converChatsList.sort((a, b) => {
      return a['createdAt'] < b['createdAt'] ? 1 : -1
    })

    const total = await prisma.converChat.count({
      where: filter,
    })
    return res.json({ code: 0, data: { list: converChatsList, total }, msg: '' })
  } catch (error) {
    console.info('/history-list list with query data: error', error)
    return res.json({ code: 500, error, msg: '获取提问历史列表失败' })
  }
})

// 数据错误反馈接口
router.post('/data-feedback', async (req: Request, res: Response) => {
  console.info('post /cover/data-feedback list with params: ', req.body)
  const username = req.user?.username || ''
  const chatId = req.body?.chatId as string
  const conversationId = req.body?.conversationId as string
  const errorType = req.body?.errorType as string
  const actualErrorType = getChatConverErrorType(errorType)
  // 校验是否有查看该conversation的权限 只能查看自己的conversation
  const verifyResult = await validateUserConversationOwnership(username, conversationId)
  if (!verifyResult) {
    return res.json({ code: 403, data: [], msg: '暂无查看当前会话的权限！' })
  }
  try {
    await prisma.converChat.update({
      data: { errorType: actualErrorType },
      where: {
        id: chatId,
      },
    })
    return res.json({ code: 0, data: null, msg: '' })
  } catch (error) {
    console.info('error', error)
    return res.json({ code: 500, data: null, msg: '更新会话数据失败' })
  }
})

// 获取一个conversation下所有的chat
router.get('/:conversationId', async (req: Request, res: Response) => {
  console.info('GET /conver-chat list with params: ', req.params)
  const username = req.user?.username || ''
  const conversationId = req.params.conversationId as string
  // 校验是否有查看该conversation的权限 只能查看自己的conversation
  const verifyResult = await validateUserConversationOwnership(username, conversationId)
  if (!verifyResult) {
    return res.json({ code: 403, data: [], msg: '暂无查看当前会话的权限！' })
  }
  const converChatsList = await prisma.converChat.findMany({
    where: {
      converId: conversationId,
    },
    orderBy: { createdAt: 'asc' },
  })
  return res.json({ code: 0, data: converChatsList, msg: '' })
})

router.post('/update-chat', async (req: Request, res: Response) => {
  console.info('POST /update-chat with body: ', req.body)
  const { chatId, metricNames, where, sceneId } = req.body
  await updateChatLlmResponse(chatId, sceneId, {}, { metricNames, where })
  return res.json({ code: 0, data: [], msg: '' })
})

/**
 * @description 更新Doc问答的结果
 */
router.post('/update-doc-chat', async (req: Request, res: Response) => {
  const { chatId, docResult } = req.body

  try {
    const currentChat = await prisma.converChat.findUnique({
      where: { id: chatId },
    })
    if (!currentChat) {
      return res.json({ code: 400, data: {}, msg: 'Chat not found' })
    }
    // 更新 Chat
    await prisma.converChat.update({
      data: {
        docResponse: [docResult],
        updatedAt: new Date(),
      },
      where: { id: chatId },
    })
    return res.json({ code: 0, msg: 'Conversation updated successfully' })
  } catch (error: any) {
    return res.json({ code: 500, data: {}, msg: 'Error updating or creating conversation: ' + error.message })
  }
})
export default router
