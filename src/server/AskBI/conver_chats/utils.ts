import { prisma } from 'src/server/dao/prisma-init'
import { getConverChatErrorTypeByResErrType } from 'src/server/utils'
import { ChatResponse, ConverChatErrorTypes, ConverChatErrorTypesMap } from 'src/shared/common-types'

export const getChatConverErrorType = (errorTypeCode?: string) => {
  if (!errorTypeCode) {
    return ConverChatErrorTypes.DATA_ERROR_FEEDBACK
  }
  return ConverChatErrorTypesMap.DATA_ERROR_FEEDBACK + '-' + errorTypeCode
}

/**
 * 获取追问的参数
 * @param conversationId
 * @param chatId
 * @param enableFollowUpQuestion
 * @returns {Promise<any | null>}
 */
export async function getFollowUpQuestionParams(
  conversationId: string,
  chatId: string,
  enableFollowUpQuestion: boolean,
): Promise<any | null> {
  if (!enableFollowUpQuestion) return null

  try {
    const latestChat = await prisma.converChat.findFirst({
      where: {
        converId: conversationId,
        id: { not: chatId },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })
    const oldLlmResponse = (latestChat?.llmResponse as any[]) || []
    if (oldLlmResponse.length > 0) {
      const selectedIndex = oldLlmResponse.findIndex((item) => item.current_select_scene === true)
      const index = selectedIndex !== -1 ? selectedIndex : 0
      const selectedResponse = oldLlmResponse[index]

      if (selectedResponse?.type === 'query-metric') {
        return selectedResponse
      }
    }
  } catch (error) {
    console.error('Error retrieving followUpQuestionParams from chat data:', error)
  }
  return null
}

/**
 * 更新某一个Chat llmResponse 用于追问 [单/多场景通用]
 * @param chatId
 * @param sceneId
 * @param response
 * @param config
 */
export async function updateChatLlmResponse(
  chatId: string,
  sceneId: string,
  response: any,
  config?: {
    metricNames: string[]
    where: string
  },
): Promise<void> {
  const { metricNames, where } = config || {}
  try {
    const currentChat = await prisma.converChat.findUnique({
      where: { id: chatId },
    })

    const oldLlmResponse = (currentChat?.llmResponse || []) as any[]

    const updatedLlmResponse = [...oldLlmResponse]
    const existingIndex = updatedLlmResponse.findIndex((item) => item.sceneId === sceneId)

    if (existingIndex !== -1) {
      // 更新metricNames where
      if (metricNames && updatedLlmResponse[existingIndex].type === 'query-metric') {
        updatedLlmResponse[existingIndex] = {
          ...updatedLlmResponse[existingIndex],
          current_select_scene: true,
          query_metric: {
            ...updatedLlmResponse[existingIndex].query_metric,
            metricNames,
            where,
          },
        }
      }
    } else {
      updatedLlmResponse.push({ ...response, sceneId })
    }

    await prisma.converChat.update({
      data: {
        llmResponse: updatedLlmResponse,
        updatedAt: new Date(),
      },
      where: { id: chatId },
    })
    console.info(`Update chat llmResponse succeed. chatId: ${chatId}, sceneId: ${sceneId}`)
  } catch (error) {
    console.error('Update chat llmResponse failed.', error)
  }
}

/**
 * 更新chat的response 单/多场景 通用
 * @param chatId {string}
 * @param responseArray {ChatResponse[]}
 */
export async function updateConverChatResponse(
  chatId: string,
  traceId: string,
  responseArray: ChatResponse[],
): Promise<void> {
  try {
    const chat = await prisma.converChat.findUnique({
      where: { id: chatId },
    })

    if (chat) {
      // 默认取第1个场景的errorType
      let errorType = ''
      const response = responseArray[0]
      if (response && response.ready === false) {
        errorType = getConverChatErrorTypeByResErrType(response.errType)
      }
      await prisma.converChat.update({
        data: {
          response: responseArray,
          updatedAt: new Date(),
          errorType,
          traceId,
        },
        where: { id: chatId },
      })
      console.info('Update converChat successfully.')
    } else {
      console.error('Update converChat failed, Chat not found.')
    }
  } catch (error) {
    console.error('Update converChat failed with error ', error)
  }
}
