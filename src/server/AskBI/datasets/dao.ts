import { enforcer } from 'src/server/auth'
import { semanticToDataset } from 'src/shared/common-utils'
import { prisma } from 'src/server/dao/prisma-init'
import { DatasetDatum } from 'src/shared/common-types'
import { executeAllXengineSql } from 'src/server/dao/xengine-query'
import { ColumnsTitleMapItem } from 'src/shared/metric-types'
import { SEMANTIC_PROJECT_ID_PLACEHOLDER } from 'src/shared/constants'

/**
 * 获取当前用户默认的 dataset
 */
export async function getDefaultDataset(id: string): Promise<DatasetDatum | null> {
  const projects = await enforcer.getProjectsFromUserId(id)
  if (!projects.length || !projects[0].semanticScenes.length) return null
  const project = projects[0]
  const scene = project.semanticScenes[0]
  return semanticToDataset({ project, scene })

  // const permissionWithSemanticProject = await findProjectsFromUserId(username)
  // // if (PROCESS_ENV.RANGER_LOGIN_ENABLE) {
  // //   permissionWithSemanticProject = await findProjectsFromUserId(username)
  // // } else {
  // //   permissionWithSemanticProject = await getPermissionListUniqueArray(username, 'Project')
  // // }

  // const semanticProjectIdsWithPermission = permissionWithSemanticProject.map((item) => item.semanticProjectId)

  // const firstScene = await prisma.semanticScene.findFirst({
  //   where: {
  //     semanticProjectId: {
  //       in: semanticProjectIdsWithPermission,
  //     },
  //   },
  //   include: {
  //     semanticProject: {
  //       select: {
  //         id: true,
  //         name: true,
  //       },
  //     },
  //   },
  //   orderBy: {
  //     updatedAt: 'desc',
  //   },
  // })
  // if (firstScene) {
  //   return {
  //     projectId: firstScene.semanticProject.id,
  //     projectName: firstScene.semanticProject.name,
  //     sceneId: firstScene.id,
  //     tableName: firstScene.tableName,
  //     sceneLabel: firstScene.label,
  //     enableFollowUpQuestion: !!firstScene.enableFollowUpQuestion,
  //     enableMetricExactMatch: !!firstScene.enableMetricExactMatch,
  //     enableTryQueryUp: !!firstScene.enableTryQueryUp,
  //     enableSelectToastWhenEmptyData: !!firstScene.enableSelectToastWhenEmptyData,
  //     enableAccMetricToastWhenEmptyData: !!firstScene.enableAccMetricToastWhenEmptyData,
  //   }
  // } else {
  //   return null
  // }
}

/** 返回一个表的所有字段英文名和字段中文名信息 */
export async function getAllColumnCommentOfTable(tableName: string): Promise<ColumnsTitleMapItem[]> {
  const columnMetaSql = `SHOW FULL COLUMNS FROM ${tableName}`

  try {
    const columnMeta = await executeAllXengineSql(columnMetaSql)
    return columnMeta.map((item) => {
      return {
        field: item.Field,
        title: item.Comment,
      }
    })
  } catch {
    return []
  }
}

export async function getProjectIdBySceneId(sceneId: string): Promise<string> {
  try {
    const sceneData = await prisma.semanticScene.findUnique({
      where: { id: sceneId },
      select: { semanticProjectId: true },
    })
    return sceneData?.semanticProjectId || SEMANTIC_PROJECT_ID_PLACEHOLDER
  } catch (error) {
    console.error('Error fetching project ID by scene ID:', error)
    return SEMANTIC_PROJECT_ID_PLACEHOLDER
  }
}
