import express, { Router, Request, Response } from 'express'
import { APIResponse } from 'src/shared/common-types'
import { executeAllXengineSql } from 'src/server/dao/xengine-query'
import MetricConfig from 'src/server/MetricStore/metric-config'
import { SampleDataResponse } from 'src/shared/metric-types'
import { setAxiosDefaults } from 'src/shared/config'
import { getAllColumnCommentOfTable, getProjectIdBySceneId } from '../datasets/dao'

const router: Router = express.Router()

/** 获取默认的 dataset */
// router.get('/default', async (req: Request, res: Response) => {
//   try {
//     const username = req.user?.username || ''
//     console.info('正在获取默认的Dataset信息，当前用户为', username)
//     const defaultDataset = await getDefaultDataset(username)
//     if (defaultDataset) {
//       return res.json({ code: 0, data: defaultDataset })
//     } else {
//       return res.json({ code: 500, msg: '暂无可用的场景，请先新建场景' })
//     }
//   } catch (error: any) {
//     console.info('获取默认的Dataset信息失败', error)
//     return res.json({ code: 500, msg: error.message })
//   }
// })

router.get('/sample-data/:sceneId', async (req: Request, res: Response<APIResponse<SampleDataResponse>>) => {
  try {
    const username = req.user?.username || ''
    const conversationId = req.query.conversationId as string
    const traceId = req.header('traceId') as string
    const sceneId = req.params.sceneId
    const projectId = await getProjectIdBySceneId(sceneId)
    const logsParams = {
      username,
      traceId,
      host: req.header('Host') || '',
      conversationId,
      sceneId: sceneId,
      projectId,
      req,
    }
    setAxiosDefaults(traceId)
    const metricConfig = await MetricConfig.createBySceneId(sceneId)
    const tableName = metricConfig.name
    const sql = `SELECT * FROM ${tableName} LIMIT 5;`
    const rows = await executeAllXengineSql(sql, logsParams)
    const countSql = `SELECT count(*) as total FROM ${tableName};`
    const totalNumRows = await executeAllXengineSql(countSql)

    const columnMeta = await getAllColumnCommentOfTable(metricConfig.name)
    if (sceneId === 'CX7sQeGbXCPLOb2U') {
      const obj: { [key: string]: string } = {
        HEADQUARTERCODE: '总公司机构代码（一级）',
        HEADQUARTERNAME: '总公司机构名称',
        PROVINCECODE: '分公司机构代码（二级）',
        PROVINCENAME: '分公司机构名称',
        BRANCHCODE: '中支机构代码（三级）',
        MANAGECOMNAME: '管理机构名称',
        CONTCVALIDATE: '保单生效日期',
        RISKTYPE: '险种类别1',
        RISKTYPENAME: '险种类别1名称',
        RISKTYPE2: '险种类别2',
        RISKTYPE2NAME: '险种类别2名称',
        SALECHNLCODE: '销售渠道代码',
        SALECHNLNAME: '销售渠道名称',
        PREM: '规模保费',
        STAND_PREM: '标准保费',
      }
      // 遍历 columnMeta 数组，更新 title 值
      columnMeta.forEach((column) => {
        if (obj[column.field]) {
          column.title = obj[column.field]
        }
      })
    }
    return res.json({
      code: 0,
      data: {
        data: rows,
        columnsTitleList: columnMeta,
        columns: rows.length > 0 ? Object.keys(rows[0]) : [],
        total: totalNumRows[0].total,
        datasourceName: tableName.split('.').slice(0, 2).join('.'),
        tableName: tableName,
      },
      msg: '',
    })
  } catch (error: any) {
    return res.json({ code: 500, msg: error.message })
  }
})

export default router
