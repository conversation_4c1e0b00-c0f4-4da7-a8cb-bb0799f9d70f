/**
 * @description: 对元数据库执行SQL
 */

import express, { Router, Request, Response } from 'express'
import { prisma } from 'src/server/dao/prisma-init'

const router: Router = express.Router()

router.post('/', async (req: Request, res: Response) => {
  try {
    const sql = req.body.sql
    console.info('Execute Raw SQL:', req.body.sql)

    const sqlResult = await prisma.$queryRawUnsafe(sql)
    return res.json({ code: 0, data: sqlResult })
  } catch (error) {
    return res.json({ code: 500, data: {}, msg: '执行SQL失败, Error: ' + (error as Error)?.message })
  }
})

export default router
