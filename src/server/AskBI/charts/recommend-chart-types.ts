import chalk from 'chalk'
import { ChartType, ChartGroup, RowsMetadata, OlapRow, isDimension, isMetric } from '@shared/common-types'
import { SHOW_TABLE_FIRST } from 'src/shared/constants'
import { QueryParamsVerified } from 'src/shared/metric-types'
import { isQueryBaoWuSubCompany } from 'src/server/custom/baowu/baowu-utils'

/**
 * 基于规则来推荐图表类型
 * GPT 不再做图表类型推荐
 */
export function getRecommendChartTypes({
  chartGroup,
  rowsMetadata,
  rows,
  showTableFirst = SHOW_TABLE_FIRST,
  verifiedMetricParams,
}: {
  chartGroup: ChartGroup
  rowsMetadata: RowsMetadata
  rows: OlapRow[]
  showTableFirst?: boolean
  verifiedMetricParams?: QueryParamsVerified
}): ChartType[] {
  const columnSize = rowsMetadata.length
  const metricSize = rowsMetadata.filter(isMetric).length
  const dimensions = rowsMetadata.filter(isDimension).map((item) => item.value)
  const dimensionSize = dimensions.length
  const virtualTimeDimensionSize = rowsMetadata.filter(
    (item) => item.type === 'dimension' && item.value.type === 'virtual-time',
  ).length

  let chartTypeList: ChartType[] = ['SimpleTable']
  if (columnSize === 2 && metricSize === 2) {
    chartTypeList = ['SimpleTable', 'ScatterChart'] // 气泡图不是很稳定，优先表格
  }
  if (columnSize === 2 && metricSize === 1 && dimensionSize === 1) {
    if (virtualTimeDimensionSize === 1 || chartGroup === 'TimeSeries') {
      chartTypeList = ['LineChart', 'ColumnChart', 'PieChart', 'TreemapChart', 'SimpleTable']
    } else if (chartGroup === 'Comparison') {
      chartTypeList = ['ColumnChart', 'PieChart', 'RankBarChart', 'LineChart', 'TreemapChart', 'SimpleTable']
    } else if (chartGroup === 'Distribution') {
      chartTypeList = ['PieChart', 'RankBarChart', 'TreemapChart', 'ColumnChart', 'LineChart', 'SimpleTable']
    } else if (chartGroup === 'Rank') {
      chartTypeList = ['RankBarChart', 'PieChart', 'ColumnChart', 'LineChart', 'TreemapChart', 'SimpleTable']
    } else {
      chartTypeList = ['ColumnChart', 'PieChart', 'RankBarChart', 'LineChart', 'TreemapChart', 'SimpleTable']
    }
  }
  if (columnSize === 3 && metricSize === 1 && dimensionSize === 2) {
    if (virtualTimeDimensionSize === 1 || chartGroup === 'TimeSeries') {
      chartTypeList = ['MultiLineChart', 'GroupColumnChart', 'StackedColumnChart', 'SimpleTable']
    } else if (chartGroup === 'Comparison') {
      chartTypeList = ['GroupColumnChart', 'StackedColumnChart', 'MultiLineChart', 'SimpleTable']
    } else {
      chartTypeList = ['GroupColumnChart', 'StackedColumnChart', 'MultiLineChart', 'SimpleTable']
    }
  }

  if (columnSize === 3 && metricSize > 1) {
    chartTypeList = ['SimpleTable', 'ScatterChart'] // 气泡图不是很稳定，优先表格
  }

  // FIXME: 宝武的需求。如果只有1个类目维度，那么就使用指标卡
  if (dimensionSize === 1 && dimensions[0].name.toUpperCase() === 'COMPANY_INNER_CODE_DES') {
    chartTypeList = ['Kpi', 'SimpleTable']
  } else if (rows.length === 1 && metricSize >= 1) {
    chartTypeList = ['Kpi', 'SimpleTable']
  }

  // 排行榜使用的场景：单维度 + 1~3个度量 使用RankBarChart
  if (metricSize >= 1 && metricSize < 4 && dimensionSize <= 2) {
    if (chartGroup === 'Rank') {
      chartTypeList = ['RankBarChart', 'ColumnChart', 'LineChart', 'TreemapChart', 'SimpleTable']
    }
  }

  console.info(chalk.blue('Original RecommendChartTypes:'), chalk.yellow(JSON.stringify(chartTypeList)))

  if (showTableFirst) {
    chartTypeList = ['SimpleTable', ...chartTypeList.filter((type) => type !== 'SimpleTable')]
    if (chartTypeList.includes('Kpi')) {
      chartTypeList = [
        'Kpi',
        'SimpleTable',
        ...chartTypeList.filter((type) => type !== 'SimpleTable' && type !== 'Kpi'),
      ]
    }
  }

  // 只在问子公司且groupBys>0时才优先展示RankBarChart
  const groupBys = verifiedMetricParams?.originalQueryParams?.groupBys
  if (isQueryBaoWuSubCompany(verifiedMetricParams?.originalQueryParams) && groupBys && groupBys.length > 0) {
    chartTypeList = ['RankBarChart', ...chartTypeList.filter((type) => type !== 'RankBarChart')]
  }

  console.info(
    chalk.green('Modified RecommendChartTypes (order adjusted):'),
    chalk.yellow(JSON.stringify(chartTypeList)),
  )

  return chartTypeList
}
