import express, { Router, Request, Response } from 'express'
import dayjs from 'dayjs'
import { prisma } from 'src/server/dao/prisma-init'
import { ConverWithDataset, DatasetDatum } from 'src/shared/common-types'
import { validateUserConversationOwnership } from './dao'

const router: Router = express.Router()

/**
 * convers表使用is_draft列来判断是否展示历史记录
 * is_draft: true 属于草稿 不展示
 * is_draft: false 默认不属于草稿 展示s
 */

/** 获取日期范围内的conversations 带有[数据库/表]中文名 */
const fetchConversations = async (username: string, startDate: Date, endDate: Date): Promise<ConverWithDataset[]> => {
  const conversations = await prisma.conver.findMany({
    where: {
      username,
      isDraft: false,
      updatedAt: {
        gte: startDate,
        lt: endDate,
      },
    },
    include: {
      semanticScene: {
        include: {
          semanticProject: true,
        },
      },
    },
    orderBy: {
      updatedAt: 'desc',
    },
  })

  return Promise.all(
    conversations.map(async (conversation) => {
      const { semanticScene, ...conversationWithoutSemanticModel } = conversation
      const dataset: DatasetDatum = {
        projectId: semanticScene?.semanticProject.id || '',
        projectName: semanticScene?.semanticProject.name || '',
        sceneId: semanticScene?.id || '',
        tableName: semanticScene?.tableName || '',
        sceneLabel: semanticScene?.label || '',
        enableFollowUpQuestion: !!semanticScene?.enableFollowUpQuestion,
        enableMetricExactMatch: !!semanticScene?.enableMetricExactMatch,
        enableTryQueryUp: !!semanticScene?.enableTryQueryUp,
        enableSelectToastWhenEmptyData: !!semanticScene?.enableSelectToastWhenEmptyData,
        enableAccMetricToastWhenEmptyData: !!semanticScene?.enableAccMetricToastWhenEmptyData,
      }

      return {
        ...conversationWithoutSemanticModel,
        dataset: dataset,
      }
    }),
  )
}

router.get('/list', async (req: Request, res: Response) => {
  const today = dayjs()
  const startOfToday = today.startOf('day')
  const endOfToday = today.endOf('day')

  const thirtyDaysAgo = today.subtract(30, 'days')
  const startOfThirtyDaysAgo = thirtyDaysAgo.startOf('day')

  const username = req.user?.username || ''
  try {
    const todayConversationListWithDataset = await fetchConversations(
      username,
      startOfToday.toDate(),
      endOfToday.toDate(),
    )

    const thirtyDaysAgoConversationListWithDataset = await fetchConversations(
      username,
      startOfThirtyDaysAgo.toDate(),
      startOfToday.toDate(),
    )

    return res.json({
      code: 0,
      data: { todayConversationListWithDataset, thirtyDaysAgoConversationListWithDataset },
      msg: '',
    })
  } catch (error: any) {
    return res.json({ code: 500, data: {}, msg: '获取会话历史列表失败，error:' + error?.message })
  }
})

router.get('/ask-history', async (req: Request, res: Response) => {
  const username = req.user?.username || ''

  console.info('get /conver_chat/history with params: ', req.query, username)
  const sceneId = req.query.sceneId as string | undefined
  const isProjectChosen = req.query.isProjectChosen as 'true' | 'false'
  const projectId = req.query.projectId as string | undefined

  const filter: { [key: string]: string | boolean | undefined } = { username: username, isDraft: false }
  if (isProjectChosen === 'true') {
    if (!projectId) {
      return res.json({ code: 400, data: {}, msg: 'Project ID is required !' })
    }

    filter.semanticProjectId = projectId
  } else {
    if (!sceneId) {
      return res.json({ code: 400, data: {}, msg: ' Scene ID is required !' })
    }
    filter.semanticSceneId = sceneId
  }

  try {
    const allAsks = await prisma.conver.findMany({
      select: { asks: true },
      where: filter,
      orderBy: { createdAt: 'desc' },
    })
    // 去重
    const askList = [...new Set(allAsks.map((e) => e.asks).flat())]
    return res.json({ code: 0, data: askList, msg: '' })
  } catch (error) {
    return res.json({ code: 500, data: {}, msg: 'Get ask history failed with error' + (error as Error).message })
  }
})

router.post('/upsert', async (req: Request, res: Response) => {
  const username = req.user?.username || ''
  const { isNewConversation, conversationId, chatId, parentId, llmType, message, sceneId, projectId } = req.body

  if (!conversationId) {
    return res.json({ code: 400, data: {}, msg: 'Conversation ID is required!' })
  }
  try {
    const conversation = await prisma.conver.findUnique({
      where: { id: conversationId },
    })
    if (conversation && !isNewConversation) {
      const updatedConversation = await prisma.conver.update({
        data: {
          asks: [...(conversation.asks as string[]), message],
          text2SqlMessages: [...(conversation.text2SqlMessages as any[]), { role: 'user', content: message }],
          updatedAt: new Date(),
        },
        where: { id: conversationId },
      })
      const currentChat = await prisma.converChat.findUnique({
        where: { id: chatId },
      })
      if (currentChat == null) {
        await prisma.converChat.create({
          data: {
            id: chatId,
            converId: conversationId,
            parentId,
            ask: message,
            response: [],
            llmResponse: [],
            docResponse: [],
          },
        })
      }
      return res.json({ code: 0, data: updatedConversation, msg: 'Conversation updated successfully' })
    }
    if (!conversation && isNewConversation) {
      const newConversation = await prisma.conver.create({
        data: {
          id: conversationId,
          title: '',
          username,
          asks: [message],
          version: 'v1',
          semanticProjectId: projectId,
          semanticSceneId: sceneId,
          text2SqlMessages: [{ role: 'user', content: message }],
          llmType,
          isDraft: false,
        },
      })

      const currentChat = await prisma.converChat.findUnique({
        where: { id: chatId },
      })
      if (currentChat == null) {
        await prisma.converChat.create({
          data: {
            id: chatId,
            converId: conversationId,
            parentId,
            ask: message,
            response: [],
            llmResponse: [],
            docResponse: [],
          },
        })
      }
      return res.json({ code: 0, data: newConversation, msg: 'Conversation created successfully' })
    }
    return res.json({ code: 400, data: {}, msg: '当前工作簿已删除，请创建新工作簿后提问' })
  } catch (error: any) {
    return res.json({ code: 500, data: {}, msg: 'Error updating or creating conversation: ' + error.message })
  }
})

/**
 * 删除会话
 */
router.delete('/:id', async (req: Request, res: Response) => {
  const username = req.user?.username || ''
  const conversationId = req.params.id
  if (conversationId == null) {
    return res.json({ code: 404, msg: '缺少 conversationId，无法删除此会话' })
  }
  const verifyResult = await validateUserConversationOwnership(username, conversationId)
  if (!verifyResult) {
    return res.json({ code: 403, data: [], msg: '暂无删除此会话的权限！' })
  }
  try {
    console.info(`Delete ${conversationId} conversation...`, conversationId, username)
    const conversation = await prisma.conver.delete({
      where: {
        id: conversationId,
        username,
      },
    })

    if (conversation == null) {
      return res.json({ code: 404, msg: `找不到conversation${conversationId}, 删除失败!` })
    }

    return res.json({ code: 0, data: { id: conversationId } })
  } catch (error) {
    console.error('Delete conversation with error :', error)
    return res.json({ code: 500, data: {}, msg: '删除会话失败，请联系管理员处理' })
  }
})

/**
 * 更新会话
 */
router.put('/:id', async (req: Request, res: Response) => {
  const username = req.user?.username || ''
  const conversationId = req.params.id
  const updatePayload = req.body

  if (conversationId == null) {
    return res.json({ code: 404, msg: '缺少 conversationId，无法更新此会话' })
  }
  const verifyResult = await validateUserConversationOwnership(username, conversationId)
  if (!verifyResult) {
    return res.json({ code: 403, data: [], msg: '暂无修改此会话的权限！' })
  }
  try {
    console.info(`Update ${conversationId} conversation...`, conversationId, username)
    const conversation = await prisma.conver.update({
      where: {
        id: conversationId,
        username,
      },
      data: { ...updatePayload, updatedAt: new Date() },
    })

    if (conversation == null) {
      return res.json({ code: 404, msg: `找不到conversation${conversationId}, 更新失败!` })
    }

    return res.json({ code: 0, data: { id: conversationId } })
  } catch (error) {
    console.error('Update conversation with error :', error)
    return res.json({ code: 500, data: {}, msg: '更新会话失败，请联系管理员处理' })
  }
})

/**
 * 更新一个用户下面所有的conversations
 */
router.put('/update/all-conversations/:sceneId', async (req: Request, res: Response) => {
  const username = req.user?.username || ''
  const updatePayload = req.body
  const sceneId = req.params.sceneId

  console.info('Update all conversations.', username, updatePayload, sceneId)

  try {
    const conversations = await prisma.conver.updateMany({
      where: {
        username,
        semanticSceneId: sceneId,
      },
      data: { ...updatePayload, updatedAt: new Date() },
    })
    return res.json({ code: 0, data: conversations })
  } catch (error) {
    console.error('Update conversations with error :', error)
    return res.json({ code: 500, data: {}, msg: '更新会话失败，请联系管理员处理' })
  }
})

export default router
