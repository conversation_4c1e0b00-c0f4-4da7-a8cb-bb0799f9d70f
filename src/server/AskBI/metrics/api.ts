/**
 * @description: 指标相关的接口
 */

import express, { Router, Request, Response } from 'express'
import multer from 'multer'
import xlsx from 'xlsx'
import chalk from 'chalk'
import { nanoid } from 'nanoid'
import isObject from 'lodash/isObject'
import { Prisma, SemanticMetric } from '@prisma/client'
import { groupBy } from 'lodash'
import axios from 'axios'
import MetricConfig from 'src/server/MetricStore/metric-config'
import Metric2Sql from 'src/server/MetricStore/metric2sql/metric2sql'
import { addLimitToSql } from 'src/server/dao/db'
import { prisma } from 'src/server/dao/prisma-init'
import { verifyAdminLogin } from 'src/server/login/auth-utils'
import { APIResponse, STRUCTURED_MESSAGE_DATA_TYPE } from 'src/shared/common-types'
import {
  AttrTimeDiffParams,
  DATE_ALIAS,
  DEFAULT_SCENE_ID,
  MetricConfigResponse,
  MetricConfigResponseForProject,
  MetricTrendResponse,
  ProjectType,
  QueryParams,
  TimeQueryParams,
} from 'src/shared/metric-types'
import { passportMiddleware } from 'src/server/auth'
import { executeAllXengineSql } from 'src/server/dao/xengine-query'
import { askBIApiUrlsWithoutBaseUrl } from 'src/shared/url-map'
import { generateDIUrl } from 'src/server/utils'
import { getDefaultDataset } from '../datasets/dao'

const router: Router = express.Router()

router.get('/detail/time/:modelId/:metricName', async (req: Request, res: Response) => {
  console.info('Get metric detail in specific time: ', req.params)
  const modelId = req.params.modelId
  const metricName = req.params.metricName
  if (modelId == null || metricName == null) {
    return res.json({ code: 500, msg: 'modelId 和 metricName 不能为空' })
  }
  try {
    const metricConfig = await MetricConfig.createBySceneId(modelId)
    const metric2Sql = new Metric2Sql(metricConfig)

    let toSqlParams: QueryParams = { metricNames: [metricName] }
    if (metricConfig && metricConfig.timeDimensionDatum) {
      toSqlParams = {
        ...toSqlParams,
        timeQueryParams: {
          timeDimensionName: metricConfig.timeDimensionDatum.timeDimensionName,
          timeStartFunction: { type: 'recentYears', years: 30 },
          timeEndFunction: { type: 'recentYears', years: 0 },
          timeGranularity: metricConfig.timeDimensionDatum.timeGranularityMin,
        },
      }
    }
    const { sql } = metric2Sql.toSql(toSqlParams)
    const rows = await executeAllXengineSql(sql)
    console.info(
      `使用 SQL 查询数据，一共 ${rows.length} 条数据。${rows.length >= 5 ? '前 5 条为' : '数据为'}`,
      rows.slice(0, 5),
    )

    return res.json({
      code: 0,
      data: { list: rows },
    })
  } catch (error) {
    console.error('获取指标预览信息错误', error)
    return res.json({ code: 500, msg: (error as Error).message })
  }
})

/** 根据modelId获取完整的 metric/dimension/measure list */
router.get(
  '/list/:sceneId',
  passportMiddleware,
  async (req: Request, res: Response<APIResponse<MetricConfigResponse>>) => {
    console.info('Get metric list with query:', req.query)
    console.info('Get metric req.user?.username:', req.user?.username)
    const userId = req.user!.id
    let sceneId = req.params.sceneId
    if (sceneId === DEFAULT_SCENE_ID) {
      const defaultDataset = await getDefaultDataset(userId)
      sceneId = defaultDataset?.sceneId || ''
    }

    try {
      const metricConfig = await MetricConfig.createBySceneId(sceneId)
      const metric2Sql = new Metric2Sql(metricConfig)
      const metricList = metricConfig.allMetrics
      const hotMetricList = metricConfig.hotMetrics

      // Assign displayExpr to each metric
      metricList.forEach((metric) => (metric.displayExpr = metric2Sql.getMetricDisplayExpr(metric)))
      hotMetricList.forEach((metric) => (metric.displayExpr = metric2Sql.getMetricDisplayExpr(metric)))
      return res.json({
        code: 0,
        data: {
          metricTableName: metricConfig.name,
          allDimensions: metricConfig.allDimensions,
          allMeasures: metricConfig.allMeasures,
          allMetrics: metricList,
          hotMetrics: hotMetricList,
          allExternalReports: metricConfig.allExternalReports,
          timeDimensionDatum: metricConfig.timeDimensionDatum,
        },
      })
    } catch (error) {
      console.error('获取该model下面的metric信息失败 ' + (error as Error)?.message, error)
      return res.json({ code: 500, msg: '获取该model下面的metric信息失败 ' + (error as Error)?.message })
    }
  },
)

router.get('/list', async (req: Request, res) => {
  console.info('Get metric list with query:', req.query)
  console.info('Get metric req.user?.username:', req.user?.username)
  try {
    const semanticProjectId = req.query.semanticProjectId?.toString()
    const semanticSceneId = req.query.semanticSceneId?.toString()
    const metricList = await prisma.semanticMetric.findMany({
      where: {
        AND: [
          {
            semanticProjectId: semanticProjectId,
          },
          {
            semanticSceneId: semanticSceneId,
          },
        ],
      },
    })

    return res.json({
      code: 0,
      data: {
        list: metricList,
        total: metricList.length,
      },
    })
  } catch (error) {
    console.error(error)
    return res.json({ code: 500, msg: '获取metric信息失败 ' + (error as Error)?.message })
  }
})

// 提参服务使用该接口获取 维度(含码值)，度量指标等数据
router.get('/list/project/:projectId', async (req: Request, res: Response) => {
  console.info('Get Scene MetricConfig detail of project with query & params', req.query, req.params)
  const projectId = req.params.projectId

  // 只返回每个场景更新时间戳
  let replySceneTimeOnly: boolean = false
  if (typeof req.query.timeOnly === 'string' && JSON.parse(req.query.timeOnly) === true) {
    replySceneTimeOnly = true
  }

  try {
    const { data: semanticProjectInfo } = await axios.get<APIResponse<ProjectType[]>>(
      generateDIUrl(askBIApiUrlsWithoutBaseUrl.operationProject.getProjectList),
      {
        headers: { Authorization: 'init' },
      },
    )
    const project = semanticProjectInfo?.data?.find((e) => e.id === projectId)

    if (!project || !project.scenes) {
      throw new Error('Failed to retrieve scene data')
    }

    const metricConfigOfProject = await Promise.all(
      project.scenes.map(async (sceneDetail) => {
        const sceneId = sceneDetail.id

        try {
          const metricConfig = await MetricConfig.createBySceneId(sceneId)
          const metric2Sql = new Metric2Sql(metricConfig)
          const metricList = metricConfig.allMetrics
          const hotMetricList = metricConfig.hotMetrics

          // Assign displayExpr to each metric
          metricList.forEach((metric) => (metric.displayExpr = metric2Sql.getMetricDisplayExpr(metric)))
          hotMetricList.forEach((metric) => (metric.displayExpr = metric2Sql.getMetricDisplayExpr(metric)))

          return {
            sceneId,
            data: {
              metricTableName: metricConfig.name,
              allDimensions: metricConfig.allDimensions,
              allMeasures: metricConfig.allMeasures,
              allMetrics: metricList,
              hotMetrics: hotMetricList,
              allExternalReports: metricConfig.allExternalReports,
              timeDimensionDatum: metricConfig.timeDimensionDatum,
            },
            updateTime: metricConfig.updateTime,
          }
        } catch (error) {
          console.error(`获取 ${sceneId} 场景的维度度量指标信息失败`, error)
          return {
            status: 'failed',
            sceneId,
            msg: (error as Error).message,
          }
        }
      }),
    )

    // 如果请求参数传入 timeOnly=true，只返回每个场景的 updateTime，调用方自己确认是否需要更新
    if (replySceneTimeOnly) {
      for (const item of metricConfigOfProject) {
        item.data = undefined
      }
    }

    const successMetricConfigOfProject = metricConfigOfProject.filter(
      (e) => e.status !== 'failed',
    ) as unknown as MetricConfigResponseForProject[]

    return res.json({
      code: 0,
      data: successMetricConfigOfProject,
      errorData: metricConfigOfProject.filter((e) => e.status === 'failed'),
    })
  } catch (error) {
    console.error('Error fetching metric list for project:', error)
    return res.json({
      code: 500,
      msg: 'Failed to retrieve metric information for the project: ' + (error as Error).message,
    })
  }
})

// 根据指标推荐问题
router.post('/recommend-question', async (req: Request, res: Response) => {
  console.info('Get metric recommend question with params: ' + JSON.stringify(req.body))
  const { sceneId, metricName } = req.body

  if (!sceneId || !metricName) {
    return res.json({ code: 500, msg: '缺少sceneId，metricName参数' })
  }

  try {
    const metricConfig = await MetricConfig.createBySceneId(sceneId)
    const { allMetrics, allDimensions, timeDimensionDatum } = metricConfig
    const dimension = allDimensions.filter((e) => e.type !== 'virtual-time')
    const metricLabel = allMetrics.find((e) => e.name === metricName)?.label

    if (!metricLabel) {
      return res.json({ code: 0, data: [] })
    }

    if (dimension.length === 0) {
      return res.json({
        code: 0,
        data: [
          {
            id: nanoid(),
            type: 'digit',
            jsonContent: [{ 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT, 'data-content': '我想看下有哪些数据' }],
          },
        ],
      })
    }

    const getRandomDimensionLabel = () => {
      const randomDimension = dimension[Math.floor(Math.random() * dimension.length)]
      return randomDimension ? randomDimension.label : dimension[0].label
    }
    let questionsList = []

    if (timeDimensionDatum) {
      questionsList = [
        {
          id: nanoid(),
          type: 'digit',
          jsonContent: [
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT, 'data-content': '今年一月各' },
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.DIMENSION, 'data-content': getRandomDimensionLabel() },
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT, 'data-content': '的' },
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.METRIC, 'data-content': metricLabel },
          ],
        },
        {
          id: nanoid(),
          type: 'trend',
          jsonContent: [
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT, 'data-content': '去年' },
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.METRIC, 'data-content': metricLabel },
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT, 'data-content': '的变化趋势' },
          ],
        },
        {
          id: nanoid(),
          type: 'topN',
          jsonContent: [
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT, 'data-content': '去年' },
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.METRIC, 'data-content': metricLabel },
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT, 'data-content': '前三的' },
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.DIMENSION, 'data-content': getRandomDimensionLabel() },
          ],
        },
      ]
    } else {
      questionsList = [
        {
          id: nanoid(),
          type: 'digit',
          jsonContent: [
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.DIMENSION, 'data-content': getRandomDimensionLabel() },
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT, 'data-content': '的' },
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.METRIC, 'data-content': metricLabel },
          ],
        },
        {
          id: nanoid(),
          type: 'digit',
          jsonContent: [
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.METRIC, 'data-content': metricLabel },
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT, 'data-content': '数据变化的原因' },
          ],
        },
        {
          id: nanoid(),
          type: 'topN',
          jsonContent: [
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT, 'data-content': '查看各' },
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.DIMENSION, 'data-content': getRandomDimensionLabel() },
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT, 'data-content': '前三的' },
            { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.METRIC, 'data-content': metricLabel },
          ],
        },
      ]
    }
    return res.json({ code: 0, data: questionsList })
  } catch (error) {
    console.error('Get metric recommend question with error ', error)
    return res.json({ code: 500, msg: '获取指标推荐问题失败' })
  }
})

// 创建指标
router.post('/', verifyAdminLogin, async (req: Request, res: Response) => {
  try {
    console.info('POST Create metric with body: ', req.body)
    if (!req.body) {
      return res.json({
        code: 400,
        msg: '创建指标的参数错误',
      })
    }

    // 强制指标名大写！！！
    const metricName: string = req.body.metricInfo.name.toUpperCase()
    req.body.metricInfo.name = metricName

    // 获取场景下指标相关信息
    let metricConfig: MetricConfig
    try {
      const tmpMetricConfig = await MetricConfig.createBySceneId(req.body.semanticSceneId)
      metricConfig = tmpMetricConfig
      // 打印指标信息日志
      //console.info('metric info :' + JSON.stringify(metricConfig))
    } catch (error) {
      console.error('获取该model下面的metric信息失败 ' + (error as Error)?.message, error)
      return res.json({ code: 500, msg: '获取该model下面的metric信息失败 ' + (error as Error)?.message })
    }
    //const metricList = metricConfig.allMetrics
    const metric2Sql = new Metric2Sql(metricConfig)

    // 指标重名检查
    if (metric2Sql.findMetricByNameWithoutErr(metricName)) {
      return res.json({
        code: 400,
        msg: '当前项目下已存在相同指标ID，请更换指标ID后重试',
      })
    }

    switch (req.body.metricInfo.type) {
      // TODO:列表指标比例指标依赖的指标都要存在
      case 'list': {
        for (const m of req.body.metricInfo.typeParams.metrics) {
          if (!metric2Sql.findMetricByNameWithoutErr(m.name)) {
            return res.json({
              code: 400,
              msg: '该指标依赖的指标' + m.name + '不存在',
            })
          }
        }
        break
      }
      case 'derived': {
        for (const m of req.body.metricInfo.typeParams.metrics) {
          if (!metric2Sql.findMetricByNameWithoutErr(m.name)) {
            return res.json({
              code: 400,
              msg: '该依赖的指标' + m.name + '不存在',
            })
          }
        }
        // TODO: 复合指标运算符需要为 ()+-*/ , 除首尾外运算符前后存在空格
        const isValidArithmeticExpression = (expression: string): boolean => {
          const regex = /(?<!\s)[+\-/*()](?!\s)/g
          return !regex.test(expression)
        }
        if (!isValidArithmeticExpression(req.body.metricInfo.typeParams.expr)) {
          return res.json({
            code: 400,
            msg: '复合指标表达式不符合规范，除首尾外运算符 +-*/() 前后要有空格',
          })
        }
        break
      }
      // 比例指标分子分母都需要存在
      case 'ratio': {
        if (!req.body.metricInfo.typeParams.denominator || !req.body.metricInfo.typeParams.numerator) {
          return res.json({
            code: 400,
            msg: '比值指标分子和分母都不能为空',
          })
        }
        const denominator = req.body.metricInfo.typeParams.denominator
        const numerator = req.body.metricInfo.typeParams.numerator
        if (!metric2Sql.findMetricByNameWithoutErr(denominator)) {
          console.error('denominator not found', denominator)
          return res.json({
            code: 400,
            msg: '分母' + denominator + '不存在，请更换指标名称',
          })
        }
        if (!metric2Sql.findMetricByNameWithoutErr(numerator)) {
          console.error('numerator not found', numerator)
          return res.json({
            code: 400,
            msg: '分子' + numerator + '不存在，请更换指标名称',
          })
        }
        break
      }
      /* 原子指标对应度量必须存在，原子指标 filter 符合标准 where SQL格式
       * 好的实现路径是，在前端做一个SQL编辑器，自动规范SQL语法，用户体验更友好
       * 这里只做宽松的度量和维度校验，过于严格可能产生误校验导致无法创建指标
       */
      case 'simple': {
        if (!metric2Sql.findMeasureByNameWithoutErr(req.body.metricInfo.typeParams.measure)) {
          return res.json({
            code: 400,
            msg: '原子指标引用的度量[' + req.body.metricInfo.typeParams.measure + ']不存在',
          })
        }
        // 提取 where 运算符左边的维度
        const extractDimension = (sqlWhere: string): string[] => {
          const regex = /(\w+)\s+(?:LIKE|NOT LIKE|IN|NOT IN|BETWEEN|NOT BETWEEN|=|<|>|<=|>=|<>|!=|IS|IS NOT)/g
          const matches: string[] = []
          sqlWhere.replace(regex, (match, word) => {
            matches.push(word)
            return match
          })
          return matches
        }
        if (req.body.metricInfo.filter?.length > 0) {
          const dimensions: string[] = extractDimension(req.body.metricInfo.filter)
          console.info('指标 ' + req.body.metricInfo.name + ' where 条件提取到维度: ' + dimensions)
          for (const dimension of dimensions) {
            console.info('指标 ' + req.body.metricInfo.name + ' 检查维度 ' + dimension)
            if (!metric2Sql.findDimensionByNameWithoutErr(dimension)) {
              return res.json({
                code: 400,
                msg: '指标筛选条件中的维度[' + dimension + ']不存在',
              })
            }
          }
        }
        break
      }
      default:
        console.error('create metric type ' + req.body.metricInfo.type)
        return res.json({
          code: 400,
          msg: '新建指标类型[' + req.body.metricInfo.type + ']不支持',
        })
    }

    // 给metric表插入数据
    const { metricInfo, semanticProjectId, semanticSceneId } = req.body
    const newMetric = await prisma.semanticMetric.create({
      data: { ...metricInfo, semanticProjectId, semanticSceneId },
    })
    return res.json({
      code: 0,
      data: newMetric,
    })
  } catch (e) {
    console.error('create metric failed', e)
    const errorMessage = (e as Error).message
    // 再次检查指标重名
    if (errorMessage.indexOf('Unique constraint failed') > -1) {
      return res.json({
        code: 400,
        msg: '当前项目下已存在相同指标ID，请更换指标ID后重试',
      })
    }
    return res.json({
      code: 500,
      msg: '创建指标失败' + (e as Error).message,
    })
  }
})

// 获取指标详情
router.get('/detail/:id', async (req: Request, res: Response) => {
  console.info('Get metric detail info with params: ' + req.params)
  const { id } = req.params

  if (!id) {
    return res.json({ code: 400, msg: 'id不能为空' })
  }

  try {
    // 首先尝试获取metric信息
    const metricInfo = await prisma.semanticMetric.findUnique({
      where: { id: id },
    })

    if (metricInfo) {
      return res.json({ code: 0, data: metricInfo })
    }

    return res.json({ code: 400, msg: '该指标不存在' })
  } catch (error) {
    console.error('获取指标详情错误:', error)
    return res.json({ code: 500, msg: '服务器内部错误' })
  }
})

// 更新指标
router.put('/:id', verifyAdminLogin, async (req: Request, res: Response) => {
  console.info('PUT Update metric with body: ', req.body)

  // update 的时候，如果 id 重复会抛出异常，导致 node 挂掉，所以需要先判断 id 是否存在
  try {
    const { id } = req.params
    const data = req.body
    if (!id || !data) {
      return res.json({ code: 400, msg: 'id 或修改数据为空' })
    }
    const existedMetric = await prisma.semanticMetric.findFirst({
      where: {
        name: data.name,
        id: {
          not: id,
        },
      },
    })
    if (existedMetric) {
      return res.json({
        code: 400,
        msg: '当前项目下已存在相同指标ID，请更换指标ID后重试',
      })
    }
    const metric = await prisma.semanticMetric.findUnique({
      where: { id },
    })
    if (!metric) {
      return res.json({ code: 400, data: {}, msg: '找不到指标' })
    }
    const updatedMetricInfo = await prisma.semanticMetric.update({
      where: { id },
      data: data,
    })
    return res.json({
      code: 0,
      data: updatedMetricInfo,
    })

    // const updatedMeasure
    // })
  } catch (e) {
    console.error('更新指标信息失败，具体原因如下', e)
    return res.json({
      code: 500,
      msg: '更新指标失败' + (e as Error).message,
    })
  }
})

/** 获取依赖该指标的指标 */
async function getMetricDep(metric: SemanticMetric): Promise<string[]> {
  if (metric.type !== 'simple') return []
  // 指标存在以后 去找 原子指标是否被占用
  const dependencies = await Promise.all(
    [metric.semanticSceneId].map(async (sceneId) => {
      const derivedMetricListWithSameSemanticModelId = await prisma.semanticMetric.findMany({
        where: {
          semanticSceneId: sceneId,
          type: 'derived',
        },
      })
      return derivedMetricListWithSameSemanticModelId
        .filter((metric) => {
          if (
            metric.typeParams &&
            isObject(metric.typeParams) &&
            'metrics' in metric.typeParams &&
            Array.isArray(metric.typeParams.metrics)
          ) {
            const childMetrics = metric.typeParams.metrics as { name: string }[]
            return childMetrics
              .filter((item) => isObject(item) && 'name' in item)
              .some((item) => item.name === metric.name)
          }
          return false
        })
        .map((metric) => metric.label || metric.name) // Return the name of the metric
    }),
  )

  return dependencies.flat()
}

router.delete('/batch', verifyAdminLogin, async (req: Request, res: Response) => {
  const ids = req.body.ids
  console.info('Batch Delete metric with params: ', ids)
  if (!Array.isArray(ids) || !ids.length) {
    return res.json({ code: 400, msg: 'id列表 存在问题' })
  }

  const metricList = await prisma.semanticMetric.findMany({
    where: {
      id: {
        in: ids,
      },
    },
  })
  if (metricList.length !== ids.length) {
    const idSet = new Set(metricList.map((v) => v.id))
    return res.json({ code: 500, msg: '存在未知ID，' + ids.filter((v) => !idSet.has(v)) })
  }
  const dependentMetricNameList = await Promise.all(
    metricList.map(async (metric) => [metric.name, await getMetricDep(metric)] as [string, string[]]),
  )
  if (dependentMetricNameList.some(([_, list]) => list.length > 0)) {
    return res.json({
      code: 400,
      msg: dependentMetricNameList
        .filter(([_, list]) => list.length > 0)
        .map(([name, list]) => {
          return `${name}指标被其他指标依赖（${list.join('，')}），无法删除。`
        })
        .join('\n'),
    })
  }
  await prisma.semanticMetric.deleteMany({
    where: { id: { in: ids } },
  })
  return res.json({ code: 0, data: { ids, message: '指标删除成功' } })
})

// 删除指标
router.delete('/:id', verifyAdminLogin, async (req: Request, res: Response) => {
  console.info('Delete metric with params: ' + req.params)
  const { id } = req.params

  if (!id) {
    return res.json({ code: 400, msg: 'id 不能为空' })
  }

  // 先检查 semanticMetric 是否存在
  const metricExists = await prisma.semanticMetric.findUnique({
    where: { id: id },
  })
  if (metricExists) {
    // 指标存在以后 去找 原子指标是否被占用
    if (metricExists.type === 'simple') {
      const dependentMetricNames = await getMetricDep(metricExists)

      if (dependentMetricNames.length > 0) {
        return res.json({ code: 400, msg: `该指标被其他指标依赖（${dependentMetricNames.join('，')}），无法删除` })
      }
    }
    await prisma.semanticMetric.delete({
      where: { id: id },
    })
    return res.json({ code: 0, data: { id: id, message: '指标删除成功' } })
  }

  // 如果 semanticMetric 不存在，检查 semanticMeasure 是否存在
  // const measureExists = await prisma.semanticMeasure.findUnique({
  //   where: { id: id },
  // })

  // if (measureExists) {
  //   await prisma.semanticMeasure.delete({
  //     where: { id: id },
  //   })
  //   return res.json({ code: 0, data: { id: id, message: '该指标由度量创建，删除成功' } })
  // }

  // 如果两者都不存在，返回错误信息
  return res.json({ code: 404, msg: '该指标/度量不存在，可能已被删除' })
})

// 归因的取数接口
router.get('/metric2sql2data-attr', async (req: Request, res: Response) => {
  console.info(chalk.cyan('收到 metric2sql2data-attr 请求：', JSON.stringify(req.query)))
  const metricNames = req.query.metricNames?.toString()?.split(',') || []
  const groupBys = req.query.groupBys?.toString()?.split(',') || []
  if (metricNames.length === 0 && groupBys.length === 0) {
    return res.json({ code: 500, msg: 'metricNames 和 groupBys 不能同时为空' })
  }
  const orderBys = req.query.orderBys?.toString()?.split(',') || []
  const where = req.query.where?.toString() || ''
  let timeQueryJson = null
  if (req.query.timeQueryParams && req.query.timeQueryParams?.toString().length > 0) {
    try {
      timeQueryJson = JSON.parse(req.query.timeQueryParams.toString())
      if (timeQueryJson == null) {
        return res.json({ code: 500, msg: 'timeQueryParams 格式错误，不是合法的 JSON' })
      }
    } catch (e) {
      return res.json({ code: 500, msg: 'timeQueryParams 格式错误，不是合法的 JSON' })
    }

    // 如果包含 baseTime 和 compareTime，那么就是归因
    if (!timeQueryJson.baseTime || !timeQueryJson.compareTime) {
      return res.json({ code: 500, msg: '归因接口 timeQueryParams 格式错误，缺少 baseTime 或 compareTime' })
    }
    // 检查 timeQueryJson.baseTime 和 timeQueryJson.compareTime 都是标准的 TimeFunction
    if (!timeQueryJson.baseTime.type || !timeQueryJson.compareTime.type) {
      console.error(new Error())
      return res.json({
        code: 500,
        msg:
          '归因接口 timeQueryJson 中 baseTime 或 compareTime 格式错误。timeQueryJson =' + JSON.stringify(timeQueryJson),
      })
    }
  }
  const modelId = req.query.modelId?.toString()
  if (modelId == null) {
    return res.json({ code: 500, msg: 'modelId 不能为空' })
  }
  const semanticModel = await prisma.semanticScene.findUnique({
    where: { id: modelId },
    include: {
      semanticProject: true,
    },
  })
  if (semanticModel == null) {
    return res.json({ code: 500, msg: 'modelId 错误，无法找到对应的场景' })
  }
  if (semanticModel.semanticProject == null) {
    return res.json({ code: 500, msg: 'modelId 错误，无法找到对应的项目' })
  }
  try {
    const metricConfig = await MetricConfig.createBySceneId(modelId)
    const metric2Sql = new Metric2Sql(metricConfig)
    const attrTimeDiffParams = timeQueryJson as AttrTimeDiffParams
    const { sql: sqlBase } = metric2Sql.toSql({
      metricNames,
      groupBys,
      orderBys,
      where,
      timeQueryParams: metricConfig.timeDimensionDatum && {
        timeDimensionName: metricConfig.timeDimensionDatum.timeDimensionName,
        timeStartFunction: attrTimeDiffParams.baseTime,
        timeEndFunction: attrTimeDiffParams.baseTime,
        timeGranularity: attrTimeDiffParams.baseTime.type === 'specificQuarter' ? 'quarter' : 'total',
      },
    })
    console.info('sqlBase ===')
    console.info(chalk.cyan(sqlBase))
    const { sql: sqlCompare } = metric2Sql.toSql({
      metricNames,
      groupBys,
      orderBys,
      where,
      timeQueryParams: {
        timeStartFunction: attrTimeDiffParams.compareTime,
        timeEndFunction: attrTimeDiffParams.compareTime,
        timeGranularity: attrTimeDiffParams.baseTime.type === 'specificQuarter' ? 'quarter' : 'total',
      },
    })
    console.info('sqlCompare ===')
    console.info(chalk.cyan(sqlCompare))
    const rowsBase = await executeAllXengineSql(sqlBase)
    const rowsCompare = await executeAllXengineSql(sqlCompare)
    // 把 rowsBase 的第一行转成 object
    const rowBase = rowsBase
    const rowCompare = rowsCompare
    //  去掉 rowBase 对象数组中的 _DATE_ 列
    rowBase.map((row) => {
      delete row[DATE_ALIAS]
    })
    rowCompare.map((row) => {
      delete row[DATE_ALIAS]
    })

    return res.json({
      code: 0,
      data: {
        sqlBase: sqlBase,
        sqlCompare: sqlCompare,
        baseRows: rowBase,
        compareRows: rowCompare,
      },
    })
  } catch (e) {
    console.error('metric2sql2data-attr error:', e)
    return res.json({ code: 500, msg: (e as Error).message })
  }
})

// 指标查询的取数接口
router.get('/metric2sql2data', async (req: Request, res: Response) => {
  console.info(chalk.cyan('收到 metric2sql2data 请求：', JSON.stringify(req.query)))
  const metricNames = req.query.metricNames?.toString()?.split(',') || []
  const groupBys = req.query.groupBys?.toString()?.split(',') || []
  if (metricNames.length === 0 && groupBys.length === 0) {
    return res.json({ code: 500, msg: 'metricNames 和 groupBys 不能同时为空' })
  }
  const orderBys = req.query.orderBys?.toString()?.split(',') || []
  const where = req.query.where?.toString() || ''
  let timeQueryJson = null
  if (req.query.timeQueryParams && req.query.timeQueryParams?.toString().length > 0) {
    try {
      timeQueryJson = JSON.parse(req.query.timeQueryParams.toString())
      if (timeQueryJson == null) {
        return res.json({ code: 500, msg: 'timeQueryParams 格式错误，不是合法的 JSON' })
      }
    } catch (e) {
      return res.json({ code: 500, msg: 'timeQueryParams 格式错误，不是合法的 JSON' })
    }

    // 如果包含 baseTime 和 compareTime，那么就是归因
    if (timeQueryJson.baseTime && timeQueryJson.compareTime) {
      return res.json({ code: 500, msg: '归因接口请使用 /metric2sql2data-attr' })
    }
  }
  const modelId = req.query.modelId?.toString()
  if (modelId == null) {
    return res.json({ code: 500, msg: 'modelId 不能为空' })
  }
  const semanticModel = await prisma.semanticScene.findUnique({
    where: { id: modelId },
    include: {
      semanticProject: true,
    },
  })
  if (semanticModel == null) {
    return res.json({ code: 500, msg: 'modelId 错误，无法找到对应的场景' })
  }
  if (semanticModel.semanticProject == null) {
    return res.json({ code: 500, msg: 'modelId 错误，无法找到对应的项目' })
  }
  try {
    const metricConfig = await MetricConfig.createBySceneId(modelId)
    const metric2Sql = new Metric2Sql(metricConfig)
    const timeQueryParams = timeQueryJson as TimeQueryParams
    let { sql, rowsMetadata } = metric2Sql.toSql({
      metricNames,
      groupBys,
      orderBys,
      where,
      timeQueryParams,
    })
    sql = addLimitToSql(sql, 100)
    console.info('sql ===')
    console.info(chalk.cyan(sql))
    const rows = await executeAllXengineSql(sql)

    return res.json({
      code: 0,
      data: {
        length: rows.length, // 归因只会返回 2 行数据
        sql: sql,
        rowsMetadata,
        rows,
      },
    })
  } catch (e) {
    console.error('metric2sql2data error:', e)
    return res.json({ code: 500, msg: (e as Error).message })
  }
})

/**
 * 获取指定指标最近30天或者最近12个月的趋势。返回 [{date: '2020-01-01', value: 100}, ...]，最后一个为当前的数据
 */
router.get(
  '/trend/:sceneId/:metricName',
  passportMiddleware,
  async (req: Request, res: Response<APIResponse<MetricTrendResponse>>) => {
    const userId = req.user!.id
    const metricName = req.params.metricName
    if (!metricName) {
      return res.json({
        code: 500,
        msg: '缺少 metricName 参数',
      })
    }
    let sceneId = req.params.sceneId
    if (!sceneId) {
      return res.json({
        code: 500,
        msg: '缺少 sceneId 参数',
      })
    }

    if (sceneId === DEFAULT_SCENE_ID) {
      const defaultDataset = await getDefaultDataset(userId)
      sceneId = defaultDataset?.sceneId || ''
    }

    try {
      const metricConfig = await MetricConfig.createBySceneId(sceneId)
      const metric2Sql = new Metric2Sql(metricConfig)
      if (metricConfig.allMetrics.some((m) => m.name === metricName) === false) {
        return res.json({ code: 500, msg: `指标 ${metricName} 不存在` })
      }

      let sql: string
      // 如果没有配置 timeDimension，只返回最新的数据
      if (metricConfig.timeDimensionDatum == null) {
        sql = metric2Sql.toSql({
          metricNames: [metricName],
        }).sql
      } else {
        // TODO: 改成其他的日期字段
        sql = metric2Sql.toSql({
          metricNames: [metricName],
          timeQueryParams: {
            timeDimensionName: metricConfig.timeDimensionDatum.timeDimensionName,
            timeStartFunction: { type: 'recentMonths', months: 12 },
            timeEndFunction: { type: 'recentMonths', months: 0 },
            timeGranularity: 'month',
          },
        }).sql
      }

      console.info(chalk.green('Get trend of metric:', metricName, ', sql is:\n' + sql))

      const response = await executeAllXengineSql(sql)
      return res.json({
        code: 0,
        data: {
          date: response.map((row) => row[DATE_ALIAS]),
          value: response.map((row) => row[metricName]),
        },
      })
    } catch (error) {
      // 处理非 Error 类型的错误
      console.error('Unknown error', error)
      if (error instanceof Error) {
        // Unknown column 'I1191' in 'field list'
        if (error.message.includes('Unknown column')) {
          return res.json({ code: 414, msg: '当前指标对应的数据库字段不存在，可能被删除' })
        }
        return res.json({ code: 500, msg: '获取metric趋势失败 ' + error.message })
      }
      return res.json({ code: 500, msg: '获取metric趋势失败，未知错误' })
    }
  },
)
/**
 * 删除场景下所有指标
 */
router.delete('/scene/:sceneId', async (req: Request, res: Response) => {
  const sceneId = req.params.sceneId
  try {
    await prisma.semanticMetric.deleteMany({
      where: {
        semanticSceneId: sceneId,
      },
    })
    return res.json({
      code: 0,
      data: 'delete metrics successfully',
    })
  } catch (err: any) {
    return res.json({
      code: 500,
      msg: err?.message || '服务端错误',
    })
  }
})
/**
 * 删除项目下所有指标
 */
router.delete('/project/:projectId', async (req: Request, res: Response) => {
  const projectId = req.params.projectId
  try {
    await Promise.all([
      prisma.semanticMetric.deleteMany({
        where: {
          semanticProjectId: projectId,
        },
      }),
      prisma.semanticMetricTreeRoot.deleteMany({
        where: {
          semanticProjectId: projectId,
        },
      }),
    ])
    return res.json({
      code: 0,
      data: 'delete metrics successfully',
    })
  } catch (err: any) {
    return res.json({
      code: 500,
      msg: err?.message || '服务端错误',
    })
  }
})

const storage = multer.memoryStorage()
const upload = multer({ storage: storage })
function createSemanticMetricList(option: {
  sheet: xlsx.WorkSheet
  semanticProjectId: string
  semanticSceneId: string
  enableDefault?: boolean
}) {
  const { sheet, semanticProjectId, semanticSceneId, enableDefault = true } = option
  const arr: Prisma.SemanticMetricCreateManyInput[] = []
  for (let i = 2; sheet['A' + i]; i++) {
    const [name, label, synonyms, isCumulative, formatTemplate, filter, typeParams, type, description] = new Array(9)
      .fill(0)
      .map((_, i) => String.fromCharCode(i + 'A'.codePointAt(0)!))
      .map((v) => sheet[`${v}${i}`]?.v)

    const data: Prisma.SemanticMetricCreateManyInput = {
      name,
      label,
      synonyms: synonyms ? JSON.parse(synonyms) : enableDefault ? [] : synonyms,
      isCumulative: isCumulative !== '否',
      formatTemplate,
      filter,
      typeParams: typeParams ? JSON.parse(typeParams) : enableDefault ? {} : typeParams,
      type,
      description,
      semanticProjectId,
      semanticSceneId,
    }
    arr.push(data)
  }
  return arr
}
router.post('/upload', upload.array('file'), verifyAdminLogin, async (req, res) => {
  try {
    const files = req.files
    if (!Array.isArray(files) || files?.length !== 1) {
      return res.json({ code: 500, msg: '文件异常' })
    }
    const { semanticSceneId, semanticProjectId } = req.body
    const file = files[0]
    const data = xlsx.read(file.buffer)
    const sheet = data.Sheets[data.SheetNames[0]]
    const arr = createSemanticMetricList({ sheet, semanticSceneId, semanticProjectId })
    const nameMap = new Map()
    for (const item of arr) {
      nameMap.set(item.name, (nameMap.get(item.name) ?? 0) + 1)
    }
    // name不能重复
    if (nameMap.size !== arr.length) {
      throw new Error(
        '存在重复的指标ID:' +
          Array.from(nameMap.entries())
            .filter(([_, v]) => v > 1)
            .map(([k]) => k)
            .join(','),
      )
    }
    // 必填的字段
    const requiredField = ['name', 'label', 'typeParams', 'type', 'synonyms'] as const
    if (arr.some((v) => !requiredField.map((key) => v[key]).every((val) => val !== undefined))) {
      const msgArr = ['创建失败:']
      for (const item of arr) {
        const fields: string[] = []
        for (const field of requiredField) {
          if (item[field] === undefined) {
            fields.push(field)
          }
        }
        if (fields.length) {
          msgArr.push(`${item.name}缺失:${fields.join(',')}`)
        }
      }
      throw msgArr.join('\n')
    }
    // name不能与库里当前项目中的重复
    const dataFindWithId = await prisma.semanticMetric.findMany({
      where: {
        name: {
          in: arr.map((item) => item.name).filter(Boolean) as string[],
        },
        semanticProjectId,
      },
    })
    if (dataFindWithId.length) {
      throw new Error('存在重复的指标ID:\n' + dataFindWithId.map((v) => v.name).join('\n'))
    }
    await prisma.semanticMetric.createMany({ data: arr })
    res.json({ code: 0, msg: '创建成功' })
  } catch (e) {
    console.error('create metric failed', e)
    return res.json({
      code: 500,
      msg: (e as Error).message ?? e?.toString(),
    })
  }
})

router.patch('/upload', upload.array('file'), verifyAdminLogin, async (req, res) => {
  try {
    const files = req.files
    if (!Array.isArray(files) || files?.length !== 1) {
      return res.json({ code: 500, msg: '文件异常' })
    }
    const { semanticSceneId, semanticProjectId } = req.body
    const file = files[0]
    const data = xlsx.read(file.buffer)
    const sheet = data.Sheets[data.SheetNames[0]]
    const metricRecord = groupBy(
      createSemanticMetricList({ sheet, semanticSceneId, semanticProjectId, enableDefault: false }),
      (item) => item.name,
    )
    const metrics = await prisma.semanticMetric.findMany({
      where: {
        name: {
          in: Object.keys(metricRecord),
        },
        semanticProjectId,
      },
    })
    const errorMessageArr: string[] = []
    for (const metric of metrics) {
      const data = metricRecord[metric.name][0]
      if (data) {
        try {
          await prisma.semanticMetric.update({
            where: { id: metric.id },
            data,
          })
        } catch (e: any) {
          if (e?.message) {
            errorMessageArr.push(`${metric.name}: ${e?.message}`)
          }
        }
      }
    }
    if (errorMessageArr.length === 0) {
      res.json({ code: 0, msg: '更新成功' })
    } else {
      throw new Error(errorMessageArr.join('\n'))
    }
  } catch (e) {
    console.error('create metric failed', e)
    return res.json({
      code: 500,
      msg: (e as Error).message,
    })
  }
})

export default router
