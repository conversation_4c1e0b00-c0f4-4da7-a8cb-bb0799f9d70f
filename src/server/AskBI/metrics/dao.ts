import chalk from 'chalk'
import { prisma } from 'src/server/dao/prisma-init'
import { ChatResponse } from 'src/shared/common-types'
import { assertExhaustive } from 'src/shared/common-utils'
import { Metric } from 'src/shared/metric-types'

/**
 * 根据聊天响应记录指标的使用频率到数据库中。
 * @param {Object} props - 属性对象。
 * @param {ChatResponse} props.chatResponse - 聊天响应对象，根据用户交互和任务类型包含不同类型的数据。
 * @param {string} props.sceneId - 发生聊天交互的场景。
 * @param {string} props.projectId - 与场景关联的项目。
 * @returns {Promise<void>}
 */
export async function recordMetricFrequencyFromResult(props: {
  chatResponse: ChatResponse
  sceneId: string
  projectId: string
}): Promise<void> {
  const { chatResponse, sceneId, projectId } = props
  const metricNames: string[] = []

  try {
    if (chatResponse.ready === true) {
      const taskType = chatResponse.taskType
      switch (taskType) {
        case 'query-metric': {
          const names = chatResponse.queryParamsVerified?.queryParams.metricNames
          if (names) {
            metricNames.push(...names)
          }
          break
        }
        case 'attribution-metric-analysis': {
          const name = chatResponse.rows[0].metricName
          metricNames.push(name)
          break
        }
        case 'metric-detail': {
          const name = chatResponse.rows
          metricNames.push(name)
          break
        }
        case 'metric-exact-match':
        case 'query-external-report':
        case 'attribution-analysis':
        case 'data-overview':
        case 'table-list':
        case 'dimension-list':
        case 'dimension-detail':
        case 'metric-list':
        case 'doc-report':
        case 'metric-tree':
        case 'llm-error':
        case 'percentage':
        case 'period-on-period':
        case 'chitchat': {
          break
        }
        default: {
          return assertExhaustive(taskType)
        }
      }
    }

    const validMetricNames = metricNames.filter((name) => typeof name === 'string' && name.length > 1)

    if (validMetricNames.length > 0) {
      // 校验一下是否都为string 并且 长度大于一
      const operations = metricNames.map((name) =>
        prisma.metricFrequency.upsert({
          where: {
            unique_semanticSceneId_metricName: { metricName: name, semanticSceneId: sceneId },
          },
          update: { frequency: { increment: 1 } },
          create: { semanticSceneId: sceneId, semanticProjectId: projectId, metricName: name, frequency: 1 },
        }),
      )

      // 使用prisma事务
      await Promise.all(operations)
      console.info(chalk.green(`Metrics recorded successfully. Metric count is ${operations.length}`))
    }
  } catch (error) {
    console.error(chalk.red('Failed to record metric frequencies:'), error)
  }
}

/**
 * 根据场景ID获取指标及其频率，结果按频率从高到低排序并限制为前10个。
 * 仅返回在 allMetrics 数组中出现的指标。
 * @param {Metric[]} allMetrics - 指标数组，用于过滤数据库查询结果。
 * @param {string} sceneId - 场景的唯一标识符。
 * @returns {Promise<Metric[]>} Metric数组
 */
export async function getHotMetricsBySceneId(
  allMetrics: Metric[],
  sceneId: string,
): Promise<(Metric & { frequency: number })[]> {
  try {
    // 首先从数据库获取前10个最频繁的指标
    const metrics = await prisma.metricFrequency.findMany({
      where: {
        semanticSceneId: sceneId,
      },
      select: {
        metricName: true,
        frequency: true,
      },
      orderBy: {
        frequency: 'desc',
      },
      take: 10,
    })

    const hotMetrics = metrics
      .map((metric) => {
        const foundMetric = allMetrics.find((m) => m.name === metric.metricName)
        if (foundMetric) {
          return {
            frequency: metric.frequency,
            ...foundMetric,
          }
        }
        return null
      })
      .filter((metric) => metric !== null)
    return hotMetrics as (Metric & { frequency: number })[]
  } catch (error) {
    console.error(chalk.red('Failed to retrieve metric frequencies:'), error)
    return []
  }
}
