import {
  ChatResponse,
  ChatResponseError,
  ChatResponseMetricExactMatch,
  ChatResponseQueryMetric,
} from 'src/shared/common-types'
import { QueryParamsVerified } from 'src/shared/metric-types'

/**
 * 处理多场景结果：
 * 1. 如果只有一个场景结果，直接返回
 * 2. 包含正确+错误的结果 只展示正确的结果
 * 3. 包含多个场景的结果 某个场景没有置信度
 *    - 3.1 某个场景有完全匹配的指标 返回完全匹配的场景
 *    - 3.2 没有场景有完全匹配的指标 所有场景都返回置信度 在前端做选择
 * 4. 包含多个场景的结果 都没有置信度
 *    - 4.1 某一个场景有完全匹配的指标 返回完全匹配的场景
 *    - 4.2 没有场景有完全匹配的指标 所有场景都返回置信度 在前端做选择
 * 5. 包含多个场景的结果 都是置信度 直接返回
 * 6. 包含多个错误的结果，只返回第0个
 * @param results
 * @returns {ChatResponse[]}
 */
function processMultiSceneResults(results: ChatResponse[]): ChatResponse[] {
  console.info(`多场景后置处理方法共收到 ${results.length} 个结果。`)
  if (results.length === 1) return results

  const validResults = results.filter((r) => r.ready || r.errType === 'LATEST_DATA_NOT_EXIST') // 正确的结果
  const hasError = results.some((r) => !r.ready) // 是否有错误的结果

  if (validResults.length === 1) return validResults
  if (hasError) {
    console.warn('检测到错误结果，已过滤错误数据')
    if (validResults.length === 0) {
      return [results[0]]
    }
  }

  const queryMetricScenes = validResults.filter((r) => r.taskType === 'query-metric')
  const confidenceScenes = validResults.filter((r) => r.taskType === 'metric-exact-match')
  const otherScenes = validResults.filter((r) => r.taskType !== 'metric-exact-match' && r.taskType !== 'query-metric')

  if (queryMetricScenes.length > 0 || confidenceScenes.length > 0) {
    const allScoresAreOneScenes = queryMetricScenes.filter((r) =>
      Object.values(r.queryParamsVerified.queryParams.extraInfo?.metric_scores || {}).every((score) => score === 1),
    )
    if (allScoresAreOneScenes.length === 1) {
      return [...allScoresAreOneScenes, ...otherScenes]
    } else {
      const convertArray: (ChatResponseQueryMetric | ChatResponseError)[] = [...queryMetricScenes]
      const dataNotExist = otherScenes.filter(
        (x) => x.taskType === 'chat-error' && x.errType === 'LATEST_DATA_NOT_EXIST',
      ) as ChatResponseError[]
      if (dataNotExist) {
        convertArray.push(...dataNotExist)
      }
      const newConfidenceScenes: ChatResponseMetricExactMatch[] = convertArray.map((r) => {
        const params = r.queryParamsVerified as QueryParamsVerified
        return {
          ready: true,
          taskType: 'metric-exact-match',
          sceneId: r.sceneId,
          infoTexts: [],
          conversationId: r.conversationId,
          metricNames: params.originalQueryParams.metricNames,
          queryParamsVerified: {
            originalQueryParams: {
              ...params.originalQueryParams,
              isMetricNamesExactMatch: false,
              isWhereExactMatch: false,
            },
            queryParams: {
              ...params.queryParams,
              isMetricNamesExactMatch: false,
              isWhereExactMatch: false,
            },
            extraParams: params.extraParams,
          },
        }
      })
      return [...newConfidenceScenes, ...confidenceScenes]
    }
  }

  return validResults
}

export { processMultiSceneResults }
