import { describe, it, expect } from 'vitest'
import { ChatResponse, ChatResponseQueryMetric } from 'src/shared/common-types'
import { processMultiSceneResults } from './utils'

const rightResult = {
  ready: true,
  taskType: 'query-metric',
  rows: [
    {
      yoy_month_growth_SUMT6200: '-2431975.990000',
      SUMT6200: '1887105.990000',
      COMPANY_INNER_CODE_DES: '宝武共享服务有限公司-法人',
      mom_growth_SUMT6200: '1462861.210000',
    },
    {
      yoy_month_growth_SUMT6200: '12539392.680000',
      SUMT6200: '21725029.540000',
      COMPANY_INNER_CODE_DES: '宝武共享服务有限公司-资产合并',
      mom_growth_SUMT6200: '20120134.010000',
    },
  ],
  rowsMetadata: [
    {
      type: 'dimension',
      value: {
        name: 'COMPANY_INNER_CODE_DES',
        label: '公司',
        synonyms: ['企业'],
        filterSwitch: true,
        description: '合并账套关联内码',
        expr: 'T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES',
        type: 'categorical',
        typeParams: {},
        values: [],
      },
    },
    {
      type: 'metric',
      value: {
        id: 'PS2724cd62-0803-479d-8651-dee2cbbc48ce',
        name: 'SUMT6200',
        label: '利润总额',
        synonyms: ['实现利润', '挣钱总计', '挣了多少钱', '亏损', '盈利', '利润'],
        description: '利润总额',
        type: 'simple',
        formatTemplate: '.2f元',
        typeParams: {
          measure: 'MONTH_AMT',
        },
        filter: "REPORT_ITEM = 'T6200'",
        rank: 10,
        category: '常用指标%99',
        keypoint: false,
        createByMeasure: false,
        isCumulative: true,
        updatedAt: '2024-09-21T17:14:02.017Z',
      },
    },
    {
      type: 'metric',
      value: {
        id: 'B5GP4gz9VHsO_PSgi8EHV',
        name: 'mom_growth_SUMT6200',
        label: '利润总额环比增长额',
        synonyms: [],
        type: 'periodOverPeriod',
        formatTemplate: '.2f元',
        typeParams: {
          type: 'momGrowth',
          metric: 'SUMT6200',
        },
      },
    },
    {
      type: 'metric',
      value: {
        id: 'CcKMILXhcntau4OZXO_iw',
        name: 'yoy_month_growth_SUMT6200',
        label: '利润总额同比增长额',
        synonyms: [],
        type: 'periodOverPeriod',
        formatTemplate: '.2f元',
        typeParams: {
          type: 'yoyMonthGrowth',
          metric: 'SUMT6200',
        },
      },
    },
  ],
  recommendChartTypes: ['Kpi', 'SimpleTable'],
  sql: "WITH DIM_TBL AS (\n  SELECT DISTINCT T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES FROM dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX WHERE dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO = '202412' AND (T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('宝武共享服务有限公司-法人', '宝武共享服务有限公司-资产合并'))\n), SUMT6200_MONTHLY_TBL AS (\n  SELECT LEFT(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 6) AS V_DATE_, T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES, CAST(SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) AS INTEGER) AS year_num, CAST(SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 5, 2) AS INTEGER) AS month_num, sum(t_ads_fact_wssj_total_index.month_amt) as SUMT6200 FROM dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX WHERE (T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('宝武共享服务有限公司-法人', '宝武共享服务有限公司-资产合并')) AND REPORT_ITEM = 'T6200' GROUP BY V_DATE_, year_num, month_num, COMPANY_INNER_CODE_DES ORDER BY V_DATE_ desc\n), SUMT6200_RECENT_TBL AS (\n  SELECT V_DATE_, year_num, month_num, COMPANY_INNER_CODE_DES, SUMT6200 FROM SUMT6200_MONTHLY_TBL WHERE V_DATE_ = '202412'\n), SUMT6200_TBL AS (\n  SELECT current_month.V_DATE_, current_month.year_num, current_month.month_num, current_month.COMPANY_INNER_CODE_DES, current_month.SUMT6200, previous_month.SUMT6200 as pre_month_SUMT6200, previous_year_month.SUMT6200 as pre_year_month_SUMT6200, (current_month.SUMT6200 - previous_month.SUMT6200) as mom_growth_SUMT6200, (current_month.SUMT6200 - previous_year_month.SUMT6200) as yoy_month_growth_SUMT6200, ((current_month.SUMT6200 - previous_month.SUMT6200) / previous_month.SUMT6200) as mom_growth_rate_SUMT6200, ((current_month.SUMT6200 - previous_year_month.SUMT6200) / previous_year_month.SUMT6200) as yoy_month_growth_rate_SUMT6200 FROM SUMT6200_RECENT_TBL AS current_month LEFT JOIN SUMT6200_MONTHLY_TBL AS previous_month ON (current_month.year_num * 12 + current_month.month_num - 1) = (previous_month.year_num * 12 + previous_month.month_num) AND current_month.COMPANY_INNER_CODE_DES = previous_month.COMPANY_INNER_CODE_DES LEFT JOIN SUMT6200_MONTHLY_TBL AS previous_year_month ON (current_month.year_num - 1) = previous_year_month.year_num AND current_month.month_num = previous_year_month.month_num AND current_month.COMPANY_INNER_CODE_DES = previous_year_month.COMPANY_INNER_CODE_DES\n)\nSELECT DIM_TBL.COMPANY_INNER_CODE_DES, SUMT6200_TBL.SUMT6200, SUMT6200_TBL.mom_growth_SUMT6200, SUMT6200_TBL.yoy_month_growth_SUMT6200 FROM DIM_TBL LEFT JOIN SUMT6200_TBL ON DIM_TBL.COMPANY_INNER_CODE_DES = SUMT6200_TBL.COMPANY_INNER_CODE_DES",
  sceneId: 'fS0U86Sz9l2k3AH7',
  chartType: 'Kpi',
  chartTitle: '图表标题',
  infoTexts: [],
  conversationId: 'eGHMa5O_PztO6OL0gnUx9',
  queryParamsVerified: {
    originalQueryParams: {
      metricNames: ['SUMT6200'],
      groupBys: [],
      where: "COMPANY_INNER_CODE_DES IN ('宝武共享服务有限公司-法人','宝武共享服务有限公司-资产合并')",
      originWhere: "COMPANY_INNER_CODE_DES IN ('宝武共享服务有限公司-法人', '宝武共享服务有限公司-资产合并')",
      orderBys: [],
      limit: null,
      timeQueryParams: {
        timeStartFunction: {
          type: 'specificDate',
          year: 2024,
          month: 12,
          day: 1,
          quarter: null,
        },
        timeEndFunction: {
          type: 'specificDate',
          year: 2024,
          month: 12,
          day: 31,
          quarter: null,
        },
        timeGranularity: 'total',
        timeDimensionName: null,
      },
      isMetricNamesExactMatch: true,
      isWhereExactMatch: null,
      extraInfo: {
        is_sub: false,
        groupbys_with_level: [],
        intent_tags: ['与召回相关', '问指标'],
        askbi: false,
        askdoc: false,
        metric_scores: {
          SUMT6200: 1,
        },
      },
      notExistMetricNames: null,
      notExistGroupBys: null,
      notExistOrderBys: null,
    },
    queryParams: {
      metricNames: ['SUMT6200'],
      groupBys: ['COMPANY_INNER_CODE_DES'],
      where: "COMPANY_INNER_CODE_DES IN ('宝武共享服务有限公司-法人','宝武共享服务有限公司-资产合并')",
      originWhere: "COMPANY_INNER_CODE_DES IN ('宝武共享服务有限公司-法人', '宝武共享服务有限公司-资产合并')",
      orderBys: [],
      limit: null,
      timeQueryParams: {
        timeStartFunction: {
          type: 'specificDate',
          year: 2024,
          month: 12,
          day: 1,
          quarter: null,
        },
        timeEndFunction: {
          type: 'specificDate',
          year: 2024,
          month: 12,
          day: 31,
          quarter: null,
        },
        timeGranularity: 'total',
        timeDimensionName: null,
      },
      isMetricNamesExactMatch: true,
      isWhereExactMatch: null,
      extraInfo: {
        is_sub: false,
        groupbys_with_level: [],
        intent_tags: ['与召回相关', '问指标'],
        askbi: false,
        askdoc: false,
        metric_scores: {
          SUMT6200: 1,
        },
      },
      notExistMetricNames: null,
      notExistGroupBys: null,
      notExistOrderBys: null,
      periodOverPeriods: ['momGrowth', 'yoyMonthGrowth'],
    },
    extraParams: {
      extraMetricNames: [],
      extraGroupBys: [],
      extraOrderBys: [],
    },
  },
  isPartialRow: false,
  partialRowMsg: '',
  isPredict: false,
  traceId: 'frontend_20250218112403_05821',
  taskId: 'yOclkMIvIfsaWwlPHCUzY',
} as unknown as ChatResponseQueryMetric
const errorResult = {
  taskType: 'chat-error',
  ready: false,
  errType: 'NO_DATA_AUTHORITY',
  conversationId: 'eGHMa5O_PztO6OL0gnUx9',
  sceneId: 'baK42VBkvngaDVPe',
  metricNames: ['COSTCW00JD0011MIX'],
  whereCompany: [],
  llmResponse: {
    code: 0,
    data: {
      type: 'query-metric',
      query_metric: {
        groupBys: [],
        metricNames: ['COSTCW00JD0011MIX'],
        isMetricNamesExactMatch: true,
        orderBys: [],
        where: '',
        where_json: {},
        isWhereExactMatch: null,
        limit: null,
        timeQueryParams: {
          timeStartFunction: {
            type: 'specificDate',
            year: 2024,
            month: 12,
            day: 1,
            quarter: null,
          },
          timeEndFunction: {
            type: 'specificDate',
            year: 2024,
            month: 12,
            day: 31,
            quarter: null,
          },
          timeGranularity: 'total',
          timeDimensionName: null,
        },
        notExistMetricNames: null,
        notExistGroupBys: null,
        notExistOrderBys: null,
      },
      query_metric_list: [],
      attribution_analysis: null,
      chit_chat: null,
      meta_result: null,
      calculator: null,
      extra_info: {
        is_sub: false,
        groupbys_with_level: [],
        intent_tags: ['与召回相关', '问指标'],
        askbi: false,
        askdoc: false,
        metric_scores: {
          COSTCW00JD0011MIX: 0.5,
        },
      },
    },
  },
  traceId: 'frontend_20250218112403_05821',
  taskId: 'yOclkMIvIfsaWwlPHCUzY',
} as unknown as ChatResponse
const rightConfidenceResult = {
  ready: true,
  conversationId: 'x9lTmYKXAaPJTWH9VenWB',
  sceneId: 'fS0U86Sz9l2k3AH7',
  taskType: 'metric-exact-match',
  metricNames: [
    'SUMI6401010_0400_03_1324',
    'SUMI6401',
    'SUMT6401',
    'SUMI6401010_0400_03_1326',
    'SUMI6401010_0400_03_12',
    'SUMI6401010_0400_03_10',
    'SUMD6111910',
    'SUMI6401010_0400_03_11',
  ],
  queryParamsVerified: {
    queryParams: {
      metricNames: [
        'SUMI6401010_0400_03_1324',
        'SUMI6401',
        'SUMT6401',
        'SUMI6401010_0400_03_1326',
        'SUMI6401010_0400_03_12',
        'SUMI6401010_0400_03_10',
        'SUMD6111910',
        'SUMI6401010_0400_03_11',
      ],
      externalReportNames: [],
      groupBys: ['COMPANY_INNER_CODE_DES'],
      where: "COMPANY_INNER_CODE_DES = '宝武共享服务有限公司-资产合并'",
      originWhere: "COMPANY_INNER_CODE_DES IN ('宝武共享服务有限公司-法人', '宝武共享服务有限公司-资产合并')",
      orderBys: [],
      limit: null,
      timeQueryParams: {
        timeStartFunction: {
          type: 'specificDate',
          year: 2024,
          month: 1,
          day: 1,
          quarter: null,
        },
        timeEndFunction: {
          type: 'specificDate',
          year: 2024,
          month: 1,
          day: 31,
          quarter: null,
        },
        timeGranularity: 'total',
        timeDimensionName: null,
      },
      isMetricNamesExactMatch: false,
      isWhereExactMatch: false,
      extraInfo: {
        is_sub: false,
        groupbys_with_level: [],
        intent_tags: ['与召回相关', '问指标'],
        askbi: false,
        askdoc: false,
        metric_scores: {
          SUMI6401010_0400_03_1324: 0.2857142857142857,
          SUMI6401: 0.5,
          SUMT6401: 0.4,
          SUMI6401010_0400_03_1326: 0.3333333333333333,
          SUMI6401010_0400_03_12: 0.2857142857142857,
          SUMI6401010_0400_03_10: 0.2857142857142857,
          SUMD6111910: 0.1111111111111111,
          SUMI6401010_0400_03_11: 0.2857142857142857,
        },
      },
      notExistMetricNames: null,
      notExistGroupBys: null,
      notExistOrderBys: null,
    },
    originalQueryParams: {
      metricNames: [
        'SUMI6401010_0400_03_1324',
        'SUMI6401',
        'SUMT6401',
        'SUMI6401010_0400_03_1326',
        'SUMI6401010_0400_03_12',
        'SUMI6401010_0400_03_10',
        'SUMD6111910',
        'SUMI6401010_0400_03_11',
      ],
      externalReportNames: [],
      groupBys: [],
      where: "COMPANY_INNER_CODE_DES IN ('宝武共享服务有限公司-法人','宝武共享服务有限公司-资产合并')",
      originWhere: "COMPANY_INNER_CODE_DES IN ('宝武共享服务有限公司-法人', '宝武共享服务有限公司-资产合并')",
      orderBys: [],
      limit: null,
      timeQueryParams: {
        timeStartFunction: {
          type: 'specificDate',
          year: 2024,
          month: 1,
          day: 1,
          quarter: null,
        },
        timeEndFunction: {
          type: 'specificDate',
          year: 2024,
          month: 1,
          day: 31,
          quarter: null,
        },
        timeGranularity: 'total',
        timeDimensionName: null,
      },
      isMetricNamesExactMatch: false,
      isWhereExactMatch: false,
      extraInfo: {
        is_sub: false,
        groupbys_with_level: [],
        intent_tags: ['与召回相关', '问指标'],
        askbi: false,
        askdoc: false,
        metric_scores: {
          SUMI6401010_0400_03_1324: 0.2857142857142857,
          SUMI6401: 0.5,
          SUMT6401: 0.4,
          SUMI6401010_0400_03_1326: 0.3333333333333333,
          SUMI6401010_0400_03_12: 0.2857142857142857,
          SUMI6401010_0400_03_10: 0.2857142857142857,
          SUMD6111910: 0.1111111111111111,
          SUMI6401010_0400_03_11: 0.2857142857142857,
        },
      },
      notExistMetricNames: null,
      notExistGroupBys: null,
      notExistOrderBys: null,
    },
    extraParams: {
      extraMetricNames: [],
      extraGroupBys: [],
      extraOrderBys: [],
    },
  },
  confidenceOriginData: {
    originWhere: "COMPANY_INNER_CODE_DES IN ('宝武共享服务有限公司-法人','宝武共享服务有限公司-资产合并')",
    isWhereExactMatch: false,
    where: "COMPANY_INNER_CODE_DES = '宝武共享服务有限公司-资产合并'",
  },
  infoTexts: [],
  traceId: 'frontend_20250218131317_12434',
  taskId: '6tWWsHOwkzPw9tSBNq5Vd',
} as unknown as ChatResponse
const rightResultNotExactMatch = {
  ready: true,
  taskType: 'query-metric',
  rows: [
    {
      yoy_month_growth_SUMT6200: '-2431975.990000',
      SUMT6200: '1887105.990000',
      COMPANY_INNER_CODE_DES: '宝武共享服务有限公司-法人',
      mom_growth_SUMT6200: '1462861.210000',
    },
    {
      yoy_month_growth_SUMT6200: '12539392.680000',
      SUMT6200: '21725029.540000',
      COMPANY_INNER_CODE_DES: '宝武共享服务有限公司-资产合并',
      mom_growth_SUMT6200: '20120134.010000',
    },
  ],
  rowsMetadata: [
    {
      type: 'dimension',
      value: {
        name: 'COMPANY_INNER_CODE_DES',
        label: '公司',
        synonyms: ['企业'],
        filterSwitch: true,
        description: '合并账套关联内码',
        expr: 'T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES',
        type: 'categorical',
        typeParams: {},
        values: [],
      },
    },
    {
      type: 'metric',
      value: {
        id: 'PS2724cd62-0803-479d-8651-dee2cbbc48ce',
        name: 'SUMT6200',
        label: '利润总额',
        synonyms: ['实现利润', '挣钱总计', '挣了多少钱', '亏损', '盈利', '利润'],
        description: '利润总额',
        type: 'simple',
        formatTemplate: '.2f元',
        typeParams: {
          measure: 'MONTH_AMT',
        },
        filter: "REPORT_ITEM = 'T6200'",
        rank: 10,
        category: '常用指标%99',
        keypoint: false,
        createByMeasure: false,
        isCumulative: true,
        updatedAt: '2024-09-21T17:14:02.017Z',
      },
    },
    {
      type: 'metric',
      value: {
        id: 'B5GP4gz9VHsO_PSgi8EHV',
        name: 'mom_growth_SUMT6200',
        label: '利润总额环比增长额',
        synonyms: [],
        type: 'periodOverPeriod',
        formatTemplate: '.2f元',
        typeParams: {
          type: 'momGrowth',
          metric: 'SUMT6200',
        },
      },
    },
    {
      type: 'metric',
      value: {
        id: 'CcKMILXhcntau4OZXO_iw',
        name: 'yoy_month_growth_SUMT6200',
        label: '利润总额同比增长额',
        synonyms: [],
        type: 'periodOverPeriod',
        formatTemplate: '.2f元',
        typeParams: {
          type: 'yoyMonthGrowth',
          metric: 'SUMT6200',
        },
      },
    },
  ],
  recommendChartTypes: ['Kpi', 'SimpleTable'],
  sql: "WITH DIM_TBL AS (\n  SELECT DISTINCT T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES FROM dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX WHERE dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO = '202412' AND (T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('宝武共享服务有限公司-法人', '宝武共享服务有限公司-资产合并'))\n), SUMT6200_MONTHLY_TBL AS (\n  SELECT LEFT(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 6) AS V_DATE_, T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES, CAST(SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) AS INTEGER) AS year_num, CAST(SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 5, 2) AS INTEGER) AS month_num, sum(t_ads_fact_wssj_total_index.month_amt) as SUMT6200 FROM dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX WHERE (T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('宝武共享服务有限公司-法人', '宝武共享服务有限公司-资产合并')) AND REPORT_ITEM = 'T6200' GROUP BY V_DATE_, year_num, month_num, COMPANY_INNER_CODE_DES ORDER BY V_DATE_ desc\n), SUMT6200_RECENT_TBL AS (\n  SELECT V_DATE_, year_num, month_num, COMPANY_INNER_CODE_DES, SUMT6200 FROM SUMT6200_MONTHLY_TBL WHERE V_DATE_ = '202412'\n), SUMT6200_TBL AS (\n  SELECT current_month.V_DATE_, current_month.year_num, current_month.month_num, current_month.COMPANY_INNER_CODE_DES, current_month.SUMT6200, previous_month.SUMT6200 as pre_month_SUMT6200, previous_year_month.SUMT6200 as pre_year_month_SUMT6200, (current_month.SUMT6200 - previous_month.SUMT6200) as mom_growth_SUMT6200, (current_month.SUMT6200 - previous_year_month.SUMT6200) as yoy_month_growth_SUMT6200, ((current_month.SUMT6200 - previous_month.SUMT6200) / previous_month.SUMT6200) as mom_growth_rate_SUMT6200, ((current_month.SUMT6200 - previous_year_month.SUMT6200) / previous_year_month.SUMT6200) as yoy_month_growth_rate_SUMT6200 FROM SUMT6200_RECENT_TBL AS current_month LEFT JOIN SUMT6200_MONTHLY_TBL AS previous_month ON (current_month.year_num * 12 + current_month.month_num - 1) = (previous_month.year_num * 12 + previous_month.month_num) AND current_month.COMPANY_INNER_CODE_DES = previous_month.COMPANY_INNER_CODE_DES LEFT JOIN SUMT6200_MONTHLY_TBL AS previous_year_month ON (current_month.year_num - 1) = previous_year_month.year_num AND current_month.month_num = previous_year_month.month_num AND current_month.COMPANY_INNER_CODE_DES = previous_year_month.COMPANY_INNER_CODE_DES\n)\nSELECT DIM_TBL.COMPANY_INNER_CODE_DES, SUMT6200_TBL.SUMT6200, SUMT6200_TBL.mom_growth_SUMT6200, SUMT6200_TBL.yoy_month_growth_SUMT6200 FROM DIM_TBL LEFT JOIN SUMT6200_TBL ON DIM_TBL.COMPANY_INNER_CODE_DES = SUMT6200_TBL.COMPANY_INNER_CODE_DES",
  sceneId: 'fS0U86Sz9l2k3AH7',
  chartType: 'Kpi',
  chartTitle: '图表标题',
  infoTexts: [],
  conversationId: 'eGHMa5O_PztO6OL0gnUx9',
  queryParamsVerified: {
    originalQueryParams: {
      metricNames: ['SUMT6200'],
      groupBys: [],
      where: "COMPANY_INNER_CODE_DES IN ('宝武共享服务有限公司-法人','宝武共享服务有限公司-资产合并')",
      originWhere: "COMPANY_INNER_CODE_DES IN ('宝武共享服务有限公司-法人', '宝武共享服务有限公司-资产合并')",
      orderBys: [],
      limit: null,
      timeQueryParams: {
        timeStartFunction: {
          type: 'specificDate',
          year: 2024,
          month: 12,
          day: 1,
          quarter: null,
        },
        timeEndFunction: {
          type: 'specificDate',
          year: 2024,
          month: 12,
          day: 31,
          quarter: null,
        },
        timeGranularity: 'total',
        timeDimensionName: null,
      },
      isMetricNamesExactMatch: true,
      isWhereExactMatch: null,
      extraInfo: {
        is_sub: false,
        groupbys_with_level: [],
        intent_tags: ['与召回相关', '问指标'],
        askbi: false,
        askdoc: false,
        metric_scores: {
          SUMT7200: 0.8,
        },
      },
      notExistMetricNames: null,
      notExistGroupBys: null,
      notExistOrderBys: null,
    },
    queryParams: {
      metricNames: ['SUMT6200'],
      groupBys: ['COMPANY_INNER_CODE_DES'],
      where: "COMPANY_INNER_CODE_DES IN ('宝武共享服务有限公司-法人','宝武共享服务有限公司-资产合并')",
      originWhere: "COMPANY_INNER_CODE_DES IN ('宝武共享服务有限公司-法人', '宝武共享服务有限公司-资产合并')",
      orderBys: [],
      limit: null,
      timeQueryParams: {
        timeStartFunction: {
          type: 'specificDate',
          year: 2024,
          month: 12,
          day: 1,
          quarter: null,
        },
        timeEndFunction: {
          type: 'specificDate',
          year: 2024,
          month: 12,
          day: 31,
          quarter: null,
        },
        timeGranularity: 'total',
        timeDimensionName: null,
      },
      isMetricNamesExactMatch: true,
      isWhereExactMatch: null,
      extraInfo: {
        is_sub: false,
        groupbys_with_level: [],
        intent_tags: ['与召回相关', '问指标'],
        askbi: false,
        askdoc: false,
        metric_scores: {
          SUMT7200: 0.8,
        },
      },
      notExistMetricNames: null,
      notExistGroupBys: null,
      notExistOrderBys: null,
      periodOverPeriods: ['momGrowth', 'yoyMonthGrowth'],
    },
    extraParams: {
      extraMetricNames: [],
      extraGroupBys: [],
      extraOrderBys: [],
    },
  },
  isPartialRow: false,
  partialRowMsg: '',
  isPredict: false,
  traceId: 'frontend_20250218112403_05821',
  taskId: 'yOclkMIvIfsaWwlPHCUzY',
} as unknown as ChatResponseQueryMetric

describe('processMultiSceneResults simple case', () => {
  it('1. Only one scene result, return directly', async () => {
    const resultArray = [rightResult, errorResult, rightConfidenceResult]

    const result1 = processMultiSceneResults([resultArray[0]])
    const result2 = processMultiSceneResults([resultArray[1]])
    const result3 = processMultiSceneResults([resultArray[2]])

    expect(result1).toEqual([resultArray[0]])
    expect(result2).toEqual([resultArray[1]])
    expect(result3).toEqual([resultArray[2]])
  })

  it('2. Contains one correct + incorrect results. Only one correct results are shown.', async () => {
    const resultArray1 = [rightResult, errorResult, errorResult]
    const resultArray2 = [rightResult, errorResult]
    const resultArray3 = [rightConfidenceResult, errorResult]

    // 过滤后的正确结果
    const expected1 = [rightResult]
    const expected2 = [rightResult]
    const expected3 = [rightConfidenceResult]

    const result1 = processMultiSceneResults(resultArray1)
    const result2 = processMultiSceneResults(resultArray2)
    const result3 = processMultiSceneResults(resultArray3)

    expect(result1).toEqual(expected1)
    expect(result2).toEqual(expected2)
    expect(result3).toEqual(expected3)
  })

  it('5. Contains results from multiple scenarios. All are confidence levels. Return directly.', async () => {
    const resultArray1 = [rightConfidenceResult, rightConfidenceResult, errorResult, errorResult]
    const resultArray2 = [rightConfidenceResult, rightConfidenceResult, errorResult]
    const resultArray3 = [rightConfidenceResult, rightConfidenceResult]
    const resultArray4 = [rightResult, rightResult, errorResult, errorResult]
    const resultArray5 = [rightResult, rightResult, errorResult]
    const resultArray6 = [rightResult, rightResult]

    const modifiedExactMatch = {
      ready: rightResult.ready,
      taskType: 'metric-exact-match',
      sceneId: rightResult.sceneId,
      conversationId: rightResult.conversationId,
      metricNames: rightResult.queryParamsVerified.originalQueryParams.metricNames,
      infoTexts: [],
      queryParamsVerified: {
        ...rightResult.queryParamsVerified,
        originalQueryParams: {
          ...rightResult.queryParamsVerified.originalQueryParams,
          isMetricNamesExactMatch: false,
          isWhereExactMatch: false,
        },
        queryParams: {
          ...rightResult.queryParamsVerified.queryParams,
          isMetricNamesExactMatch: false,
          isWhereExactMatch: false,
        },
      },
    }
    // 过滤掉错误结果后，预期返回的正确数据
    const expected1 = [rightConfidenceResult, rightConfidenceResult]
    const expected2 = [modifiedExactMatch, modifiedExactMatch]

    const result1 = processMultiSceneResults(resultArray1)
    const result2 = processMultiSceneResults(resultArray2)
    const result3 = processMultiSceneResults(resultArray3)
    const result4 = processMultiSceneResults(resultArray4)
    const result5 = processMultiSceneResults(resultArray5)
    const result6 = processMultiSceneResults(resultArray6)

    expect(result1).toEqual(expected1)
    expect(result2).toEqual(expected1)
    expect(result3).toEqual(expected1)
    expect(result4).toEqual(expected2)
    expect(result5).toEqual(expected2)
    expect(result6).toEqual(expected2)
  })
})

describe('processMultiSceneResults corner case', () => {
  it('3.1 One exact, one confidence, return exact', async () => {
    const resultArray = [rightResult, rightConfidenceResult, errorResult]

    const processedResults = processMultiSceneResults(resultArray)

    expect(processedResults).toEqual([rightResult])
  })

  it('3.2 One not exact, one confidence, return rewritten exact & confidence', async () => {
    const resultArray = [rightResultNotExactMatch, rightConfidenceResult, errorResult]

    const modifiedExactMatch = {
      ready: rightResultNotExactMatch.ready,
      taskType: 'metric-exact-match',
      sceneId: rightResultNotExactMatch.sceneId,
      conversationId: rightResultNotExactMatch.conversationId,
      metricNames: rightResultNotExactMatch.queryParamsVerified.originalQueryParams.metricNames,
      infoTexts: [],
      queryParamsVerified: {
        ...rightResultNotExactMatch.queryParamsVerified,
        originalQueryParams: {
          ...rightResultNotExactMatch.queryParamsVerified.originalQueryParams,
          isMetricNamesExactMatch: false,
          isWhereExactMatch: false,
        },
        queryParams: {
          ...rightResultNotExactMatch.queryParamsVerified.queryParams,
          isMetricNamesExactMatch: false,
          isWhereExactMatch: false,
        },
      },
    }

    const processedResults = processMultiSceneResults(resultArray)

    expect(processedResults).toEqual([modifiedExactMatch, rightConfidenceResult])
  })

  it('4.1 Two exact, return two confidence', async () => {
    const resultArray = [rightResult, rightResult, errorResult]
    const modifiedExactMatch = {
      ready: rightResult.ready,
      taskType: 'metric-exact-match',
      sceneId: rightResult.sceneId,
      conversationId: rightResult.conversationId,
      metricNames: rightResult.queryParamsVerified.originalQueryParams.metricNames,
      infoTexts: [],
      queryParamsVerified: {
        ...rightResult.queryParamsVerified,
        originalQueryParams: {
          ...rightResult.queryParamsVerified.originalQueryParams,
          isMetricNamesExactMatch: false,
          isWhereExactMatch: false,
        },
        queryParams: {
          ...rightResult.queryParamsVerified.queryParams,
          isMetricNamesExactMatch: false,
          isWhereExactMatch: false,
        },
      },
    }
    const processedResults = processMultiSceneResults(resultArray)
    expect(processedResults).toEqual([modifiedExactMatch, modifiedExactMatch])
  })

  it('4.1 Two not exact, return rewrite exact', async () => {
    const resultArray = [rightResultNotExactMatch, rightResultNotExactMatch, errorResult]
    const processedResults = processMultiSceneResults(resultArray)
    const modifiedExactMatch = {
      ready: rightResultNotExactMatch.ready,
      taskType: 'metric-exact-match',
      sceneId: rightResultNotExactMatch.sceneId,
      conversationId: rightResultNotExactMatch.conversationId,
      metricNames: rightResultNotExactMatch.queryParamsVerified.originalQueryParams.metricNames,
      infoTexts: [],
      queryParamsVerified: {
        ...rightResultNotExactMatch.queryParamsVerified,
        originalQueryParams: {
          ...rightResultNotExactMatch.queryParamsVerified.originalQueryParams,
          isMetricNamesExactMatch: false,
          isWhereExactMatch: false,
        },
        queryParams: {
          ...rightResultNotExactMatch.queryParamsVerified.queryParams,
          isMetricNamesExactMatch: false,
          isWhereExactMatch: false,
        },
      },
    }
    expect(processedResults).toEqual([modifiedExactMatch, modifiedExactMatch])
  })
})
