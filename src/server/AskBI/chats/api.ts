/**
 * 文字生成图表问答的 API 接口定义
 */
import express, { Router, Request, Response, Handler } from 'express'
import { CronJob } from 'cron'
import { nanoid } from 'nanoid'
import axios from 'axios'
import chalk from 'chalk'
import { cloneDeep } from 'lodash'
import {
  Message,
  ChatResponse,
  LlmType,
  ChatResponseQueryMetric,
  ChatResponseAttributionMetricAnalysis,
  ChatResponseErrorTypes,
  isMetric,
  ChatProgress,
  ChatResponseErrorMap,
  ChatResponseError,
} from '@shared/common-types'
import { setAxiosDefaults } from '@shared/config'
import { MULTI_SCENE_CHAT_MOCK_SCENE_ID, TAG_PROMPT_TEST, DEFAULT_TIMEOUT } from '@shared/constants'
import { PROCESS_ENV } from 'src/server/server-constants'
import { prisma } from 'src/server/dao/db'
import { CategoricalDimension, MetricWithPeriodOverPeriod, QueryParamsVerified } from 'src/shared/metric-types'
import MetricConfig from 'src/server/MetricStore/metric-config'
import { paramsExtractChat, projectParamsExtractChat } from 'src/server/ai/askbi-client'
import { saveAskBotBusinessLogs } from 'src/server/winston-log'
import { isBaoWu, isBaoWuFinancial } from 'src/shared/baowu-share-utils'
import { generateMetricFeaturePrompt } from '../../ai/prompts'
import { ALL_LLMS, asyncResponseWrapper } from '../../utils'
import { getFollowUpQuestionParams, updateChatLlmResponse, updateConverChatResponse } from '../conver_chats/utils'
import { recordMetricFrequencyFromResult } from '../metrics/dao'
import { getProjectIdBySceneId } from '../datasets/dao'
import { convertBaowuTimeParamsAndGroupBys } from '../../MetricStore/metric2sql/customize/baowu'
import { chatMetrics, generateResponseOfQueryMetric } from './chat-pipelines'
import { generateFollowUpMessageForBaowu, generateMessagesFromConversation } from './dao'
import { createDocReport, getDocReportColumnClassify } from './doc-report'
import { processMultiSceneResults } from './utils'

const router: Router = express.Router()

export function getRandomQuestions(allQuestions: string[], num: number) {
  const selectedQuestions = []
  const availableQuestions = [...allQuestions]

  while (selectedQuestions.length < num && availableQuestions.length > 0) {
    const randomIndex = Math.floor(Math.random() * availableQuestions.length)
    const selectedQuestion = availableQuestions.splice(randomIndex, 1)[0]
    selectedQuestions.push(selectedQuestion)
  }
  return selectedQuestions
}

router.get('/suggestions', async (req: Request, res: Response) => {
  console.info('Get recommend questions with query:', req.query)
  const sceneId = req.query.sceneId?.toString()
  const tableName = req.query.tableName?.toString()
  if (!sceneId) {
    return res.json({ code: 500, msg: '缺少 sceneId 参数' })
  }

  // 北京人寿项目，客户提供的推荐问题列表
  const isChinaLife = tableName
    ? tableName.includes('vt_beijing_renshou_baodan') || tableName.includes('vt_renshou_new')
    : false
  if (isChinaLife) {
    return res.json({
      code: 0,
      data: [
        '每个月度，各个分公司标准保费的收入情况',
        '每个月度，各个分公司规模保费的收入情况',
        '每个月度，各个渠道标准保费的收入情况',
        '每个月度，各个渠道规模保费的收入情况',
        '北京分公司各个渠道1月份的保费收入',
        '北京分公司各个渠道1月份的标准保费收入',
        '个险渠道，寿险产品中，各类产品的保费收入情况',
        '中介渠道销售最好的产品是哪一类，在全年的占比情况如何',
        '全年保费收入最高的分公司是哪一家',
      ],
    })
  }

  // 针对反诈场景做判断，如果包含 vt_zhapianshibie_dateset 或者 vt_anti_fraud_data 关键字，则不推荐 今年${getMetricLabel()}变化的原因 这个问题
  const isAntiFraud = tableName
    ? tableName.includes('vt_zhapianshibie_dateset') || tableName.includes('vt_anti_fraud')
    : false

  try {
    let dimensionIndex = 0
    let metricIndex = 0
    let dimensionValues: string[] = []
    let dimensionValueIndex = 0
    const metricConfig = await MetricConfig.createBySceneId(sceneId)
    const { allMetrics, allMeasures, allDimensions, timeDimensionDatum } = metricConfig

    if (allDimensions?.length) {
      const dimension = allDimensions.find((item) => item.type === 'categorical' && item.values) as CategoricalDimension
      if (dimension?.values?.length) {
        dimensionValues = dimension.values
      }
    }
    const getMetricLabel = () => (allMetrics.length ? allMetrics[metricIndex++ % allMetrics.length]?.label || '' : '')
    let questionsList = [
      '我想看下有哪些数据',
      `各${allDimensions[dimensionIndex++ % allDimensions.length]?.label || ''}维度近一年的${getMetricLabel()}趋势`,
      `最近三年${getMetricLabel()}、${getMetricLabel()}？`,
      `各${allDimensions[dimensionIndex++ % allDimensions.length]?.label || ''}维度近三年的${getMetricLabel()}、${getMetricLabel()}变化？`,
      `去年${getMetricLabel()}趋势`,
    ]

    if (!isAntiFraud) {
      questionsList.splice(3, 0, `今年${getMetricLabel()}变化的原因`)
    }

    if (allMeasures.length === 1 && allDimensions.length === 0) {
      questionsList = [
        '我想看下有哪些数据',
        `近一年的${getMetricLabel()}趋势`,
        `今年2月${getMetricLabel()}变化的原因`,
        `今年${getMetricLabel()}相比去年变化的原因`,
        `最近三年${getMetricLabel()}变化趋势`,
        `去年上半年${getMetricLabel()}情况`,
      ]
    } else if (allMeasures.length === 1 && allDimensions.length === 1) {
      questionsList = [
        '我想看下有哪些数据',
        `各${allDimensions[0].label}维度近一年的${getMetricLabel()}趋势`,
        `最近三年${getMetricLabel()}？`,
        `今年2月${getMetricLabel()}变化的原因`,
        `今年${getMetricLabel()}相比去年变化的原因`,
        `去年上半年${getMetricLabel()}情况`,
      ]
    }
    if (!timeDimensionDatum) {
      if (allMeasures.length === 1 && allDimensions.length === 0) {
        questionsList = [
          '我想看下有哪些数据',
          `${getMetricLabel()}的情况`,
          `${getMetricLabel()}数据表现好吗？`,
          `${getMetricLabel()}数据变化的原因`,
          `${getMetricLabel()}为什么表现比较差`,
          `今年${getMetricLabel()}情况`,
        ]
      } else if (allMeasures.length === 1 && allDimensions.length === 1) {
        questionsList = [
          '我想看下有哪些数据',
          `各${allDimensions[0].label}的${getMetricLabel()}的趋势`,
          `${allDimensions[0].label}维度中${dimensionValues[dimensionValueIndex++ % dimensionValues.length] || ''}的${getMetricLabel()}`,
          `${allDimensions[0].label}中${getMetricLabel()}前三是？`,
          `各${allDimensions[0].label}中${getMetricLabel()}最高的是？`,
          `各${allDimensions[0].label}中${getMetricLabel()}最低的是？`,
        ]
      } else {
        questionsList = [
          '我想看下有哪些数据',
          `各${allDimensions[0].label}的${getMetricLabel()}`,
          `${allDimensions[0].label}维度中${dimensionValues[dimensionValueIndex++ % dimensionValues.length] || ''}的${getMetricLabel()}`,
          `${allDimensions[0].label}维度中${dimensionValues[dimensionValueIndex++ % dimensionValues.length] || ''}的${getMetricLabel()}`,
          `各${allDimensions[0].label}中${getMetricLabel()}最高的是？`,
          `各${allDimensions[0].label}中${getMetricLabel()}最低的是？`,
        ]
      }
    }

    return res.json({
      code: 0,
      data: questionsList,
    })
  } catch (error) {
    console.error('获取推荐问题失败 ' + (error as Error)?.message)
    return res.json({ code: 0, data: [] })
  }
})

router.post('/insight-streaming', async (req: Request, res: Response) => {
  console.info('POST /api/chats/insight-streaming with body: ', req.body)
  try {
    const traceId = req.header('traceId') as string
    const username = req.user?.username || ''
    const host = req.header('Host') || ''
    const readyChatResponse = JSON.parse(req.body.responseData) as
      | ChatResponseQueryMetric
      | ChatResponseAttributionMetricAnalysis
    const sceneId = req.body.sceneId
    const projectId = await getProjectIdBySceneId(sceneId)
    const paramsExtractApi = req.body.currentParamsExtractApi
    const baseUrl = paramsExtractApi || PROCESS_ENV.PARAMS_EXTRACT_URL_HOST
    const chartInsightUrl = `${baseUrl}/api/v1/chart_insight`
    const modelDesc = await prisma.semanticScene.findFirst({ where: { id: sceneId } })

    const metricConfig = await MetricConfig.createBySceneId(sceneId)
    const metricNames =
      readyChatResponse.taskType === 'query-metric'
        ? readyChatResponse.queryParamsVerified?.queryParams.metricNames || []
        : []

    const metricFeaturePrompt = generateMetricFeaturePrompt(
      metricConfig.allMetrics,
      metricConfig.allDimensions,
      metricNames,
    )

    // 设置响应头
    res.setHeader('Content-Type', 'text/event-stream')
    res.setHeader('Cache-Control', 'no-cache')
    res.setHeader('Connection', 'keep-alive')

    const llmType: LlmType = req.body.llmType

    const chartTitle = readyChatResponse.chartTitle
    const chartType = readyChatResponse.chartType
    const rowsMetadata = readyChatResponse.taskType === 'query-metric' ? readyChatResponse.rowsMetadata : []
    const metrics: MetricWithPeriodOverPeriod[] = rowsMetadata.filter(isMetric).map((item) => item.value)
    const rows =
      readyChatResponse.taskType === 'query-metric'
        ? readyChatResponse.rows
            .sort((a, b) => parseFloat(b[metrics[0].name]) - parseFloat(a[metrics[0].name]))
            .slice(0, 100)
        : readyChatResponse.rows
    const sql = readyChatResponse.taskType === 'query-metric' ? readyChatResponse.sql : ''
    const businessBackground = modelDesc?.description

    const requestBody = {
      model_id: sceneId,
      model_type: llmType,
      metric_feature_prompt: metricFeaturePrompt,
      chart_title: chartTitle,
      chart_type: chartType,
      rows: rows,
      sql: sql,
      business_background: businessBackground,
    }

    console.info(chalk.yellow('Insight Report URL: ', chartInsightUrl))
    console.info('Insight Report Body: ', requestBody)

    const startTime = Date.now()
    await axios
      .post(chartInsightUrl, requestBody, {
        responseType: 'stream',
        headers: { 'Content-Type': 'application/json' },
      })
      .then((response) => {
        let result = ''
        response.data.on('data', (chunk: Buffer) => {
          const chunkStr = chunk.toString('utf8')
          result += chunkStr
          process.stdout.write(chunkStr)
          res.write(JSON.stringify({ code: 0, data: chunkStr }))
        })

        response.data.on('end', () => {
          console.info('分析报告处理完成：', result)
          saveAskBotBusinessLogs({
            serviceType: 'web_service',
            moduleType: 'chat-insight-streaming',
            host,
            username,
            traceId,
            startTime,
            input: requestBody,
            output: result,
            resultCode: 0,
            semanticProjectId: projectId,
            semanticSceneId: sceneId,
            debug: { url: chartInsightUrl, payload: requestBody, method: 'POST', response: result },
            req,
          })
          res.end()
        })
      })
      .catch((error) => {
        console.error('chatStreaming failed in generate insight report: ', error)
        saveAskBotBusinessLogs({
          serviceType: 'web_service',
          moduleType: 'chat-insight-streaming',
          host,
          username,
          traceId,
          startTime,
          input: requestBody,
          output: error,
          resultCode: 500,
          semanticProjectId: projectId,
          semanticSceneId: sceneId,
          debug: { url: chartInsightUrl, payload: requestBody, method: 'POST', response: error },
          req,
        })
        res.write(JSON.stringify({ code: 500, data: '生成分析报告失败' }))
        res.end()
      })
  } catch (error) {
    console.error('Generate insight report failed: ', error)
  }
})

router.post('/attr-analysis-insight-streaming', async (req: Request, res: Response) => {
  console.info('POST /api/chats/attr-analysis-insight-streaming with body: ', req.body)
  try {
    const readyChatResponse = JSON.parse(req.body.responseData) as ChatResponseAttributionMetricAnalysis
    const sceneId = req.body.sceneId
    // 设置响应头
    res.setHeader('Content-Type', 'text/event-stream')
    res.setHeader('Cache-Control', 'no-cache')
    res.setHeader('Connection', 'keep-alive')

    const llmType: LlmType = req.body.llmType
    const paramsExtractApi = req.body.currentParamsExtractApi
    const baseUrl = paramsExtractApi || PROCESS_ENV.PARAMS_EXTRACT_URL_HOST
    const attrAnalysisReportUrl = `${baseUrl}/api/v1/attr_analysis_report`

    const bodyForPython = {
      model_id: sceneId,
      model_type: llmType,
      report_doc_prompt: readyChatResponse.rows[0].reportPrompt,
    }

    console.info(chalk.yellow('Attr Analysis Report URL: ', attrAnalysisReportUrl))
    console.info('Attr Analysis Report Body: ', bodyForPython)

    await axios
      .post(attrAnalysisReportUrl, bodyForPython, {
        responseType: 'stream',
        headers: { 'Content-Type': 'application/json' },
      })
      .then((response) => {
        let result = ''
        response.data.on('data', (chunk: Buffer) => {
          const chunkStr = chunk.toString('utf8')
          result += chunkStr
          process.stdout.write(chunkStr)
          res.write(JSON.stringify({ code: 0, data: chunkStr }))
        })

        response.data.on('end', () => {
          console.info('归因分析报告处理完成：', result)
          res.end()
        })
      })
      .catch((error) => {
        console.error('chatStreaming failed in generate insight report: ', error)
        res.write(JSON.stringify({ code: 500, data: '生成归因分析报告失败' }))
        res.end()
      })
  } catch (error) {
    console.error('Generate attribution analysis report failed: ', error)
  }
})

// TODO: 和 metric-query 合并代码
/** 用于图表编辑页面，指标详情页面的展示 */
router.post('/query-metric', async (req: Request, res: Response) => {
  console.info('POST /api/chats/query-metric with body: ', req.body)
  // 获取到header中的traceid并设置到axios请求头中
  const traceId = req.header('traceId') as string
  setAxiosDefaults(traceId)
  const username = req.user?.username || ''
  const userIp = req.header('X-Real-IP') || req.socket.remoteAddress
  const host = req.header('Host')

  console.info('Request and user info:', 'username =', username, 'user ip =', userIp, 'server host =', host)

  const sceneId = req.body.sceneId
  const projectId = await getProjectIdBySceneId(sceneId)
  if (sceneId == null) {
    return res.json({ code: 500, msg: '缺少 sceneId 参数' })
  }
  let conversationId = req.body.conversationId
  if (conversationId == null) {
    conversationId = nanoid()
  }

  try {
    const queryParamsVerified = req.body.queryParamsVerified as QueryParamsVerified
    const metricConfig = await MetricConfig.createBySceneId(sceneId)
    const resultForFrontend = await generateResponseOfQueryMetric({
      sceneId,
      projectId,
      metricConfig,
      verifiedMetricParams: queryParamsVerified,
      conversationId: 'CONVERSATION_ID_PLACEHOLDER',
      message: '数据图表',
      infoTexts: [],
      username,
      traceId,
      host: host || '',
    })
    return res.json(resultForFrontend)
  } catch (e) {
    console.error('指标查询失败', e)
    return res.json({ code: 500, msg: (e as Error)?.message || '未知错误' })
  }
})

router.post('/metric-query', async (req: Request, res: Response) => {
  console.info('POST /api/chats/metric-query with body: ', req.body)
  // 获取到header中的traceid并设置到axios请求头中
  const traceId = req.header('traceId') as string
  setAxiosDefaults(traceId)
  const username = req.user?.username || ''
  const userIp = req.header('X-Real-IP') || req.socket.remoteAddress
  const host = req.header('Host')

  console.info('Request and user info:', 'username =', username, 'user ip =', userIp, 'server host =', host)

  const sceneId = req.body.sceneId
  const projectId = await getProjectIdBySceneId(sceneId)
  const chatId = req.body.chatId
  if (sceneId == null) {
    return res.json({ code: 500, msg: '缺少 sceneId 参数' })
  }
  console.info('/chat api sceneId =', sceneId)
  // 对于新的对话没有 conversationId，在服务端返回一个 nanoid 作为 conversationId。
  let conversationId = req.body.conversationId
  if (conversationId == null) {
    conversationId = nanoid()
  }
  const llmType: LlmType = req.body.llmType

  if (
    !ALL_LLMS.some((llm) => {
      return llm.type === llmType && !llm.disable
    })
  ) {
    const result = {
      taskType: 'chat-error',
      success: false,
      errType: ChatResponseErrorTypes.LLM_ERROR,
      errMsg: '模型不存在',
      ready: false,
      unreadyReason: '模型不存在',
      conversationId,
    }
    return res.json(result)
  }

  try {
    const queryParamsVerified = req.body.queryParamsVerified as QueryParamsVerified
    let queryParams = queryParamsVerified.queryParams

    const metricConfig = await MetricConfig.createBySceneId(sceneId)
    if (isBaoWuFinancial(metricConfig.name) && req.body?.customize) {
      console.info('Rewrite timeQueryParams by customize.')
      queryParams = convertBaowuTimeParamsAndGroupBys(
        { ...queryParams, timeQueryParams: queryParamsVerified.originalQueryParams.timeQueryParams },
        metricConfig,
      )
    }

    const resultForFrontend = await generateResponseOfQueryMetric({
      sceneId,
      projectId,
      metricConfig,
      verifiedMetricParams: { ...queryParamsVerified, queryParams },
      conversationId,
      message: '数据图表',
      infoTexts: req.body.infoTexts || [],
      username,
      traceId,
      host: host || '',
    })
    if (chatId) {
      updateConverChatResponse(chatId, traceId, [resultForFrontend])
      updateChatLlmResponse(
        chatId,
        sceneId,
        {},
        { metricNames: queryParams.metricNames, where: queryParams.where || '' },
      )
    }
    return res.json(resultForFrontend)
  } catch (e) {
    console.error('指标查询失败', e)
    const chatResponseError: ChatResponseError = {
      taskType: 'chat-error',
      errType: ChatResponseErrorTypes.METRICS_NOT_EXIST,
      ready: false,
      unreadyReason: (e as Error)?.message || '您询问的指标不存在',
      conversationId,
      sceneId,
    }
    res.json(chatResponseError)
  }
})

async function genMessages({
  parentId,
  message,
  metricConfig,
  conversationId,
  sceneId,
}: {
  parentId: string | null
  message: string
  metricConfig: MetricConfig
  conversationId: string
  sceneId: string
}) {
  const userQuestion: string = overWriteMessage(message, metricConfig)
  let messages: Message[]
  if (parentId == null) {
    messages = [{ role: 'user', content: userQuestion }]
  } else {
    messages = await generateMessagesFromConversation(conversationId, parentId, sceneId, userQuestion)
  }

  // [3]宝武场景 添加意图识别追问
  if (isBaoWu(metricConfig.name)) {
    messages = await generateFollowUpMessageForBaowu(conversationId, userQuestion)
  }
  return messages
}

/**
 * 通用会话处理函数，对原有的chat函数逻辑进行了抽离复用，用于：
 * 1. chat接口，原有的会话接口，需要校验登陆
 * 2. chat-nocheck接口，为了解决在prompt-studio中需要无登陆态会话，做了特殊处理
 */
function createChatHandler({
  url,
  beforeHandler,
  defaultUsername = '',
}: {
  url: string
  // return true表示需要终止后续操作
  beforeHandler?: (...args: Parameters<Handler>) => Promise<ReturnType<Handler> | boolean> | void | boolean
  defaultUsername?: string
}) {
  const handler: Handler = async (req, res, next) => {
    if ((await beforeHandler?.(req, res, next)) === true) {
      return
    }
    console.info(chalk.cyan(`POST ${url} with request body: `), req.body)
    // 获取到header中的traceid并设置到axios请求头中
    const traceId = req.header('traceId') as string
    setAxiosDefaults(traceId)
    const username = req.user?.username || defaultUsername
    const userIp = req.header('X-Real-IP') || req.socket.remoteAddress
    const host = req.header('Host') || ''

    console.info(`${url} Request and user info: username = ${username}, user ip = ${userIp}, server host = ${host}.`)

    const message: string = req.body.message
    const chatId = req.body.chatId
    const parentId: string | null = req.body.parentId
    // 后续启用默认追问功能
    const enableFollowUpQuestion = req.body.enableFollowUpQuestion
    const enableMetricExactMatch = req.body.enableMetricExactMatch
    // 提参接口api地址
    const currentParamsExtractApi = req.body.currentParamsExtractApi

    // 宝武权限相关数据
    const authData = { userId: username }

    const sceneId = req.body.sceneId
    const projectId = req.body.projectId
    const taskId = req.body.taskId
    if (sceneId == null) {
      return res.json({ code: 500, msg: '缺少 sceneId 参数' })
    }
    // 对于新的对话没有 conversationId，在服务端返回一个 nanoid 作为 conversationId。
    let conversationId = req.body.conversationId
    if (conversationId == null) {
      conversationId = nanoid()
    }

    const llmType: LlmType = req.body.llmType

    if (
      !ALL_LLMS.some((llm) => {
        return llm.type === llmType && !llm.disable
      })
    ) {
      const result = {
        taskType: 'chat-error',
        errType: ChatResponseErrorTypes.LLM_ERROR,
        ready: false,
        unreadyReason: '模型不存在',
        conversationId,
      }
      return res.json(result)
    }

    try {
      // 耗时 0.1 ~ 1.7s，需要添加缓存
      const metricConfig = await MetricConfig.createBySceneId(sceneId)
      // 用户问题重写
      const userQuestion: string = overWriteMessage(message, metricConfig)
      // [1]默认追问功能，会自动携带上一轮python提参结果 history_params_extract_data
      const followUpQuestionParams = await getFollowUpQuestionParams(conversationId, chatId, enableFollowUpQuestion)

      // [2]用户前端点击按钮追问 会带上历史的会话 和SQL查数结果
      const messages = await genMessages({ parentId, message, metricConfig, conversationId, sceneId })

      const response = await paramsExtractChat(
        messages,
        sceneId,
        projectId,
        llmType,
        enableMetricExactMatch,
        followUpQuestionParams,
        currentParamsExtractApi,
        { traceId, username, host, conversationId, chatId, req },
        req.body.taskId,
      )

      const originLlmResponse = cloneDeep(response)

      const chatResponse = await chatMetrics({
        sceneId,
        projectId,
        conversationId,
        chatId,
        message: userQuestion,
        llmType,
        metricConfig,
        enableMetricExactMatch,
        authData,
        username,
        host,
        traceId,
        paramsExactResponse: response,
      })

      // 更新ConverChat 异步执行 不阻塞
      updateChatLlmResponse(chatId, sceneId, response.data)
      updateConverChatResponse(chatId, traceId, [chatResponse])

      // 记录热门指标
      recordMetricFrequencyFromResult({ chatResponse: chatResponse, sceneId, projectId })
      // setTimeout(() => {
      //   startChatProcessTest(taskId, 0)
      // }, 3000)
      return res.json({ ...chatResponse, traceId, taskId, originLlmResponse })
    } catch (e) {
      console.error(`POST ${url} encountered an error`, e)

      const chatResponse: ChatResponse = {
        taskType: 'chat-error',
        errType: ChatResponseErrorTypes.E_UNKNOWN,
        ready: false,
        unreadyReason: (e as Error)?.message || '超纲了，大模型正在努力学习中...',
        conversationId,
        sceneId,
        traceId,
        taskId: req.body.taskId,
      }

      updateConverChatResponse(chatId, traceId, [chatResponse])
      res.json(chatResponse)
    }
  }
  return handler
}

router.post(
  '/chat-nocheck',
  createChatHandler({
    url: '/api/chats/chat-nocheck',
    defaultUsername: TAG_PROMPT_TEST,
    beforeHandler: async (req, res) => {
      if (req.header('secret-key') !== TAG_PROMPT_TEST) {
        res.json({ code: 401, data: {}, msg: '登录已过期，请重新登录' })
        return true
      }
    },
  }),
)

router.post('/chat', createChatHandler({ url: '/api/chats/chat' }))

function overWriteMessage(message: string, metricConfig: MetricConfig) {
  console.info(chalk.green('origin Message =>', message))
  let newMessage = message
  if (isBaoWu(metricConfig.name)) {
    newMessage = message.replace('宝物', '宝武')
  }
  console.info(chalk.green('message after rewriting =>', newMessage))
  return newMessage
}

router.post('/chatMultiScenes', async (req: Request, res: Response) => {
  console.info('POST /api/chats/chatMultiScenes with request body: ', req.body)
  const { conversationId, chatId, projectId, message, llmType, currentParamsExtractApi } = req.body
  const traceId = req.header('traceId') as string
  setAxiosDefaults(traceId)
  const username = req.user?.username || ''
  const host = req.header('Host') || ''
  const taskId = nanoid()
  const authData = { userId: username }

  // TODO: remove this constant
  const enableMetricExactMatch = true
  const enableFollowUpQuestion = true
  const messages = enableFollowUpQuestion
    ? await generateFollowUpMessageForBaowu(conversationId, message)
    : [{ role: 'user' as const, content: message }]
  const followUpParams = await getFollowUpQuestionParams(conversationId, chatId, enableFollowUpQuestion)

  try {
    const projectParamsExtractResult = await projectParamsExtractChat(
      messages,
      projectId,
      llmType,
      enableMetricExactMatch,
      followUpParams && followUpParams?.sceneId ? { [followUpParams.sceneId]: followUpParams } : followUpParams,
      currentParamsExtractApi,
      { traceId, host, username, conversationId, chatId, req },
    )

    if (projectParamsExtractResult.code !== 0) {
      return res.json({
        data: [
          {
            ready: false,
            taskType: 'chat-error',
            errType:
              ChatResponseErrorMap[projectParamsExtractResult.code as unknown as keyof typeof ChatResponseErrorMap],
            conversationId: conversationId,
            llmResponse: projectParamsExtractResult,
            sceneId: MULTI_SCENE_CHAT_MOCK_SCENE_ID,
          } as ChatResponseError,
        ],
      })
    }

    const multiScenesResult = projectParamsExtractResult.data
    const sceneIds = Object.keys(multiScenesResult)

    if (sceneIds.length === 0) {
      throw new Error('没有匹配到场景来应答此问题')
    }

    const multiScenesResultForFrontend = await Promise.all(
      sceneIds.map(async (id) => {
        try {
          const metricConfig = await MetricConfig.createBySceneId(id)
          const result = multiScenesResult[id]
          const chatResponse = await chatMetrics({
            conversationId,
            chatId,
            sceneId: id,
            projectId,
            message,
            llmType,
            metricConfig,
            enableMetricExactMatch,
            username,
            host,
            traceId,
            paramsExactResponse: { code: 0, data: result },
            authData,
          })

          // 更新 llmResponse 用于追问 异步执行 不阻塞
          updateChatLlmResponse(chatId, id, result)
          // 记录热门指标
          recordMetricFrequencyFromResult({ chatResponse, sceneId: id, projectId })
          return { ...chatResponse, traceId, taskId }
        } catch (error) {
          return {
            taskType: 'chat-error' as const,
            errType: ChatResponseErrorTypes.E_UNKNOWN,
            ready: false as const,
            unreadyReason: (error as Error)?.message || '超纲了，大模型正在努力学习中...',
            conversationId,
            sceneId: id,
            traceId,
          }
        }
      }),
    )
    const result = processMultiSceneResults(multiScenesResultForFrontend)
    updateConverChatResponse(chatId, traceId, result)
    res.json({ code: 0, ready: true, data: result, multiScenesResultForFrontend })
  } catch (error) {
    console.error('POST /api/chats/chatMultiScenes encountered an error: ' + error)
    const chatResponse: ChatResponseError = {
      ready: false,
      taskType: 'chat-error',
      errType: ChatResponseErrorTypes.E_UNKNOWN,
      unreadyReason: (error as Error)?.message || '未知错误',
      conversationId,
      sceneId: MULTI_SCENE_CHAT_MOCK_SCENE_ID,
    }
    updateConverChatResponse(chatId, traceId, [chatResponse])
    return res.json({ data: [chatResponse] })
  }
})

router.post('/docReport', async (req: Request, res: Response) => {
  console.info('POST /api/chats/docReport with request body: ', req.body)
  const { conversationId, chatId, sceneId, useScene, docReportParams, isReportModelNameTag } = req.body
  const traceId = req.header('traceId') as string
  try {
    setAxiosDefaults(traceId)

    let resultForFrontend
    if (docReportParams) {
      // 生成报告
      console.info('node-生成报告')
      resultForFrontend = await createDocReport(docReportParams)
    } else {
      // 获取文档字段分类情况
      console.info('node-获取文档字段分类情况')
      resultForFrontend = await getDocReportColumnClassify(isReportModelNameTag, useScene)
    }
    const result: ChatResponse = {
      ready: true,
      conversationId: conversationId,
      sceneId,
      rows: { ...resultForFrontend, fileId: isReportModelNameTag || resultForFrontend.fileId },
      taskType: 'doc-report',
    }
    updateConverChatResponse(chatId, traceId, [result])
    res.json({ code: 0, ready: true, data: result })
  } catch (error) {
    const chatResponse: ChatResponse = {
      ready: false,
      taskType: 'chat-error',
      errType: ChatResponseErrorTypes.E_UNKNOWN,
      unreadyReason: (error as Error)?.message || '未知错误',
      conversationId,
      sceneId: isReportModelNameTag || docReportParams.fileId, // 这里表示fileId
    }

    updateConverChatResponse(chatId, traceId, [chatResponse])
    res.json(chatResponse)
  }
})

const mq = new Map<string, { updateAt: number; data: ChatProgress[] }>()

const _ = new CronJob('0 0 0 * * *', async () => {
  const entries = Array.from(mq.entries())
  for (const [key, data] of entries) {
    if (Date.now() - data.updateAt > 1000 * 60 * 60 * 1) {
      mq.delete(key)
    }
  }
})

router.get('/chat-progress', async (req, res) => {
  console.info('chat-progress', req.url, req.query)
  const taskId = req.query.taskId as string
  if (!taskId) {
    return res.json({ code: 1, msg: 'taskId不存在' })
  }
  if (!mq.has(taskId)) {
    return res.json({ code: 0, data: [] })
  }
  const data = mq.get(taskId)!
  mq.delete(taskId)
  return res.json({ code: 0, data: data.data })
})

router.post('/chat-progress-callback', async (req, res) => {
  console.info('chat-progress-callback', req.url, req.query, req.body)
  const taskId = req.query.taskId as string
  if (!taskId) {
    return res.json({ code: 1, msg: 'taskId不存在' })
  }
  const data = req.body
  if (!mq.has(taskId))
    mq.set(taskId, {
      updateAt: 0,
      data: [],
    })
  const item = mq.get(taskId)!
  item.updateAt = Date.now()
  item.data.push(data)
  return res.json({ code: 0 })
})

router.post('/chitchat', async (req, res) => {
  const currentParamsExtractApi = req.body.currentParamsExtractApi
  const baseUrl = currentParamsExtractApi || PROCESS_ENV.PARAMS_EXTRACT_URL_HOST
  const chitChat = `${baseUrl}/api/v1/chitchat`

  res.setHeader('Content-Type', 'text/event-stream')
  res.setHeader('Cache-Control', 'no-cache')
  res.setHeader('Connection', 'keep-alive')
  await axios
    .post(
      chitChat,
      {
        messages: req.body.messages,
        model_type: req.body.llmType ?? PROCESS_ENV.DEEPSEEK_MODEL_TYPE,
        web_search: req.body.webSearch,
      },
      {
        timeout: DEFAULT_TIMEOUT * 3,
        responseType: 'stream',
        headers: { 'Content-Type': 'application/json' },
      },
    )
    .then((response) => {
      let result = ''
      response.data.on('data', (chunk: Buffer) => {
        const chunkStr = chunk.toString('utf8')
        result += chunkStr
        process.stdout.write(chunkStr)
        res.write(`${JSON.stringify({ code: 0, data: chunkStr })}\n\n`)
      })

      response.data.on('end', () => {
        console.info('\n\n闲聊完成：', result)
        res.end()
      })
    })
    .catch((error) => {
      console.error('chitchat failed in generate insight report: ', error)
      res.write(JSON.stringify({ code: 500, data: error?.toString() ?? '闲聊失败' }))
      res.end()
    })
})

router.post(
  '/condense',
  asyncResponseWrapper(async (req, res) => {
    const currentParamsExtractApi = req.body.currentParamsExtractApi
    const baseUrl = currentParamsExtractApi || PROCESS_ENV.PARAMS_EXTRACT_URL_HOST
    const condenseUrl = `${baseUrl}/api/v1/condense`
    const pyRes = await axios.post(
      condenseUrl,
      {
        messages: req.body.messages,
        model_type: req.body.llmType ?? 'baowu-glm-4-9b',
      },
      {
        timeout: DEFAULT_TIMEOUT * 3,
        headers: { 'Content-Type': 'application/json' },
      },
    )
    return res.json({ code: 0, data: pyRes.data.data })
  }),
)

router.post(
  '/result-analysis',
  asyncResponseWrapper(async (req, res) => {
    const currentParamsExtractApi = req.body.currentParamsExtractApi
    const baseUrl = currentParamsExtractApi || PROCESS_ENV.PARAMS_EXTRACT_URL_HOST
    const condenseUrl = `${baseUrl}/api/v1/result_analysis`
    res.setHeader('Content-Type', 'text/event-stream')
    res.setHeader('Cache-Control', 'no-cache')
    res.setHeader('Connection', 'keep-alive')

    const { sceneId, parentId, message, conversationId = nanoid(), deepThinkContent } = req.body
    const metricConfig = await MetricConfig.createBySceneId(sceneId)
    const messages = await genMessages({
      parentId,
      message,
      metricConfig,
      conversationId,
      sceneId,
    })
    messages.push({ role: 'assistant', content: deepThinkContent })
    console.info('===> messages', messages)
    await axios
      .post(
        condenseUrl,
        {
          messages,
          model_type: req.body.llmType ?? PROCESS_ENV.DEEPSEEK_MODEL_TYPE,
        },
        {
          timeout: DEFAULT_TIMEOUT * 3,
          headers: { 'Content-Type': 'application/json' },
          responseType: 'stream',
        },
      )
      .then((response) => {
        let result = ''
        response.data.on('data', (chunk: Buffer) => {
          const chunkStr = chunk.toString('utf8')
          result += chunkStr
          process.stdout.write(chunkStr)
          res.write(`${JSON.stringify({ code: 0, data: chunkStr })}\n\n`)
        })

        response.data.on('end', () => {
          console.info('\n\n闲聊完成：', result)
          res.end()
        })
      })
      .catch((error) => {
        console.error('chitchat failed in generate insight report: ', error)
        res.write(JSON.stringify({ code: 500, data: error?.toString() ?? '闲聊失败' }))
        res.end()
      })
  }),
)

export default router
