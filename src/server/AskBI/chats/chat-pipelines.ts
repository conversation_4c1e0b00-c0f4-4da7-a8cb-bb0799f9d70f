/* eslint-disable no-extra-semi */
/* eslint-disable @typescript-eslint/no-extra-semi */
/**
 * 文字生成图表问答的管道流程
 * 分为3类：大模型通用链路、强模型指标查询、弱模型指标查询
 */
import chalk from 'chalk'
import dayjs from 'dayjs'
import { uniq } from 'lodash'
import { produce } from 'immer'
import {
  ChartGroup,
  LlmType,
  AttrAnalysisResult,
  ChatResponse,
  ChatResponseQueryMetric,
  ChatResponseError,
  ChatResponseErrorTypes,
  AuthData,
  OlapRow,
} from '@shared/common-types'
import { QueryParamsVerified, ResultOfParamsExtract } from '@shared/metric-types'
import Metric2Sql from 'src/server/MetricStore/metric2sql/metric2sql'
import MetricConfig from 'src/server/MetricStore/metric-config'
import { executeAllXengineSql } from 'src/server/dao/xengine-query'
import { assertExhaustive, getTimeQuery } from 'src/shared/common-utils'
import { saveAskBotBusinessLogs } from 'src/server/winston-log'
import { convertTimeToSpecificMonth } from 'src/shared/time-utils'
import { isBaoWu, isBaoWuCost, isBaoWuFinancial } from 'src/shared/baowu-share-utils'
import { handleBaowuCostQueryMetric } from 'src/server/custom/baowu/baowu-cost-scene'
import { addLimitToSql } from '../../dao/db'
import { parseParamsExtractResponse } from '../../ai/askbi-client'
import { getRecommendChartTypes } from '../charts/recommend-chart-types'
import {
  getCompaniesFromOriginWhere,
  processBaoWuCompanyFilter,
  sortAndFilterCompanies,
} from '../../custom/baowu/baowu-utils'

/**
 * 处理参数 [用于单场景问答]
 * @param {Object} props - 函数参数
 * @param {string} props.conversationId - 会话ID
 * @param {string} props.chatId - 聊天ID
 * @param {string} props.sceneId - 场景ID
 * @param {string} props.projectId - 项目ID
 * @param {string} props.message - 消息
 * @param {LlmType} props.llmType - 大模型类型
 * @param {MetricConfig} props.metricConfig - 场景详情配置
 * @param {boolean} props.enableMetricExactMatch - 场景开启精确匹配 置信度功能
 * @param {boolean} props.paramsExactResponse - 场景提参结果
 * @returns {Promise<ChatResponse>} - 处理后的聊天响应 返回给前端
 */
export async function chatMetrics(props: {
  conversationId: string
  chatId: string
  projectId: string
  sceneId: string
  message: string
  llmType: LlmType
  metricConfig: MetricConfig
  enableMetricExactMatch: boolean
  host: string
  username: string
  traceId: string
  paramsExactResponse: any
  authData?: AuthData
}): Promise<ChatResponse> {
  const {
    conversationId,
    chatId,
    projectId,
    sceneId,
    message,
    llmType,
    metricConfig,
    enableMetricExactMatch,
    host,
    username,
    traceId,
    paramsExactResponse: response,
    authData,
  } = props
  console.info('Call chatMetrics authData->>>>>: ', authData)
  try {
    if (response.code === 0 && isBaoWuFinancial(metricConfig?.name) && response.data?.query_metric) {
      // 宝武的查询要进行数据权限处理, 修改where, 查询有权限的数据, 如果没有任何公司权限,就返回没数据的错误
      const baowuCompanyFilter = await processBaoWuCompanyFilter({
        authData,
        extractedResponse: response,
        metricConfig,
      })
      const whereCompany = getCompaniesFromOriginWhere(response.data.query_metric?.where)
      console.info('----baowuCompanyFilter----->>>>', baowuCompanyFilter)
      if (baowuCompanyFilter !== false) {
        // 先存一份原始的提参where
        response.data.query_metric.originWhere = response.data.query_metric.where
        // 将合成后的where赋值给queryMetric
        response.data.query_metric.where = baowuCompanyFilter
      } else {
        return {
          taskType: 'chat-error',
          ready: false,
          errType: ChatResponseErrorTypes.NO_DATA_AUTHORITY,
          conversationId: conversationId,
          sceneId,
          metricNames: response.data.query_metric?.metricNames,
          whereCompany,
          llmResponse: response,
        }
      }
    }

    const result = await parseParamsExtractResponse({
      resp: { ...response, message },
      config: { enableMetricExactMatch, metricConfig },
    })
    if ('limit' in result && isBaoWu(metricConfig.name)) {
      // 如果limit存在且大于一,则认为是取前n的数据, 那么就要*3保证取到至少limit个账套的数据
      const originLimit = result.limit
      result.limit = originLimit && originLimit > 1 ? originLimit * 3 : originLimit
    }
    const chatResponse = await processExtractedParams({
      resultOfParamsExtract: result,
      metricConfig: metricConfig,
      message,
      sceneId,
      projectId,
      conversationId,
      chatId,
      llmType,
      username,
      traceId,
      host,
      llmResponse: response,
    })

    return chatResponse
  } catch (e) {
    console.error('chatMetrics 链路出现错误', e)
    let unreadyReason = '处理出错，请联系管理员排查'
    if (e instanceof Error) {
      unreadyReason += ' ' + e.message
      if (e.message === 'connect ETIMEDOUT') {
        unreadyReason = '数据库连接失败，请检查数据源配置'
      }
    } else {
      unreadyReason += `: ${String(e)}`
    }

    return {
      ready: false,
      errType: ChatResponseErrorTypes.E_UNKNOWN,
      taskType: 'chat-error',
      conversationId,
      unreadyReason,
      sceneId,
      llmResponse: unreadyReason,
    }
  }
}

/**
 * 处理提取参数的结果 [不处理异常]
 * @param {Object} props - 函数参数
 * @param {ResultOfParamsExtract} props.resultOfParamsExtract - 大模型提取的参数结果
 * @param {MetricConfig} props.metricConfig - 指标配置
 * @param {string} props.message - 消息
 * @param {string} props.sceneId - 场景ID
 * @param {string} props.conversationId - 会话ID
 * @param {string} props.chatId - 聊天ID
 * @param {LlmType} props.llmType - 大模型类型
 * @returns {Promise<ChatResponse>} - 处理后的聊天响应 返回给前端
 */
export async function processExtractedParams(props: {
  resultOfParamsExtract: ResultOfParamsExtract
  metricConfig: MetricConfig
  message: string
  sceneId: string
  projectId: string
  conversationId: string
  chatId: string
  llmType: LlmType
  username: string
  traceId: string
  host: string
  llmResponse: any
}): Promise<ChatResponse> {
  const {
    resultOfParamsExtract,
    metricConfig,
    message,
    sceneId,
    projectId,
    conversationId,
    username,
    traceId,
    host,
    llmResponse,
  } = props
  const resultType = resultOfParamsExtract.type
  switch (resultType) {
    case 'query-metric':
    case 'predict': {
      const infoTexts = []
      const metric2Sql = new Metric2Sql(metricConfig)
      const verifiedMetricParams = metric2Sql.verifyQueryParams({
        metricNames: resultOfParamsExtract.metricNames,
        groupBys: resultOfParamsExtract.groupBys,
        where: resultOfParamsExtract.where,
        originWhere: resultOfParamsExtract.originWhere,
        orderBys: resultOfParamsExtract.orderBys,
        limit: resultOfParamsExtract.limit,
        timeQueryParams: metricConfig.timeDimensionDatum == null ? undefined : resultOfParamsExtract.timeQueryParams,
        isMetricNamesExactMatch: resultOfParamsExtract.isMetricNamesExactMatch,
        isWhereExactMatch: resultOfParamsExtract.isWhereExactMatch,
        extraInfo: resultOfParamsExtract.extraInfo,
        notExistMetricNames: resultOfParamsExtract.notExistMetricNames,
        notExistGroupBys: resultOfParamsExtract.notExistGroupBys,
        notExistOrderBys: resultOfParamsExtract.notExistOrderBys,
      })
      infoTexts.push(...resultOfParamsExtract.infoTexts)
      console.info('chatMetrics queryParams: ' + JSON.stringify(verifiedMetricParams.queryParams))
      console.info('chatMetrics extraParams: ' + JSON.stringify(verifiedMetricParams.extraParams))
      const verifiedMetricParamsWithPop = verifiedMetricParams
      // console.info('<<<verifiedMetricParams>>>', JSON.stringify(verifiedMetricParams))

      if (metricConfig.timeDimensionDatum == null && resultOfParamsExtract.timeQueryParams != null) {
        infoTexts.push('没有配置时间维度，当前返回的为最新的数据。')
      }

      if (message.includes('同比') || message.includes('环比') || message.includes('同环比')) {
        verifiedMetricParamsWithPop.hasPop = true
        if (metricConfig.timeDimensionDatum == null) {
          // 如果没有时间维度，不支持同环比
          infoTexts.push('当前没有配置时间维度，无法自动计算同环比。请添加同环比指标或者配置时间维度。')
        } else {
          // 如果没有指定时间，默认上个月
          if (verifiedMetricParamsWithPop.queryParams.timeQueryParams == null) {
            verifiedMetricParamsWithPop.queryParams.timeQueryParams = {
              timeDimensionName: metricConfig.timeDimensionDatum.timeDimensionName,
              timeStartFunction: { type: 'recentMonths', months: 1 },
              timeEndFunction: { type: 'recentMonths', months: 1 },
              timeGranularity: 'month',
            }
          }
          if (verifiedMetricParamsWithPop.queryParams.timeQueryParams.timeGranularity === 'total') {
            // 但开启了同环比，没有分组的时候改为月分组
            verifiedMetricParamsWithPop.queryParams.timeQueryParams.timeGranularity = 'month'
          }
          if (verifiedMetricParamsWithPop.queryParams.timeQueryParams.timeGranularity !== 'month') {
            // 年、季度、日暂时不支持同环比
            infoTexts.push('目前只支持月份的同环比，暂不支持其他时间周期。')
          } else {
            if (message.includes('同环比')) {
              verifiedMetricParamsWithPop.queryParams.periodOverPeriods = [
                'momGrowth',
                'momGrowthRate',
                'yoyMonthGrowth',
                'yoyMonthGrowthRate',
              ]
            } else if (message.includes('环比增长额')) {
              verifiedMetricParamsWithPop.queryParams.periodOverPeriods = ['momGrowth']
            } else if (message.includes('环比增长率')) {
              verifiedMetricParamsWithPop.queryParams.periodOverPeriods = ['momGrowthRate']
            } else if (message.includes('环比')) {
              verifiedMetricParamsWithPop.queryParams.periodOverPeriods = ['momGrowth', 'momGrowthRate']
            } else if (message.includes('同比增长额')) {
              verifiedMetricParamsWithPop.queryParams.periodOverPeriods = ['yoyMonthGrowth']
            } else if (message.includes('同比增长率')) {
              verifiedMetricParamsWithPop.queryParams.periodOverPeriods = ['yoyMonthGrowthRate']
            } else if (message.includes('同比')) {
              verifiedMetricParamsWithPop.queryParams.periodOverPeriods = ['yoyMonthGrowth', 'yoyMonthGrowthRate']
            }
          }
        }
      }

      const response = await generateResponseOfQueryMetric({
        sceneId,
        projectId,
        metricConfig,
        verifiedMetricParams: verifiedMetricParamsWithPop,
        conversationId,
        message,
        infoTexts,
        username,
        traceId,
        host,
        needLog: true,
        confidenceOriginData: resultOfParamsExtract.confidenceOriginData,
        predictRows: resultType === 'predict' ? resultOfParamsExtract.predictRows : undefined,
      })

      return response
    }
    case 'metric-exact-match': {
      const infoTexts: string[] = []
      const metric2Sql = new Metric2Sql(metricConfig)
      const verifiedMetricParams = metric2Sql.verifyQueryParams({
        metricNames: resultOfParamsExtract.metricNames,
        externalReportNames: resultOfParamsExtract.externalReportNames,
        groupBys: resultOfParamsExtract.groupBys,
        where: resultOfParamsExtract.where,
        originWhere: resultOfParamsExtract.originWhere,
        orderBys: resultOfParamsExtract.orderBys,
        limit: resultOfParamsExtract.limit,
        timeQueryParams: metricConfig.timeDimensionDatum == null ? undefined : resultOfParamsExtract.timeQueryParams,
        isMetricNamesExactMatch: resultOfParamsExtract.isMetricNamesExactMatch,
        isWhereExactMatch: resultOfParamsExtract.isWhereExactMatch,
        extraInfo: resultOfParamsExtract.extraInfo,
        notExistMetricNames: resultOfParamsExtract.notExistMetricNames,
        notExistGroupBys: resultOfParamsExtract.notExistGroupBys,
        notExistOrderBys: resultOfParamsExtract.notExistOrderBys,
      })
      infoTexts.push(...resultOfParamsExtract.infoTexts)
      const confidenceOriginData = resultOfParamsExtract.confidenceOriginData
      const queryParamsVerified = confidenceOriginData
        ? {
            queryParams: {
              ...verifiedMetricParams.queryParams,
              where: confidenceOriginData.where,
              isWhereExactMatch: confidenceOriginData.isWhereExactMatch,
            },
            originalQueryParams: {
              ...verifiedMetricParams.originalQueryParams,
              where: confidenceOriginData.originWhere,
              isWhereExactMatch: confidenceOriginData.isWhereExactMatch,
            },
            extraParams: verifiedMetricParams.extraParams,
            hasPop: verifiedMetricParams.hasPop,
          }
        : verifiedMetricParams
      return {
        ready: true,
        conversationId: conversationId,
        sceneId: sceneId,
        taskType: 'metric-exact-match',
        metricNames: resultOfParamsExtract.metricNames,
        queryParamsVerified,
        confidenceOriginData,
        infoTexts,
      }
    }
    case 'query-external-report': {
      const confidenceOriginData =
        resultOfParamsExtract.externalReports.includes('BAOWU_FEIYONGZHICHU_REPORT') ||
        resultOfParamsExtract.externalReports.includes('BAOWU_ZICHANFUZHAILV_REPORT')
          ? resultOfParamsExtract.confidenceOriginData
          : undefined
      const timeQueryParams = resultOfParamsExtract.timeQueryParams
        ? resultOfParamsExtract.timeQueryParams
        : {
            timeStartFunction: { type: 'recentMonths', months: 1 } as const,
            timeEndFunction: { type: 'recentMonths', months: 1 } as const,
            timeGranularity: 'total' as const,
          }
      const queryParams = {
        metricNames: [],
        where: resultOfParamsExtract.where,
        isWhereExactMatch: false,
        isMetricNamesExactMatch: true,
        externalReportNames: resultOfParamsExtract.externalReports,
        timeQueryParams,
      }
      return {
        ready: true,
        conversationId: conversationId,
        sceneId: sceneId,
        taskType: resultOfParamsExtract.type,
        externalReports: resultOfParamsExtract.externalReports,
        where: resultOfParamsExtract.where,
        timeQueryParams,
        queryParamsVerified: {
          originalQueryParams: {
            ...queryParams,
            where: confidenceOriginData?.originWhere || resultOfParamsExtract.where,
          },
          queryParams,
          extraParams: {
            extraMetricNames: [],
            extraGroupBys: [],
            extraOrderBys: [],
          },
        },
        confidenceOriginData,
      }
    }
    case 'attribution-analysis': {
      // 如果 timeDimensionDatum 为空，那么就不支持归因分析
      if (metricConfig.timeDimensionDatum == null) {
        return {
          ready: false,
          errType: ChatResponseErrorTypes.ATTR_ANALYSIS_NOT_SUPPORT,
          taskType: 'chat-error',
          sceneId,
          conversationId: conversationId,
          unreadyReason: '没有配置虚拟时间维度，无法进行归因分析',
        }
      }
      const sql = resultOfParamsExtract.metric_sql
      const attrAnalysisSql = `${sql.sqlBase ? '-- Base Metric SQL \n' + sql.sqlBase.trim() + ';' : ''}${sql.sqlCompare ? '-- Compare Metric SQL \n' + sql.sqlCompare.trim() + ';' : ''}`
      return {
        ready: true,
        conversationId: conversationId,
        sceneId: sceneId,
        sql: attrAnalysisSql,
        chartType: 'AttrAnalysis',
        taskType: 'attribution-analysis',
        chartTitle: '归因分析',
        rows: [resultOfParamsExtract as AttrAnalysisResult],
        isPartialRow: false,
        partialRowMsg: '',
      }
    }
    case 'attribution-metric-analysis': {
      return {
        ready: true,
        sceneId,
        conversationId,
        taskType: 'attribution-metric-analysis',
        chartTitle: '',
        chartType: 'AttrMetricAnalysis',
        rows: [resultOfParamsExtract],
        isPartialRow: false,
        partialRowMsg: '',
      }
    }
    case 'data-overview':
    case 'table-list':
    case 'dimension-list':
    case 'dimension-detail':
    case 'metric-list':
    case 'metric-detail':
    case 'metric-tree': {
      return {
        ready: true,
        conversationId: conversationId,
        sceneId,
        rows: resultOfParamsExtract.content,
        taskType: isBaoWu(metricConfig.name) ? 'data-overview' : resultType,
        isPartialRow: false,
        partialRowMsg: '',
      }
    }
    case 'chitchat': {
      return {
        ready: false,
        taskType: 'chat-error',
        errType: ChatResponseErrorTypes.CHIT_CHAT,
        sceneId,
        conversationId: conversationId,
        unreadyReason: resultOfParamsExtract.content,
      }
    }
    case 'llm-error': {
      return {
        ready: false,
        taskType: 'chat-error',
        errType: ChatResponseErrorTypes.LLM_ERROR,
        sceneId,
        conversationId: conversationId,
        unreadyReason: resultOfParamsExtract.content,
      }
    }
    case 'chat-error': {
      return {
        ready: false,
        taskType: 'chat-error',
        errType: resultOfParamsExtract.errType || ChatResponseErrorTypes.E_UNKNOWN,
        sceneId,
        conversationId: conversationId,
        unreadyReason: resultOfParamsExtract.content,
      }
    }
    case 'percentage': {
      return {
        ready: true,
        taskType: 'percentage',
        conversationId: conversationId,
        sceneId: sceneId,
        calculator: llmResponse.data.calculator,
      }
    }
    case 'period_on_period': {
      return {
        ready: true,
        taskType: 'period-on-period',
        conversationId: conversationId,
        sceneId: sceneId,
        calculator: llmResponse.data.calculator,
      }
    }
    default: {
      assertExhaustive(resultType, `Not supported type ${resultType}`)
    }
  }
}

/** 根据 queryParams 来做 metric2sql 和查询数据拼装结果返回 */
export const generateResponseOfQueryMetric = async ({
  sceneId,
  projectId,
  metricConfig,
  verifiedMetricParams,
  conversationId,
  message,
  infoTexts,
  username,
  traceId,
  host,
  needLog,
  predictRows,
  confidenceOriginData,
}: {
  sceneId: string
  projectId: string
  metricConfig: MetricConfig
  verifiedMetricParams: QueryParamsVerified
  conversationId: string
  message: string
  infoTexts: string[]
  username: string
  traceId: string
  host: string
  needLog?: boolean
  predictRows?: OlapRow[]
  confidenceOriginData?: {
    where: string
    isWhereExactMatch: boolean
    originWhere: string
  }
}): Promise<ChatResponseQueryMetric | ChatResponseError> => {
  if (isBaoWuCost(metricConfig.name)) {
    const result = await handleBaowuCostQueryMetric({
      sceneId,
      projectId,
      metricConfig,
      verifiedMetricParams,
      conversationId,
      message,
      infoTexts,
      username,
      traceId,
      host,
    })
    return result
  }
  console.info('Generate Response Of QueryMetric with queryParams.')
  const startTime = Date.now()
  const hasOrderBy = verifiedMetricParams.originalQueryParams.orderBys?.length
  if (
    isBaoWu(metricConfig.name) &&
    verifiedMetricParams.queryParams.metricNames
      .map((v) => metricConfig.allMetrics.find((item) => item.name === v))
      .every((v) => v?.type !== 'derived' && v?.type !== 'list') &&
    !hasOrderBy
  ) {
    const queryParams = verifiedMetricParams.queryParams
    if (queryParams.timeQueryParams != null) {
      const startMonthFunction = convertTimeToSpecificMonth(queryParams.timeQueryParams.timeStartFunction)
      const endMonthFunction = convertTimeToSpecificMonth(queryParams.timeQueryParams.timeEndFunction)
      const timeSpan = Math.abs(
        startMonthFunction.year * 12 + startMonthFunction.month - (endMonthFunction.year * 12 + endMonthFunction.month),
      )
      if (timeSpan === 0)
        // 宝武场景如果时间跨度为0，支持同环比
        verifiedMetricParams = produce(verifiedMetricParams, (v) => {
          v.queryParams.periodOverPeriods = uniq(
            (v.queryParams.periodOverPeriods ?? []).concat('momGrowth', 'yoyMonthGrowth'),
          )
        })
    }
  }

  if (
    verifiedMetricParams.queryParams.metricNames.length === 0 &&
    (verifiedMetricParams.queryParams.groupBys || []).length === 0
  ) {
    return {
      sceneId,
      ready: false,
      taskType: 'chat-error',
      errType: ChatResponseErrorTypes.METRICS_NOT_EXIST,
      conversationId,
      unreadyReason: '找不到任何维度和指标，请换个问题',
      infoTexts,
    }
  }
  console.info('Starting Metric To SQL...')
  // TODO: 这里做个拆分，单独做 title、chartType 的推断
  const metric2Sql = new Metric2Sql(metricConfig)
  let { sql, rowsMetadata } = metric2Sql.toSql(verifiedMetricParams.queryParams)
  sql = addLimitToSql(sql, 100) // 给 SQL 添加 limit，如果原来没有就添加 100
  console.info(chalk.green('指标拼接的SQL为：\n' + sql))
  sql = predictRows ? '' : processCustomizeSql(sql, metricConfig, message)

  const metric2SqlDone = Date.now()
  const rows = predictRows
    ? predictRows
    : await executeAllXengineSql(sql, { username, traceId, host, conversationId, sceneId, projectId })
  const sql2DataDone = Date.now()

  console.info(
    `使用 SQL 查询数据，一共 ${rows.length} 条数据。${rows.length >= 5 ? '前 5 条为' : '数据为'}`,
    rows.slice(0, 5),
  )

  const queryParamsVerified = confidenceOriginData
    ? {
        queryParams: {
          ...verifiedMetricParams.queryParams,
          where: confidenceOriginData.where,
          isWhereExactMatch: confidenceOriginData.isWhereExactMatch,
        },
        originalQueryParams: {
          ...verifiedMetricParams.originalQueryParams,
          where: confidenceOriginData.originWhere,
          isWhereExactMatch: confidenceOriginData.isWhereExactMatch,
        },
        extraParams: verifiedMetricParams.extraParams,
        hasPop: verifiedMetricParams.hasPop,
      }
    : verifiedMetricParams

  const isPartialRow = false
  const partialRowMsg = ''
  if (rows.length === 0 || isRowsNoData(rows, verifiedMetricParams.queryParams.metricNames, metricConfig)) {
    if (verifiedMetricParams.queryParams.timeQueryParams?.timeEndFunction) {
      const originEndMonth = convertTimeToSpecificMonth(
        verifiedMetricParams.queryParams.timeQueryParams.timeEndFunction,
      )
      const endMonthDay = dayjs()
        .year(originEndMonth.year)
        .month(originEndMonth.month - 1)
        .startOf('month')
      const lastMonth = dayjs().subtract(1, 'month').startOf('month')

      const whereCompany = isBaoWu(metricConfig.name)
        ? getCompaniesFromOriginWhere(verifiedMetricParams.queryParams.where)
        : undefined

      const { timeQueryParams } = verifiedMetricParams.queryParams
      let subUnreadyReason = ''
      let date = ''
      if (timeQueryParams) {
        date = getTimeQuery(timeQueryParams)
        subUnreadyReason = `当前查询时间：${date}`
      }
      if (endMonthDay.isSame(lastMonth, 'month')) {
        // 上月没数据
        return {
          sceneId,
          ready: false,
          taskType: 'chat-error',
          errType: ChatResponseErrorTypes.LATEST_DATA_NOT_EXIST,
          conversationId,
          unreadyReason: `${date ? date + '的' : ''}报表数据还没有出来哦，请等数据库更新后再尝试`,
          subUnreadyReason,
          sql,
          queryParamsVerified: queryParamsVerified,
          metricNames: queryParamsVerified.queryParams.metricNames,
          whereCompany,
          confidenceOriginData,
          infoTexts,
        }
      }
      if (endMonthDay.isAfter(lastMonth, 'month')) {
        // 本月及以后没数据
        return {
          sceneId,
          ready: false,
          taskType: 'chat-error',
          errType: ChatResponseErrorTypes.FUTURE_DATA_NOT_EXIST,
          conversationId,
          unreadyReason: '数据尚未产生，请耐心等待...',
          subUnreadyReason,
          sql,
          queryParamsVerified: queryParamsVerified,
          metricNames: queryParamsVerified.queryParams.metricNames,
          whereCompany,
          confidenceOriginData,
          infoTexts,
        }
      } else {
        // 上月以前没数据
        return {
          sceneId,
          ready: false,
          taskType: 'chat-error',
          errType: ChatResponseErrorTypes.LATEST_DATA_NOT_EXIST,
          conversationId,
          unreadyReason: `该时间段下无数值`,
          subUnreadyReason,
          sql,
          queryParamsVerified: queryParamsVerified,
          metricNames: queryParamsVerified.queryParams.metricNames,
          whereCompany,
          confidenceOriginData,
          infoTexts,
        }
      }
    }
  }
  //  else {
  // 暂时不去判断展示是否个别公司没数据, 因为排名类问数没有基准的数量可供判断
  // const rowsLength = getBaoWuRowsCompanyLength(rows)
  // isPartialRow = await checkIsPartialRow({
  //   rowsLength,
  //   metricConfig,
  //   where: verifiedMetricParams.queryParams.where || '',
  //   limit: verifiedMetricParams.queryParams.limit,
  // })
  // if (isPartialRow) {
  //   const timeStr = getTimeQuery(verifiedMetricParams.queryParams.timeQueryParams)
  //   const metricName = getMetricNamesByMetricCodes(
  //     metricConfig.allMetrics,
  //     verifiedMetricParams.queryParams.metricNames,
  //   )
  //   partialRowMsg = `很抱歉，有个别子公司报表还未出来，现为您提供已有报表的部分子公司${timeStr}${metricName}。`
  // }
  // }

  // 图表推荐：获取推荐的图表类型数组，第一个为首选推荐的图表，其他的为可以切换的图表
  // 规则来判断类型：如果有 排名，排行 就使用
  let chartGroup: ChartGroup = 'Others'
  if (hasOrderBy) {
    chartGroup = 'Rank'
  }

  const recommendChartTypes = getRecommendChartTypes({ chartGroup, rowsMetadata, rows, verifiedMetricParams })
  const chartType = recommendChartTypes[0]

  const transformedRows = isBaoWu(metricConfig?.name)
    ? sortAndFilterCompanies(rows, verifiedMetricParams, !!hasOrderBy)
    : rows

  // TODO: 把图标标题放到前端异步生成 @Sa Kong
  const chartTitle = '图表标题'

  /**
   * 获取底层物理表SQL
   * const physicalTableSQL = await executeUnwrapSql(datasource, sql)
   */

  needLog &&
    saveAskBotBusinessLogs({
      serviceType: 'web_service',
      moduleType: 'metric-to-sql-to-data',
      host,
      username,
      traceId,
      startTime,
      resultCode: 0,
      input: { queryParams: verifiedMetricParams.queryParams, sql },
      output: { data: rows.slice(0, 5), rowsMetadata, recommendChartTypes, infoTexts, isPartialRow, partialRowMsg },
      semanticProjectId: projectId,
      semanticSceneId: sceneId,
      debug: {
        conversationId,
        sceneId,
        taskType: 'query-metric',
        queryParams: verifiedMetricParams.queryParams,
        sql,
        data: rows.slice(0, 5),
        duration: {
          metric2sqlTime: metric2SqlDone - startTime,
          sql2DataTime: sql2DataDone - metric2SqlDone,
          data2ResultTime: Date.now() - sql2DataDone,
        },
      },
    })

  return afterResultsProcessing({
    ready: true,
    taskType: 'query-metric',
    rows: transformedRows,
    rowsMetadata: rowsMetadata,
    recommendChartTypes: recommendChartTypes,
    sql,
    sceneId: sceneId,
    chartType: chartType,
    chartTitle,
    infoTexts,
    conversationId,
    queryParamsVerified,
    isPartialRow,
    partialRowMsg,
    confidenceOriginData,
    isPredict: !!predictRows,
  })
}

/** 后置处理SQL 定制处理 */
function processCustomizeSql(sql: string, metricConfig: MetricConfig, message: string): string {
  let originalSql = sql
  // FIXME: REMOVE 宝武定制逻辑
  if (isBaoWu(metricConfig.name)) {
    if (message.includes('期初') || message.includes('年初')) {
      console.info(chalk.yellow('定制化处理宝武SQL，将end_amt替换为start_amt...'))
      originalSql = originalSql.replaceAll('end_amt', 'start_amt')
    }
  }
  return originalSql
}

/** 后置处理结果 */
function afterResultsProcessing(result: ChatResponseQueryMetric): ChatResponseQueryMetric {
  const { queryParamsVerified, rows, rowsMetadata } = result
  // FIXME: REMOVE 宝武定制逻辑
  let newRows = rows
  let newRowsMetadata = rowsMetadata
  if (queryParamsVerified?.queryParams.metricNames.includes('OTOTAL_CASH_OUTFLOW')) {
    newRows = rows.map((row) => {
      const values = Object.entries(row)
        .filter(([key]) => key !== 'COMPANY_INNER_CODE_DES')
        .map(([key, value]) => ({ key, value: value !== null ? parseFloat(value) : null }))
      values.sort((a, b) => {
        if (a.value === null) return 1
        if (b.value === null) return -1
        return b.value - a.value
      })
      const topThreeValues = values.slice(0, 3)
      const newObject: any = { COMPANY_INNER_CODE_DES: row.COMPANY_INNER_CODE_DES }
      topThreeValues.forEach((item) => {
        if (item.key) {
          newObject[item.key] = item.value
        }
      })
      return newObject
    })
    const rowsKeyMap = Object.keys(newRows[0])
    newRowsMetadata = rowsMetadata
      .map((item) => {
        if (rowsKeyMap.includes(item?.value?.name)) {
          return item
        }
        return null
      })
      .filter((item) => item !== null)
  }

  return {
    ...result,
    rows: newRows,
    rowsMetadata: newRowsMetadata,
  }
}

function isRowsNoData(
  rows: { [key: string]: string | number | null }[],
  metricNames: string[],
  metricConfig: MetricConfig,
) {
  if (metricNames.length === 0) {
    return false
  }

  const names = metricNames.flatMap((m) =>
    metricConfig.allMetrics
      .filter((metric) => metric.name === m)
      .flatMap((metric) => (metric.type === 'list' ? metric.typeParams.metrics.map((i) => i.name) : [m])),
  )

  // 存在,没数据,但是rows length不等于0, 数组中的指标对应的值是null的情况,所以需要判断下, 数据结构是{码值:xxx, 指标:xxx}, 所以得专门针对指标的值来判断
  // 如果item[key] 不存在,且item[key]!==0, 则认为是空数据
  return rows.every((item) => {
    return Object.keys(item).every((key) => {
      if (names.includes(key)) {
        return !item[key] && item[key] !== 0
      }
      return true
    })
  })
}
