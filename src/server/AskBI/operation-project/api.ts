/**
 * 获取项目场景接口
 */
import axios from 'axios'
import express, { Router } from 'express'
import multer from 'multer'
import { generateDIUrl } from 'src/server/utils'
import { PROCESS_ENV } from 'src/server/server-constants'
import { commonProxy } from 'src/server/commonProxy'
const storage = multer.memoryStorage()
const upload = multer({ storage: storage })

const router: Router = express.Router()

router.all('/*', upload.none(), async (req, res) => {
  try {
    const { path, baseUrl } = req
    const url = generateDIUrl(path, baseUrl)
    commonProxy(req, res, url)
  } catch (error: any) {
    console.error(`出现错误，请求地址${req.url}`, error)
    return res.status(500).json({ code: 500, msg: '服务端错误' })
  }
})

router.get('/project', async (req, res) => {
  try {
    const result = await axios.get(`${PROCESS_ENV.DI_PROXY_URI}/api/operation/project`, { params: req.query })
    return res.json(result.data)
  } catch (error) {
    console.error('get-operation-project' + (error as any).response?.data?.msg || (error as any).message || '')
    return res.json({
      code: 500,
      msg: (error as any).response?.data?.msg || (error as any).message || '',
    })
  }
})

export default router
