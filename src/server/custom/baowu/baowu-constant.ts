export const baowuSubCompanies = [
  {
    parentCompany: '中国宝武钢铁集团有限公司-资产合并',
    // parentCompany: null,
    children: [
      '宝山钢铁股份有限公司-资产合并',
      '宝武碳业科技股份有限公司-资产合并',
      '上海宝信软件股份有限公司-资产合并',
      '欧冶云商股份有限公司-资产合并',
      '上海宝钢不锈钢有限公司-资产合并',
      '宝钢特钢有限公司-资产合并',
      '宝武特种冶金有限公司-资产合并',
      '宝武集团中南钢铁有限公司-资产合并',
      '宝钢集团新疆八一钢铁有限公司-资产合并',
      '宝武资源有限公司-资产合并',
      '太原钢铁（集团）有限公司-资产合并',
      '西藏矿业资产经营有限公司-资产合并',
      '宝钢金属有限公司-资产合并',
      '上海宝钢包装股份有限公司-资产合并',
      '宝钢工程技术集团有限公司-资产合并',
      '宝武装备智能科技有限公司-资产合并',
      '华宝信托有限责任公司-资产合并',
      '华宝基金管理有限公司-资产合并',
      '华宝投资有限公司-资产合并',
      '中国中钢集团有限公司-资产合并',
      '中钢国际工程技术股份有限公司-资产合并',
      '中钢装备技术有限公司-资产合并',
      '新余钢铁集团有限公司-资产合并',
      '宝武集团环境资源科技有限公司-资产合并',
      '上海宝地不动产资产管理有限公司-资产合并',
      '宝武水务科技有限公司-资产合并',
      '宝武清洁能源有限公司-资产合并',
      '欧冶工业品股份有限公司-资产合并',
      '长平（杭州）实业控股有限公司-资产合并',
      '武钢集团有限公司-资产合并',
      '宝武共享服务有限公司-资产合并',
      '马钢(集团)控股有限公司-资产合并',
      '宝武重工有限公司-资产合并',
      '欧冶链金再生资源有限公司-资产合并',
      '宝武集团财务有限责任公司-法人',
      '宝钢香港投资有限公司-法人',
      '华宝证券股份有限公司-法人',
      '华宝（上海）股权投资基金管理有限公司-法人',
      '华宝资本有限公司-法人',
      '宝武原料供应有限公司-法人',
      '宝武产教融合发展（上海）有限公司-法人',
      '北京中诚宝管理咨询有限公司-法人',
    ],
  },
]

export const baowuCostMetricMap = {
  COSTCB00JD0001: 'COSTCB00JD0001MIX',
  COSTCW00JT0016: 'COSTCB00JD0001MIX',
  COSTCW00GS0007: 'COSTCB00JD0001MIX',
  COSTCB00JD0002: 'COSTCB00JD0002MIX',
  COSTCW00JT0013: 'COSTCB00JD0002MIX',
  COSTCW00GS0008: 'COSTCB00JD0002MIX',
  COSTCW00JD0001: 'COSTCW00JD0001MIX',
  COSTCW00JT0015: 'COSTCW00JD0001MIX',
  COSTCW00JD0004: 'COSTCW00JD0004MIX',
  COSTCW00JT0012: 'COSTCW00JD0004MIX',
  COSTCW00GS0006: 'COSTCW00JD0004MIX',
  COSTCW00JD0007: 'COSTCW00JD0007MIX',
  COSTCW00JT0011: 'COSTCW00JD0007MIX',
  COSTCW00GS0005: 'COSTCW00JD0007MIX',
  COSTCW00JD0011: 'COSTCW00JD0011MIX',
  COSTCW00JT0010: 'COSTCW00JD0011MIX',
  COSTCW00GS0004: 'COSTCW00JD0011MIX',
  COSTCW00JD0013: 'COSTCW00JD0013MIX',
  COSTCW00JT0008: 'COSTCW00JD0013MIX',
  COSTCW00GS0002: 'COSTCW00JD0013MIX',
  COSTCW00JD0016: 'COSTCW00JD0016MIX',
  COSTCW00GS0009: 'COSTCW00JD0016MIX',
  COSTZZ00JD0001: 'COSTZZ00JD0001MIX',
  COSTZZ00GS0003: 'COSTZZ00JD0001MIX',
  COSTZZ00JD0015: 'COSTZZ00JD0015MIX',
  COSTZZ00JD0019: 'COSTZZ00JD0015MIX',
  COSTZZ00JT0003: 'COSTZZ00JD0015MIX',
  COSTZZ00GS0002: 'COSTZZ00JD0015MIX',
  COSTZZ00JD0016: 'COSTZZ00JD0016MIX',
  COSTZZ00JD0017: 'COSTZZ00JD0016MIX',
  COSTZZ00JT0002: 'COSTZZ00JD0016MIX',
  COSTZZ00GS0001: 'COSTZZ00JD0016MIX',
  COSTZZ00JD0020: 'COSTZZ00JD0020MIX',
  COSTZZ00JD0021: 'COSTZZ00JD0020MIX',
  COSTZZ00JD0022: 'COSTZZ00JD0022MIX',
  COSTZZ00JD0023: 'COSTZZ00JD0022MIX',
  COSTZZ00JD0024: 'COSTZZ00JD0024MIX',
  COSTZZ00JD0025: 'COSTZZ00JD0024MIX',
  COSTZZ00JD0026: 'COSTZZ00JD0026MIX',
  COSTZZ00JD0027: 'COSTZZ00JD0026MIX',
  COSTZZ00JD0028: 'COSTZZ00JD0028MIX',
  COSTZZ00JD0029: 'COSTZZ00JD0028MIX',
  COSTZZ00JD0030: 'COSTZZ00JD0030MIX',
  COSTZZ00JD0031: 'COSTZZ00JD0030MIX',
  COSTZZ00JD0032: 'COSTZZ00JD0032MIX',
  COSTZZ00JD0033: 'COSTZZ00JD0032MIX',
  COSTZZ00JD0034: 'COSTZZ00JD0034MIX',
  COSTZZ00JD0035: 'COSTZZ00JD0034MIX',
  COSTZZ00JD0036: 'COSTZZ00JD0036MIX',
  COSTZZ00JD0037: 'COSTZZ00JD0036MIX',
  COSTZZ00JD0038: 'COSTZZ00JD0038MIX',
  COSTZZ00JD0039: 'COSTZZ00JD0038MIX',
  COSTZZ00JD0040: 'COSTZZ00JD0040MIX',
  COSTZZ00JD0041: 'COSTZZ00JD0040MIX',
  COSTZZ00JD0042: 'COSTZZ00JD0042MIX',
  COSTZZ00JD0043: 'COSTZZ00JD0042MIX',
  COSTZZ00JD0044: 'COSTZZ00JD0044MIX',
  COSTZZ00JD0045: 'COSTZZ00JD0044MIX',
} as const

export const baowuCostSubCompanies = [
  '永锋钢铁',
  '宝钢股份',
  '山钢股份',
  '八钢公司',
  '太钢集团',
  '马钢集团',
  '昆钢公司',
  '新钢集团',
  '中南钢铁',
]
