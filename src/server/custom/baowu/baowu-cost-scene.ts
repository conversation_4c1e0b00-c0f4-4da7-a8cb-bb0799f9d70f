import axios from 'axios'
import {
  ChatResponseError,
  ChatResponseErrorTypes,
  ChatResponseQueryMetric,
  RowsMetadata,
} from 'src/shared/common-types'
import { DEFAULT_TIMEOUT } from 'src/shared/constants'
import MetricConfig from 'src/server/MetricStore/metric-config'
import { QueryParamsVerified } from 'src/shared/metric-types'
import { getRecommendChartTypes } from 'src/server/AskBI/charts/recommend-chart-types'
import { companyArr2Where, getCompaniesFromOriginWhere, handleOriginWhere } from './baowu-utils'
import { baowuCostMetricMap, baowuCostSubCompanies } from './baowu-constant'

export async function handleBaowuCostQueryMetric({
  sceneId,
  projectId,
  metricConfig,
  verifiedMetricParams,
  conversationId,
  message,
  infoTexts,
  username,
  traceId,
  host,
}: {
  sceneId: string
  projectId: string
  metricConfig: MetricConfig
  verifiedMetricParams: QueryParamsVerified
  conversationId: string
  message: string
  infoTexts: string[]
  username: string
  traceId: string
  host: string
}): Promise<ChatResponseQueryMetric | ChatResponseError> {
  try {
    const params = {
      sceneId,
      projectId,
      queryParamsVerified: verifiedMetricParams,
      conversationId,
      message,
      infoTexts,
      username,
      traceId,
      host,
    }
    const response = await axios.post('https://bwsk-eplattest.baowugroup.cn/bzcw-mwares-sdapi/ask', params, {
      timeout: DEFAULT_TIMEOUT,
    })

    console.info('cost scene request params: ', JSON.stringify(params))
    console.info('Response data', response.data)
    if (response.data.ready === false) {
      if (response.data?.errType === ChatResponseErrorTypes.LATEST_DATA_NOT_EXIST) {
        const whereCompany = getCompaniesFromOriginWhere(verifiedMetricParams.queryParams?.where)
        return {
          sceneId,
          ready: false,
          taskType: 'chat-error',
          errType: ChatResponseErrorTypes.LATEST_DATA_NOT_EXIST,
          conversationId,
          unreadyReason: `该时间段下无数值`,
          subUnreadyReason: response.data?.subUnreadyReason,
          sql: response.data?.sql,
          queryParamsVerified: verifiedMetricParams,
          metricNames: verifiedMetricParams.queryParams.metricNames,
          infoTexts,
          whereCompany,
        }
      }
      return {
        sceneId,
        ready: false,
        taskType: 'chat-error',
        errType: ChatResponseErrorTypes.COST_SCENE_ERROR,
        conversationId,
        unreadyReason: response.data.msg,
        infoTexts: response.data.msg,
        queryParamsVerified: verifiedMetricParams,
      }
    }

    const rowsMetadata: RowsMetadata = response.data.rowsMetadata
    const rawMetricName = rowsMetadata.find((r) => r.type === 'metric')?.value.name as string
    const mappedMetricName = baowuCostMetricMap[rawMetricName as keyof typeof baowuCostMetricMap] || rawMetricName
    const mappedMetricInfo = metricConfig.allMetrics.find((p) => p.name === mappedMetricName)
    console.info('rawMetricName, mappedMetricName, mappedMetricInfo', rawMetricName, mappedMetricName, mappedMetricInfo)

    const updatedRowsMetadata: RowsMetadata = rowsMetadata.map((r) => {
      if (r.type === 'metric') {
        return {
          ...r,
          value: {
            ...r.value,
            name: mappedMetricName,
            description: mappedMetricInfo?.description || r.value.description,
            label: mappedMetricInfo?.label || r.value.label,
            formatTemplate: r.value.formatTemplate.replace(/3/g, '³'),
          },
        }
      }
      if (r.type === 'dimension' && r.value.name === 'ACCOUNT_NAME') {
        return {
          ...r,
          value: {
            ...r.value,
            name: 'COMPANY_INNER_CODE_DES',
          },
        }
      }
      return r
    })

    const updatedRows = response.data.rows.map((row: Record<string, any>) => {
      if (Object.prototype.hasOwnProperty.call(row, rawMetricName) && rawMetricName !== mappedMetricName) {
        return {
          ...row,
          [mappedMetricName]: row[rawMetricName],
          ...{ [rawMetricName]: undefined },
        }
      }
      return row
    })

    const updatedQueryParamsVerified = {
      ...response.data.queryParamsVerified,
      queryParams: {
        ...response.data.queryParamsVerified.queryParams,
        metricNames: response.data.queryParamsVerified.queryParams.metricNames.map((metricName: string) =>
          metricName === rawMetricName ? mappedMetricName : metricName,
        ),
      },
    }

    const recommendedChartTypes = getRecommendChartTypes({
      chartGroup: 'Others',
      rowsMetadata: updatedRowsMetadata,
      rows: updatedRows,
      verifiedMetricParams: updatedQueryParamsVerified,
    })

    return {
      ...response.data,
      rowsMetadata: updatedRowsMetadata,
      rows: updatedRows,
      queryParamsVerified: updatedQueryParamsVerified,
      chartType: recommendedChartTypes[0],
      recommendChartTypes: recommendedChartTypes,
    }
  } catch (error) {
    console.error('Error in handleBaowuCostQueryMetric:', error)
    return {
      sceneId,
      ready: false,
      taskType: 'chat-error',
      errType: ChatResponseErrorTypes.E_UNKNOWN,
      conversationId,
      unreadyReason: '成本场景链路异常',
      infoTexts,
      queryParamsVerified: verifiedMetricParams,
    }
  }
}

export function getCostSubCompanies({ where }: { where: string }) {
  const { companyInnerCodeDesWhere, otherWhere } = handleOriginWhere(where)

  if (companyInnerCodeDesWhere === '') {
    const subCompanies = baowuCostSubCompanies
    return [companyArr2Where(subCompanies), ...otherWhere].filter(Boolean).join(' AND ')
  } else {
    return where
  }
}
