import chalk from 'chalk'
import { executeAllXengineSql } from 'src/server/dao/xengine-query'
import MetricConfig from 'src/server/MetricStore/metric-config'
import { OlapRow } from 'src/shared/common-types'
import { getAllCompaniesFromAllDimensions } from 'src/shared/baowu-share-utils'
import {
  companyArr2Where,
  getPermissionTablePath,
  getCompaniesFromOriginWhere,
  getQueriedCompanyFromWhere,
  handleOriginWhere,
} from './baowu-utils'
// import { baowuSubCompanies } from './baowu-constant'

type UniversalProps = {
  metricConfig: MetricConfig
  parentCompanies: string[]
  queryLevel?: number
}

type SubCompanyListProps = {
  parentCompanyMap: {
    parentManageCompany: OlapRow[]
    // parentPropertyCompany: OlapRow[]
  }
} & Pick<UniversalProps, 'queryLevel' | 'metricConfig'>

type QueryWhereProps = {
  where: string
  hasOrderBy: boolean
} & Pick<UniversalProps, 'queryLevel' | 'metricConfig'>

type GenSqlProps = {
  table: string
  parentCompany: OlapRow[]
} & Pick<SubCompanyListProps, 'queryLevel'>

const slashReg = /-[^-]*$/

// 获取查询公司级别
export function getBaowuQueryLevel(queryMetric: any) {
  const { extraInfo: { groupbys_with_level: groupBysWithLevel = [] } = {}, where } = queryMetric || {}
  const whereCompany = getCompaniesFromOriginWhere(where)

  const isGroupCompanySubQuery = whereCompany.every((item) => item.startsWith('中国宝武钢铁集团有限公司'))
  const fistGroupBy = groupBysWithLevel?.[0] || {}
  if (fistGroupBy.groupBys === 'COMPANY_INNER_CODE_DES') {
    // 只有问集团二级子公司时才查二级子公司, 其他情况都认为是查所有子公司
    return fistGroupBy.groupByLevel > 0 && isGroupCompanySubQuery ? 1 : undefined
  }
}

export async function genNewSubCompanyQueryWhere({ metricConfig, where, queryLevel, hasOrderBy }: QueryWhereProps) {
  const { companyInnerCodeDesWhere, otherWhere } = handleOriginWhere(where)

  // //fix: 0311改了方案, 还是取子公司后按优先级展示, 后续可以删掉下面注释
  //  如果是问的集团二级子公司,则直接返回固定的42加子公司
  // if (queryLevel === 1) {
  //   const subCompanies =
  //     baowuSubCompanies.find((item: any) => item.parentCompany === '中国宝武钢铁集团有限公司-资产合并')?.children || []
  //   return [companyArr2Where(subCompanies), ...otherWhere].filter(Boolean).join(' AND ')
  // }

  let queriedCompanies: string[] = []
  if (companyInnerCodeDesWhere) {
    queriedCompanies = getQueriedCompanyFromWhere(companyInnerCodeDesWhere).map((item) => item.replace(slashReg, ''))
  }
  let subCompanies = await getBaowuSubCompanies({
    metricConfig,
    queryLevel,
    parentCompanies: [...new Set(queriedCompanies)],
  })
  if (hasOrderBy) {
    // 如果是排名类问题,按照问的是否是二级子公司, 来决定是展示法人口径还是资产口径
    /**
     * 问子公司排序回答逻辑：先查出该公司管理树下所有子公司，再取每家法人口径数据排序
     * 问二级子公司或二级单位排序回答逻辑：先查出管理树下42家子公司，再取每家资产合并口径数据排序
     * 但凡问“子公司”，就是指所有子公司，“二级单位”指二级子公司42家。
     */
    subCompanies = subCompanies.filter((comp) => {
      if (queryLevel === 1) {
        // return !comp.startsWith('中国宝武钢铁集团有限公司')
        return true
      } else {
        return comp.endsWith('法人')
      }
    })
  }
  if (subCompanies.length > 0) {
    return [companyArr2Where(subCompanies), ...otherWhere].filter(Boolean).join(' AND ')
  } else {
    return where
  }
}

export async function getBaowuSubCompanies({ metricConfig, parentCompanies, queryLevel }: UniversalProps) {
  const baowuParentCompanyMap = await getBaowuParentCompanyList({ metricConfig, parentCompanies, queryLevel })
  const baowuSubCompanies = await getBaowuSubCompanyList({
    metricConfig,
    parentCompanyMap: baowuParentCompanyMap,
    queryLevel,
  })
  const companies = getAllCompaniesFromAllDimensions(metricConfig.allDimensions)
  const allParentCompanies = getAllQueriedParentCompanies(baowuParentCompanyMap)
  const result: string[] = []

  baowuSubCompanies.forEach((COMPANY_INNER_CODE_DES) => {
    companies.forEach((comp) => {
      if (
        // 如果是资产合并或者管理合并,则证明是当前问的父公司, 不需要包含到子公司的结果中
        // 当问到的公司是中国宝武集团,及宝信(宝武集团的子公司)的子公司时, 结果会不包含宝信的管理/资产合并, 但是目前暂不考虑这种情况
        allParentCompanies.includes(COMPANY_INNER_CODE_DES) &&
        comp.endsWith('管理合并')
      ) {
        return
      }
      if (comp.startsWith(COMPANY_INNER_CODE_DES + '-')) {
        result.push(comp)
      }
    })
  })

  return result
}

function getAllQueriedParentCompanies(parentCompanyMap: SubCompanyListProps['parentCompanyMap']) {
  const allParentCompanies = mergeCompanies(
    parentCompanyMap.parentManageCompany,
    // parentCompanyMap.parentPropertyCompany,
  )
  return allParentCompanies
}

function getFieldLikeCondition(companies: string[], column: string, exact: boolean = false) {
  // fix: 如果like的公司比较多, xengine查sql会报错,需要有个办法不报错
  return companies
    .map((item) => {
      return `${column} LIKE '${item}${exact ? '' : '%'}'`
    })
    .join(' OR ')
}

export async function getBaowuParentCompanyList({ metricConfig, parentCompanies }: UniversalProps) {
  const tableName = getPermissionTablePath(metricConfig.name)
  // 获取管理树中级别最高公司
  const manageParentSqlWhere = getFieldLikeCondition(parentCompanies, 'COMPANY_INNER_CODE_DES', true)
  const queryManageParentCompanySql = `
      SELECT 
          LEFT(COMPANY_INNER_CODE, 4) AS COMPANY_INNER_CODE,
          NODE_LEVEL,
          COMPANY_INNER_CODE_DES
      FROM ${tableName}.T_ADS_FACT_PERMISSION_LEVEL_MANAGE
      ${manageParentSqlWhere ? 'WHERE ' + manageParentSqlWhere : ''}
  `

  // 暂时不从资产树获取子公司, 后续可能还会有专门问资产树子公司的情况, 所以先保留相关代码
  //  // 获取资产树中级别最高的母公司, 当存在COMPANY_INNER_CODE相同,node_level不同的多条数据时,只保留node_level最小的

  // const propertyParentSqlWhere = getFieldLikeCondition(parentCompanies, 'COMPANY_INNER_CODE_DES')
  // const queryPropertySubCompanySql = `
  //     WITH RankedCompanies AS (
  //         SELECT
  //             LEFT(COMPANY_INNER_CODE, 4) AS COMPANY_INNER_CODE,
  //             NODE_LEVEL,
  //             COMPANY_INNER_CODE_DES,
  //             ROW_NUMBER() OVER (PARTITION BY LEFT(COMPANY_INNER_CODE, 4) ORDER BY NODE_LEVEL ASC) AS rn
  //         FROM ${tableName}.T_ADS_FACT_PERMISSION_LEVEL_PROPERTY
  //         ${propertyParentSqlWhere ? 'WHERE ' + propertyParentSqlWhere : ''}
  //     )
  //     SELECT
  //         COMPANY_INNER_CODE,
  //         NODE_LEVEL,
  //         COMPANY_INNER_CODE_DES
  //     FROM RankedCompanies
  //     WHERE rn = 1
  //     ORDER BY NODE_LEVEL ASC;
  // `

  console.info(chalk.green('-----查询管理树母公司列表-----'))
  const parentManageCompany = await executeAllXengineSql(queryManageParentCompanySql)

  // 取结果中级别最高的所有不同母公司
  // console.info(chalk.green('-----查询资产树母公司列表-----'))
  // const parentPropertyCompany = await executeAllXengineSql(queryPropertySubCompanySql)

  // const newParentPropertyCompany = parentPropertyCompany.map((item) => {
  //   if (item.COMPANY_INNER_CODE.startsWith('1000')) {
  //     return {
  //       ...item,
  //       NODE_LEVEL: item.NODE_LEVEL - 1,
  //     }
  //   }
  //   return item
  // })

  console.info('parentManageCompany 实际查询结果为: ', parentManageCompany)
  // console.info('parentPropertyCompany 实际查询结果为: ', newParentPropertyCompany)

  return {
    parentManageCompany,
    // , parentPropertyCompany: newParentPropertyCompany
  }
}

function getSubCompanyFieldLikeCondition(
  companyInfo: OlapRow[],
  queryLevel?: number,
  alias = {
    INNER_CODE: 'PARENT.PARENT_COMPANY_INNER_CODE',
  },
) {
  const whereItems = companyInfo.map((item) => {
    const levelQueryWhere =
      queryLevel && queryLevel > 0 ? `AND NODE_LEVEL = ${Number(item.NODE_LEVEL) + Number(queryLevel)}` : ''
    const companyCodeQueryWhere = `LEFT(${alias.INNER_CODE}, 4) = '${item.COMPANY_INNER_CODE}'`
    return `( ${companyCodeQueryWhere} ${levelQueryWhere} )`
  })

  return whereItems.join(' OR ')
}

function mergeCompanies(
  manageCompanies: OlapRow[],
  // , propertyCompanies: OlapRow[]
) {
  const newManageCompanies = manageCompanies.map((item) => item.COMPANY_INNER_CODE_DES.replace(slashReg, ''))
  // const newPropertyCompanies = propertyCompanies.map((item) => item.COMPANY_INNER_CODE_DES.replace(slashReg, ''))
  return [
    ...new Set([
      ...newManageCompanies,
      //  ...newPropertyCompanies
    ]),
  ]
}

// 获取子公司
// 如果传了queryLevel, 表示要查某一级子公司,只要查层级树的表就行了;
// 如果没传queryLevel, 表示要查所有子公司, 就需要从两张表一起查并去重
export async function getBaowuSubCompanyList({ metricConfig, parentCompanyMap, queryLevel }: SubCompanyListProps) {
  const tableName = getPermissionTablePath(metricConfig.name)

  const manageTable = `${tableName}.T_ADS_FACT_PERMISSION_LEVEL_MANAGE`
  // const propertyTable = `${tableName}.T_ADS_FACT_PERMISSION_LEVEL_PROPERTY`
  // 获取管理树中级别最高公司
  const queryManageSubCompanySql = queryLevel
    ? genSqlWithLevel({ table: manageTable, parentCompany: parentCompanyMap.parentManageCompany, queryLevel })
    : genSqlWithoutLevel({ table: manageTable, parentCompany: parentCompanyMap.parentManageCompany })

  // // 获取资产树中级别最高的母公司
  // const queryPropertySubCompanySql = queryLevel
  //   ? genSqlWithLevel({ table: propertyTable, parentCompany: parentCompanyMap.parentPropertyCompany, queryLevel })
  //   : genSqlWithoutLevel({ table: propertyTable, parentCompany: parentCompanyMap.parentPropertyCompany })

  console.info(chalk.green('-----查询管理树子公司列表-----'))
  const subManageCompany = await executeAllXengineSql(queryManageSubCompanySql)

  // console.info(chalk.green('-----查询资产树子公司列表-----'))
  // const subPropertyCompany = await executeAllXengineSql(queryPropertySubCompanySql)

  return mergeCompanies(
    subManageCompany,
    //  subPropertyCompany
  )
}

function genSqlWithLevel({ parentCompany, queryLevel, table }: GenSqlProps) {
  const where = getSubCompanyFieldLikeCondition(parentCompany, queryLevel, {
    INNER_CODE: 'SUB.TARGET_COMPANY_INNER_CODE',
  })

  return `
        SELECT DISTINCT 
            PARENT.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES ,
            PARENT.COMPANY_INNER_CODE AS COMPANY_INNER_CODE ,
            PARENT.PARENT_COMPANY_INNER_CODE AS PARENT_COMPANY_INNER_CODE,
            PARENT.NODE_LEVEL AS NODE_LEVEL 
        FROM ${table} PARENT
        INNER JOIN ${table}_ALLNEXT SUB 
        ON SUB.COMPANY_INNER_CODE = PARENT.COMPANY_INNER_CODE ${where ? ' AND (' + where + ' )' : ''} 
        WHERE  PARENT.COMPANY_INNER_CODE_DES != ''
        ORDER BY PARENT_COMPANY_INNER_CODE ASC, COMPANY_INNER_CODE ASC
     `
}

function genSqlWithoutLevel({ parentCompany, table }: GenSqlProps) {
  const manageWhere = getSubCompanyFieldLikeCondition(parentCompany, undefined, {
    INNER_CODE: 'PARENT.PARENT_COMPANY_INNER_CODE',
  })

  const allNextWhere = getSubCompanyFieldLikeCondition(parentCompany, undefined, {
    INNER_CODE: 'SUB.TARGET_COMPANY_INNER_CODE',
  })

  return `
        SELECT DISTINCT
            CONCAT(PARENT.COMPANY_INNER_CODE_DES,'-管理合并') AS COMPANY_INNER_CODE_DES,
            PARENT.PARENT_COMPANY_INNER_CODE AS PARENT_COMPANY_INNER_CODE,
            PARENT.COMPANY_INNER_CODE AS COMPANY_INNER_CODE
          
        FROM ${table} PARENT
        
        ${manageWhere ? `WHERE ${manageWhere} AND PARENT.COMPANY_INNER_CODE_DES != ''` : ''}

        UNION
      
        SELECT DISTINCT
            TRIM(SUB.COMPANY_INNER_CODE_DES) AS COMPANY_INNER_CODE_DES,
            TRIM(SUB.TARGET_COMPANY_INNER_CODE) AS PARENT_COMPANY_INNER_CODE,
            TRIM(SUB.COMPANY_INNER_CODE) AS COMPANY_INNER_CODE
        FROM ${table}_ALLNEXT SUB
      
        ${allNextWhere ? `WHERE ${allNextWhere} AND SUB.COMPANY_INNER_CODE_DES != ''` : ''}
      
        ORDER BY PARENT_COMPANY_INNER_CODE ASC, COMPANY_INNER_CODE ASC
        `
}
