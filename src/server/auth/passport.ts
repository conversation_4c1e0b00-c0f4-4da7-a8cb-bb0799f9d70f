/* eslint-disable @typescript-eslint/no-namespace */
import passport from 'passport'
import { <PERSON><PERSON>, Request } from 'express'
import { Strategy as JWTStrategy, ExtractJwt, JwtFromRequestFunction } from 'passport-jwt'
import { TOKEN_BI } from 'src/shared/constants'
import { getQueryState } from 'src/shared/common-utils'
import { askBIApiUrls, loginWhitelist } from 'src/shared/url-map'
import { prisma } from '../dao/db'

declare global {
  namespace Express {
    interface User {
      username: string
      id: string
      // groups: {
      //   id: string
      //   groupName: string
      // }[]
    }
  }
}

const cookieExtractor: (key: string) => JwtFromRequestFunction<Request> = (key) => (req) => req?.cookies[key] ?? null

// JWT配置选项
export const jwtOptions = {
  jwtFromRequest: ExtractJwt.fromExtractors([ExtractJwt.fromAuthHeaderAsBearerToken(), cookieExtractor(TOKEN_BI)]),
  secretOrKey: 'bestlyg', // 替换为真实的安全密钥，在生产环境中要妥善保管
}

// 创建JWT策略
passport.use(
  new JWTStrategy(jwtOptions, (jwtPayload: { username: string }, done: (...v: any[]) => void) => {
    const { username } = jwtPayload
    prisma.xUser.findFirst({ where: { username } }).then(
      (user) => {
        done(null, user)
      },
      () => {
        return done(null, false)
      },
    )
  }),
)

export const passportMiddleware: Handler = (req, res, next) => {
  passport.authenticate(
    'jwt',
    {
      session: false,
    },
    (err: any, user: Express.User, _: string) => {
      if (err || !user) {
        const queryState = getQueryState(false, req.query as Record<string, string>)
        if (
          req.url.startsWith('/assets') ||
          req.url.startsWith('/img') ||
          loginWhitelist.some((whitelistedUrl) => req.url.startsWith(whitelistedUrl)) ||
          req.url.startsWith(askBIApiUrls.diProxyLogin.namespace) ||
          req.url.startsWith(askBIApiUrls.diProxyLogin.service) ||
          req.url.startsWith(askBIApiUrls.diProxyLogin.pod) ||
          req.url.startsWith(askBIApiUrls.diProxyLogin.diDefaultProxy) ||
          queryState.enableAutoLogin ||
          !req.url.startsWith('/api')
        ) {
          next()
          return
        }
        // console.info('JWT', err, user, info)
        return res.status(200).json({
          code: 401,
          message: '认证失败',
        })
      }
      req.user = user
      next()
    },
  )(req, res, next)
}

export default passport
