import axios from 'axios'
import { Request } from 'express'
import chalk from 'chalk'
import { XENGINE_QUERY_TIMEOUT } from 'src/shared/constants'
import { askBIApiUrlsWithoutBaseUrl } from 'src/shared/url-map'
import { Datasource, OlapRow } from '../../shared/common-types'
import { generateXengineUrl } from '../utils'
import { saveAskBotBusinessLogs } from '../winston-log'

export async function executeAllXengineSql(
  sql: string,
  logsParams?: {
    username: string
    traceId: string
    host: string
    conversationId: string
    sceneId: string
    projectId: string
    req?: Request
  },
): Promise<OlapRow[]> {
  let resultSql = sql
  const startTime = Date.now()
  // 替换xengine不支持的sql
  if (sql.includes('information_schema.TABLES')) {
    resultSql = 'show full tables;'
  } else if (sql.includes('information_schema.COLUMNS')) {
    const sqlSplit = sql.split('=')
    const fullTableName = sqlSplit[sqlSplit.length - 1].trim().replace(/'/g, '')
    resultSql = `show full columns from ${fullTableName}`
  } else if (sql.includes('--虚拟表 SQL')) {
    const endIndex = sql.indexOf('--物理表')
    resultSql = endIndex !== -1 ? sql.substring(0, endIndex) : sql
  }
  console.info(chalk.cyan(`Execute Xengine sql, sql is:${resultSql}`))
  const url = generateXengineUrl(askBIApiUrlsWithoutBaseUrl.xengineDatasource.executeSql)
  const headers = {
    Authorization: 'init',
    Accept: 'application/json',
    'Content-Type': 'application/json',
    Connection: 'keep-alive',
  }
  const data = {
    sqlContent: resultSql,
  }

  const response = await axios.post(url, data, { headers, timeout: XENGINE_QUERY_TIMEOUT })
  console.info(
    `Xengine sql Api response data:一共 ${response.data.data[0]['data'].length} 条数据。${response.data.data[0]['data'].length >= 5 ? '前 5 条为' : '数据为'}`,
    response.data.data[0]['data'].slice(0, 5),
  )
  if (logsParams) {
    saveAskBotBusinessLogs({
      serviceType: 'web_service',
      moduleType: 'sql-to-data',
      host: logsParams.host,
      username: logsParams.username,
      traceId: logsParams.traceId,
      startTime,
      input: {
        SQL: resultSql,
      },
      output: {
        length: response.data.data[0]['data']?.length,
        example: response.data.data[0]['data']?.slice(0, 5),
      },
      resultCode: response.data.code,
      semanticProjectId: logsParams.projectId,
      semanticSceneId: logsParams.sceneId,
      debug: {
        conversationId: logsParams.conversationId,
        sceneId: logsParams.sceneId,
        url,
        method: 'POST',
        headers: { headers, timeout: XENGINE_QUERY_TIMEOUT },
        response: response.data,
        rows: response.data.data[0]['data']?.slice(0, 100),
      },
    })
  }

  return response.data.data[0]['data']
}

/**
 * 把逻辑表的 sql 转成 xengine 物理表的 sql
 */
export async function executeUnwrapSql(dbConfig: Datasource, sql: string): Promise<string> {
  console.info(chalk.cyan(`Xengine execute sql, sql is:${sql}, dbConfig is:${JSON.stringify(dbConfig)}`))
  const url = `http://${dbConfig.host}:${dbConfig.port}/api/v1/statements/unwrapSql`
  const headers = {
    Authorization: 'init',
    Accept: 'application/json',
    'Content-Type': 'application/json',
    Connection: 'keep-alive',
  }
  const data = {
    statement: sql,
    database: dbConfig.databaseName,
    catalog: 'dipeak',
  }

  try {
    const response = await axios.post(url, data, { headers, timeout: XENGINE_QUERY_TIMEOUT })
    console.info('executeUnwrapSql res data is:', response.data.data)
    return response.data.data
  } catch (error) {
    console.error('Xengine get unwrap sql failed with error:', (error as Error).message)
    return 'Get original sql error!'
  }
}
