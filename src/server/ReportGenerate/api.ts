/**
 * 报告生成 API
 */
import express, { Router, Request, Response } from 'express'
import axios from 'axios'
import { askBIApiUrlsWithoutBaseUrl } from 'src/shared/url-map'
import { DOC_REPORT_TIMEOUT } from 'src/shared/constants'
import { getParamsExtractUrl, removeDomainFromUrl } from '../utils'

const router: Router = express.Router()

/**
 * 生成大纲阶段的保存
 */
router.post('/outline/create', async (req: Request, res: Response) => {
  try {
    console.info('outline/create', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.postOutlineCreate,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('outline/create', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取维度码值_new
 */
router.get('/column-value', async (req: Request, res: Response) => {
  try {
    console.info('column-value', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.getColumnValue, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('column-value-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 模板列表
 */
router.get('/template/list', async (req: Request, res: Response) => {
  try {
    console.info('template/list', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.getTemplateList, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    // 暂时node处理掉后端返回的url中带ip和端口前缀的，需要在下次迭代的时候让后端去掉前缀，不要暴露后台内部的 url 给浏览器
    const handleData = { ...result.data }
    if (handleData.data && handleData.data.templateList.length > 0) {
      handleData.data.templateList.forEach((item: { thumbnailPath: string }) => {
        if (item.thumbnailPath) {
          item.thumbnailPath = removeDomainFromUrl(item.thumbnailPath)
        }
      })
    }

    return res.json(handleData)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('template/list-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})
/**
 * 获取运算符列表_new
 */
router.get('/data-filter-operator', async (req: Request, res: Response) => {
  try {
    console.info('data-filter-operator', req.query)
    const result = await axios.get(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.dataFilterOperator,
      {
        params: req.query,
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('data-filter-operator-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})
/**
 * 获取指标模型字段分类情况
 */
router.get('/column-classify', async (req: Request, res: Response) => {
  try {
    console.info('column-classify', req.query)
    const result = await axios.get(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.getColumnClassify,
      {
        params: req.query,
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('column-classify-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取报告列表
 */
router.get('/report/list', async (req: Request, res: Response) => {
  try {
    console.info('report/list', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.getReportList, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('report/list-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取报告详情
 */
router.get('/report-detail', async (req: Request, res: Response) => {
  try {
    console.info('report-detail', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.getReportDetail, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('report-detail-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 删除报告
 */
router.delete('/report', async (req: Request, res: Response) => {
  try {
    const result = await axios.delete(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.deleteReport, {
      params: req.query,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('delete - report-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 保存报告
 */
router.post('/report/save', async (req: Request, res: Response) => {
  try {
    console.info('report/save', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.saveReport,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('report/save-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 导出报告
 */
router.get('/report/export', async (req: Request, res: Response) => {
  try {
    console.info('report/export', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.exportReport, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    const handleData = { ...result.data }
    const { pdfUrl, wordUrl } = handleData.data || {}

    if (pdfUrl) {
      handleData.data.pdfUrl = removeDomainFromUrl(pdfUrl)
    }
    if (wordUrl) {
      handleData.data.wordUrl = removeDomainFromUrl(wordUrl)
    }

    return res.json(handleData)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('report/export-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 段落重命名
 */
router.put('/section-name', async (req: Request, res: Response) => {
  try {
    console.info('section-name', req.body)
    const result = await axios.put(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.updateSectionName,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('section-name', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})
/**
 * 更新报告大纲
 */
router.post('/template/outline/update', async (req: Request, res: Response) => {
  try {
    console.info('/template/outline/update', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.updateOutline,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('/template/outline/update', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 更新目标段落
 */
router.post('/section/update', async (req: Request, res: Response) => {
  try {
    console.info('section/update', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.updateSection,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('section/update-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取报告段落配置
 */
router.get('/template/section-config', async (req: Request, res: Response) => {
  try {
    console.info('section-config', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.sectionConfig, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('section-config-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取模版段落配置
 */
router.get('/template/section-config', async (req: Request, res: Response) => {
  try {
    console.info('template/section-config', req.query)
    const result = await axios.get(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.templateSectionConfig,
      {
        params: req.query,
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('template/section-config-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 更新目标段落
 */
router.post('/report-copy', async (req: Request, res: Response) => {
  try {
    console.info('report-copy', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.reportCopy,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('report-copy', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 新增或者更新段落数据算子
 */
router.post('/create-or-update-data-operator', async (req: Request, res: Response) => {
  try {
    console.info('create-or-update-data-operator', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.createOrUpdateDataOperator,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('create-or-update-data-operator', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 *  新增或者更新段落文本算子
 */
router.post('/create-or-update-text-operator', async (req: Request, res: Response) => {
  try {
    console.info('create-or-update-text-operator', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.createOrUpdateTextOperator,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('create-or-update-text-operator', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取数据算子计算类型列表
 */
router.get('/data-operator-compute-type', async (req: Request, res: Response) => {
  try {
    console.info('data-operator-compute-type', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.computeTypeList, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('data-operator-compute-type-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取文本算子类型
 */
router.get('/text-operator-type', async (req: Request, res: Response) => {
  try {
    console.info('text-operator-type', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.textOperatorType, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('text-operator-type-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取算子的类型列表
 */
router.get('/data-operator-type', async (req: Request, res: Response) => {
  try {
    console.info('data-operator-type', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.dataOperatorType, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('data-operator-type-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取数据算子的码值
 */
router.get('/list-data-operator-column-value', async (req: Request, res: Response) => {
  try {
    console.info('list-data-operator-column-value', req.query)
    const result = await axios.get(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.listDataOperatorColumnValue,
      {
        params: req.query,
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('list-data-operator-column-value', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取模版详情
 */
router.get('/template/detail', async (req: Request, res: Response) => {
  try {
    console.info('template/detail', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.templateDetail, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('template/detail', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 *  数据算子预览
 */
router.post('/get-data-operator-preview', async (req: Request, res: Response) => {
  try {
    console.info('get-data-operator-preview', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.dataOperatorPreview,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('get-data-operator-preview', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 文本算子预览
 */
router.post('/get-text-operator-preview', async (req: Request, res: Response) => {
  try {
    console.info('get-text-operator-preview', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.textOperatorPreview,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('get-text-operator-preview', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取时间粒度
 */
router.get('/time_granularity', async (req: Request, res: Response) => {
  try {
    console.info('time_granularity', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.timeGranularity, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('time_granularity', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取模版段落算子详情
 */
router.get('/template/section-operators', async (req: Request, res: Response) => {
  try {
    console.info('/template/section-operators', req.query)
    const result = await axios.get(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.getSectionOperators,
      {
        params: req.query,
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('/template/section-operators', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 保存模版
 */
router.post('/template/save', async (req: Request, res: Response) => {
  try {
    console.info('/template/save', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.saveTemplate,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('/template/save - Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 新增模版
 */
router.post('/template/create', async (req: Request, res: Response) => {
  try {
    console.info('/template/create', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.createTemplate,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('/template/create - Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 删除模板
 */
router.delete('/template/delete', async (req: Request, res: Response) => {
  try {
    console.info('template/delete-Error', req.body)
    const result = await axios.delete(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.deleteTemplate,
      {
        params: req.query,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('template/delete-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取全文
 */
router.get('/full-report', async (req: Request, res: Response) => {
  try {
    console.info('full-report', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.getFullReport, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('full-report', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

router.get('/science-city/get-task-list', async (req: Request, res: Response) => {
  try {
    console.info('get_task_list', req.query)
    const result = await axios.get(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.getScienceCityTaskList,
      {
        params: req.query,
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    //console.info('get_task_list', result.data.data)
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('get_task_list', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

export default router
