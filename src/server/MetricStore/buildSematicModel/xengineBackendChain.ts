import { exec } from 'child_process'
import axios from 'axios'
import chalk from 'chalk'
import { nanoid } from 'nanoid'
import { SemanticMetric } from '@prisma/client'
import { generateDIUrl, generateXengineUrl } from 'src/server/utils'
import { askBIApiUrlsWithoutBaseUrl } from 'src/shared/url-map'
import {
  CategoricalDimension,
  SemanticModel,
  Measure,
  MeasureAgg,
  MetricModelType,
  ProjectType,
  SceneType,
  TimeDimensionDatum,
  TimeDimensionType,
  TimeDimensionTypes,
  Dimension,
  TimeDimension,
  columnCodeValue,
} from 'src/shared/metric-types'
import {
  APIResponse,
  TimeDimensionFormats,
  TimeGranularityMinDateOptions,
  TimeGranularityMinType,
} from 'src/shared/common-types'
import { prisma } from 'src/server/dao/prisma-init'
import { PROCESS_ENV } from 'src/server/server-constants'
import { codeValueSplitChar } from 'src/shared/constants'
import { DataBuildContext, DataChainItem, DataTrigger } from './type'

const chainItemName = 'inner_xengine'

class XengineBackendTrigger implements DataTrigger {
  triggerInterval: number // 触发周期, 单位秒
  triggerFlag: boolean
  triggerTimer: NodeJS.Timeout
  constructor(triggerInterval: number = 600) {
    this.triggerInterval = triggerInterval * 1000
    this.triggerFlag = true
    this.triggerTimer = setInterval(() => {
      this.triggerFlag = true
    }, this.triggerInterval)
  }

  async trigger(inputContext: DataBuildContext): Promise<{ flag: boolean; dataContext: DataBuildContext }> {
    if (this.triggerFlag) {
      this.triggerFlag = false
      console.info(chalk.green(`XengineBackendTrigger was activated at ${Date()}`))
      return { flag: true, dataContext: inputContext }
    }
    return { flag: false, dataContext: inputContext }
  }
}

type XengineBackendLoader = {
  load: (inputContext: DataBuildContext) => Promise<{ flag: boolean; dataContext: DataBuildContext }>
}

type XengineBackendSinker = {
  sink: (inputContext: DataBuildContext) => Promise<{ flag: boolean; dataContext: DataBuildContext }>
}

// 获取维度码值 - 新接口
async function pullDimensionValues(modelName: string) {
  const response = await axios
    .get<APIResponse<columnCodeValue[]>>(
      `${generateXengineUrl(askBIApiUrlsWithoutBaseUrl.model.DownloadAllColumnCodeValues)}?modelName=${encodeURIComponent(modelName)}&withoutTimeDimensionCodes=true`,
      {
        headers: { Authorization: 'init' },
      },
    )
    .catch((error) => {
      console.error(`semantic model ${modelName} dimension codes is empty, detail ${error}`)
    })

  if (!response?.data?.data) {
    console.info(`get model ${modelName} dimension codes empty`)
    return {} as Record<string, string[]>
  }
  const columnCodeValueList: columnCodeValue[] = response.data.data
  const result = columnCodeValueList.reduce(
    (acc, cur) => {
      const codeFormatValue = cur.codeValues.map((code) => {
        if (code.synonyms && code.synonyms.length > 0) {
          return code.codeValue + codeValueSplitChar + code.synonyms.join(codeValueSplitChar)
        }
        return code.codeValue
      })
      acc[cur.columnName] = codeFormatValue
      return acc
    },
    {} as Record<string, string[]>,
  )
  return result
}

// 获取场景 data model 数据
const getSceneModelDesc = async (
  scene: SceneType,
  modelUpdateTime: Map<string, number>,
): Promise<MetricModelType[]> => {
  const result: MetricModelType[] = []
  for (const modelId of scene.modelNames || []) {
    if (!modelId) {
      console.error(chalk.red(`${scene.label || '此'}-场景没有关联指标模型，请检查`))
      throw new Error(`${scene.label || '此'}-场景没有关联指标模型，请检查`)
    }
    const apiUrl: string = `${generateXengineUrl(askBIApiUrlsWithoutBaseUrl.model.meta)}?name=${encodeURIComponent(modelId)}&withColumns=false`
    console.info(`get scene ${scene.id} ${scene.label} model ${modelId} from xengine backend Url: ${apiUrl}`)
    const [model] = await Promise.all([
      axios.get(apiUrl, { headers: { Authorization: 'init' } }).then((res) => {
        // timeColumnDesc.granularity 都改成小写
        if (res.data.data.dataModelDesc.timeColumnDesc?.granularity) {
          res.data.data.dataModelDesc.timeColumnDesc.granularity =
            res.data.data.dataModelDesc.timeColumnDesc.granularity.toLowerCase()
        }
        // 重写时间列类型，转成标准格式 string | date | datetime
        if (res.data.data.dataModelDesc.timeColumnDesc?.column?.type) {
          const columnType = res.data.data.dataModelDesc.timeColumnDesc.column.type
          const timeDimensionType = /char|text/gi.test(columnType)
            ? 'string'
            : /date|timestamp/gi.test(columnType)
              ? 'date'
              : 'datetime'
          res.data.data.dataModelDesc.timeColumnDesc.column.type = timeDimensionType
        }
        return res.data.data
      }),
    ]).catch((error) => {
      if (error.message.indexOf('模型不存在') > 0) {
        console.error(error.message)
        return [null]
      } else {
        console.error(`scene ${scene.id} ${scene.label} pull model ${modelId} error, detail: ${error.message}`)
        return [null]
      }
    })
    if (!model) {
      console.error(chalk.red(`scene ${scene.id} ${scene.label} model ${modelId} information is null, skip the model!`))
      continue
    }
    // 依据时间戳判定是否需要更新
    if (modelUpdateTime && modelUpdateTime.has(modelId)) {
      const remoteUpdateTime = (model as MetricModelType).updateTime || 0
      const existUpdateTime = modelUpdateTime.get(modelId) || 0
      if (remoteUpdateTime <= existUpdateTime) {
        //console.error(
        //  chalk.green(
        //    `scene ${scene.id} ${scene.label} model ${modelId} no need update, remote ${remoteUpdateTime} local ${existUpdateTime}`,
        //  ),
        //)
        continue
      }
    }
    // 获取维度码值
    //const allCodeValues = await pullDimensionValues(modelId)
    //if (allCodeValues) {
    //  for (const dimension of model.dataModelDesc.dimensions) {
    //    dimension.values = allCodeValues[dimension.name]
    //  }
    //}
    result.push(model)
  }
  return result
}

type LoaderData = {
  dimensions: Map<string, Dimension[]>
  measures: Map<string, Measure[]>
  dataModels: Map<string, SemanticModel>
  scene2ProjectId: Map<string, string>
  model2ProjectId: Map<string, string[]>
  model2SceneId: Map<string, string[]>
}

// 获取DB中已有数据模型时间戳
async function getExistModelUpdateStamp(): Promise<Map<string, number>> {
  const records = await prisma.semanticModel.findMany({
    select: {
      name: true,
      createdAt: true,
    },
  })
  const result = new Map<string, number>()
  for (const item of records) {
    result.set(item.name, item.createdAt.getTime())
  }
  return result
}

/** 加载数据源
 * 1. Operator接口获取 Project - Scene - SemanticModel
 * 2. Backend 接口获取 DataModelDesc meta 数据
 * 3. 转换出 SemanticModel
 */
const loader: XengineBackendLoader = {
  load: async (inputContext: DataBuildContext) => {
    /** 1. Operator接口获取 Project - Scene - SemanticModel */
    const data: LoaderData = {
      dimensions: new Map<string, Dimension[]>(),
      measures: new Map<string, Measure[]>(),
      dataModels: new Map<string, SemanticModel>(),
      scene2ProjectId: new Map<string, string>(),
      model2ProjectId: new Map<string, string[]>(),
      model2SceneId: new Map<string, string[]>(),
    }
    try {
      console.info('get project list use Url: ' + generateDIUrl(askBIApiUrlsWithoutBaseUrl.project.list))
      const responseProjects = await axios.get<APIResponse<ProjectType[]>>(
        generateDIUrl(askBIApiUrlsWithoutBaseUrl.project.list),
        {
          headers: { Authorization: 'init' },
        },
      )
      if (!responseProjects.data?.data) {
        console.info('get project list result is empty')
        return { flag: false, dataContext: inputContext }
      }
      const projectList: ProjectType[] = responseProjects.data.data
      //console.info(chalk.green('get project list result:\n', JSON.stringify(responseProjects.data.data)))

      /** 2. Backend 接口获取 SemanticModel */
      const existModelUpdateTime = await getExistModelUpdateStamp()
      const allDataModelDesc: MetricModelType[] = []
      for (const project of projectList) {
        for (const scene of project.scenes) {
          // 跳过非 AskBI 项目
          if ((scene.agent || '').indexOf('BI') === -1) {
            continue
          }
          const sceneModelDesc = await getSceneModelDesc(scene, existModelUpdateTime)
          allDataModelDesc.push(...sceneModelDesc)
          sceneModelDesc.map((model) => {
            data.scene2ProjectId.set(scene.id, project.id)
            // model 可以对应多个 project
            if (data.model2ProjectId.has(model.name)) {
              data.model2ProjectId.set(model.name, [...(data.model2ProjectId.get(model.name) || []), project.id])
            } else {
              data.model2ProjectId.set(model.name, [project.id])
            }
            // model 可以对应多个 scene
            if (data.model2SceneId.has(model.name)) {
              data.model2SceneId.set(model.name, [...(data.model2SceneId.get(model.name) || []), scene.id])
            } else {
              data.model2SceneId.set(model.name, [scene.id])
            }
          })
        }
      }

      /** 3. 转换出 SemanticModel，并更新
       * 只更新 DataModelDesc 生成的Measure/Dimension, 不用做 Metric 上卷更新
       */
      for (const model of allDataModelDesc) {
        // 维度
        const dimensionsOneModel: Dimension[] = []
        for (const e of model.dataModelDesc.dimensions || []) {
          // 新数据（多类型维度）
          if (e.dimensionType) {
            switch (e.dimensionType) {
              case 'TIME':
              case 'TIME_DEFAULT': {
                // 过滤非法数据
                if (!e.granularity || !e.timeFormatPattern) {
                  console.error(chalk.red(`model ${model.name} dimension ${e.name} granularity or format is null`))
                  continue
                }
                const dimension: TimeDimension = {
                  id: e.name,
                  name: e.name,
                  label: e.nameZh || '',
                  synonyms: e.synonyms || [],
                  filterSwitch: e.filterSwitch ?? true,
                  description: e.comment || '',
                  expr: e.expression || '',
                  type: e.dimensionType.toLocaleLowerCase() as 'time' | 'time_default',
                  typeParams: {
                    timeFormat: e.timeFormatPattern
                      .replace(/YYYY/g, 'yyyy')
                      .replace(/mm/g, 'MM')
                      .replace(/DD/g, 'dd') as (typeof TimeDimensionFormats)[number],
                    timeGranularity: e.granularity.trim().toLocaleLowerCase() as TimeGranularityMinType,
                    timeType: /char|text/gi.test(e.columnDesc.type)
                      ? 'string'
                      : /date|timestamp/gi.test(e.columnDesc.type)
                        ? 'date'
                        : 'datetime',
                  },
                }
                dimensionsOneModel.push(dimension)
                break
              }
              case 'CATEGORY': {
                const dimension: CategoricalDimension = {
                  id: e.name,
                  name: e.name,
                  label: e.nameZh || '',
                  synonyms: e.synonyms || [],
                  filterSwitch: e.filterSwitch ?? true,
                  description: e.comment || '',
                  expr: e.expression || '',
                  type: 'categorical',
                  typeParams: {},
                }
                dimensionsOneModel.push(dimension)
                break
              }
              default:
                console.error(chalk.red(`model ${model.name} dimension ${e.name} unknown type ${e.dimensionType}`))
            }
          } else {
            // 历史数据兼容
            const dimension: CategoricalDimension = {
              id: e.name,
              name: e.name,
              label: e.nameZh || '',
              synonyms: e.synonyms || [],
              filterSwitch: e.filterSwitch ?? true,
              description: e.comment || '',
              expr: e.expression || '',
              type: 'categorical',
              typeParams: {},
            }
            dimensionsOneModel.push(dimension)
          }
        }
        data.dimensions.set(model.name, dimensionsOneModel)

        // 度量
        const measuresOneModel: Measure[] = (model.dataModelDesc.measures || []).map((e) => {
          const agg = (e.function.function as string).trim().toLocaleLowerCase() as MeasureAgg
          let createMetric = true
          if (e?.createMetric === false) {
            createMetric = e.createMetric
          }
          return {
            id: e.name,
            name: e.name,
            label: e.nameZh || '',
            synonyms: e.synonyms || [],
            description: e.comment || '',
            expr: e.expression,
            agg,
            createMetric,
            formatTemplate: e.formatTemplate ? e.formatTemplate : '',
            createRankMetric: false,
          }
        })
        data.measures.set(model.name, measuresOneModel)

        // 转换 model 时间列信息
        let timeDimensionDatum: TimeDimensionDatum | undefined = undefined
        // 检查时间列信息
        if (model.dataModelDesc.timeColumnDesc == null || model.dataModelDesc.timeColumnDesc.column == null) {
          timeDimensionDatum = undefined
        } else {
          // 检查时间字段数值类型, 支持 string, date, datetime
          if (!TimeDimensionTypes.includes(model.dataModelDesc.timeColumnDesc.column.type as any)) {
            console.error(
              chalk.red(
                `model ${model.name} Time dimension type ${model.dataModelDesc.timeColumnDesc.column.type} is not supported`,
              ),
            )
          }
          // 检查最小时间粒度类型, 支持 day, mouth, year
          if (
            !TimeGranularityMinDateOptions.map((o) => o.value.toLowerCase()).includes(
              model.dataModelDesc.timeColumnDesc.granularity,
            )
          ) {
            console.error(
              chalk.red(
                `model ${model.name} Time granularity min ${model.dataModelDesc.timeColumnDesc.granularity} is not supported`,
              ),
            )
          }
          // 处理时间列样式 统一到 yyyy, MM, dd 组合,不出现其余大小写组合
          if (model.dataModelDesc.timeColumnDesc.formatPattern) {
            model.dataModelDesc.timeColumnDesc.formatPattern = model.dataModelDesc.timeColumnDesc.formatPattern
              .replace(/YYYY/g, 'yyyy')
              .replace(/mm/g, 'MM')
              .replace(/DD/g, 'dd') as (typeof TimeDimensionFormats)[number]
          } else {
            // meta接口脏数据打印个日志，反馈Backend
            console.warn(`model ${model.name} timeColumnDesc.formatPattern is null`)
          }

          timeDimensionDatum = {
            timeDimensionName: model.dataModelDesc.timeColumnDesc.column.name,
            timeDimensionType: model.dataModelDesc.timeColumnDesc.column.type as TimeDimensionType,
            timeDimensionFormat: model.dataModelDesc.timeColumnDesc.formatPattern,
            timeGranularityMin: model.dataModelDesc.timeColumnDesc.granularity,
          }
        }

        const semanticModel: SemanticModel = {
          name: model.name,
          tableName: model.dataModelDesc.factTable,
          sourceCreateTime: model.updateTime ? model.updateTime : model.createTime,
        }
        // 存在时间维度
        if (timeDimensionDatum) {
          semanticModel.timeDimensionName = timeDimensionDatum.timeDimensionName
          semanticModel.timeDimensionType = timeDimensionDatum.timeDimensionType
          semanticModel.timeDimensionFormat = timeDimensionDatum.timeDimensionFormat
          semanticModel.timeGranularityMin = timeDimensionDatum.timeGranularityMin
        }
        data.dataModels.set(model.name, semanticModel)
      }
    } catch (e) {
      console.error(e)
      return { flag: false, dataContext: inputContext }
    }
    inputContext.data = data
    return { flag: true, dataContext: inputContext }
  },
}

// 获取数据模型时间戳
async function getModelUpdateStamp(models: Map<string, SemanticModel>): Promise<Map<string, number>> {
  const modelNames: string[] = []
  for (const model of models) {
    modelNames.push(model[0])
  }
  const records = await prisma.semanticModel.findMany({
    where: {
      name: {
        in: modelNames,
      },
    },
    select: {
      name: true,
      createdAt: true,
    },
  })
  const result = new Map<string, number>()
  for (const item of records) {
    result.set(item.name, item.createdAt.getTime())
  }
  return result
}

// 执行shell脚本函数
function executeShellScript(script: string): Promise<string> {
  return new Promise((resolve, reject) => {
    exec(script, (error: Error | null, stdout: string, stderr: string) => {
      if (error) {
        reject(error)
        return
      }
      if (stderr) {
        reject(new Error('Shell script error output: ' + stderr))
        return
      }
      resolve(stdout)
    })
  })
}

/** 持久化 SemanticModel 到 MySQL */
const sinker: XengineBackendSinker = {
  sink: async (inputContext: DataBuildContext) => {
    const hostname = (await executeShellScript('uname -n')) || ''
    const debugInfo: string =
      hostname.replace(/[\r\n]+/gm, '') + ' | ' + PROCESS_ENV.ASK_BI_HOST + ' | ' + PROCESS_ENV.PORT

    const sinkData: LoaderData = inputContext.data as LoaderData
    inputContext.data = null

    const modelStampRecords = await getModelUpdateStamp(sinkData.dataModels)

    // 更新模型与自动指标
    for (const [modelName, modelDetail] of sinkData.dataModels) {
      // 判定 data model 源头是否更新，依据时间戳
      let createTime: Date = new Date()
      if (modelDetail.sourceCreateTime > 0) {
        createTime = new Date(modelDetail.sourceCreateTime)
      }
      const sceneIdList = sinkData.model2SceneId.get(modelName) || ''
      /** 1.1 如果旧记录更新时间比最新数据源更新时间旧，更新
       *  1.2 如果旧记录更新时间比最新数据源更新时间新，忽略
       */
      const modelStamp = modelStampRecords.get(modelName)
      if (modelStamp && modelStamp === createTime.getTime()) {
        continue
      }
      // 拉取码值
      let allCodeValues: Record<string, string[]> | null = await pullDimensionValues(modelName)
      // 启动数据更新
      try {
        // 更新model事务开始
        //await prisma.$transaction(async (prisma) => {
        // 1. 删除旧 model
        if (modelStamp) {
          await prisma.semanticModel.delete({
            where: {
              name: modelName,
            },
          })
          // 为了应对某些情况DB不开发/支持外键权限，这里还是手动删除一下Dimension和Measure
          await prisma.semanticDimension.deleteMany({
            where: {
              semanticModelName: modelName,
            },
          })
          await prisma.semanticMeasure.deleteMany({
            where: {
              semanticModelName: modelName,
            },
          })
        }
        // 2. 插入新 model
        await prisma.semanticModel.create({
          data: {
            name: modelName,
            tableName: modelDetail.tableName,
            createdAt: createTime,
            timeDimensionName: modelDetail.timeDimensionName,
            timeDimensionType: modelDetail.timeDimensionType,
            timeDimensionFormat: modelDetail.timeDimensionFormat,
            timeGranularityMin: modelDetail.timeGranularityMin,
            debugInfo: debugInfo,
            semanticDimensions: {
              create: (sinkData.dimensions.get(modelName) || []).map((dimension) => ({
                name: dimension.name,
                label: dimension.label,
                synonyms: dimension.synonyms,
                filterSwitch: dimension.filterSwitch,
                description: dimension.description,
                expr: dimension.expr,
                type: dimension.type,
                typeParams: dimension.typeParams,
                //values: dimension.values,
                values: allCodeValues ? allCodeValues[dimension.name] : undefined,
                createdAt: createTime,
              })),
            },
            semanticMeasures: {
              create: (sinkData.measures.get(modelName) || []).map((measure) => ({
                name: measure.name,
                label: measure.label,
                synonyms: measure.synonyms,
                description: measure.description,
                expr: measure.expr,
                agg: measure.agg,
                formatTemplate: measure.formatTemplate ? measure.formatTemplate : null,
                createMetric: measure.createMetric,
                createdAt: createTime,
              })),
            },
          },
        }) //\2. 插入新 model
        // }) //\ 更新model事务结束
        allCodeValues = null
        // 度量创建的指标拼接上model名字，避免项目中同名
        const createMetricList: Measure[] = []
        for (const item of sinkData.measures.get(modelName) || []) {
          if (item.createMetric === true) {
            const tmpItem = item
            tmpItem.name = modelName + '_' + item.name
            createMetricList.push(tmpItem)
          }
        }
        // 3. 更新指标表中自动指标, 支持到多个 scene 复用 model
        for (const sceneId of sceneIdList) {
          const projectId = sinkData.scene2ProjectId.get(sceneId) || ''
          if (projectId.length === 0) {
            console.error(
              chalk.red(`projectId ${projectId} sceneId ${sceneId} model ${modelName} sink fail at ${Date()}`),
            )
            continue
          }
          try {
            // 数据模型中度量不需要生成指标
            if (!createMetricList || createMetricList.length === 0) {
              await prisma.semanticMetric.deleteMany({
                where: {
                  autoCreateByMeasure: true,
                  semanticProjectId: projectId,
                  semanticSceneId: sceneId,
                },
              })
              console.info(`project ${projectId} scene ${sceneId} model ${modelName} no need create metric`)
              continue
            }
            if (projectId.length > 0 && sceneId.length > 0) {
              // 查找旧的 metric 记录，有些属性需要更保留之前的值
              const records = await prisma.semanticMetric.findMany({
                where: {
                  name: {
                    in: createMetricList.map((item) => item.name),
                  },
                  semanticProjectId: projectId,
                  semanticSceneId: sceneId,
                },
              })

              // 获取已经存在的 metric, 保持 id 不变
              const metricName2SemanticMetric = new Map<string, SemanticMetric>()
              for (const record of records) {
                metricName2SemanticMetric.set(record.name, record)
              }
              // 更新自动指标
              await prisma.$transaction(async (prisma) => {
                const records = await prisma.semanticMetric.findFirst({
                  where: {
                    autoCreateByMeasure: true,
                    semanticProjectId: projectId,
                    semanticSceneId: sceneId,
                  },
                  select: {
                    id: true,
                  },
                })
                if (records) {
                  await prisma.semanticMetric.deleteMany({
                    where: {
                      autoCreateByMeasure: true,
                      semanticProjectId: projectId,
                      semanticSceneId: sceneId,
                    },
                  })
                }
                console.info(
                  `project ${projectId} scene ${sceneId} model ${modelName} create metric count ${createMetricList.length}`,
                )
                await prisma.semanticMetric.createMany({
                  data: createMetricList.map((measure) => ({
                    id: metricName2SemanticMetric.get(measure.name)?.id || nanoid(),
                    name: measure.name,
                    label: measure.label,
                    synonyms: measure.synonyms,
                    description: measure.description || measure.label,
                    type: 'simple',
                    formatTemplate: measure.formatTemplate,
                    typeParams: {
                      measure: measure.name.substring(modelName.length + 1),
                    },
                    semanticProjectId: projectId,
                    semanticSceneId: sceneId,
                    createdAt: createTime,
                    autoCreateByMeasure: true,
                    isCumulative: metricName2SemanticMetric.get(measure.name)?.isCumulative || false,
                    rank: metricName2SemanticMetric.get(measure.name)?.rank || -1,
                    category: metricName2SemanticMetric.get(measure.name)?.category || null,
                    windowDescConfigStr: metricName2SemanticMetric.get(measure.name)?.windowDescConfigStr || null,
                    keypoint: metricName2SemanticMetric.get(measure.name)?.keypoint || false,
                  })),
                })
              })
            }
          } catch (e) {
            console.error(chalk.red(`projectId ${projectId} model ${modelName} sink fail at ${Date()}, detail: ${e}`))
          }
        } //\ 3. 更新指标表中自动指标
      } catch (e) {
        console.error(chalk.red(`model ${modelName} sink fail at ${Date()}, detail: ${e}`))
      }
    }
    return { flag: true, dataContext: inputContext }
  },
}

/** 语义模型构建触发
 * !!! 特别特别说明的是 用户在界面定义的指标是立即生效的，不必担心延误 !!!
 * 这里时间是用来控制语义模型构建周期，数据来源是xengine数据建模元数据(ModelDataDesc),该数据使用中更新是很低频的操作，通常在以下场景中需要更新:
 * 1. 接入新表建模
 * 2. 现有表修改维度，或者码值（含码值同义词）
 * 因此实际使用过程中 10分钟 应是足够，特殊情况可以酌情改小
 * 修改方式: 直接修改 .env 文件中参数 SEMANTIC_MODEL_BUILD_CYCLE=600 , 默认是 10分钟
 */
console.info(chalk.green(`semantic model build cycle ${PROCESS_ENV.SEMANTIC_MODEL_BUILD_CYCLE}s`))
const trigger = new XengineBackendTrigger(PROCESS_ENV.SEMANTIC_MODEL_BUILD_CYCLE)
const chainItem: DataChainItem = { name: chainItemName, trigger, loader, sinker }
export default chainItem
