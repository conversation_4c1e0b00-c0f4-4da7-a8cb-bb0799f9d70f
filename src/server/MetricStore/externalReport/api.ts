/**
 * 外部报表 ExternalReport 相关接口
 */
import multer from 'multer'
import { groupBy } from 'lodash'
import dayjs from 'dayjs'
import xlsx from 'xlsx'
import { Prisma } from '@prisma/client'
import express, { Router, Request, Response } from 'express'
import { prisma } from 'src/server/dao/prisma-init'
import {
  getCompanyInnerCodes,
  getQueriedCompanyFromWhere,
  handleOriginWhere,
} from 'src/server/custom/baowu/baowu-utils'
import { BaowuReportType, determineBaowuReportType } from 'src/shared/baowu-share-utils'
import { verifyAdminLogin } from 'src/server/login/auth-utils'
import { convertTimeToSpecificDate, convertTimeToSpecificMonth } from 'src/shared/time-utils'
import { TimeQueryParams } from 'src/shared/metric-types'
import { assertExhaustive } from 'src/shared/common-utils'
import MetricConfig from '../metric-config'

const router: Router = express.Router()

/**
 * 获取某一个场景下的外部报表 ExternalReport 数据
 */
router.get('/:sceneId', async (req: Request, res: Response) => {
  console.info('Get externalReport data with params', req.params)
  try {
    const sceneId = req.params.sceneId
    const allExternalReports = await prisma.semanticExternalReport.findMany({
      where: { semanticSceneId: sceneId },
    })
    if (allExternalReports) {
      return res.json({
        code: 0,
        data: { list: allExternalReports, total: allExternalReports.length },
      })
    }
    return res.json({ code: 400, data: [], msg: '未找到相关外部报表' })
  } catch (error) {
    console.error('Get externalReport data with error', error)
    return res.json({ code: 500, msg: '获取外部报表数据失败:' + (error as Error).message })
  }
})

/**
 * 更新某个外部报表的信息
 */
router.put('/:id', async (req: Request, res: Response) => {
  console.info('Update externalReport data with params', req.params, req.body)
  try {
    const externalReportId = req.params.id
    if (externalReportId == null) {
      return res.json({ code: 411, data: {}, msg: '缺少 externalReportId，更新外部报表失败!' })
    }
    const newData = req.body
    console.info(`Update ExternalReport id=${externalReportId}, data :`, newData)
    const reportDetail = await prisma.semanticExternalReport.update({
      where: { id: externalReportId },
      data: newData,
    })
    if (reportDetail) {
      return res.json({
        code: 0,
        data: { id: externalReportId, data: reportDetail },
        msg: 'Update externalExport success!',
      })
    }
    return res.json({ code: 400, data: {}, msg: '更新外部报表失败：' + externalReportId })
  } catch (error) {
    console.error('Update externalReport data with error', error)
    return res.json({ code: 500, msg: `更新外部报表相关的数据失败，请联系管理员处理` })
  }
})

/** 批量删除报表 */
router.delete('/batch', verifyAdminLogin, async (req: Request, res: Response) => {
  const ids = req.body.ids
  console.info('Batch Delete metric with params: ', ids)
  if (!Array.isArray(ids) || !ids.length) {
    return res.json({ code: 400, msg: 'id列表 存在问题' })
  }
  const dataList = await prisma.semanticExternalReport.findMany({
    where: {
      id: {
        in: ids,
      },
    },
  })
  if (dataList.length !== ids.length) {
    const idSet = new Set(dataList.map((v) => v.id))
    return res.json({ code: 500, msg: '存在未知ID，' + ids.filter((v) => !idSet.has(v)) })
  }
  await prisma.semanticExternalReport.deleteMany({
    where: { id: { in: ids } },
  })
  return res.json({ code: 0, data: { ids, message: '外部报表删除成功' } })
})

/**
 * 删除外部报表
 */
router.delete('/:id', async (req: Request, res: Response) => {
  console.info('Delete externalReport data with params', req.params)
  try {
    const externalReportId = req.params.id
    if (externalReportId == null) {
      return res.json({ code: 404, msg: '缺少externalReportId，无法删除此外部报表' })
    }
    console.info(`Delete ${externalReportId} externalReport...`)
    const externalReport = await prisma.semanticExternalReport.delete({
      where: { id: externalReportId },
    })
    if (externalReport) {
      return res.json({ code: 0, data: { id: externalReportId }, msg: 'Delete externalExport success!' })
    }

    return res.json({ code: 404, msg: `找不到外部报表${externalReportId}, 删除失败!` })
  } catch (error) {
    console.error('Delete externalReport with error: ', error)
    return res.json({ code: 500, data: {}, msg: '删除外部报表失败，请联系管理员处理' })
  }
})

/**
 * 创建外部报表
 */
router.post('/', async (req: Request, res: Response) => {
  const body = req.body
  console.info('Create a externalReport with data: ', body)
  try {
    const { name, label, type, synonyms = [], description = null, semanticSceneId, semanticProjectId } = body
    const newExternalReport = { name, label, type, synonyms, description, semanticSceneId, semanticProjectId }
    const metricNamesOfScene = await prisma.semanticMetric.findMany({
      select: { name: true },
      where: { semanticSceneId },
    })
    // 检查是否有重复的指标名称
    const isDuplicate = metricNamesOfScene.some((metric) => metric.name === name)

    if (isDuplicate) {
      return res.json({ code: 400, msg: '报表名称与现有指标重复，创建失败!' })
    }
    const externalReport = await prisma.semanticExternalReport.create({
      data: newExternalReport,
    })

    return res.json({ code: 0, data: externalReport })
  } catch (error) {
    console.error('Create a externalReport failed.', error)
    return res.json({ code: 500, msg: '创建外部报表失败: ' + (error as Error).message })
  }
})

/**
 * 获取宝武报表的URL
 */
router.post('/baowu-report/detail', async (req: Request, res: Response) => {
  const {
    where,
    reportId,
    timeQueryParams,
    username,
  }: { where: string; reportId: string; timeQueryParams?: TimeQueryParams; username: string } = req.body
  console.info('Generate baowu report url with data:', JSON.stringify({ where, reportId, timeQueryParams }))

  try {
    const reportDetail = await prisma.semanticExternalReport.findUnique({
      where: { id: reportId },
    })
    if (!reportDetail) {
      return res.json({ code: 404, msg: '报表未找到' })
    }
    const metricConfig = await MetricConfig.createBySceneId(reportDetail.semanticSceneId)

    const { companyInnerCodeDesWhere } = handleOriginWhere(where)
    const companyDescriptions = getQueriedCompanyFromWhere(companyInnerCodeDesWhere)
    const companyInnerCodes = await getCompanyInnerCodes(companyDescriptions, metricConfig.name)

    if (companyInnerCodes.length === 0) {
      return res.json({ code: 400, msg: '没找到对应的inner code代码，无法展示' })
    }

    const codeForUrl = companyInnerCodes[0]['COMPANY_INNER_CODE']
    const nameForUrl = encodeURIComponent(companyInnerCodes[0]['COMPANY_INNER_CODE_DES'])

    // 判断宝武报表的具体类型
    const reportType = determineBaowuReportType(reportDetail.name)

    let timeSpan = 0
    if (timeQueryParams) {
      const startMonthFunction = convertTimeToSpecificMonth(timeQueryParams.timeStartFunction)
      const endMonthFunction = convertTimeToSpecificMonth(timeQueryParams.timeEndFunction)
      timeSpan = Math.abs(
        startMonthFunction.year * 12 + startMonthFunction.month - (endMonthFunction.year * 12 + endMonthFunction.month),
      )
      console.info('timeSpan: ', timeSpan)
    }

    // 获取时间
    let timePoint = ''
    if (timeQueryParams) {
      const endDate: { type: 'specificDate'; year: number; month: number; day: number } = convertTimeToSpecificDate(
        timeQueryParams.timeEndFunction,
        'end',
      )
      const dateStr = `${endDate.year}-${endDate.month}-${endDate.day}`
      console.info('dateStr------------->', dateStr, timeQueryParams.timeEndFunction)
      timePoint =
        timeQueryParams.timeGranularity === 'year' || timeSpan === 11
          ? dayjs(dateStr, 'YYYY-M-D').format('YYYY') // 如果粒度是年，则只取年份
          : dayjs(dateStr, 'YYYY-M-D').format('YYYYMM') // 默认使用 YYYYMM 格式
    } else {
      timePoint = dayjs().subtract(1, 'month').format('YYYYMM')
    }

    // 获取日期类型
    let isDate = 'm'
    if (timeQueryParams) {
      if (timeQueryParams.timeGranularity === 'quarter') {
        isDate = 'q'
      }
      if (timeQueryParams.timeGranularity === 'year' || timeSpan === 11) {
        isDate = 'y'
      }
    }

    switch (reportType) {
      case BaowuReportType.Simplified: {
        const responseUrl = `${reportDetail.description}&code=${codeForUrl}&name=${nameForUrl}&date=${timePoint.length === 4 ? timePoint + '12' : timePoint}&userId=${username || req.user?.username}`
        return res.json({ code: 0, data: { url: responseUrl, companyInnerCodes } })
      }
      case BaowuReportType.ExpenseAndAssets: {
        const responseUrl = `${reportDetail.description}&code=${codeForUrl.slice(0, 4)}&name=${nameForUrl}&date=${timePoint}&isDate=${isDate}`
        return res.json({ code: 0, data: { url: responseUrl, companyInnerCodes } })
      }
      case BaowuReportType.Overview: {
        const responseUrl = `${reportDetail.description}?code=${codeForUrl.slice(0, 4)}&company=${nameForUrl}&date=${timePoint.slice(0, 4)}&isDate=y&userId=${username || req.user?.username}`
        return res.json({ code: 0, data: { url: responseUrl, companyInnerCodes } })
      }
      default:
        return assertExhaustive(reportType)
    }
  } catch (error) {
    console.error('获取外部报表详细信息失败:', error)
    return res.json({ code: 500, msg: `获取报表详细信息失败` })
  }
})

export default router

const storage = multer.memoryStorage()
const upload = multer({ storage: storage })
function createSemanticExternalReportList(option: {
  sheet: xlsx.WorkSheet
  semanticProjectId: string
  semanticSceneId: string
  enableDefault?: boolean
}) {
  const { sheet, semanticProjectId, semanticSceneId, enableDefault = true } = option
  const arr: Prisma.SemanticExternalReportCreateManyInput[] = []
  for (let i = 2; sheet['A' + i]; i++) {
    const [name, label, type, synonyms, description] = new Array(26)
      .fill(0)
      .map((_, i) => String.fromCharCode(i + 'A'.codePointAt(0)!))
      .map((v) => sheet[`${v}${i}`]?.v)

    const data: Prisma.SemanticExternalReportCreateManyInput = {
      name,
      label,
      synonyms: synonyms ? JSON.parse(synonyms) : enableDefault ? [] : synonyms,
      type,
      description,
      semanticProjectId,
      semanticSceneId,
    }
    arr.push(data)
  }
  return arr
}

router.post('/upload', upload.array('file'), verifyAdminLogin, async (req, res) => {
  try {
    const files = req.files
    if (!Array.isArray(files) || files?.length !== 1) {
      return res.json({ code: 500, msg: '文件异常' })
    }
    const { semanticSceneId, semanticProjectId } = req.body
    const file = files[0]
    const data = xlsx.read(file.buffer)
    const sheet = data.Sheets[data.SheetNames[0]]
    const arr = createSemanticExternalReportList({ sheet, semanticSceneId, semanticProjectId })
    const nameMap = new Map()
    for (const item of arr) {
      nameMap.set(item.name, (nameMap.get(item.name) ?? 0) + 1)
    }
    // name不能重复
    if (nameMap.size !== arr.length) {
      throw new Error(
        '存在重复的name:' +
          Array.from(nameMap.entries())
            .filter(([_, v]) => v > 1)
            .map(([k]) => k)
            .join(','),
      )
    }
    // 必填的字段
    const requiredField = ['name', 'label', 'type'] as const
    if (arr.some((v) => !requiredField.map((key) => v[key]).every((val) => val !== undefined))) {
      const msgArr = ['创建失败:']
      for (const item of arr) {
        const fields: string[] = []
        for (const field of requiredField) {
          if (item[field] === undefined) {
            fields.push(field)
          }
        }
        if (fields.length) {
          msgArr.push(`${item.name}缺失:${fields.join(',')}`)
        }
      }
      throw msgArr.join('\n')
    }
    // name不能与库里重复
    const dataFindWithId = await prisma.semanticExternalReport.findMany({
      where: {
        name: {
          in: arr.map((item) => item.name).filter(Boolean) as string[],
        },
      },
    })
    if (dataFindWithId.length) {
      throw new Error('存在重复的name:\n' + dataFindWithId.map((v) => v.name).join('\n'))
    }
    await prisma.semanticExternalReport.createMany({ data: arr })
    res.json({ code: 0, msg: '创建成功' })
  } catch (e) {
    console.error('create external report failed', e)
    return res.json({
      code: 500,
      msg: (e as Error).message ?? e?.toString(),
    })
  }
})

router.patch('/upload', upload.array('file'), verifyAdminLogin, async (req, res) => {
  try {
    const files = req.files
    if (!Array.isArray(files) || files?.length !== 1) {
      return res.json({ code: 500, msg: '文件异常' })
    }
    const { semanticSceneId, semanticProjectId } = req.body
    const file = files[0]
    const data = xlsx.read(file.buffer)
    const sheet = data.Sheets[data.SheetNames[0]]
    const metricRecord = groupBy(
      createSemanticExternalReportList({ sheet, semanticSceneId, semanticProjectId, enableDefault: false }),
      (item) => item.name,
    )
    const metrics = await prisma.semanticExternalReport.findMany({
      where: {
        name: {
          in: Object.keys(metricRecord),
        },
      },
    })
    const errorMessageArr: string[] = []
    for (const metric of metrics) {
      const data = metricRecord[metric.name][0]
      if (data) {
        try {
          await prisma.semanticExternalReport.update({
            where: { id: metric.id },
            data,
          })
        } catch (e: any) {
          if (e?.message) {
            errorMessageArr.push(`${metric.name}: ${e?.message}`)
          }
        }
      }
    }
    if (errorMessageArr.length === 0) {
      res.json({ code: 0, msg: '更新成功' })
    } else {
      throw new Error(errorMessageArr.join('\n'))
    }
  } catch (e) {
    console.error('update external report failed', e)
    return res.json({
      code: 500,
      msg: (e as Error).message,
    })
  }
})
