import express, { Router, Request, Response } from 'express'
import { SemanticMetricTree } from '@prisma/client'
import { nanoid } from 'nanoid'
import { prisma } from 'src/server/dao/db'
import { checkTreeHasCycle } from 'src/shared/metric-utils'

const router: Router = express.Router()

type SemanticMetricNode = Pick<
  SemanticMetricTree,
  Exclude<keyof SemanticMetricTree, 'createdAt' | 'updatedAt' | 'semanticProjectId' | 'childrenNames'>
> & { childrenNames: string[] }
type SemanticMetricTreeNode = Pick<
  SemanticMetricNode,
  Exclude<keyof SemanticMetricNode, 'childrenNames' | 'semanticProjectId' | 'semanticMetricTreeRootId'>
> & {
  treeName?: string
  children: SemanticMetricTreeNode[]
}

/**
 * 树转列表，把 children 对象转换成 children metricNames 数组，并根据 metricName 去重
 * @param node
 * @returns
 */
function treeToList(node: SemanticMetricTreeNode): SemanticMetricNode[] {
  const list: SemanticMetricNode[] = []
  const semanticMetricTreeRootId = node.id
  function traverse(node: SemanticMetricTreeNode) {
    const existingNode = list.find((item) => item.metricName === node.metricName)
    if (!existingNode) {
      list.push({
        id: node.id,
        metricName: node.metricName,
        exprCalc: node.exprCalc,
        exprRelation: node.exprRelation,
        childrenNames: node.children.map((child) => {
          return child.metricName
        }),
        semanticMetricTreeRootId,
      })
    }

    node.children.forEach((child) => {
      traverse(child)
    })
  }

  traverse(node)
  return list
}

/** 根据 rootId 将列表转换为树 */
function listToTree<T extends SemanticMetricNode>(items: T[], rootId: string): SemanticMetricTreeNode | null {
  // 找到 root 节点
  const root = items.find((item) => item.id === rootId)
  if (root == null) {
    return null
  }

  const node = {
    id: root.id,
    metricName: root.metricName,
    exprCalc: root.exprCalc,
    exprRelation: root.exprRelation,
    children: [] as SemanticMetricTreeNode[],
  } as SemanticMetricTreeNode
  // 递归构建树
  function traverse(node: SemanticMetricTreeNode) {
    if (checkTreeHasCycle(node)) {
      throw new Error('树有环，在节点' + checkTreeHasCycle(node))
    }
    // 给 node 配置 children
    items
      .find((item) => item.metricName === node.metricName)
      ?.childrenNames.forEach((childName) => {
        const child = items.find((item) => item.metricName === childName)
        if (child) {
          node.children.push({
            id: nanoid(),
            metricName: child.metricName,
            exprCalc: child.exprCalc,
            exprRelation: child.exprRelation,
            children: [] as SemanticMetricTreeNode[],
          })
        }
      })
    node.children.forEach(traverse)
  }
  traverse(node)
  return node
}

/**
 * 指标树的list接口
 * @returns data: { list: SemanticMetricTree[] }
 */
router.get('/:projectId/', async (req: Request, res: Response) => {
  console.info('Query semantic metric trees with params:', JSON.stringify(req.params))

  try {
    const projectId = req.params.projectId
    if (projectId == null) {
      return res.json({ code: 404, data: {}, msg: '缺少projectId，查询metricTree失败' })
    }

    const treeList = await prisma.semanticMetricTreeRoot.findMany({
      where: {
        semanticProjectId: projectId,
      },
      include: {
        semanticMetricTrees: true,
      },
    })

    const result = treeList
      .map((item) => {
        const lists = item.semanticMetricTrees.map((node) => {
          return {
            id: node.id,
            metricName: node.metricName,
            exprCalc: node.exprCalc,
            exprRelation: node.exprRelation,
            childrenNames: node.childrenNames as string[],
            semanticMetricTreeRootId: node.semanticMetricTreeRootId,
          }
        })
        const parsedTree = listToTree(lists, item.id)
        console.info('====', parsedTree)
        if (parsedTree == null) {
          return null
        } else {
          parsedTree.treeName = item.treeName
          return parsedTree
        }
      })
      .filter(Boolean)

    return res.json({ code: 0, data: { list: result, total: result.length } })
  } catch (error: any) {
    console.error(error)
    return res.json({ code: 500, data: {}, msg: '获取Semantic metric tree失败' })
  }
})

router.get('/:projectId/:treeRootId', async (req: Request, res: Response) => {
  console.info('Query semantic metric trees with params:', JSON.stringify(req.params))

  try {
    const projectId = req.params.projectId
    const treeRootId = req.params.treeRootId
    if (projectId == null) {
      return res.json({ code: 404, data: {}, msg: '缺少projectId，查询metricTree失败' })
    }
    const semanticMetricTree = await prisma.semanticMetricTree.findMany({
      where: {
        semanticProjectId: projectId,
        semanticMetricTreeRootId: treeRootId,
      },
    })
    const semanticMetricTreeRoot = await prisma.semanticMetricTreeRoot.findFirst({
      where: {
        semanticProjectId: projectId,
        id: treeRootId,
      },
    })
    const items: SemanticMetricNode[] = semanticMetricTree.map((item) => {
      return {
        id: item.id,
        metricName: item.metricName,
        exprCalc: item.exprCalc,
        exprRelation: item.exprRelation,
        childrenNames: item.childrenNames as string[],
        semanticMetricTreeRootId: item.semanticMetricTreeRootId,
      }
    })
    const tree = listToTree(items, treeRootId)
    if (!tree) {
      return res.json({ code: 404, data: {}, msg: '指标树不存在' })
    }
    tree.treeName = semanticMetricTreeRoot?.treeName
    return res.json({ code: 0, data: { tree: tree } })
  } catch (error: any) {
    console.error(error)
    return res.json({ code: 500, data: {}, msg: '获取Semantic metric tree失败' })
  }
})

// 新增指标树
router.post('/', async (req: Request, res: Response) => {
  console.info('create metric trees with:', JSON.stringify(req.body))
  const metricName = req.body.metricName
  const projectId = req.body.projectId
  if (!metricName || !projectId) {
    return res.json({ code: 400, data: {}, msg: '缺少metricName或projectId' })
  }
  const existedSemanticMetricTreeRoot = await prisma.semanticMetricTreeRoot.findFirst({
    where: {
      metricName,
      semanticProjectId: projectId,
    },
  })
  if (existedSemanticMetricTreeRoot) {
    return res.json({
      code: 400,
      data: {
        msg: '该指标树已存在，请正确创建',
      },
    })
  }

  try {
    const { metricName, projectId, treeName } = req.body
    const semanticProject = await prisma.semanticProject.findUnique({
      where: { id: projectId },
    })
    if (semanticProject == null) {
      return res.json({ code: 404, msg: '找不到该semanticProject' })
    }
    // 给metric表插入数据
    let newMetricTree
    await prisma.$transaction(async (prisma) => {
      const newMetricTreeRoot = await prisma.semanticMetricTreeRoot.create({
        data: {
          treeName,
          metricName,
          semanticProjectId: projectId,
        },
      })
      newMetricTree = await prisma.semanticMetricTree.create({
        data: {
          id: newMetricTreeRoot.id,
          semanticMetricTreeRootId: newMetricTreeRoot.id,
          metricName,
          childrenNames: [],
          semanticProjectId: projectId,
        },
      })
    })
    return res.json({
      code: 0,
      data: newMetricTree,
    })
  } catch (error: any) {
    console.error(error)
    return res.json({ code: 500, data: {}, msg: '创建Semantic metric tree失败' })
  }
})
// 编辑指标树 不diff  直接暴力删除
router.put('/:projectId/:id', async (req: Request, res: Response) => {
  console.info('update metric trees with:', JSON.stringify(req.body))

  try {
    const id = req.params.id
    const projectId = req.params.projectId
    const tree = req.body.tree
    const treeName = req.body.tree.treeName
    const list = treeToList(tree)
    const newRoot = list.find((i) => {
      return i.metricName === tree.metricName
    })
    if (!newRoot) {
      return res.json({ code: 400, data: {}, msg: '根节点解析失败' })
    }

    await prisma.$transaction(async (prisma) => {
      // 删除原始树
      await prisma.semanticMetricTree.deleteMany({
        where: {
          semanticMetricTreeRootId: id,
          semanticProjectId: projectId,
        },
      })

      // 更新根节点
      await prisma.semanticMetricTreeRoot.update({
        where: {
          id,
          semanticProjectId: projectId,
        },
        data: { treeName },
      })

      // 创建新的节点
      await prisma.semanticMetricTree.createMany({
        data: list.map((i) => {
          return {
            id: i.id,
            metricName: i.metricName,
            exprCalc: i.exprCalc || null,
            exprRelation: i.exprRelation || null,
            childrenNames: i.childrenNames,
            semanticProjectId: projectId,
            semanticMetricTreeRootId: i.semanticMetricTreeRootId,
          }
        }),
      })
    })
    const semanticMetricTree = await prisma.semanticMetricTree.findMany({
      where: {
        semanticMetricTreeRootId: id,
        semanticProjectId: projectId,
      },
    })
    const items: SemanticMetricNode[] = semanticMetricTree.map((item) => {
      return {
        id: item.id,
        metricName: item.metricName,
        exprCalc: item.exprCalc,
        exprRelation: item.exprRelation,
        childrenNames: item.childrenNames as string[],
        semanticMetricTreeRootId: item.semanticMetricTreeRootId,
      }
    })
    const resultTree = listToTree(items, id)
    return res.json({ code: 0, data: { tree: resultTree } })
  } catch (error: any) {
    console.error(error)
    return res.json({ code: 500, data: {}, msg: '获取Semantic metric tree失败' })
  }
})
// 删除指标树
router.delete('/:projectId/:id', async (req: Request, res: Response) => {
  console.info('Query semantic metric trees with params:', JSON.stringify(req.params))

  try {
    const projectId = req.params.projectId
    const id = req.params.id
    if (projectId !== null) {
      await prisma.$transaction(async (prisma) => {
        // delete original tree
        await prisma.semanticMetricTreeRoot.delete({
          where: {
            id: id,
            semanticProjectId: projectId,
          },
        })
        // 删除其他 更新 root
        await prisma.semanticMetricTree.deleteMany({
          where: {
            semanticMetricTreeRootId: id,
            semanticProjectId: projectId,
          },
        })
      })
    }
    return res.json({ code: 0, data: { id: id, message: '删除成功' } })
  } catch (error: any) {
    console.error(error)
    return res.json({ code: 500, data: {}, msg: '删除Semantic metric tree失败' })
  }
})

router.get('/:projectId/searchMetric', async (req: Request, res: Response) => {
  console.info('Query semantic metric trees with params:', JSON.stringify(req.params))

  try {
    const projectId = req.params.projectId
    const metricName = req.query.metricName?.toString()
    const list = await prisma.semanticMetricTree.findMany({
      where: {
        metricName: {
          contains: metricName,
        },
        semanticProjectId: projectId,
      },
    })
    return res.json({ code: 0, data: { list, total: list.length } })
  } catch (error: any) {
    console.error(error)
    return res.json({ code: 500, data: {}, msg: '查询失败' })
  }
})

export default router
