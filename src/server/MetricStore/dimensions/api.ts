/**
 * 维度相关的接口
 */
import express, { Router, Request, Response } from 'express'
import { DEFAULT_SCENE_ID } from 'src/shared/metric-types'
import { getDefaultDataset } from 'src/server/AskBI/datasets/dao'
import { passportMiddleware } from 'src/server/auth'
import { executeAllXengineSql } from 'src/server/dao/xengine-query'
import { OlapRow } from 'src/shared/common-types'
import MetricConfig from '../metric-config'

const router: Router = express.Router()

// 获取维度列表
router.get('/list/:sceneId', passportMiddleware, async (req: Request, res: Response) => {
  console.info('get dimensionList', req.query)
  const userId = req.user!.id
  let sceneId = req.params.sceneId
  if (sceneId === DEFAULT_SCENE_ID) {
    const defaultDataset = await getDefaultDataset(userId)
    sceneId = defaultDataset?.sceneId || ''
  }
  try {
    const metricConfig = await MetricConfig.createBySceneId(sceneId)

    return res.json({
      code: 0,
      data: { list: metricConfig.allDimensions, total: metricConfig.allDimensions.length },
    })
  } catch (error) {
    console.error('获取维度列表错误', error)
    return res.json({ code: 500, msg: '获取维度列表错误' })
  }
})

/** 获取维度的码值 以及码值出现的次数 用于趋势展示[取前20条数据] */
router.get('/trend/:tableName/:dimensionName', async (req: Request, res: Response) => {
  const dimensionName = req.params.dimensionName
  const tableName = req.params.tableName
  try {
    if (!dimensionName) {
      return res.json({
        code: 500,
        msg: '缺少 dimensionName 参数',
      })
    }

    const sql = `SELECT ${dimensionName}, COUNT(*) AS count FROM ${tableName} GROUP BY ${dimensionName} ORDER BY count DESC LIMIT 20;`

    const data = await executeAllXengineSql(sql)
    return res.json({
      code: 0,
      data: extractDataForChart(data),
    })
  } catch (error) {
    console.error(error)
    return res.json({ code: 500, msg: '获取dimension trend信息失败' })
  }
})

function extractDataForChart(originData: OlapRow[]) {
  if (!originData || originData.length === 0) return { xAxisData: [], data: [] }

  const keys = Object.keys(originData[0])
  if (keys.length < 2) return { xAxisData: [], data: [] }

  const key1 = keys[0]
  const key2 = keys[1]
  const xAxisData = originData.map((item) => item[key1 === 'count' ? key2 : key1])
  const data = originData.map((item) => item[key1 === 'count' ? key1 : key2])

  return { xAxisData, data }
}

export default router
