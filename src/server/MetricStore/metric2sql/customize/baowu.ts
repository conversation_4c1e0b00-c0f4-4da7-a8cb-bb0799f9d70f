/**
 * 存放宝武场景的定制逻辑
 * 1. 如果没有时间周期，就使用上个月
 * 2. 如果问今年的数据 截止时期默认到上个月
 * 3. 区分 可累加/不可累加 指标的取数逻辑(不可累加的指标 取时间区间内最后一个月的数据)
 * 4. groupBy 里面一定要有子公司名
 *
 * TODO: 覆盖单元测试
 * 保证多次执行 结果一致
 */
import chalk from 'chalk'
import dayjs from 'dayjs'
import { convertTimeToSpecificDate, convertTimeToSpecificMonth } from 'src/shared/time-utils'
import { NoListMetric, QueryParams } from 'src/shared/metric-types'
import MetricConfig from '../../metric-config'

export function convertBaowuTimeParamsAndGroupBys(queryParams: QueryParams, metricConfig: MetricConfig): QueryParams {
  console.info(chalk.yellow('宝武定制逻辑--->>>'), queryParams)

  let baowuTimeQueryParams = queryParams.timeQueryParams
  // groupBy 里面一定要有子公司名
  const baowuGroupBys = ['COMPANY_INNER_CODE_DES']
  if (queryParams.timeQueryParams == null) {
    baowuTimeQueryParams = {
      timeStartFunction: { type: 'recentMonths', months: 1 },
      timeEndFunction: { type: 'recentMonths', months: 1 },
      timeGranularity: 'total',
    }
  } else if (queryParams.timeQueryParams.timeGranularity) {
    // 获取所有指标名称，包括非列表指标和列表指标展开的子指标名称
    const allMetricNames = metricConfig.allMetrics.flatMap((metric) => {
      if (queryParams.metricNames.includes(metric.name)) {
        return metric.type === 'list' ? metric.typeParams.metrics.map((i) => i.name) : metric.name
      }
      return []
    })
    // 判断所有非列表指标是否都可以累加
    const allMetricsCanBeCumulative = metricConfig.allMetrics
      .filter((metric) => allMetricNames.includes(metric.name))
      .every((metric) => (metric as NoListMetric).isCumulative)
    console.info('allMetricNames, allMetricsCanBeCumulative=>', allMetricNames, allMetricsCanBeCumulative)

    const startMonthFunction = convertTimeToSpecificMonth(queryParams.timeQueryParams.timeStartFunction)
    const endMonthFunction = convertTimeToSpecificMonth(queryParams.timeQueryParams.timeEndFunction)
    const timeSpan = Math.abs(
      startMonthFunction.year * 12 + startMonthFunction.month - (endMonthFunction.year * 12 + endMonthFunction.month),
    )
    // 判断时间跨度  1< x <=12
    const isWithinTwelveMonths = timeSpan <= 12

    console.info(
      'startMonthFunction, endMonthFunction, timeSpan, isWithinTwelveMonths',
      startMonthFunction,
      endMonthFunction,
      timeSpan,
      isWithinTwelveMonths,
    )

    const startDate: { type: 'specificDate'; year: number; month: number; day: number } = convertTimeToSpecificDate(
      queryParams.timeQueryParams.timeStartFunction,
      'start',
    )
    const endDate: { type: 'specificDate'; year: number; month: number; day: number } = convertTimeToSpecificDate(
      queryParams.timeQueryParams.timeEndFunction,
      'end',
    )
    const specificStartDate = dayjs(`${startDate.year}-${startDate.month}-${startDate.day}`, 'YYYY-M-D')
    const specificEndDate = dayjs(`${endDate.year}-${endDate.month}-${endDate.day}`, 'YYYY-M-D')
    const today = dayjs().startOf('day')

    // 如果 timeGranularity 是 total
    // 时间周期跨度 1< x <=12，那么要自动修正为按月聚合
    // 时间周期跨度 x > 12 修正为按年聚合
    if (queryParams.timeQueryParams.timeGranularity === 'total') {
      if (isWithinTwelveMonths) {
        if (startMonthFunction.year !== endMonthFunction.year || startMonthFunction.month !== endMonthFunction.month) {
          // 如果开始时间小于今天 结束时间大于今天 结束时间修正为上个月
          if (!specificStartDate.isAfter(today) && !specificEndDate.isBefore(today)) {
            console.info(chalk.yellow('开始时间早于今天，结束时间晚于今天，结束时间修正为上个月'))
            baowuTimeQueryParams = {
              timeStartFunction: queryParams.timeQueryParams.timeStartFunction,
              timeEndFunction: { type: 'recentMonths', months: 1 },
              timeGranularity: 'total',
            }
          }
        }
        if (specificStartDate.isSame(today) && specificEndDate.isSame(today)) {
          console.info(chalk.yellow('开始时间结束时间都等于今天，结束时间修正为上个月'))
          baowuTimeQueryParams = {
            timeStartFunction: { type: 'recentMonths', months: 1 },
            timeEndFunction: { type: 'recentMonths', months: 1 },
            timeGranularity: 'total',
          }
        }
        if (specificStartDate.isSame(today, 'day') && specificEndDate.isSame(today.endOf('month'), 'day')) {
          const year = specificEndDate.year()
          const month = specificEndDate.month() + 1
          console.info(
            chalk.green('开始时间是今天，结束时间是本月底，整体时间修正为上个月。 curYear, curMonth', year, month),
          )
          baowuTimeQueryParams = {
            timeStartFunction: { type: 'recentMonths', months: 1 },
            timeEndFunction: { type: 'recentMonths', months: 1 },
            timeGranularity: 'total',
          }
        }
      }
    }
    // 不可累加的指标 聚合方式不是月份 时间跨度大于1个月
    if (!allMetricsCanBeCumulative && timeSpan > 0 && queryParams.timeQueryParams.timeGranularity !== 'month') {
      console.info(chalk.yellow('对与不可累加的指标，取时间区间的最后一个月的值'))
      const endFunction = baowuTimeQueryParams?.timeEndFunction || queryParams.timeQueryParams.timeEndFunction

      baowuTimeQueryParams = {
        timeStartFunction: endFunction.type === 'specificDate' ? { ...endFunction, day: 1 } : endFunction,
        timeEndFunction: endFunction,
        timeGranularity: 'total',
      }
    }
  }

  return {
    ...queryParams,
    timeQueryParams: baowuTimeQueryParams,
    groupBys: baowuGroupBys,
  }
}
