/**
 * @description 这是一个 sql 生成工具，会生成批量的 sql。最终在项目根目录下生成一个 sql-batch.sql 文件，里面包含了所有的 sql
 * 需要修改的地方：sceneId，和后面指标、维度的变量值。
 * 使用方法：npx tsx src/server/MetricStore/metric2sql/baowu-sql-generator.ts
 */
import fs from 'fs'
import path from 'path'
import { PeriodOverPeriodType, QueryParams, TimeQueryParams } from 'src/shared/metric-types'
import MetricConfig from '../metric-config'
import Metric2Sql from './metric2sql'

const SCENE_ID = 'fS0U86Sz9l2k3AH7'
const TARGET_FILE = 'sql-batch.sql'
const DIMENSION_VALUES = [
  '中国宝武钢铁集团有限公司',
  '宝武共享服务有限公司',
  '宝钢湛江钢铁有限公司',
  '宝钢资源有限公司',
  '欧冶云商股份有限公司(总部)',
]

async function getMetricConfig() {
  return MetricConfig.createBySceneId(SCENE_ID)
}

function generateTimeQueryParamsOptions(): TimeQueryParams[] {
  return [
    {
      timeStartFunction: { type: 'recentMonths', months: 0 },
      timeEndFunction: { type: 'recentMonths', months: 0 },
      timeGranularity: 'total',
    },
    {
      timeStartFunction: { type: 'recentMonths', months: 1 },
      timeEndFunction: { type: 'recentMonths', months: 1 },
      timeGranularity: 'total',
    },
    {
      timeStartFunction: { type: 'recentQuarters', quarters: 0 },
      timeEndFunction: { type: 'recentQuarters', quarters: 0 },
      timeGranularity: 'total',
    },
    {
      timeStartFunction: { type: 'recentQuarters', quarters: 3 },
      timeEndFunction: { type: 'recentQuarters', quarters: 3 },
      timeGranularity: 'total',
    },
    {
      timeStartFunction: { type: 'recentYears', years: 0 },
      timeEndFunction: { type: 'recentYears', years: 0 },
      timeGranularity: 'total',
    },
    {
      timeStartFunction: { type: 'recentYears', years: 3 },
      timeEndFunction: { type: 'recentYears', years: 3 },
      timeGranularity: 'total',
    },
    {
      timeStartFunction: { type: 'recentMonths', months: 12 },
      timeEndFunction: { type: 'recentMonths', months: 0 },
      timeGranularity: 'month',
    },
    {
      timeStartFunction: { type: 'recentQuarters', quarters: 12 },
      timeEndFunction: { type: 'recentQuarters', quarters: 0 },
      timeGranularity: 'quarter',
    },
    {
      timeStartFunction: { type: 'recentYears', years: 5 },
      timeEndFunction: { type: 'recentYears', years: 0 },
      timeGranularity: 'year',
    },
  ]
}

function generateQueryParams(allMetrics: any[]): QueryParams[] {
  const timeQueryParamsOptions = generateTimeQueryParamsOptions()
  const whereOptions = [
    '',
    `COMPANY_INNER_CODE_DES = '${DIMENSION_VALUES[0]}'`,
    `COMPANY_INNER_CODE_DES = '${DIMENSION_VALUES[1]}'`,
  ]
  const groupBysOptions = [['COMPANY_INNER_CODE_DES']]
  const topMetricNames = allMetrics.slice(0, 20).map((metric) => metric.name)
  const periodOverPeriodsOptions: PeriodOverPeriodType[][] = [
    ['momGrowth'],
    ['yoyMonthGrowth'],
    [
      'momGrowth',
      'yoyMonthGrowth',
      'momGrowthRate',
      'yoyMonthGrowthRate',
      'qoqGrowth',
      'yoyQuarterGrowth',
      'qoqGrowthRate',
      'yoyQuarterGrowthRate',
    ],
  ]

  const queryParams: QueryParams[] = []

  topMetricNames.forEach((metricName) => {
    timeQueryParamsOptions.forEach((timeQueryParams) => {
      whereOptions.forEach((where) => {
        groupBysOptions.forEach((groupBys) => {
          periodOverPeriodsOptions.forEach((periodOverPeriods) => {
            queryParams.push({
              metricNames: [metricName],
              groupBys,
              where,
              timeQueryParams,
              periodOverPeriods,
            })
          })
        })
      })
    })
  })

  return queryParams
}

async function generateFile() {
  const metricConfig = await getMetricConfig()
  const queryParams = generateQueryParams(metricConfig.allMetrics)

  const metric2Sql = new Metric2Sql(metricConfig)
  const sqlResults = queryParams.map((queryParam) => metric2Sql.toSql(queryParam).sql)

  const formattedSQL = sqlResults.map((sql, index) => `-- 第 ${index + 1} 个\n${sql}`).join('\n\n')

  fs.writeFileSync(path.join(__dirname, '../../../../', TARGET_FILE), formattedSQL)
}

generateFile().catch((error) => {
  console.error('Error generating SQL file:', error)
})
