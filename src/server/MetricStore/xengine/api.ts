/**
 * @description 代理到xengine到接口，做一层转发
 */

import express from 'express'
import axios from 'axios'
import multer from 'multer'
import FormData from 'form-data'
import { omit } from 'lodash'
const storage = multer.memoryStorage()
const upload = multer({ storage: storage })
import { generateXengineUrl } from 'src/server/utils'
import { commonProxy } from 'src/server/commonProxy'

const router = express.Router()

router.post(
  [
    '/v1/vtable/createBatchVT',
    '/v1/metricmodel/bi/json',
    '/v1/metricmodel/import/batchUpdateColumnCodeValueSynonyms',
    '/v1/metricmodel/bi/csv',
  ],
  upload.array('file'),
  async (req, res) => {
    try {
      const { path, headers, baseUrl } = req
      const url = generateXengineUrl(path, baseUrl)
      const formData = new FormData()
      const files = req.files as Express.Multer.File[]
      const file = files[0]
      if (file) {
        formData.append(file.fieldname, file.buffer, {
          filename: file.originalname,
          contentType: file.mimetype, // 使用文件的原 MIME 类型
          knownLength: file.size,
        })
      }
      const response = await axios.post(url, formData, {
        headers: {
          ...omit(headers, ['content-length']),
          ...formData.getHeaders(),
        },
      })
      return res.json(response.data || {})
    } catch (error: any) {
      console.error(`出现错误，请求地址${req.url}`, error)
      const response = error?.response
      if (response?.code !== undefined) {
        return res.json(response)
      }
      return res.status(500).json({ code: 500, msg: '服务端错误' })
    }
  },
)

router.put(['/v1/csv', '/v1/csv/inferColumnType'], upload.array('file'), async (req, res) => {
  try {
    const { path, headers, baseUrl } = req
    const { ptable } = req.body
    const url = generateXengineUrl(path, baseUrl)
    const formData = new FormData()
    const files = req.files as Express.Multer.File[]
    const file = files[0]
    if (file) {
      formData.append(file.fieldname, file.buffer, {
        filename: file.originalname,
        contentType: file.mimetype, // 使用文件的原 MIME 类型
        knownLength: file.size,
      })
    }
    if (ptable) {
      formData.append('ptable', ptable)
    }
    const response = await axios.put(url, formData, {
      headers: {
        ...omit(headers, ['content-length']),
        ...formData.getHeaders(),
        Authorization: 'init',
      },
      timeout: 30 * 60 * 1000,
    })

    return res.json(response.data || {})
  } catch (error: any) {
    console.error(`出现错误，请求地址${req.url}`, error)
    const response = error?.response
    if (response?.code !== undefined) {
      return res.json(response)
    }
    return res.status(500).json({ code: 500, msg: '服务端错误' })
  }
})

router.all('/*', upload.none(), async (req, res) => {
  try {
    const { path, baseUrl } = req
    const url = generateXengineUrl(path, baseUrl)
    commonProxy(req, res, url)
  } catch (error: any) {
    console.error(`出现错误，请求地址${req.url}`, error)
    return res.status(500).json({ code: 500, msg: '服务端错误' })
  }
})

export default router
