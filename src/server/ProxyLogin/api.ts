/**
 * 文字生成图表问答的 API 接口定义
 */
import express, { Router, Request, Response } from 'express'
import axios from 'axios'
import { PROCESS_ENV } from '../server-constants'

const router: Router = express.Router()

/**
 * 获取env中默认的代理后台服务地址 -- di
 */
router.get('/get-default-proxy', async (_req: Request, res: Response) => {
  return res.json({ code: 0, data: PROCESS_ENV.XENGINE_PROXY_URL || '' })
})

/**
 * 获取登录页代理的namespace
 */
router.get('/get-kube-namespace', async (_req: Request, res: Response) => {
  try {
    const result = await axios.get(`${PROCESS_ENV.DI_DITEST_URI}/api/ditest/v1/kube/namespace`)
    return res.json(result.data)
  } catch (error) {
    console.error('get-kube-namespace' + (error as any).response?.data?.msg || (error as any).message || '')
    return res.json({
      code: 500,
      msg: (error as any).response?.data?.msg || (error as any).message || '',
    })
  }
})

router.get('/get-kube-service', async (req: Request, res: Response) => {
  try {
    const result = await axios.get(`${PROCESS_ENV.DI_DITEST_URI}/api/ditest/v1/kube/service`, { params: req.query })
    return res.json(result.data)
  } catch (error) {
    console.error('get-kube-service' + (error as any).response?.data?.msg || (error as any).message || '')
    return res.json({
      code: 500,
      msg: (error as any).response?.data?.msg || (error as any).message || '',
    })
  }
})

router.get('/get-kube-pod', async (req: Request, res: Response) => {
  try {
    const result = await axios.get(`${PROCESS_ENV.DI_DITEST_URI}/api/ditest/v1/kube/pod`, { params: req.query })
    return res.json(result.data)
  } catch (error) {
    console.error('get-kube-pod' + (error as any).response?.data?.msg || (error as any).message || '')
    return res.json({
      code: 500,
      msg: (error as any).response?.data?.msg || (error as any).message || '',
    })
  }
})

router.get('/check-containers-status', async (req: Request, res: Response) => {
  try {
    const result = await axios.get(`${PROCESS_ENV.CHECK_CONTAINERS}/api/ditest/v1/bi_devops/check_containers_status`, {
      params: req.query,
    })
    return res.json(result.data)
  } catch (error) {
    return res.json({
      code: 500,
      msg: (error as any).response?.data?.msg || (error as any).message || '',
    })
  }
})

router.get('/check-k8s-status', async (req: Request, res: Response) => {
  try {
    const result = await axios.get(`${PROCESS_ENV.CHECK_CONTAINERS}/api/ditest/v1/bi_devops/check_k8s_status`, {
      params: req.query,
    })
    return res.json(result.data)
  } catch (error) {
    return res.json({
      code: 500,
      msg: (error as any).response?.data?.msg || (error as any).message || '',
    })
  }
})

router.get('/operation-containers/:pk', async (req: Request, res: Response) => {
  try {
    const pk = req.params.pk
    const result = await axios.get(`${PROCESS_ENV.CHECK_CONTAINERS}/api/ditest/v1/bi_devops/containers/${pk}`, {
      params: req.query,
    })
    return res.json(result.data)
  } catch (error) {
    return res.json({
      code: 500,
      msg: (error as any).response?.data?.msg || (error as any).message || '',
    })
  }
})
export default router
