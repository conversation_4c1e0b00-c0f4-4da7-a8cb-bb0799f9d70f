{
  "compilerOptions": {
    "target": "ES2022",
    "useDefineForClassFields": true,
    "forceConsistentCasingInFileNames": true,
    "noEmitOnError": true,
    "lib": ["ES2023", "DOM", "DOM.Iterable"],
    "baseUrl": "./",
    "paths": {
      "src/*": ["src/*"],
      "@shared/*": ["src/shared/*"],
      "@components/*": ["src/client/components/*"],
      "@charts/*": ["src/client/charts/*"],
      "@client/utils": ["src/client/utils"],
      "@server/utils": ["src/server/utils"],
      "@api": ["src/client/x-engine/api"],
      "@api*": ["src/client/x-engine/api*"],
      "@atoms": ["src/client/x-engine/atoms"],
      "@atoms*": ["src/client/x-engine/atoms*"],
      "@libs": ["src/client/x-engine/common/libs"],
      "@libs*": ["src/client/x-engine/common/libs*"],
      "@XEngineRouter": ["src/client/x-engine/widget/router"],
      "@XEngineRouter*": ["src/client/x-engine/widget/router*"],
      "@ui*": ["src/client/x-engine/widget/ui*"],
      "@constant": ["src/client/x-engine/widget/constant"],
      "@constant*": ["src/client/x-engine/widget/constant*"],
      "@components*": ["src/client/x-engine/widget/components*"],
      "@model*": ["src/client/x-engine/widget/model*"],
      "@pages*": ["src/client/x-engine/pages*"],
      "@images*": ["src/client/x-engine/common/images*"],
      "@text*": ["src/client/x-engine/common/text*"]
    },
    "module": "CommonJS",
    "skipLibCheck": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react",
    /* Linting */
    "strict": true /* Enable all strict type-checking options. */,
    "strictNullChecks": true /* When type checking, take into account 'null' and 'undefined'. */,
    // "noUnusedLocals": true,
    "noUnusedParameters": true,
    "esModuleInterop": true /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables 'allowSyntheticDefaultImports' for type compatibility. */,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src"],
  "exclude": ["src/client/x-engine/**/*"],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ]
}
