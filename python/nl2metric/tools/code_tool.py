import re

from typing import Type, Dict
from common.llm.general import create_chat_model
from common.types.base import (
    ParamsExtractStage,
    CHAIN_META,
    ChainMeta,
)
from langchain_core.runnables import RunnableConfig, RunnableLambda
from langchain_core.tools import BaseTool
from langchain_core.pydantic_v1 import BaseModel, Field
from langchain_core.output_parsers import StrOutputParser
from nl2agent.common.agent_reporter import reporter_run_chain
from typing import Optional, List
from smolagents import LocalPythonExecutor

from nl2agent.tools.base_tool import register_tool


class Input(BaseModel):
    query: str = Field(description="具体的任务")


additional_authorized_imports = [
    "time",
    "datetime",
    "json",
    "pandas",
    "numpy",
    "matplotlib",
    "scipy",
]


@register_tool(name="python_code_tool")
class PyCodeInterpreterTool(BaseTool):
    name = "python_code_tool"
    description = "智能python代码生成"
    args_schema: Type[BaseModel] = Input
    breadcrumbs: Optional[List[str]] = None

    def _preprocess(self, query, config: RunnableConfig):
        if isinstance(query, dict):
            query = query["query"]
        csv_mgr = config[CHAIN_META][ChainMeta.CSV_MGR]
        return {
            "query": query,
            "csv_file_descps": csv_mgr.file_descps,
        }

    def _create_chat_model(self, prompt, config: RunnableConfig):
        model_type = config[CHAIN_META][ChainMeta.CHAT_MODEL_TYPE]
        model = create_chat_model(model_type=model_type)
        return reporter_run_chain(
            breadcrumbs=self.breadcrumbs,
            chain=model,
            input=prompt,
            config=config,
            name=self.name,
        )

    def _postprocess(self, input, config: RunnableConfig):
        pattern = r"<think>.*?</think>"
        code = re.sub(pattern, "", input, flags=re.DOTALL).strip()
        match = re.search(r"```python(.*?)```", code, re.DOTALL)
        if match:
            return match.group(1).strip()
        else:
            return code

    def _run_py_code_tool(self, code, config: RunnableConfig):
        executor = LocalPythonExecutor(additional_authorized_imports)
        executor.send_tools({})
        csv_mgr = config[CHAIN_META][ChainMeta.CSV_MGR]
        executor.send_variables(csv_mgr.file_variables)
        _, logs, _ = executor(code)
        return logs

    def _run(self, query: str, config: RunnableConfig) -> Dict:
        prompt_selector = config[CHAIN_META][ChainMeta.PROMPT_SELECTOR]
        chain = (
            RunnableLambda(self._preprocess, name=f"py_code_tool_preprocess")
            | RunnableLambda(
                prompt_selector.gen_prompt,
                name="PromptSelectorBase.gen_prompt:"
                + ParamsExtractStage.AGENT_PY_CODE_TOOL,
            ).bind(stage=ParamsExtractStage.AGENT_PY_CODE_TOOL)
            | RunnableLambda(
                self._create_chat_model, name=f"py_code_tool_create_chat_model"
            )
            | StrOutputParser()
            | RunnableLambda(self._postprocess, name=f"py_code_tool_postprocess")
            | RunnableLambda(self._run_py_code_tool, name=f"py_code_tool")
        )
        return chain.invoke(query, config=config)


if __name__ == "__main__":
    tool = PyCodeInterpreterTool()
    # tool
    print(tool.invoke("print('hello world')", config={}))

    code = """
import pandas as pd
import numpy as np  # 用于生成示例数据
# 1. 创建示例 DataFrame
data = {
    'Name': ['Alice', 'Bob', 'Charlie', 'David', 'Eva'],
    'Math': [90, 85, 88, 92, 78],
    'Physics': [88, 90, 78, 85, 80],
    'Chemistry': [92, 85, 80, 88, 75]
}
df = pd.DataFrame(data)

print(df)
print("--------------")
df
    """

    print(tool.invoke(code, config={}))
