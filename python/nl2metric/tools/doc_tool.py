import asyncio
from typing import Type, Dict

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from common.types.base import CHAIN_META, ChainMeta
from config.app_config import doc_endpoint
from nl2agent.tools.base_tool import register_tool

import aiohttp
from typing import List


class RetrieverArgs(BaseModel):
    query: str = Field(description="用户输入的查询")


@register_tool(name="doc_retrieval")
class RetrieverDocument(BaseTool):
    name = "doc_retrieval"
    description = "文档查询工具，关于文档内容相关的问题"
    args_schema: Type[BaseModel] = RetrieverArgs

    async def _arun(self, query: str, config: RunnableConfig) -> str:
        url = f"{doc_endpoint}/api/doc_index/query_document"  # 替换为基础地址
        file_ids = config[CHAIN_META][ChainMeta.DOC_FILE_IDS]
        payload = {"query": query, "ids": file_ids or [], "only_nodes": True}
        async with aiohttp.ClientSession() as session:
            async with session.post(url=url, json=payload) as res:
                res.raise_for_status()
                ret = await res.json()
                if ret.get("code") != 0:
                    return "Error"
                text_nodes: List[Dict] = (
                    ret.get("data", {}).get("sourceNodes", {}).get("textNodes")
                )
                content = "\n".join([node.get("content") for node in text_nodes])
                return content

    def _run(self, query: str, config: RunnableConfig) -> str:
        return asyncio.run(self._arun(query, config))


class QueryDocuments(BaseTool):
    name = "doc_query"
    description = "文档查询工具，关于文档内容相关的问题."
    args_schema: Type[BaseModel] = RetrieverArgs

    async def _arun(self, query: str, config: RunnableConfig) -> str:
        url = f"{doc_endpoint}/api/doc_index/query_document"  # 替换为基础地址
        file_ids = config[CHAIN_META][ChainMeta.DOC_FILE_IDS]
        payload = {"query": query, "ids": file_ids}
        async with aiohttp.ClientSession() as session:
            async with session.post(url=url, json=payload) as res:
                res.raise_for_status()
                ret = await res.json()
                if ret.get("code") != 0:
                    return "Error"
                return ret.get("data", {}).get("content", "")

    def _run(self, query: str, config: RunnableConfig) -> str:
        return asyncio.run(self._arun(query, config))


if __name__ == "__main__":
    # QueryDocuments().invoke(
    #     {"query": "中国宝武钢铁集团有限公司 2024 年 03 月的资产总计是多少？期末金额相比年初金额有何变化？"}
    # )
    RetrieverDocument().invoke(
        {
            "query": "中国宝武钢铁集团有限公司 2024 年 03 月的资产总计是多少？期末金额相比年初金额有何变化？",
        }
    )
