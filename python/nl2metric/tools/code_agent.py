import asyncio

import aiohttp
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import BaseTool
from langchain_core.pydantic_v1 import BaseModel, Field
from smolagents import ActionStep

import json
from datetime import datetime

from common.types.base import CHAIN_META, ChainMeta
from config import app_config
from nl2agent.tools.base_tool import register_tool
from typing import Type


def to_markdown_string(
    chunk: ActionStep,
    max_input_messages: int = 3,
    collapse_initial_system_prompt: bool = False,
) -> str:
    """
    将ActionStep对象转换为结构化Markdown格式字符串
    包含时间戳、Token统计、代码高亮等优化显示

    Args:
        chunk (ActionStep): 需要转换的ActionStep实例

    Returns:
        str: 优化排版后的Markdown字符串
    """

    lines = []

    # 步骤头部信息
    header = "## "
    if chunk.step_number is not None:
        header += f"步骤 {chunk.step_number}"
    else:
        header += "执行步骤"

    # 时间信息处理
    if chunk.start_time and chunk.end_time:
        duration = (
            chunk.duration
            if chunk.duration is not None
            else chunk.end_time - chunk.start_time
        )
        time_str = (
            f"<small>(🕒 {datetime.fromtimestamp(chunk.start_time).strftime('%Y-%m-%d %H:%M:%S')} - "
            f"{datetime.fromtimestamp(chunk.end_time).strftime('%H:%M:%S')} | "
            f"耗时: {duration:.2f}秒)</small>"
        )
        header += f" {time_str}"
    lines.append(header)

    # 资源消耗统计
    meta_lines = []
    if hasattr(chunk, "input_token_count") and hasattr(chunk, "output_token_count"):
        meta_lines.append(
            f"🔢 输入Token: {chunk.input_token_count:,} | 输出Token: {chunk.output_token_count:,}"
        )
    if hasattr(chunk, "duration") and chunk.duration:
        meta_lines.append(f"⏳ 处理耗时: {chunk.duration:.2f}秒")
    if meta_lines:
        lines.append("\n".join(["<sub>" + " | ".join(meta_lines) + "</sub>"]))

    # 优化后的模型输入处理
    if chunk.model_input_messages:
        lines.append("### 📥 模型输入")

        # 智能过滤系统提示
        filtered_msgs = [
            msg
            for msg in chunk.model_input_messages
            if not collapse_initial_system_prompt or msg["role"] != "system"
        ]

        # 按需截断
        display_msgs = filtered_msgs[-max_input_messages:]

        for msg in display_msgs:
            role_icon = "👤" if msg["role"] == "user" else "🤖"
            content = msg.get("content", "")

            # 处理多模态内容
            if isinstance(content, list):
                text_content = [
                    item["text"] for item in content if item["type"] == "text"
                ]
                content = "\n".join(text_content)

            # 折叠长文本
            if len(content) > 500:
                lines.append(
                    f"<details><summary>{role_icon} {msg['role'].capitalize()} (点击展开)</summary>\n\n{content}\n</details>"
                )
            else:
                lines.append(f"**{role_icon} {msg['role'].capitalize()}**\n{content}")

    # 工具调用明细
    if chunk.tool_calls:
        lines.append("### 🛠️ 工具调用")
        for tool_call in chunk.tool_calls:
            # 根据工具类型确定代码语言
            code_lang = "python" if tool_call.name == "python_interpreter" else "json"

            # 参数格式化处理
            try:
                args = json.loads(tool_call.arguments)
                formatted_args = json.dumps(args, indent=2, ensure_ascii=False)
            except json.JSONDecodeError:
                formatted_args = tool_call.arguments.strip()

            lines.extend(
                [
                    f"**{tool_call.name}** `(调用ID: {tool_call.id})`",
                    f"```{code_lang}\n{formatted_args}\n```",
                ]
            )

    # 模型原始输出
    if chunk.model_output:
        lines.append("### 📤 模型原始输出")
        code_block = chunk.model_output.strip()
        code_lang = (
            "python" if "python_interpreter" in str(chunk.tool_calls) else "text"
        )
        lines.append(f"```{code_lang}\n{code_block}\n```")

    # 执行结果输出
    if chunk.action_output is not None:
        lines.append("### 🖥️ 执行结果")
        lines.append(f"```bash\n{chunk.action_output}\n```")

    # 观察记录处理
    if chunk.observations:
        lines.append("### 🔍 系统观察")
        lines.append(f"```\n{chunk.observations.strip()}\n```")

    if chunk.observations_images:
        lines.append("### 📸 捕获图像")
        lines.extend([f"![观测图像]({img})" for img in chunk.observations_images])

    # 错误信息处理
    if chunk.error:
        lines.append("### ❌ 执行异常")
        lines.append(f"```error\n{str(chunk.error).strip()}\n```")

    # 添加分割线
    lines.append("\n---")

    return "\n\n".join(lines)


class AgentInput(BaseModel):
    query: str = Field(description="具体的任务")


@register_tool(name="code_agent")
class PyCodeAgent(BaseTool):
    name = "code_agent"
    description = "智能python代码生成与分析助手"
    args_schema: Type[BaseModel] = AgentInput

    async def _arun(self, query: str, config: RunnableConfig):
        url = f"http://127.0.0.1:{app_config.DEFAULT_BIND_PORT}/api/v1/fallback_chat"  # 替换为基础地址
        payload = {
            "query": query,
            "model_type": config[CHAIN_META][ChainMeta.CHAT_MODEL_TYPE],
            "is_web_search": False,
            "model_id": config[CHAIN_META][ChainMeta.MODEL_ID],
            # 这里只执行一次，出错的话后面在外层加重试
            "max_steps": config[CHAIN_META].get("max_steps", 1),
        }
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload) as res:
                res.raise_for_status()
                ret = await res.text()
        return ret

    def _run(self, query: str, config: RunnableConfig):
        return asyncio.run(self._arun(query, config))


if __name__ == "__main__":
    agent = PyCodeAgent()
    chunk = agent.invoke(
        {"query": "求第16个斐波那契数。"},
        config={
            "model_id": "CYLuY5VklxU2UTK2",
            "model_type": "deepseek-14b",
            "max_steps": 10,
        },
    )
    print(chunk)
