import json
import uuid

import requests
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
from smolagents import VisitWebpageTool

from config import app_config
from nl2agent.tools.base_tool import register_tool
from typing import Type


class WebSearchArgs(BaseModel):
    query: str = Field(description="用户输入的查询")


@register_tool(name="web_search")
class WebSearcherTool(BaseTool):
    name = "web_search"
    description = "搜索工具，搜索网络公开信息"
    args_schema: Type[BaseModel] = WebSearchArgs

    def _run(self, query: str, config: RunnableConfig) -> str:
        request_id = str(uuid.uuid4())
        payload = {
            "request_id": request_id,
            "tool": "web-search-pro",
            "stream": False,
            "messages": [{"role": "user", "content": query}],
        }
        api_url = "https://open.bigmodel.cn/api/paas/v4/tools"
        headers = {
            "Authorization": app_config.ZHIPU_WEB_SEARCH_API_KEY,
            "Content-Type": "application/json",
        }
        try:
            response = requests.post(api_url, json=payload, headers=headers)
        except requests.exceptions.RequestException as e:
            raise RuntimeError(f"网络请求失败: {str(e)}") from e

        if response.status_code != 200:
            raise RuntimeError(f"搜索接口返回错误: {response.status_code}, 内容: {response.text}")

        try:
            results = response.json()["choices"][0]["message"]["tool_calls"][1].get(
                "search_result", []
            )
        except (KeyError, IndexError, json.JSONDecodeError) as e:
            raise ValueError(f"响应格式不符合预期: {str(e)}, 响应内容: {response.text}") from e

        # 解析结果并验证字段
        parsed_results = []
        for result in results:
            # 确保字段存在，避免 KeyError
            title = result.get("title", "[无标题]")
            content = result.get("content", "[无摘要]")
            link = result.get("link", "[无链接]")
            parsed_results.append("\n".join([title, content, link]))

        return "\n".join(parsed_results)


class FetchArgs(BaseModel):
    url: str = Field(description="The url of the webpage to fetch")


class FetchWebpageTool(BaseTool):
    name = "visit_webpage"
    description = "Visits a webpage at the given url and reads its content as a markdown string. Use this to browse webpages."
    args_schema: Type[BaseModel] = FetchArgs

    def _run(self, url: str, config: RunnableConfig) -> str:
        return VisitWebpageTool()(url)


if __name__ == "__main__":
    tool = WebSearcherTool()
    print(tool.run("如何使用python爬取网页"))
    # fetched_webpage_tool = FetchWebpageTool()
    # print(fetched_webpage_tool.run("https://www.langchain.com/"))
