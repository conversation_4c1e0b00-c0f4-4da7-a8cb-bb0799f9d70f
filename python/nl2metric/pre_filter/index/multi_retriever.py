from typing import List, Dict, Any

from langchain_core.runnables import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from llama_index.core.indices.base_retriever import BaseRetriever
from llama_index.core.indices.query.schema import QueryBundle
from llama_index.core.schema import NodeWithScore

from common.logging.logger import get_logger
from config.project_config import get_project_config
from nl2metric.few_shots import FENGHUO_PROJECT_NAME

logger = get_logger(__name__)


# union based on the order of the input
# otherwise llm ans will change after index rebuild
def default_merge_multi_retrievers_union(
    retriever_name_to_output: Dict[str, List[NodeWithScore]], **kwargs: Any
) -> List[NodeWithScore]:
    seen = set()
    union_list = []
    for key in retriever_name_to_output:
        for node in retriever_name_to_output[key]:
            if node.node_id not in seen:
                seen.add(node.node_id)
                union_list.append(node)
    return union_list


# intersection based on the order of the input
# otherwise llm ans will change after index rebuild
def default_merge_multi_retrievers_intersection(
    retriever_name_to_output: Dict[str, List[NodeWithScore]], **kwargs: Any
) -> List[NodeWithScore]:
    if not retriever_name_to_output:
        return []

    # Initialize intersection with the node_ids of the first list
    iter_keys = iter(retriever_name_to_output)
    first_key = next(iter_keys)
    intersection_set = {node.node_id for node in retriever_name_to_output[first_key]}

    # Intersect with node_ids from all other lists
    for key in iter_keys:
        current_set = {node.node_id for node in retriever_name_to_output[key]}
        intersection_set.intersection_update(current_set)

    # Preserve the order from the first list
    first_list = retriever_name_to_output[first_key]
    intersection_list = [
        node for node in first_list if node.node_id in intersection_set
    ]

    return intersection_list


def metric_merge_multi_retrievers_result(
    retriever_name_to_output: Dict[str, List[NodeWithScore]], **kwargs: Any
) -> List[NodeWithScore]:
    result_nodes: List[NodeWithScore] = []

    # Do merge retriever_name_to_output

    return result_nodes


def dimension_merge_multi_retrievers_result(
    retriever_name_to_output: Dict[str, List[NodeWithScore]], **kwargs: Any
) -> List[NodeWithScore]:
    result_nodes: List[NodeWithScore] = []

    # Do merge retriever_name_to_output

    return result_nodes


def dimension_value_merge_multi_retrievers_result(
    retriever_name_to_output: Dict[str, List[NodeWithScore]], **kwargs: Any
) -> List[NodeWithScore]:
    project_name = kwargs.get("project_name", FENGHUO_PROJECT_NAME)
    dimension_value_list = get_project_config(project_name, None).dimension_value_list
    dimension_value_nodes = kwargs.get("dimension_value_nodes", [])

    retrieve_nodes = default_merge_multi_retrievers_union(retriever_name_to_output)
    expand_nodes: List[NodeWithScore] = []
    for node_with_score in retrieve_nodes:
        for dim_value_list in dimension_value_list:
            if node_with_score.node.text in dim_value_list:
                for dim_value_name in dim_value_list:
                    node = [
                        n for n in dimension_value_nodes if n.text == dim_value_name
                    ]
                    expand_nodes.extend(NodeWithScore(node=node) for node in node)

    return retrieve_nodes + expand_nodes


class MultiRetriever(BaseRetriever):
    def __init__(
        self,
        retrievers,
        name,
        func=default_merge_multi_retrievers_union,
        mode: str = "OR",
        **kwargs: Any,
    ) -> None:
        """Init params."""
        if mode not in ("AND", "OR"):
            raise ValueError("Invalid mode.")
        self._mode = mode
        if retrievers is None:
            raise ValueError("Invalid retrievers.")
        self._name = name
        self._retrievers = retrievers
        self._merge_multi_retrievers_result = func
        self._dimension_value_nodes = kwargs.get("dimension_value_nodes", None)
        self._project_name = kwargs.get("project_name", FENGHUO_PROJECT_NAME)

    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        """Retrieve nodes given query."""
        if self._retrievers is None:
            return []
        # init collection
        retriever_name_to_output: Dict[str, List[NodeWithScore]] = {}
        for retriever in self._retrievers:
            # parse retriever name, and retrieve nodes
            cur_name = getattr(retriever, "custom_name", retriever.__class__.__name__)
            # use langchain to trace result in langfuse
            chain = RunnableLambda(
                retriever.retrieve,
                name=f"{self._name}:{cur_name}",
            )
            cur_nodes = chain.invoke(query_bundle)
            retriever_name_to_output[cur_name] = cur_nodes
        retrieve_nodes = self._merge_multi_retrievers_result(
            retriever_name_to_output,
            dimension_value_nodes=self._dimension_value_nodes,
            project_name=self._project_name,
        )
        return retrieve_nodes


def batch_option(list_with_set, option_type):
    if option_type not in ("union", "intersection"):
        raise ValueError("Invalid option_type.")
    size = len(list_with_set)
    if size == 0:
        return []
    res = list_with_set[0]
    if size == 1:
        return res

    for cur_set in list_with_set:
        if option_type == "union":
            res = res.union(cur_set)
        else:
            res = res.intersection(cur_set)
    return res
