from typing import List, Optional

from jinja2 import Template
from llama_index.core.indices.postprocessor import BaseNodePostprocessor
from llama_index.core.indices.query.schema import QueryBundle
from llama_index.core.schema import NodeWithScore
from pydantic import PrivateAttr

from common.llm.general import llm_predict
from common.utils.json_utils import extract_json_from_string
from config.app_config import DEFAULT_MODEL_TYPE
from metastore.base import BaseMetaStore, Dimension
from pre_filter.index.const import DIMENSION_NAME_KEY, MODEL_NAME_KEY
from pre_filter.index.post_processors import time_filter_prompt


class TimeFilterPostProcessor(BaseNodePostprocessor):
    _metastore: BaseMetaStore = PrivateAttr()

    def __init__(self, metastore: BaseMetaStore):
        self._metastore = metastore
        super().__init__()

    def _postprocess_nodes(
        self,
        nodes: List[NodeWithScore],
        query_bundle: Optional[QueryBundle] = None,
    ) -> List[NodeWithScore]:
        assert query_bundle is not None
        time_dimensions: List[Dimension] = []
        no_time_nodes: List[NodeWithScore] = []
        time_nodes: List[NodeWithScore] = []
        for node in nodes:
            dimension_name = node.metadata[DIMENSION_NAME_KEY]
            model_name = node.metadata[MODEL_NAME_KEY]
            dimension = self._metastore.get_dimension(model_name, dimension_name)
            assert dimension is not None
            if dimension.type == "time":
                time_dimensions.append(dimension)
                time_nodes.append(node)
            else:
                no_time_nodes.append(node)
        prompt = self._format_prompt(
            query=query_bundle.query_str,
            time_columns=[dimension.description or "" for dimension in time_dimensions],
        )
        result = extract_json_from_string(llm_predict(prompt, DEFAULT_MODEL_TYPE).text, "TimeFilterPostProcessor")  # type: ignore
        ret = []
        ret.extend(no_time_nodes)
        if result["need_time"]:
            for idx in result["relevant_time_dimension_columns"]:
                ret.append(time_nodes[idx])
        return ret

    def _format_prompt(self, query: str, time_columns: List[str]):
        template = Template(time_filter_prompt.PROMPT)
        return template.render(query=query, time_columns=time_columns)
