from collections import defaultdict, OrderedDict
from typing import Dict, List, Set, Tuple, Optional, Any

from llama_index.core import QueryBundle

from common.logging.logger import get_logger
from config.project_config import get_project_config_by_id
from metastore import get_metastore
from metastore.base import Dimension, Metric
from pre_filter.index.builder import SchemaIndexBuilder
from pre_filter.index.const import (
    DIMENSION_NAME_KEY,
    METRIC_NAME_KEY,
    MODEL_NAME_KEY,
    DIMENSION_NAME_MODEL_KEY,
)
from config.project_config import get_project_config
from common.types.base import CHAIN_META, ChainMeta
from langchain_core.runnables import RunnableConfig
from metastore.base import BaseMetaStore

HANLP_TASKS = ("tok/fine", "pos/pku")

logger = get_logger(__name__)


class PreFilterService:
    def __init__(
        self,
        project_id: str,
        model_name: str,
        metrics: Dict[str, Metric],
        dimensions: Dict[str, Dimension],
        metastore: BaseMetaStore,
    ) -> None:
        logger.debug(
            f"PreFilterService for project_id {project_id} and model_name {model_name} creating..."
        )
        config = get_project_config_by_id(project_id, model_name)
        self._dimension_value_lcs_match_ratio = config.dimension_value_lcs_match_ratio

        builder = SchemaIndexBuilder(
            project_id=project_id,
            vector_store_type=config.vector_store_type,
            dimension_value_index_type=config.dimension_value_index_type,
        )
        builder.with_metrics(metrics)
        builder.with_dimensions(dimensions)
        self._metrics = metrics
        # for model_name none, the _dimensions is merged from all model
        self._dimensions = dimensions
        self._schema_index = builder.build()
        self._model_id_list = metastore._model_id_list
        self._project_id = project_id
        logger.info(
            f"PreFilterService for project_id {project_id} and model_name {model_name} created, model_id_list {self._model_id_list}"
        )

    def need_refresh(self):
        if get_project_config_by_id(self._project_id, None).always_refresh_cache:
            return True
        metastore = get_metastore(self._project_id)
        if metastore.same_content(self._model_id_list):
            return False
        logger.info(
            f"need_refresh_pre_filter_service metastore updated, "
            f"old metastore ts {self._model_id_list}, "
            f"new metastore ts {metastore._model_id_list}"
        )
        return True

    @classmethod
    def from_model(cls, project_id, model_name):
        metastore = get_metastore(project_id)
        metrics = metastore.list_metrics_by_model_name(model_name)
        # filter out all time-related dimensions, only keep type == categorical
        dimensions = metastore.list_dimensions_by_model_name(model_name, "categorical")
        return cls(
            project_id=project_id,
            model_name=model_name,
            metrics=metrics,
            dimensions=dimensions,
            metastore=metastore,
        )

    @classmethod
    def from_dimension_labels(cls, project_id, model_name, dimension_labels):
        metastore = get_metastore(project_id)
        dimensions = metastore.list_dimensions_by_dimension_labels(
            model_name, dimension_labels
        )
        return cls(
            project_id=project_id,
            model_name=model_name,
            metrics={},
            dimensions=dimensions,
            metastore=metastore,
        )

    def retrieve_metric_nodes(
        self, query: str, top_k: int = 10, relative_score_threshold=0.0
    ):
        metric_retriever = self._schema_index.create_metric_retriever(
            top_k=top_k,
            relative_score_threshold=relative_score_threshold,
        )
        return metric_retriever.retrieve(QueryBundle(query))

    def retrieve_metrics(
        self, query: str, top_k: int = 10, relative_score_threshold=0.0
    ) -> List[Metric]:
        nodes = self.retrieve_metric_nodes(query, top_k, relative_score_threshold)
        metric_names = [n.metadata[METRIC_NAME_KEY] for n in nodes]
        ret = []
        for name in metric_names:
            metric = self._metrics[name]
            if not metric:
                logger.error("Metric not found, name=%s", name)
                continue
            ret.append(metric)
        return ret

    def retrieve_dimension_nodes(
        self, query: str, top_k: int = 10, relative_score_threshold=0.0
    ):
        dimension_retriever = self._schema_index.create_dimension_retriever(
            top_k=top_k, relative_score_threshold=relative_score_threshold
        )
        return dimension_retriever.retrieve(QueryBundle(query))

    def retrieve_dimension_value_nodes(self, query: str, top_k: int = 10):
        dimension_value_retriever = self._schema_index.create_dimension_value_retriever(
            lcs_match_ratio=self._dimension_value_lcs_match_ratio,
            top_k=top_k,
        )
        return dimension_value_retriever.retrieve(QueryBundle(query))

    def retrieve_dimensions(
        self,
        query: str,
        config: RunnableConfig,
        top_k: int = 10,
        relative_score_threshold=0.0,
    ) -> List[Dimension]:
        nodes = self.retrieve_dimension_nodes(query, top_k, relative_score_threshold)
        dimensions: Dict[object, Dimension] = {}
        dimension_values: Dict[object, OrderedDict] = defaultdict(OrderedDict)
        for node in nodes:
            dimension_name = node.metadata[DIMENSION_NAME_KEY]
            model_name = node.metadata[MODEL_NAME_KEY]
            dimension = self._dimensions[dimension_name]
            if not dimension:
                logger.error(
                    "Dimension not found, model=%s, name=%s",
                    model_name,
                    dimension_name,
                )
                continue
            dimensions[dimension.key] = dimension

        value_nodes = self.retrieve_dimension_value_nodes(query, top_k)

        limit_num, total_limit_num = get_project_config(
            config[CHAIN_META][ChainMeta.PROJECT_NAME], None
        ).limit_num_of_dimension_from_value_node
        dimension_values_filter = get_project_config(
            config[CHAIN_META][ChainMeta.PROJECT_NAME], None
        ).dimension_values_filters
        cnt = defaultdict(int)
        total_cnt = 0
        for value_node in value_nodes:
            for dimension_name, model_name in value_node.metadata[
                DIMENSION_NAME_MODEL_KEY
            ]:
                dimension = self._dimensions[dimension_name]
                dimension_label = dimension.label
                # 过滤掉不需要的码值
                skip = False
                for (
                    filtered_dimension,
                    filtered_dimension_value,
                ) in dimension_values_filter:
                    if (
                        filtered_dimension == dimension_label
                        and filtered_dimension_value == value_node.text
                    ):
                        skip = True
                if not skip:
                    if limit_num > 0:
                        if dimension.key in dimensions:
                            dimension_values[dimension.key][value_node.text] = None
                        else:
                            # limit the number of dimension to avoid llm max token error
                            if cnt[value_node.text] >= limit_num:
                                continue
                            if total_limit_num > 0 and total_cnt >= total_limit_num:
                                continue
                            dimensions[dimension.key] = dimension
                            dimension_values[dimension.key][value_node.text] = None
                            cnt[value_node.text] += 1
                            total_cnt += 1
                    else:
                        dimensions[dimension.key] = dimension
                        dimension_values[dimension.key][value_node.text] = None

        ret: List[Dimension] = []
        for dimension in dimensions.values():
            copy = dimension.model_copy()
            values = copy.values
            retrieved_values = dimension_values.get(dimension.key, OrderedDict()).keys()
            copy.set_values({v.key: v for v in values if v.name in retrieved_values})
            ret.append(copy)
        return ret
