from dotenv import load_dotenv
from pathlib import Path

# specify path to easily change env in docker
env_file = Path(__file__).parent / "../../../.env"
print(f"load dotenv {env_file}")
load_dotenv(dotenv_path=env_file, override=True)

from common.db_model.model import (
    NLMetricFewShotModel,
    get_semantic_project_by_name,
    save_nl_metric_few_shot_model,
)
from nl2metric.example_selector import SimpleFilterExampleSelector
from nl2metric.few_shots import (
    FEW_SHOT_PROJECT_NAME_KEY,
    JIAOHANG_PROJECT_NAME,
    JINGFEN_BI,
    TIANHONG_PROJECT_NAME,
    ZHONGYUAN_PROJECT_NAME,
    CHINA_LIFE_NAME,
)
from common.prompt_selector.prompts.gen_nl2metric_metrics import (
    JIAO_HANG_EXAMPLES,
    JING<PERSON>EN_EXAMPLES,
    TIAN_HONG_EXAMPLES,
    ZHONG_YUAN_EXAMPLES,
    CHINA_LIFE_EXAMPLES,
)

for example in JIAO_HANG_EXAMPLES:
    example[SimpleFilterExampleSelector.METADATA_LABELS_KEY] = {
        FEW_SHOT_PROJECT_NAME_KEY: JIAOHANG_PROJECT_NAME
    }

for example in ZHONG_YUAN_EXAMPLES:
    example[SimpleFilterExampleSelector.METADATA_LABELS_KEY] = {
        FEW_SHOT_PROJECT_NAME_KEY: ZHONGYUAN_PROJECT_NAME
    }

for example in TIAN_HONG_EXAMPLES:
    example[SimpleFilterExampleSelector.METADATA_LABELS_KEY] = {
        FEW_SHOT_PROJECT_NAME_KEY: TIANHONG_PROJECT_NAME
    }
for example in JINGFEN_EXAMPLES:
    example[SimpleFilterExampleSelector.METADATA_LABELS_KEY] = {
        FEW_SHOT_PROJECT_NAME_KEY: JINGFEN_BI
    }

examples = CHINA_LIFE_EXAMPLES


def migrate_few_shot_models():
    model_list = []

    for few_shot in examples:
        project_name = few_shot[SimpleFilterExampleSelector.METADATA_LABELS_KEY][
            FEW_SHOT_PROJECT_NAME_KEY
        ]
        semantic_project_id = get_semantic_project_by_name(project_name).id
        few_shot_model = NLMetricFewShotModel(
            semantic_project_id=semantic_project_id,
            question=few_shot["question"],
            scene="metric",
            metrics=[
                {
                    "name": metric.name,
                    "label": metric.label,
                }
                for metric in few_shot["metrics"]
            ]
            if "metrics" in few_shot and few_shot["metrics"]
            else [],
            dimensions=[
                {
                    "name": dimension.name,
                    "label": dimension.label,
                    "values": [
                        {
                            "name": value.name,
                            "description": value.description,
                        }
                        for value in dimension.values
                    ],
                }
                for dimension in few_shot["dimensions"]
            ]
            if "dimensions" in few_shot and few_shot["dimensions"]
            else [],
            think=few_shot["think"],
            result=few_shot["result"],
            labels={"PROJECT_NAME": project_name},
        )
        model_list.append(few_shot_model)
    # import to mysql
    save_nl_metric_few_shot_model(model_list)


if __name__ == "__main__":
    migrate_few_shot_models()
