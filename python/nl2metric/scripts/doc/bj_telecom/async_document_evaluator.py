import asyncio
import os
from collections import defaultdict
from graphlib import <PERSON><PERSON><PERSON><PERSON><PERSON>
from pathlib import Path
from typing import List, Tuple

import pandas as pd
from dotenv import load_dotenv

from scripts.csv_to_html import csv_to_html

# specify path to easily change env in docker
env_file = Path(__file__).parent.parent.parent.parent / ".env"
print(f"env_file: {env_file}")
load_dotenv(dotenv_path=env_file, override=True)
# both app_config and doc_config needs load_doten


from llama_index.core import (
    ServiceContext,
    VectorStoreIndex,
    get_response_synthesizer,
    Response,
)
from llama_index.core.evaluation import RelevancyEvaluator, EvaluationResult

from llama_index.core.llama_dataset import LabelledRagDataset, BaseLlamaDataExample
from llama_index.core.llama_dataset.generator import RagDatasetGenerator
from llama_index.core.node_parser import TokenTextSplitter
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.response_synthesizers import ResponseMode
from llama_index.core.schema import BaseNode, QueryBundle, NodeWithScore
from llama_index.embeddings.openai import OpenAIEmbedding

from common.llm.llama_llm import get_llm
from config import app_config, doc_config
from config.app_config import (
    embedding_api_base,
    embedding_model_name,
    embedding_api_key,
)
from nl2document.index.query_engine.retrieve_engine import generate_retriever_engine

from llama_index.core.vector_stores import (
    MetadataFilters,
    MetadataFilter,
    FilterOperator,
)

from nl2document.common.vector.vector_store import get_vector_store

from nl2document.common.models.base_model import get_data_source, UploadStatus
from nl2document.common.models.model import CommonDocumentModel

vector_store = get_vector_store()
embed_model = OpenAIEmbedding(
    api_base=embedding_api_base,
    model_name=embedding_model_name,
    api_key=embedding_api_key,
    embed_batch_size=100,
)
service_context = ServiceContext.from_defaults(
    llm=get_llm(app_config.VLLM_MODEL_NAME),
    embed_model=embed_model,
    text_splitter=TokenTextSplitter(),
    context_window=5000,
)
index: VectorStoreIndex = VectorStoreIndex.from_vector_store(
    vector_store=vector_store, service_context=service_context
)
query_params = {
    "rank_topk": doc_config.rank_topk,
    "ask_doc_similarity_top_k": doc_config.ask_doc_similarity_top_k,
    "score_threshold": doc_config.ask_doc_similarity_threshold,
}


def sort_nodes(nodes: List[BaseNode]) -> List[BaseNode]:
    # 第一步：创建节点字典
    node_dict = {n.node_id: n for n in nodes}

    # 第二步：初始化 TopologicalSorter
    sorter = TopologicalSorter()

    # 第三步：添加依赖关系
    for node in nodes:
        if node.prev_node and node.prev_node.node_id in node_dict:
            sorter.add(node.node_id, node.prev_node.node_id)  # 当前节点依赖于前置节点
        else:
            sorter.add(node.node_id)  # 无依赖的节点

    # 第四步：执行排序
    try:
        sorted_ids = list(sorter.static_order())  # 获取排序后的节点 ID
        return [node_dict[node_id] for node_id in sorted_ids]  # 转换为节点对象列表
    except ValueError:
        raise ValueError("图中存在循环，无法完成排序。")


def find_connected_nodes(nodes):
    """
    根据已排序的节点列表，找到相连的节点集合。

    参数:
    nodes (list): 已排序的节点对象列表，每个节点对象应具有 node_id 和 prev_node 属性。

    返回:
    list of set: 每个集合包含一组相连的节点ID。
    """
    node_connections = defaultdict(set)
    dict = {node.node_id: node for node in nodes}
    # 遍历节点列表，构建连接关系
    for node in nodes:
        node_id = node.node_id
        prev_node = node.prev_node

        # 如果前驱节点存在，将当前节点和前驱节点互相添加到彼此的集合中
        if prev_node is not None:
            prev_node_id = prev_node.node_id
            node_connections[node_id].add(prev_node_id)
            node_connections[prev_node_id].add(node_id)

    # 将连接关系转化为集合列表
    visited = set()
    connected_components = []

    def dfs(node_id, component):
        """深度优先搜索，用于查找连通分量。"""
        stack = [node_id]
        while stack:
            current = stack.pop()
            if current not in visited:
                visited.add(current)
                component.add(current)
                for neighbor in node_connections[current]:
                    if neighbor not in visited:
                        stack.append(neighbor)

    # 查找所有连通分量
    for node_id in node_connections:
        if node_id not in visited:
            component = set()
            dfs(node_id, component)
            connected_components.append(component)
    ret = [[dict[nid] for nid in sets] for sets in connected_components]

    return ret


async def get_nodes_by_doc(doc: CommonDocumentModel) -> List[BaseNode]:
    filters = MetadataFilters(
        filters=[
            MetadataFilter(key="file_id", value=doc.id, operator=FilterOperator.EQ),
        ]
    )

    nodes = await vector_store.aget_nodes(filters=filters)
    nodes = list(filter(lambda x: x.metadata.get("parent_node") is None, nodes))
    nodes = sort_nodes(nodes)
    # ret = find_connected_nodes(nodes)
    # print([("none", n.node_id) if n.prev_node == None else (n.prev_node.node_id, n.node_id) for n in nodes])

    if nodes is None:
        print(f"file {doc.file_name} has no nodes")
        nodes = []
    return nodes


async def gen_eval_questionset(doc: CommonDocumentModel) -> LabelledRagDataset:
    nodes = await get_nodes_by_doc(doc)
    data_generator = RagDatasetGenerator(
        nodes, service_context=service_context, num_questions_per_chunk=1
    )
    eval_questions: LabelledRagDataset = (
        await data_generator.agenerate_questions_from_nodes()
    )
    return eval_questions


async def retriever_answer(
    retriever_engine: RetrieverQueryEngine, query: str
) -> tuple[Response, str]:
    chain = await retriever_engine.aretrieve(QueryBundle(query))
    nodes: List[NodeWithScore] = await chain.ainvoke(QueryBundle(query))
    text_chunks = [node.node.get_content() for node in nodes]

    response_synthesizer = get_response_synthesizer(
        use_async=True,
        response_mode=ResponseMode.TREE_SUMMARIZE,
        service_context=service_context,
    )
    answer = await response_synthesizer.aget_response(
        query_str=query, text_chunks=text_chunks
    )
    resp = Response(source_nodes=nodes, response=answer)
    return (resp, query)


async def gen_retriever_engine(doc: CommonDocumentModel):
    filters = MetadataFilters(
        filters=[
            MetadataFilter(key="file_id", value=doc.id, operator=FilterOperator.EQ),
        ]
    )
    retriever_engine: RetrieverQueryEngine = generate_retriever_engine(
        index,
        None,
        service_context=service_context,
        filters=filters,
        query_params=query_params,
    )
    return retriever_engine


async def evaluator_answer(
    evaluator: RelevancyEvaluator, query: str, response: Response
) -> pd.DataFrame:
    eval_result: EvaluationResult = await evaluator.aevaluate_response(
        query=query, response=response
    )
    # 获取上下文并将其转换为字符串
    context = "\n".join(eval_result.contexts)
    # 获取反馈和通过状态
    feedback = eval_result.feedback
    passing = eval_result.passing
    # 创建一个数据框存储查询和评估结果
    df = pd.DataFrame(
        {
            "query": query,
            "response": response.response,
            "context": context,
            "feedback": feedback,
            "passing": passing,
        },
        index=[0],
    )
    return df


async def evaluator_task(doc: CommonDocumentModel):
    csv_file = f"eval_result_{doc.file_name}.csv"
    if os.path.exists(csv_file):
        return
    eval_questions = await gen_eval_questionset(doc)
    evaluator = RelevancyEvaluator(service_context=service_context)

    retriever_engine = await gen_retriever_engine(doc)
    examples: List[BaseLlamaDataExample] = eval_questions.examples

    tasks = [retriever_answer(retriever_engine, example.query) for example in examples]
    dataframes_list = []
    for task in asyncio.as_completed(tasks):
        response, query = await task
        print(f"query: {query} completed")
        df = await evaluator_answer(evaluator, query, response)
        # 将数据框添加到列表中
        dataframes_list.append(df)
    # 将所有数据框合并成一个
    combined_df = pd.concat(dataframes_list, ignore_index=True)
    combined_df.to_csv(csv_file, index=False)
    csv_to_html(f"eval_result_{doc.file_name}.csv", f"eval_result_{doc.file_name}.html")


async def main():
    doc_list: List[CommonDocumentModel] = get_data_source().get_document_by_status(
        [UploadStatus.INDEX_BUILD_SUCCESS]
    )
    for doc in doc_list:
        await evaluator_task(doc)
        print(f"completed file {doc.file_name}")
    print("all done")


if __name__ == "__main__":
    asyncio.run(main())
