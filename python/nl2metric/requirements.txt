requests==2.32.3
pandas==2.0.2
numpy==1.24.4
pylint==2.17.5
black==23.3.0
blinker==1.7.0
click==8.1.7
Flask==2.3.3
waitress==2.1.2
isort==5.13.2
Jinja2==3.1.3
mypy-extensions==1.0.0
opentelemetry-api==1.21.0
opentelemetry-exporter-jaeger==1.21.0
opentelemetry-exporter-jaeger-proto-grpc==1.21.0
opentelemetry-exporter-jaeger-thrift==1.21.0
opentelemetry-instrumentation==0.42b0
opentelemetry-instrumentation-flask==0.42b0
opentelemetry-instrumentation-grpc==0.42b0
opentelemetry-instrumentation-requests==0.42b0
opentelemetry-instrumentation-wsgi==0.42b0
opentelemetry-sdk==1.21.0
opentelemetry-semantic-conventions==0.42b0
opentelemetry-util-http==0.42b0
python-dotenv==0.21.1
python-json-logger==2.0.7
thrift==0.16.0
tomli==2.0.1
tqdm==4.66.1
typing_extensions==4.9.0
Werkzeug==3.0.1
PyYAML==6.0.1
sqlalchemy==2.0.25
pymysql==1.1.0
hanlp~=2.1.0b50
llama-index==0.10.58
flake8==7.0.0
langchain==0.2.11
langchain-community==0.2.10
langchain-openai==0.1.17
llama-index-embeddings-langchain
sentence-transformers==3.4.1
pyinstrument==4.5.1
types-decorator==5.1.8.20240106
types-PyMySQL==1.1.0.1
types-PyYAML==6.0.12.12
types-requests==2.25.0
types-setuptools==69.0.0.20240125
types-waitress==2.1.4.20240106
colorama==0.4.6
poe-api-wrapper==1.3.7
pytest==8.0.1
mypy==1.8.0
FlagEmbedding==1.2.5
sql-metadata==2.10.0
httpx==0.27.0
httpx-sse==0.4.0
PyJWT==2.8.0
deepdiff
langfuse
#jieba
qianfan
tabulate

# askdoc
boto3~=1.28.9
s3fs==2023.12.2
zhon==2.0.2
redis==5.0.1
faiss-cpu==1.7.4
pdfplumber==0.10.3
python-docx==0.8.11
openpyxl==3.1.2
jieba==0.42.1
paddleocr==2.10.0
fastapi==0.112.2
pytesseract==0.3.10
# PyMuPDF==1.19.0
python-pptx==0.6.23
PyPDF2==3.0.1
pdf2image==1.17.0
pymilvus==2.5.5
llama-index-vector-stores-milvus==0.1.20
llama-index-vector-stores-faiss==0.1.2
llama-index-retrievers-bm25==0.2.2
paddlepaddle==2.6.1
ultralytics==8.2.51
zhipuai==2.1.4
#ray==2.34.0
xinference-client==0.14.1
aiomysql==0.2.0
aiosqlite==0.20.0
uvicorn==0.30.6
httptools==0.6.1
markdown==3.5.2
pdfkit==1.0.0
bm25s~=0.1.9
readerwriterlock==1.0.9
llamaindex-py-client==0.1.19
python-multipart==0.0.20
#smolagents==1.10.0
statsmodels==0.14.4
prometheus-client
alibabacloud_dysmsapi20170525