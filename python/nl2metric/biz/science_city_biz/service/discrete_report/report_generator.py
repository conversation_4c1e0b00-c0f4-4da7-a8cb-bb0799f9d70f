import ast
import asyncio
import json
import os
import uuid
import base64
from typing import Dict, List, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

from biz.science_city_biz.data.models.report_template import ScienceCitySectionConfig
from biz.science_city_biz.data.models.tree import TreeNode
from biz.science_city_biz.data.dao.assessment_dao import (
    AssessmentDataAccessor,
    DatabaseAssessmentAccessor,
    JsonAssessmentAccessor,
)
from biz.science_city_biz.internal_common import common
from biz.science_city_biz.service.discrete_report.assessment import AssessmentService
from biz.science_city_biz.service.discrete_report.template import TemplateService
from biz.science_city_biz.utils.utils import (
    generate_products_html_table,
    timeit_context,
)
from biz.science_city_biz.service.discrete_report.product_category import (
    ProductCategoryService,
)
from biz.science_city_biz.visualization.charts import ChartGenerator
from common.utils.concurrent_utils import run_concurrently
from biz.science_city_biz.data.models.report_task import TaskType
from biz.science_city_biz.config.config import (
    font_file_path,
    refer_scene_id,
    PUBLIC_REPORT_URL,
)
from common.utils.json_utils import extract_json_from_string
from config.app_config import VLLM_MODEL_NAME
from biz.science_city_biz.internal_common.llm import get_llm_service
from config.doc_config import default_endpoint_url
from nl2document.index.service.service import DocumentIndexService
from nl2document.common.msg.docservice import QueryDocumentRequest
from common.logging import logger

from nl2document.common.models.model import ONE_DAY_SECONDS, get_file_ids_by_scene_id
from biz.science_city_biz.data.models.assessment import CompanyInfo
from fastapi import Request
from starlette.datastructures import Headers
from biz.science_city_biz.data.models.report_template import DEFAULT_SECTION_CONFIGS
from common.fs.fs import get_s3_file_system
from common.fs.storage import create_s3_client
from biz.science_city_biz.config.config import science_city_report_s3_bucket_name

logger = logger.get_logger(__name__)


class ReportGenerator:
    def __init__(
        self,
        task_id: str = None,
        task_type: str = TaskType.DISCRETE,
        section_config_dict: dict = DEFAULT_SECTION_CONFIGS,
    ):
        self.template = TemplateService()
        self.dao: AssessmentDataAccessor = (
            DatabaseAssessmentAccessor(task_type)
            if task_id
            else JsonAssessmentAccessor()
        )
        self.section_config_dict = section_config_dict
        self.task_id = task_id
        self.assessment_service = AssessmentService()
        self.llm = get_llm_service(model=VLLM_MODEL_NAME)
        self.chart_generator = ChartGenerator(font_path=font_file_path)
        self.product_category = ProductCategoryService()
        self.chapter2 = ""
        self.final_suggests = ""
        self.suggests_dict = {}
        self.report_logs = {}
        self.company_info: CompanyInfo = None
# plan_and_route 现在使用延迟初始化
        self.report_outline_desc = []
        self.chapter3_dataconvert_content_split = []
        self.chapter3_dataconvert_detail_intro_output = ""
        self.chapter3_top_design_output = ""
        self.chapter3_dataconvert_detail_suggests_output = ""

        # 数据加载
        assessment_data = self.dao.load_assessment_data(self.task_id)
        (
            self.metric_tree,
            self.freq_dict,
        ) = self.assessment_service.build_assessment_tree(assessment_data)
        self._generate_charts(
            self.metric_tree,
        )
        self.company_info = assessment_data.company_info
        self.company_info.score = self.metric_tree.final_score
        self.company_info.level = self.metric_tree.get_metric_level()[0]
        self.total_table_content = self._generate_total_table(self.metric_tree)
        self.pri_node_table_content_dict: dict = {}
        self._init_pri_metric_html_table()
        self.hit_sec_node_list = []
        self.ter_qua_dict: dict = {}
        self.suggestion_dict_flatten = ""
        self.report_outline_desc_str = ""
        self.chapter3_content_dict = {}
        # 延迟初始化plan_and_route，避免在__init__中使用asyncio.run
        self._plan_and_route = None
        self._plan_and_route_initialized = False

        # 添加方法映射字典
        self.method_mapping = {
            1: self.get_total_level_desc,
            2: self.get_pri_metric_desc,
            3: self.get_sec_metric_desc_parallel,
            4: self.generate_digital_transform_v2,
            5: self.generate_top_design_v2,
            6: self.generate_qua_metric_suggestion,
            7: self.generate_build_plan,
            8: self.generate_system_software_adaptation_v2,
            9: self.generate_summary_v2,
        }

        # 初始化依赖关系管理
        self._init_dependency_graph()

    @property
    def plan_and_route(self):
        """获取plan_and_route，如果未初始化则自动初始化"""
        if not self._plan_and_route_initialized:
            self._ensure_plan_and_route_initialized()
        return self._plan_and_route

    @plan_and_route.setter
    def plan_and_route(self, value):
        """设置plan_and_route"""
        self._plan_and_route = value
        self._plan_and_route_initialized = True

    def _init_pri_metric_html_table(self):
        for pri_node in self.metric_tree.get_nodes_by_level(1):
            if pri_node.name == "数字化成效":
                continue
            self.pri_node_table_content_dict[
                pri_node.name
            ] = pri_node.generate_metric_html_table()

    def _init_dependency_graph(self):
        """初始化依赖关系图"""
        self.dependency_graph = {
            1: {
                "name": "get_total_level_desc",
                "dependencies": set(),
                "dependents": {4, 5, 7, 9},
            },
            2: {
                "name": "get_pri_metric_desc",
                "dependencies": set(),
                "dependents": {4, 5, 7, 9},
            },
            3: {
                "name": "get_sec_metric_desc_parallel",
                "dependencies": set(),
                "dependents": {5, 6, 7, 8, 9},
            },
            4: {
                "name": "generate_digital_transform_v2",
                "dependencies": {1, 2},
                "dependents": {5, 7, 9},
            },
            5: {
                "name": "generate_top_design_v2",
                "dependencies": {1, 2, 4},
                "dependents": {7, 9},
            },
            6: {
                "name": "generate_qua_metric_suggestion",
                "dependencies": {3},
                "dependents": {7, 8, 9},
            },
            7: {
                "name": "generate_build_plan",
                "dependencies": {1, 2, 3, 4, 5, 6},
                "dependents": {9},
            },
            8: {
                "name": "generate_system_software_adaptation_v2",
                "dependencies": {3, 6},
                "dependents": {9},
            },
            9: {
                "name": "generate_summary_v2",
                "dependencies": {1, 2, 3, 4, 5, 6, 7, 8},
                "dependents": set(),
            },
        }

    def get_dependencies(self, op_id: int) -> set:
        """获取操作的直接依赖项"""
        return self.dependency_graph[op_id]["dependencies"]

    def get_all_dependencies(self, op_id: int) -> set:
        """获取操作的所有依赖项(包括间接依赖)"""
        all_deps = set()
        direct_deps = self.get_dependencies(op_id)
        all_deps.update(direct_deps)

        for dep in direct_deps:
            all_deps.update(self.get_all_dependencies(dep))

        return all_deps

    def get_dependents(self, op_id: int) -> set:
        """获取操作的直接依赖者"""
        return self.dependency_graph[op_id]["dependents"]

    def get_all_dependents(self, op_id: int) -> set:
        """获取操作的所有依赖者(包括间接依赖)"""
        all_deps = set()
        direct_deps = self.get_dependents(op_id)
        all_deps.update(direct_deps)

        for dep in direct_deps:
            all_deps.update(self.get_all_dependents(dep))

        return all_deps

    def get_execution_order(self) -> list:
        """获取操作的执行顺序"""
        # 使用拓扑排序获取执行顺序
        in_degree = {
            op_id: len(self.get_dependencies(op_id)) for op_id in self.dependency_graph
        }
        queue = [op_id for op_id, degree in in_degree.items() if degree == 0]
        order = []

        while queue:
            op_id = queue.pop(0)
            order.append(op_id)

            for dependent in self.get_dependents(op_id):
                in_degree[dependent] -= 1
                if in_degree[dependent] == 0:
                    queue.append(dependent)

        if len(order) != len(self.dependency_graph):
            raise ValueError("存在循环依赖")

        return order

    def save_report_logs(self):
        with open("report_logs.json", "w") as f:
            json.dump(self.report_logs, f, ensure_ascii=False, indent=4)

    def _ensure_plan_and_route_initialized(self):
        """确保plan_and_route已初始化（同步版本）"""
        if not self._plan_and_route_initialized:
            try:
                # 检查是否有运行中的事件循环
                try:
                    loop = asyncio.get_running_loop()
                    # 如果有运行中的事件循环，使用run_in_executor在线程池中执行
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(self._get_plan_and_route_sync)
                        self._plan_and_route = future.result(timeout=30)
                except RuntimeError:
                    # 没有运行中的事件循环，可以安全使用asyncio.run
                    self._plan_and_route = asyncio.run(self._get_plan_and_route_async())

                self._plan_and_route_initialized = True
            except Exception as e:
                logger.error(f"初始化plan_and_route失败: {str(e)}")
                self._plan_and_route = ""
                self._plan_and_route_initialized = True

    def _get_plan_and_route_sync(self):
        """同步版本的获取规划方法"""
        try:
            # 创建新的事件循环来执行异步操作
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self._get_plan_and_route_async())
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"同步获取规划失败: {str(e)}")
            return ""

    async def _get_plan_and_route_async(self):
        """异步获取企业数字化发展规划和路线图"""
        try:
            # 创建模拟请求对象
            fake_headers = Headers(
                {
                    "Traceid": "internal_call_" + uuid.uuid4().hex,
                    "User-Agent": "ReportGenerator/1.0",
                }
            )
            fake_request = Request(scope={"type": "http", "headers": fake_headers.raw})

            response = await DocumentIndexService().query_document(
                QueryDocumentRequest(
                    query=f'"{self.company_info.type}"进行数字化转型的规划和路径是什么',
                    stream=False,
                    ids=[
                        str(file_id)
                        for file_id in get_file_ids_by_scene_id(refer_scene_id)
                    ],
                ),
                request=fake_request,  # 添加模拟的request参数
            )

            if "code" in response and response["code"] == 0:
                return response["data"]["content"]
            else:
                logger.error(f"获取规划失败: {response.msg}")
                return ""
        except Exception as e:
            logger.error(f"规划生成异常: {str(e)}")
            return ""

    def generate_full_report(self) -> str:
        """主报告生成入口"""
        # 报告构建
        report = []
        with timeit_context("parallel exec report"):
            tasks = [
                lambda: self._build_header(),
                lambda: self._build_chapter1(self.metric_tree),
                lambda: self._build_chapter2(self.metric_tree, self.freq_dict),
            ]
            results = run_concurrently(tasks, max_workers=4, timeout_seconds=300)
            for result in results:
                report.append(result)

        with timeit_context("build chapter3"):
            chapter3 = self._build_chapter3(self.metric_tree)
        report.append(chapter3)

        return "\n".join(report)

    def _build_chapter1(self, tree: TreeNode) -> str:
        """构建第一章节"""
        # 1. 章节标题
        content = ["# 1.数字化总体发展水平"]

        # 2. 整体水平分析
        total_score_input = self.template.get_total_score_prompt(
            total_score=tree.final_score, total_level=tree.get_metric_level()[0]
        )
        # print("total_score_input:", total_score_input)
        total_score_output = self.llm.complete(total_score_input)
        # print("total_score_output:", total_score_output)

        content.extend(["## 1.1 整体水平", total_score_output or ""])
        self.report_outline_desc.extend(content)

        # 3. 一级指标得分
        content.extend(["\n**一级指标得分**", self.total_table_content])

        # 4. 二级指标得分
        content.extend(
            ["\n**二级指标得分**", "![企业数字化水平二级指标得分雷达图](data/pic/mypic_level2.png)"]
        )

        # 5. 三级指标得分
        content.extend(
            ["\n**三级指标得分**", "![企业数字化水平三级指标得分雷达图](data/pic/mypic_level3.png)"]
        )

        return "\n".join(content)

    def _generate_charts(self, tree: TreeNode):
        """生成所需的图表"""
        # 生成二级指标雷达图
        level2_data = {
            node.name: node.final_score for node in tree.get_nodes_by_level(2)
        }
        prefix_path = "data/pic/"
        if self.task_id:
            prefix_path = f"data/pic/{self.task_id}/"
        if not os.path.exists(prefix_path):
            os.makedirs(prefix_path)
        self.chart_generator.create_radar_chart(
            level2_data, f"{prefix_path}mypic_level2.png"
        )

        # 生成三级指标雷达图
        level3_data = {
            node.name: node.final_score for node in tree.get_nodes_by_level(3)
        }
        self.chart_generator.create_radar_chart(
            level3_data, f"{prefix_path}mypic_level3.png"
        )

        for pri_node in tree.get_nodes_by_level(1):
            pri_ter_dict = dict()
            for sec_node in pri_node.children:
                for ter_node in sec_node.children:
                    pri_ter_dict[ter_node.name] = ter_node.final_score
            self.chart_generator.create_radar_chart(
                pri_ter_dict, f"{prefix_path}mypic_level_{pri_node.name}.png"
            )

    def _get_rader_png_path(self, level_id: int) -> str:
        prefix_path = "data/pic/"
        if self.task_id:
            prefix_path = f"data/pic/{self.task_id}/"
        if not os.path.exists(prefix_path):
            os.makedirs(prefix_path)
        return f"{prefix_path}mypic_level{level_id}.png"

    def _get_pri_node_rader_png_path(self, pri_node_name: str) -> str:
        prefix_path = "data/pic/"
        if self.task_id:
            prefix_path = f"data/pic/{self.task_id}/"
        if not os.path.exists(prefix_path):
            os.makedirs(prefix_path)
        return f"{prefix_path}mypic_level_{pri_node_name}.png"

    def _generate_total_table(self, tree: TreeNode) -> str:
        """生成总分表格"""
        table = ["\n表1：企业数字化水平一级指标得分等级分布\n"]
        table.append("| 评分模块 | 评估得分 | 对应等级 | 建议提升等级 |")
        table.append("|----------|----------|----------|--------------|")

        # 添加一级指标行
        for node in tree.get_nodes_by_level(1):
            level, desc = node.get_metric_level()
            next_level = self.template.get_next_level().get(level, level)
            table.append(
                f"| {node.name} | {node.final_score:.1f} | {level}({desc}) | {next_level} |"
            )

        # 添加总分行
        total_level, total_desc = tree.get_metric_level()
        next_total = self.template.get_next_level().get(total_level, total_level)
        table.append(
            f"| 总分 | {tree.final_score:.1f} | {total_level}({total_desc}) | {next_total} |"
        )

        return "\n".join(table)

    def _build_header(self) -> str:
        """构建报告头部"""
        return (
            self.template.get_company_content(company_info=self.company_info)
            + self.template.get_starting_content()
        )

    def _build_chapter2(self, tree: TreeNode, freq_dict: Dict[str, str]) -> str:
        """构建第二章节:短板及发展建议"""
        content = ["# 2.短板及发展建议"]
        self.report_outline_desc.extend(content)
        self.report_logs["sec_metric"] = {}
        # 遍历一级指标
        tasks = []
        for pri_index_temp, pri_node_temp in enumerate(tree.get_nodes_by_level(1), 1):
            if pri_node_temp.name == "数字化成效":
                continue
            # 使用默认参数立即绑定当前循环变量值，避免闭包问题
            tasks.append(
                lambda pri_node=pri_node_temp, pri_index=pri_index_temp: self._build_chapter2_first_level(
                    pri_node, pri_index, freq_dict
                )
            )
        results = run_concurrently(tasks, max_workers=4, timeout_seconds=300)
        valid_pri_index = 1
        for result in results:
            content.extend(result["content"])
            if len(result["final_suggests"]) > 1:
                for i in range(len(result["final_suggests"])):
                    if i == 0:
                        self.final_suggests += (
                            f"### 3.2.{valid_pri_index} {result['final_suggests'][i]}"
                        )
                    else:
                        if len(result["final_suggests"][i]) > 1:
                            self.final_suggests += f"#### 3.2.{valid_pri_index}.{i} {result['final_suggests'][i]}"
                valid_pri_index += 1
            self.report_outline_desc.extend(result["report_outline_desc"])

        return "\n".join(content)

    def _build_chapter2_first_level(
        self, pri_node: TreeNode, pri_index: int, freq_dict: Dict[str, str]
    ) -> Dict:
        results_dict = {"content": [], "final_suggests": [], "report_outline_desc": []}
        results_dict["final_suggests"].append(f"""{pri_node.name}\n""")
        results_dict["content"].append(f"## 2.{pri_index} {pri_node.name}")
        results_dict["report_outline_desc"].append(results_dict["content"][-1])
        pri_metric_output = self._pri_metric_report(pri_node)
        results_dict["content"].append(pri_metric_output)
        results_dict["report_outline_desc"].append(pri_metric_output)
        node_html = pri_node.generate_metric_html_table()
        results_dict["content"].append(node_html)
        results_dict["content"].append(
            f"\n![{pri_node.name}三级指标得分雷达图](data/pic/mypic_level_{pri_node.name}.png)\n"
        )

        # 遍历二级指标
        tasks = []
        for sec_index, sec_node in enumerate(pri_node.children, 1):
            tasks.append(
                lambda sec_node_temp=sec_node, pri_node_temp=pri_node, pri_index_temp=pri_index, sec_index_temp=sec_index: self._process_secondary_metric(
                    sec_node_temp,
                    pri_node_temp,
                    pri_index_temp,
                    sec_index_temp,
                    freq_dict,
                )
            )
        results = run_concurrently(tasks, max_workers=4, timeout_seconds=300)
        for result in results:
            results_dict["content"].extend(result["content"])
            if len(result["final_suggests"]) > 0:
                results_dict["final_suggests"].append(result["final_suggests"])
            results_dict["report_outline_desc"].extend(result["report_outline_desc"])
        return results_dict

    def _pri_metric_report(self, pri_node: TreeNode) -> str:
        """一级指标报告"""
        pri_metric_input = self.template.get_pri_metric_prompt(
            pri_metric_name=pri_node.name,
            pri_metric_score=pri_node.final_score,
            pri_metric_level=pri_node.get_metric_level()[0],
        )
        pri_metric_output = self.llm.complete(pri_metric_input)
        return pri_metric_output

    def _process_secondary_metric(
        self,
        sec_node: TreeNode,
        pri_node: TreeNode,
        pri_index: int,
        sec_index: int,
        freq_dict: Dict[str, str],
    ):
        """处理二级指标"""
        results_dict = {"content": [], "final_suggests": "", "report_outline_desc": []}
        # 生成二级指标标题
        results_dict["content"].append(
            f"### 2.{pri_index}.{sec_index} {sec_node.name.split('（')[0]}"
        )

        # 收集答案列表
        answer_list = []
        for qua_node in sec_node.get_nodes_by_level(4):
            if qua_node.parent.parent.name == sec_node.name:
                answer_list.append(qua_node.answer)

        # 生成二级指标分析
        sec_metric_input = self.template.get_secondary_metric_prompt(
            sec_node.get_sec_metric_desc_content(),
            sec_node.name,
            answer_list,
            freq_dict[pri_node.name],
        )

        return_txt = self.llm.complete(sec_metric_input) or ""

        # 记录日志
        self.report_logs["sec_metric"][sec_node.name] = {
            "sec_metric_input": sec_metric_input,
            "sec_metric_output": return_txt,
        }

        # 解析返回结果
        try:
            return_dict = json.loads(
                return_txt.strip().replace("```json", "").replace("```", "").strip()
            )
        except json.JSONDecodeError:
            # 如果JSON解析失败,尝试使用ast.literal_eval
            try:
                return_dict = ast.literal_eval(
                    return_txt.strip().replace("```json", "").replace("```", "").strip()
                )
            except Exception as e:
                logger.error(
                    f"Failed to parse return text: {return_txt}, error: {str(e)}"
                )
                return_dict = {"content": "", "is_hit": "否"}

        results_dict["content"].append(str(return_dict.get("content", "")))

        # 处理三级指标
        if return_dict.get("is_hit") == "是" and pri_node.name != "数字化成效":
            results_dict["final_suggests"] += f" {sec_node.name.split('（')[0]}\n"
            with timeit_context(
                f"process tertiary metrics {sec_node.name} {pri_node.name}"
            ):
                results_dict["final_suggests"] += self._process_tertiary_metrics(
                    sec_node, pri_node, freq_dict
                )

        return results_dict

    def _process_tertiary_metrics(
        self, sec_node: TreeNode, pri_node: TreeNode, freq_dict: Dict[str, str]
    ) -> str:
        """处理三级指标"""
        final_suggests = ""
        self.report_logs["ter_metric"] = {}

        # 创建并发任务列表
        tasks = []
        for ter_node in sec_node.children:
            tasks.append(
                lambda ter_node_temp=ter_node: self._process_single_tertiary_node(
                    ter_node_temp, pri_node, freq_dict
                )
            )

        # 并发执行三级指标处理
        results = run_concurrently(tasks, max_workers=4, timeout_seconds=300)

        # 按顺序汇总结果
        for result in results:
            final_suggests += result["suggestion"]
            self.report_logs["ter_metric"].update(result["log"])

        return final_suggests

    def _process_single_tertiary_node(self, ter_node, pri_node, freq_dict):
        """处理单个三级指标节点"""
        log_entry = {ter_node.name: {}}
        suggestion = f"##### {ter_node.name}\n"

        # 收集四级指标建议
        qua_dict = self._collect_quarterly_suggestions(ter_node, pri_node, freq_dict)

        # 生成建议
        if ter_node.name == "设备":
            suggestion += self._generate_equipment_suggestions(qua_dict)
        else:
            suggestion += self._generate_general_suggestions(qua_dict)

        return {"suggestion": suggestion, "log": log_entry}

    def _collect_quarterly_suggestions(
        self, ter_node: TreeNode, pri_node: TreeNode, freq_dict: Dict[str, str]
    ) -> Dict[str, List[str]]:
        """收集四级指标建议"""
        qua_dict = {}

        # 特殊处理人才指标
        if ter_node.name == "人才":
            return self._collect_talent_suggestions(ter_node, pri_node, freq_dict)

        # 一般情况处理
        for item in ter_node.children:
            if item.product_detail not in qua_dict:
                qua_dict[item.product_detail] = []

            suggestion = self._get_common_ter_metric_question_output(
                item, freq_dict[pri_node.name]
            )
            item.suggests = suggestion
            if suggestion:
                qua_dict[item.product_detail].append(suggestion)

        return qua_dict

    def _collect_talent_suggestions(
        self, ter_node: TreeNode, pri_node: TreeNode, freq_dict: Dict[str, str]
    ) -> Dict[str, List[str]]:
        """收集人才相关建议"""
        qua_dict = {}
        tmp_questions = []
        tmp_answers = []

        for item in ter_node.children:
            if item.product_detail not in qua_dict:
                qua_dict[item.product_detail] = []

            if item.name in ["6", "7"]:
                tmp_questions.append(item.question)
                tmp_answers.append(item.answer)

            if item.name == "8" or len(tmp_questions) == 2:
                ter_metric_question_output = self._get_talent_suggestion(
                    item, tmp_questions, tmp_answers, freq_dict[pri_node.name]
                )
                item.suggests = ter_metric_question_output
                if ter_metric_question_output:
                    qua_dict[item.product_detail].append(ter_metric_question_output)
                tmp_questions.clear()
                tmp_answers.clear()

        return qua_dict

    def _generate_equipment_suggestions(self, qua_dict: Dict[str, List[str]]) -> str:
        """生成设备相关建议"""
        # 设备汇总建议
        final_suggests = ""
        shebei_total = {k: v for k, v in qua_dict.items() if k in ["设备联网", "设备数控化"]}
        if shebei_total:
            total_suggestion = self._get_general_suggestion(
                product_detail=",".join(shebei_total.keys()),
                qua_suggests=str(list(shebei_total.values())),
            )
            final_suggests += f"{total_suggestion}\n"

        # 设备分项建议
        shebei_split = {k: v for k, v in qua_dict.items() if k not in ["设备联网", "设备数控化"]}
        if shebei_split:
            split_suggestion = self._get_general_suggestion(
                product_detail=",".join(shebei_split.keys()),
                qua_suggests=str(list(shebei_split.values())),
            )
            final_suggests += f"{split_suggestion}\n"

            # 单项建议
            for qua_k, qua_v in shebei_split.items():
                suggestion = self._get_general_suggestion(
                    product_detail=qua_k, qua_suggests=str(qua_v)
                )
                final_suggests += f"\n- 【{qua_k}】{suggestion}\n"
        return final_suggests

    def _get_general_suggestion(self, product_detail: str, qua_suggests: str):
        total_prompt = self.template.qua_metric_question_prompt(
            product_detail=product_detail, qua_suggests=qua_suggests
        )
        # print("total_prompt:", total_prompt)
        return_txt = self.llm.complete(total_prompt)
        # print("total_output:", return_txt)
        return return_txt

    def _generate_general_suggestions(self, qua_dict: Dict[str, List[str]]) -> str:
        """生成通用建议"""
        final_suggests = ""
        tasks = []
        qua_k_list = []
        for qua_k, qua_v in qua_dict.items():
            qua_k_list.append(qua_k)
            tasks.append(
                lambda qua_k_temp=qua_k, qua_v_temp=qua_v: self._get_general_suggestion(
                    product_detail=qua_k_temp, qua_suggests=str(qua_v_temp)
                )
            )
        results = run_concurrently(tasks, max_workers=4, timeout_seconds=300)
        for index, result in enumerate(results):
            final_suggests += f"- 【{qua_k_list[index]}】{result}\n"
        return final_suggests

    def _get_talent_suggestion(
        self,
        item: TreeNode,
        temp_question: List[str],
        temp_answers: List[str],
        freq_name: str,
    ):
        """生成人才相关建议"""
        ter_metric_question_prompt = self.template.get_ter_metric_question_prompt(
            item.question if item.name == "8" else "\n\n".join(temp_question),
            str(item.rules).replace("短板及发展建议【X】", ""),
            item.answer if item.name == "8" else str(temp_answers),
            freq_name,
        )
        return_txt = self.llm.complete(ter_metric_question_prompt)
        return return_txt

    def _get_common_ter_metric_question_output(self, item: TreeNode, freq_name: str):
        ter_metric_question_prompt = self.template.get_ter_metric_question_prompt(
            question=item.question,
            rules=str(item.rules).replace("短板及发展建议【X】", ""),
            answer=item.answer,
            freq_name=freq_name,
        )
        # print("ter_metric_question_prompt:", ter_metric_question_prompt)
        return_txt = self.llm.complete(ter_metric_question_prompt)
        # print("ter_metric_question_output:", return_txt)
        return return_txt

    def _build_chapter3(self, tree: TreeNode) -> str:
        """构建第三章节:数字化转型顶层设计"""
        content = []

        # 1. 章节标题
        content.append("# 3.数字化转型顶层设计")
        self.chapter3_dataconvert_content_split.append(content[-1])

        # 2. 生成总述
        with timeit_context("generate chapter3 intro"):
            intro = self._generate_chapter3_intro()
        content.append(intro)

        # 3. 顶层设计
        with timeit_context("generate top design"):
            top_design = self._generate_top_design()
        temp_list = ["## 3.1 顶层设计", top_design]
        content.extend(temp_list)
        self.chapter3_dataconvert_content_split.extend(temp_list)

        # 4. 建设方案
        with timeit_context("generate build plan"):
            self._generate_build_plan()
        dataconvert_detail_suggests_dict = ast.literal_eval(
            self.chapter3_dataconvert_detail_suggests_output.strip()
            .replace("```json", "")
            .replace("```", "")
            .strip()
        )
        dataconvert_detail_suggests_output_start = dataconvert_detail_suggests_dict[
            "开头"
        ]
        dataconvert_detail_suggests_output_end = dataconvert_detail_suggests_dict["结尾"]

        temp_list = [
            "## 3.2 建设方案",
            dataconvert_detail_suggests_output_start,
            self.final_suggests,
            dataconvert_detail_suggests_output_end,
        ]
        content.extend(temp_list)
        self.chapter3_dataconvert_content_split.extend(temp_list)

        # 5. 系统软件适配
        with timeit_context("generate system software adaptation"):
            system_software_adaptation = self._generate_system_software_adaptation(tree)
        temp_list = ["## 3.3 系统软件适配", system_software_adaptation]
        content.extend(temp_list)
        self.chapter3_dataconvert_content_split.extend(temp_list)

        # 6. 总结
        with timeit_context("generate summary"):
            summary = self._generate_summary()
        content.append(summary)
        self.chapter3_dataconvert_content_split.append(summary)

        return "\n".join(content)

    def _generate_chapter3_intro(self) -> str:
        """生成第三章节总述"""
        # 构建提示词
        prompt = self.template.get_chapter3_intro_prompt(
            company_info=self.company_info,
            plan_and_route=self.plan_and_route,
            report_outline_desc="\n".join(self.report_outline_desc),
        )
        self.chapter3_dataconvert_detail_intro_output = self.llm.complete(prompt) or ""
        self.chapter3_dataconvert_content_split.append(
            self.chapter3_dataconvert_detail_intro_output
        )
        return self.chapter3_dataconvert_detail_intro_output

    def _generate_top_design(self) -> str:
        """生成顶层设计部分"""
        prompt = self.template.get_chapter3_top_design_prompt(
            company_info=self.company_info,
            plan_and_route=self.plan_and_route,
            report_outline_desc="\n".join(self.report_outline_desc),
            dataconvert_detail_intro_output=self.chapter3_dataconvert_detail_intro_output,
        )
        self.chapter3_top_design_output = self.llm.complete(prompt) or ""
        self.chapter3_dataconvert_content_split.append(self.chapter3_top_design_output)
        return self.chapter3_top_design_output

    def _generate_build_plan(self) -> str:
        """生成建设方案部分"""
        prompt = self.template.get_chapter3_dataconvert_detail_suggests_input(
            dataconvert_content_split="\n".join(
                self.chapter3_dataconvert_content_split
            ),
            final_suggests=self.final_suggests,
        )
        logger.info(f"_generate_build_plan prompt: {prompt}")
        self.chapter3_dataconvert_detail_suggests_output = (
            self.llm.complete(prompt) or ""
        )
        self.chapter3_dataconvert_content_split.append(
            self.chapter3_dataconvert_detail_suggests_output
        )

        return self.chapter3_dataconvert_detail_suggests_output

    def _generate_system_software_adaptation(self, tree: TreeNode) -> str:
        type_nodes = tree.get_nodes_by_type()
        dataconvert_detail_products_dict = dict()

        # 创建并行任务列表
        tasks = []
        for ty, node_list in type_nodes.items():
            # 使用默认参数立即绑定当前循环变量值
            tasks.append(
                lambda ty_temp=ty, node_list_temp=node_list: self._process_single_type(
                    ty_temp, node_list_temp
                )
            )

        # 并行执行所有类型的处理
        results = run_concurrently(tasks, max_workers=4, timeout_seconds=300)

        # 收集处理结果
        for ty, temp_list in results:
            dataconvert_detail_products_dict[ty] = temp_list

        return generate_products_html_table(dataconvert_detail_products_dict)

    def _process_single_type(
        self, ty: str, node_list: List[TreeNode]
    ) -> tuple[str, list]:
        """处理单个类型的并行任务"""

        # 生成类型建议列表
        type_suggest_list = []
        for node in node_list:
            if node.parent.parent.parent.name not in self.suggests_dict:
                continue
            if (
                node.parent.parent.name
                not in self.suggests_dict[node.parent.parent.parent.name]
            ):
                continue
            if (
                node.parent.name
                not in self.suggests_dict[node.parent.parent.parent.name][
                    node.parent.parent.name
                ]
            ):
                continue
            node_suggests = self.suggests_dict[node.parent.parent.parent.name][
                node.parent.parent.name
            ][node.parent.name]
            if len(node_suggests) > 1:
                type_suggest_list.append(
                    f"ID:{node.name}, 所属指标：{node.parent.parent.parent.name}/{node.parent.parent.name}/{node.parent.name}, "
                    f"具体场景:{node.product_detail}, 短板陈述:{node_suggests}"
                )

        # 如果没有建议,返回空结果
        if not type_suggest_list:
            return (ty, [])

        # 分批处理建议
        all_results = []
        for i in range(0, len(type_suggest_list), 10):
            batch_suggests = type_suggest_list[i : i + 10]
            type_suggests = ";\n".join(batch_suggests)

            prompt = self.template.get_chapter3_system_software_adaptation_prompt(
                node_type=ty,
                type_suggests=type_suggests,  # 只传递当前类型
                type_result_manual="\n".join(
                    self.product_category.get_manual_category[ty]
                ),
            )

            try:
                return_txt = self.llm.complete(prompt) or ""

                # 解析返回结果
                try:
                    batch_results = json.loads(
                        return_txt.strip()
                        .replace("```json", "")
                        .replace("```", "")
                        .strip()
                    )
                except json.JSONDecodeError:
                    try:
                        batch_results = ast.literal_eval(
                            return_txt.strip()
                            .replace("```json", "")
                            .replace("```", "")
                            .strip()
                        )
                    except Exception as e:
                        logger.error(
                            f"Failed to parse return text: {return_txt}, error: {str(e)}"
                        )
                        batch_results = []

                # 过滤结果
                filtered_results = [
                    item
                    for item in batch_results
                    if item["产品服务名称"] in self.product_category.get_manual_category[ty]
                ]
                all_results.extend(filtered_results)

            except Exception as e:
                logger.error(f"Failed to process batch {i//10 + 1}: {str(e)}")
                continue

        return (ty, all_results)

    def _generate_summary(self) -> str:
        """生成总结部分"""
        prompt = self.template.get_chapter3_summary_prompt(
            dataconvert_content_split="\n".join(self.chapter3_dataconvert_content_split)
        )
        logger.info(f"summary prompt: {prompt}")
        return self.llm.complete(prompt) or ""

    # 获取整体水平
    def get_total_level_desc(self, section_config: ScienceCitySectionConfig) -> str:
        total_score_prompt = (
            section_config.prompt.replace(
                "{common.TOTAL_SCORE_LEVEL_DESC}",
                json.dumps(common.TOTAL_SCORE_LEVEL_DESC, ensure_ascii=False),
            )
            .replace("{total_score}", str(self.metric_tree.final_score))
            .replace("{total_level}", self.metric_tree.get_metric_level()[0])
        )
        total_score_prompt += (
            f"\n字数限制: {section_config.min_len} - {section_config.max_len}"
        )
        total_level_desc = self.llm.complete(total_score_prompt) or ""
        self.report_outline_desc_str = "\n".join(
            ["1. 数字化总体发展水平", "1.1 整体水平", total_level_desc]
        )

        return self.llm.complete(total_score_prompt) or ""

    # 一级指标的短板及发展建议
    def get_pri_metric_desc(self, section_config: ScienceCitySectionConfig):
        pri_metric_output_dict = {}
        for pri_index_temp, pri_node_temp in enumerate(
            self.metric_tree.get_nodes_by_level(1), 1
        ):
            if pri_node_temp.name == "数字化成效":
                continue
            pri_metric_input = (
                section_config.prompt.replace("{pri_metric_name}", pri_node_temp.name)
                .replace(
                    "{common.PRI_SCORE_LEVEL_DESC[pri_metric_name]}",
                    json.dumps(common.PRI_SCORE_LEVEL_DESC[pri_node_temp.name]),
                )
                .replace("{pri_metric_score}", str(pri_node_temp.final_score))
                .replace("{pri_metric_level}", pri_node_temp.get_metric_level()[0])
            )
            pri_metric_input += (
                f"\n字数限制: {section_config.min_len} - {section_config.max_len}"
            )
            pri_metric_output = self.llm.complete(pri_metric_input) or ""
            pri_metric_output_dict[pri_node_temp.name] = pri_metric_output

            self.report_outline_desc_str += "\n".join(
                [f"2.{pri_index_temp}. {pri_node_temp.name}", pri_metric_output]
            )
        return pri_metric_output_dict

    @property
    def get_answer_list_dict(self) -> dict:
        answer_list_dict = {}
        for pri_index_temp, pri_node_temp in enumerate(
            self.metric_tree.get_nodes_by_level(1), 1
        ):
            if pri_node_temp.name == "数字化成效":
                continue
            answer_list_dict[pri_node_temp.name] = {}
            for sec_index_temp, sec_node_temp in enumerate(pri_node_temp.children, 1):
                answer_list_dict[pri_node_temp.name][sec_node_temp.name] = []
                for qua_node in sec_node_temp.get_nodes_by_level(4):
                    if qua_node.parent.parent.name == sec_node_temp.name:
                        answer_list_dict[pri_node_temp.name][sec_node_temp.name].append(
                            qua_node.answer
                        )
        return answer_list_dict

    # 二级指标的短板及发展建议
    def get_sec_metric_desc_parallel(self, section_config: ScienceCitySectionConfig):
        """并行处理版本的二级指标描述获取方法"""
        sec_metric_output_dict = {}
        tasks = []

        # 收集所有需要处理的任务
        for pri_node_temp in self.metric_tree.get_nodes_by_level(1):
            if pri_node_temp.name == "数字化成效":
                continue
            for sec_node_temp in pri_node_temp.children:
                tasks.append((sec_node_temp, pri_node_temp))

        def process_secondary_metric(sec_node_temp, pri_node_temp):
            try:
                answer_list = self.get_answer_list_dict[pri_node_temp.name][
                    sec_node_temp.name
                ]
                sec_metric_input = (
                    section_config.prompt.replace(
                        "{sec_metric_desc}", sec_node_temp.get_sec_metric_desc_content()
                    )
                    .replace("{sec_metric_name}", sec_node_temp.name)
                    .replace("{answer_list}", str(answer_list))
                    .replace("{freq_name}", self.freq_dict[pri_node_temp.name])
                )

                return_txt = self.llm.complete(sec_metric_input) or ""

                try:
                    return_dict = extract_json_from_string(return_txt)
                except json.JSONDecodeError:
                    try:
                        return_dict = ast.literal_eval(
                            return_txt.strip()
                            .replace("```json", "")
                            .replace("```", "")
                            .strip()
                        )
                    except Exception as e:
                        logger.error(
                            f"Failed to parse return text: {return_txt}, error: {str(e)}"
                        )
                        return_dict = {"content": "", "is_hit": "否"}

                if return_dict.get("is_hit") == "是":
                    self.hit_sec_node_list.append(sec_node_temp.name)

                return (
                    pri_node_temp.name,
                    sec_node_temp.name,
                    str(return_dict.get("content", "")),
                )
            except Exception as e:
                logger.error(
                    f"Failed to process secondary metric {sec_node_temp.name}: {str(e)}"
                )
                return pri_node_temp.name, sec_node_temp.name, ""

        # 使用线程池并行处理
        with ThreadPoolExecutor(max_workers=4) as executor:
            # 提交所有任务
            future_to_sec = {
                executor.submit(process_secondary_metric, sec, pri): (
                    sec.name,
                    pri.name,
                )
                for sec, pri in tasks
            }

            # 收集结果
            for future in as_completed(future_to_sec):
                pri_name, sec_name, content = future.result()
                if pri_name not in sec_metric_output_dict:
                    sec_metric_output_dict[pri_name] = {}
                sec_metric_output_dict[pri_name][sec_name] = content

        self.ter_qua_dict = self._get_qua_dict()
        return sec_metric_output_dict

    def get_talent_suggestion_v2(
        self,
        section_config: ScienceCitySectionConfig,
        item: TreeNode,
        temp_question: List[str],
        temp_answers: List[str],
        freq_name: str,
    ):
        talent_prompt_input = (
            section_config.prompt.replace(
                "{question}",
                item.question if item.name == "8" else "\n\n".join(temp_question),
            )
            .replace("{rules}", str(item.rules).replace("短板及发展建议【X】", ""))
            .replace("{answer}", item.answer if item.name == "8" else str(temp_answers))
            .replace("{freq_name}", freq_name)
        )
        talent_prompt_input += (
            f"\n字数限制: {section_config.min_len} - {section_config.max_len}"
        )
        return self.llm.complete(talent_prompt_input) or ""

    def get_common_ter_metric_question_output_v2(
        self, section_config: ScienceCitySectionConfig, item: TreeNode, freq_name: str
    ):
        ter_metric_question_prompt = (
            section_config.prompt.replace("{question}", item.question)
            .replace("{rules}", str(item.rules).replace("短板及发展建议【X】", ""))
            .replace("{answer}", item.answer)
            .replace("{freq_name}", freq_name)
        )
        ter_metric_question_prompt += (
            f"\n字数限制: {section_config.min_len} - {section_config.max_len}"
        )
        return self.llm.complete(ter_metric_question_prompt) or ""

    def _get_qua_dict(self) -> dict:
        ter_qua_dict = {}
        # 创建并行任务列表
        tasks = []

        for pri_node_temp in self.metric_tree.get_nodes_by_level(1):
            if pri_node_temp.name == "数字化成效":
                continue
            for sec_node_temp in pri_node_temp.children:
                if sec_node_temp.name not in self.hit_sec_node_list:
                    continue

                # 为每个三级节点创建处理任务
                tasks.extend(
                    [
                        lambda t=ter: self._process_tertiary_node(t, pri_node_temp)
                        for ter in sec_node_temp.children
                    ]
                )

        # 并行执行所有三级节点处理
        results = run_concurrently(tasks, max_workers=8, timeout_seconds=300)

        # 合并处理结果
        for ter_node_name, qua_dict in results:
            ter_qua_dict[ter_node_name] = qua_dict

        return ter_qua_dict

    def _process_tertiary_node(self, ter_node, pri_node) -> tuple[str, dict]:
        """并行处理单个三级节点"""
        qua_dict = {}

        if ter_node.name == "人才":
            # 处理人才指标（保持原有逻辑）
            tmp_questions, tmp_answers = [], []
            talent_tasks = []

            for item in ter_node.children:
                if item.name in ["6", "7"]:
                    tmp_questions.append(item.question)
                    tmp_answers.append(item.answer)

                if item.name == "8" or len(tmp_questions) == 2:
                    # 创建人才建议的并行任务
                    talent_tasks.append(
                        lambda i=item, q=tmp_questions.copy(), a=tmp_answers.copy(): self._process_talent_suggestion(
                            i, q, a, pri_node
                        )
                    )
                    tmp_questions.clear()
                    tmp_answers.clear()

            # 并行执行人才建议生成
            talent_results = run_concurrently(talent_tasks, max_workers=4)
            for product_detail, suggestion in talent_results:
                qua_dict.setdefault(product_detail, []).append(suggestion)

        else:
            # 创建普通指标处理任务
            item_tasks = [
                lambda i=item: self._process_common_item(i, pri_node)
                for item in ter_node.children
            ]

            # 并行处理所有子项
            item_results = run_concurrently(item_tasks, max_workers=4)
            for product_detail, suggestion in item_results:
                qua_dict.setdefault(product_detail, []).append(suggestion)

        return ter_node.name, qua_dict

    def _process_talent_suggestion(self, item, questions, answers, pri_node):
        """处理人才建议（独立可并行单元）"""
        suggestion = self._get_talent_suggestion(
            item, questions, answers, self.freq_dict[pri_node.name]
        )
        item.suggests = suggestion
        return (item.product_detail, suggestion) if suggestion else (None, None)

    def _process_common_item(self, item, pri_node):
        """处理普通子项（独立可并行单元）"""
        suggestion = self._get_common_ter_metric_question_output(
            item, self.freq_dict[pri_node.name]
        )
        item.suggests = suggestion
        return (item.product_detail, suggestion) if suggestion else (None, None)

    # 四级指标建议格式化
    def _format_suggestions_dict(self, suggestions_dict: dict) -> str:
        """将建议字典格式化为特定格式的字符串"""
        formatted_suggestions = ""
        valid_pri_index = 1

        for pri_name, sec_dict in suggestions_dict.items():
            # 添加一级指标标题
            formatted_suggestions += f"### 3.2.{valid_pri_index} {pri_name}\n"

            # 遍历二级指标
            for i, (sec_name, ter_dict) in enumerate(sec_dict.items(), 1):
                formatted_suggestions += f"#### 3.2.{valid_pri_index}.{i} {sec_name}\n"

                # 遍历三级指标内容
                for ter_name, suggestion in ter_dict.items():
                    formatted_suggestions += f"##### {ter_name}\n"
                    formatted_suggestions += f"{suggestion}\n"

            valid_pri_index += 1

        return formatted_suggestions

    def generate_qua_metric_suggestion(
        self, section_config: ScienceCitySectionConfig
    ) -> dict:
        suggestion_dict = {}
        # 提前过滤一级指标
        pri_nodes = [
            n for n in self.metric_tree.get_nodes_by_level(1) if n.name != "数字化成效"
        ]

        # 并行处理一级指标
        tasks = []
        for pri_node in pri_nodes:
            tasks.append(
                (
                    pri_node,
                    [
                        sec_node
                        for sec_node in pri_node.children
                        if sec_node.name in self.hit_sec_node_list
                    ],
                )
            )

        # 使用线程池并行处理
        results = run_concurrently(
            [
                lambda p=pri, s=sec_nodes: self._process_pri_node(p, s, section_config)
                for pri, sec_nodes in tasks
            ],
            max_workers=4,
        )

        # 合并结果
        for pri_name, sec_dict in results:
            suggestion_dict[pri_name] = sec_dict

        # 格式化建议
        self.suggests_dict = suggestion_dict
        self.final_suggests = self._format_suggestions_dict(suggestion_dict)
        return suggestion_dict

    def _process_pri_node(self, pri_node, sec_nodes, section_config):
        """并行处理单个一级指标"""
        sec_dict = {}
        for sec_node in sec_nodes:
            ter_dict = {}
            # 批量获取三级节点
            ter_nodes = list(sec_node.children)

            # 并行处理三级节点
            ter_tasks = [
                lambda t=ter: (t.name, self._process_ter_node(t, section_config))
                for ter in ter_nodes
            ]
            ter_results = run_concurrently(ter_tasks, max_workers=4)

            for ter_name, suggestion in ter_results:
                ter_dict[ter_name] = suggestion

            sec_dict[sec_node.name] = ter_dict
        return pri_node.name, sec_dict

    def _process_ter_node(self, ter_node, section_config):
        """处理单个三级节点"""
        qua_dict = self.ter_qua_dict.get(ter_node.name, {})
        if ter_node.name == "设备":
            return self._generate_equipment_suggestions_v2(section_config, qua_dict)
        return self._generate_general_suggestions_v2(section_config, qua_dict)

    def _generate_equipment_suggestions_v2(
        self, section_config: ScienceCitySectionConfig, qua_dict: dict
    ) -> str:
        """生成设备相关建议"""
        # 设备汇总建议
        final_suggests = ""
        shebei_total = {k: v for k, v in qua_dict.items() if k in ["设备联网", "设备数控化"]}
        if shebei_total:
            total_suggestion = self._get_general_suggestion_v2(
                section_config=section_config,
                product_detail=",".join(shebei_total.keys()),
                qua_suggests=str(list(shebei_total.values())),
            )
            final_suggests += f"{total_suggestion}\n"

        # 设备分项建议
        shebei_split = {k: v for k, v in qua_dict.items() if k not in ["设备联网", "设备数控化"]}
        if shebei_split:
            split_suggestion = self._get_general_suggestion_v2(
                section_config=section_config,
                product_detail=",".join(shebei_split.keys()),
                qua_suggests=str(list(shebei_split.values())),
            )
            final_suggests += f"{split_suggestion}\n"

            # 单项建议
            for qua_k, qua_v in shebei_split.items():
                suggestion = self._get_general_suggestion_v2(
                    section_config=section_config,
                    product_detail=qua_k,
                    qua_suggests=str(qua_v),
                )
                final_suggests += f"\n- 【{qua_k}】{suggestion}\n"
        return final_suggests

    def _get_general_suggestion_v2(
        self,
        section_config: ScienceCitySectionConfig,
        product_detail: str,
        qua_suggests: str,
    ) -> str:
        general_suggestion_prompt = section_config.prompt.replace(
            "{product_detail}", product_detail
        ).replace("{qua_suggests}", qua_suggests)
        # general_suggestion_prompt += f"\n字数限制: {section_config.min_len} - {section_config.max_len}"
        return self.llm.complete(general_suggestion_prompt) or ""

    def _generate_general_suggestions_v2(
        self, section_config: ScienceCitySectionConfig, qua_dict: dict
    ):
        final_suggests = ""
        tasks = []
        qua_k_list = []
        for qua_k, qua_v in qua_dict.items():
            qua_k_list.append(qua_k)
            tasks.append(
                lambda qua_k_temp=qua_k, qua_v_temp=qua_v: self._get_general_suggestion_v2(
                    section_config=section_config,
                    product_detail=qua_k_temp,
                    qua_suggests=str(qua_v_temp),
                )
            )
        results = run_concurrently(tasks, max_workers=4, timeout_seconds=300)
        for index, result in enumerate(results):
            final_suggests += f"- 【{qua_k_list[index]}】{result}\n"
        return final_suggests

    # 顶层设计 依赖顶层设计总述
    def generate_top_design_v2(self, section_config: ScienceCitySectionConfig) -> str:
        prompt = (
            self.template.get_chapter3_common_content(
                self.company_info, self.plan_and_route, self.report_outline_desc_str
            )
            + section_config.prompt.replace(
                "{dataconvert_detail_intro_output}",
                self.chapter3_dataconvert_detail_intro_output,
            )
            + f"\n字数限制: {section_config.min_len} - {section_config.max_len}"
        )
        self.chapter3_top_design_output = self.llm.complete(prompt) or ""
        self.chapter3_dataconvert_content_split.append(self.chapter3_top_design_output)
        self.chapter3_content_dict[2] = ["## 3.1 顶层设计", self.chapter3_top_design_output]
        return self.chapter3_top_design_output

    """
    1. 数字化总体发展水平
    1.1 整体水平
    企业在数字化水平评估中获得了44.45分，达到了L3发展级。整体来看，企业已经形成了清晰的数字化转型战略，其核心业务具备了一定的转型能力，并开始着重于数据集成和跨领域的信息共享。这种进步表明，企业正在积极推进协同创新，为未来的数字化发展打下坚实基础。企业在数字化转型过程中已经取得了一定的成效，并展现了较高的成熟度，具备继续拓展和深化数字化应用的潜力。
    2. 短板及发展建议
    2.1 要素就绪度（一级指标）
    在"要素就绪度"指标的评估中，该企业获得了17.82分，处于L3发展级。这显示该企业已经明确制定了数字化转型的战略，并且在设备、网络、资金等关键要素的建设上取得了稳步进展。虽然还有提升空间，但其数字化转型基础较为扎实，表明企业在推进数字化转型的道路上已经迈出坚实的一步。同时，该企业在此阶段的努力为其未来在更广泛和深入的范围内利用数字化技术奠定了良好的基础。
    2.2 数字化经营（一级指标）
    企业在"数字化经营"方面的得分是22.63，达到了L2成长级。这意味着企业已经开始规划数字化转型项目，并且在某些具体环节积极运用数字化技术或工具。他们正在特定场景中进行数字化转型实践的探索，致力于推动业务场景的改造和升级。这表明企业在数字化转型的旅程中已迈出了重要的一步，并且在为进一步发展打下坚实基础。不过，要想在未来实现更高层级的数字化成熟度，他们还需在更广泛的领域系统地深化数字化应用。
    2.3 数字化成效（一级指标）
    在"数字化成效"指标上获得了4.0分，属于L2成长级。这表明企业在数字化转型过程中取得了一定的成效，尤其在提升质量、提高效率、降低成本和增加收入等方面有所突破。尽管距离最高级别还有空间提升，但企业已开始通过数字化手段实现部分业务优化，为未来的发展奠定了良好的基础。继续深化数字化措施有望带来更显著的成效。
    """

    # 数字化转型顶层设计, 依赖前两章的输出 report_outline_desc
    # 需先执行 self.get_total_level_desc, self.get_pri_metric_desc
    def generate_digital_transform_v2(
        self, section_config: ScienceCitySectionConfig
    ) -> str:
        prompt = (
            self.template.get_chapter3_common_content(
                self.company_info, self.plan_and_route, self.report_outline_desc_str
            )
            + section_config.prompt
            + f"\n字数限制: {section_config.min_len} - {section_config.max_len}"
        )
        self.chapter3_dataconvert_detail_intro_output = self.llm.complete(prompt) or ""
        self.chapter3_dataconvert_content_split.append(
            self.chapter3_dataconvert_detail_intro_output
        )
        self.chapter3_content_dict[1] = [
            "# 3.数字化转型顶层设计",
            self.chapter3_dataconvert_detail_intro_output,
        ]
        return self.chapter3_dataconvert_detail_intro_output

    # 建设方案开头与结尾, 依赖第三章的总述与顶层设计,建议
    def generate_build_plan(self, section_config: ScienceCitySectionConfig) -> dict:
        prompt = section_config.prompt.replace(
            "{dataconvert_detail_intro_output}",
            self.chapter3_dataconvert_detail_intro_output,
        ).replace("{final_suggests}", self.final_suggests)
        self.chapter3_dataconvert_detail_suggests_output = (
            self.llm.complete(prompt) or ""
        )
        dataconvert_detail_suggests_output_start = ""
        dataconvert_detail_suggests_output_end = ""
        if self.chapter3_dataconvert_detail_suggests_output:
            dataconvert_detail_suggests_dict = extract_json_from_string(
                self.chapter3_dataconvert_detail_suggests_output
            )
            dataconvert_detail_suggests_output_start = dataconvert_detail_suggests_dict[
                "开头"
            ]
            dataconvert_detail_suggests_output_end = dataconvert_detail_suggests_dict[
                "结尾"
            ]

        self.chapter3_content_dict[3] = [
            "## 3.2 建设方案",
            dataconvert_detail_suggests_output_start,
            dataconvert_detail_suggests_output_end,
        ]
        return {
            "dataconvert_detail_suggests_output_start": dataconvert_detail_suggests_output_start,
            "dataconvert_detail_suggests_output_end": dataconvert_detail_suggests_output_end,
        }

    def _process_single_type_v2(
        self,
        ty: str,
        node_list: List[TreeNode],
        section_config: ScienceCitySectionConfig,
    ) -> tuple[str, list]:
        """处理单个类型的并行任务"""
        BATCH_SIZE = 10  # 每批处理的节点数量

        # 生成类型建议列表
        type_suggest_list = []
        for node in node_list:
            if node.parent.parent.parent.name not in self.suggests_dict:
                continue
            if (
                node.parent.parent.name
                not in self.suggests_dict[node.parent.parent.parent.name]
            ):
                continue
            if (
                node.parent.name
                not in self.suggests_dict[node.parent.parent.parent.name][
                    node.parent.parent.name
                ]
            ):
                continue
            node_suggests = self.suggests_dict[node.parent.parent.parent.name][
                node.parent.parent.name
            ][node.parent.name]
            if len(node_suggests) > 1:
                type_suggest_list.append(
                    f"ID:{node.name}, 所属指标：{node.parent.parent.parent.name}/{node.parent.parent.name}/{node.parent.name}, "
                    f"具体场景:{node.product_detail}, 短板陈述:{node_suggests}"
                )

        # 如果没有建议,返回空结果
        if not type_suggest_list:
            return (ty, [])

        # 分批处理建议
        all_results = []
        for i in range(0, len(type_suggest_list), BATCH_SIZE):
            batch_suggests = type_suggest_list[i : i + BATCH_SIZE]
            type_suggests = ";\n".join(batch_suggests)

            prompt = (
                section_config.prompt.replace("{type_suggests}", type_suggests)
                .replace(
                    "{type_result_manual}",
                    "\n".join(self.product_category.get_manual_category[ty]),
                )
                .replace("{node_type}", ty)
            )

            try:
                return_txt = self.llm.complete(prompt) or ""

                # 解析返回结果
                try:
                    batch_results = json.loads(
                        return_txt.strip()
                        .replace("```json", "")
                        .replace("```", "")
                        .strip()
                    )
                except json.JSONDecodeError:
                    try:
                        batch_results = ast.literal_eval(
                            return_txt.strip()
                            .replace("```json", "")
                            .replace("```", "")
                            .strip()
                        )
                    except Exception as e:
                        logger.error(
                            f"Failed to parse return text: {return_txt}, error: {str(e)}"
                        )
                        batch_results = []

                # 过滤结果
                filtered_results = [
                    item
                    for item in batch_results
                    if item["产品服务名称"] in self.product_category.get_manual_category[ty]
                ]
                all_results.extend(filtered_results)

            except Exception as e:
                logger.error(f"Failed to process batch {i//BATCH_SIZE + 1}: {str(e)}")
                continue

        return (ty, all_results)

    def generate_system_software_adaptation_v2(
        self, section_config: ScienceCitySectionConfig
    ) -> str:
        type_nodes = self.metric_tree.get_nodes_by_type()
        dataconvert_detail_products_dict = dict()

        # 创建并行任务列表
        tasks = []
        for ty, node_list in type_nodes.items():
            # 使用默认参数立即绑定当前循环变量值
            tasks.append(
                lambda ty_temp=ty, node_list_temp=node_list: self._process_single_type_v2(
                    ty_temp, node_list_temp, section_config
                )
            )

        # 并行执行所有类型的处理
        results = run_concurrently(tasks, max_workers=4, timeout_seconds=300)

        # 收集处理结果
        for ty, temp_list in results:
            dataconvert_detail_products_dict[ty] = temp_list

        table_content = generate_products_html_table(dataconvert_detail_products_dict)
        self.chapter3_content_dict[4] = [
            "## 3.3 系统软件适配",
            table_content,
        ]
        return table_content

    # 顶层设计 依赖前序章节
    def generate_summary_v2(self, section_config: ScienceCitySectionConfig):
        dataconvert_content_split = ""
        for i in range(1, 3):
            dataconvert_content_split += "\n".join(self.chapter3_content_dict[i])
        prompt = section_config.prompt.replace(
            "{dataconvert_content_split}", dataconvert_content_split
        )
        prompt += f"\n字数限制: {section_config.min_len} - {section_config.max_len}"
        return self.llm.complete(prompt) or ""

    def _get_parallel_groups(self, dependencies: set) -> List[List[int]]:
        """
        获取可以并行执行的操作组，确保正确处理依赖关系

        Args:
            dependencies: 所有依赖的操作ID集合

        Returns:
            按层级分组的操作ID列表
        """
        # 获取所有依赖的操作
        all_ops = list(dependencies)

        # 构建完整的依赖图(包括间接依赖)
        graph = {op_id: set() for op_id in all_ops}
        in_degree = {op_id: 0 for op_id in all_ops}

        # 构建依赖图，确保边的方向正确（从被依赖者指向依赖者）
        for op_id in all_ops:
            # 获取所有依赖(包括间接依赖)
            all_deps = self.get_all_dependencies(op_id)
            # 只保留在all_ops中的依赖
            valid_deps = all_deps.intersection(all_ops)

            # 更新依赖关系
            for dep in valid_deps:
                # 添加边：从被依赖者(dep)指向依赖者(op_id)
                graph[dep].add(op_id)
                # 增加依赖者的入度
                in_degree[op_id] += 1

        # 按层级分组
        groups = []
        remaining_ops = set(all_ops)

        while remaining_ops:
            # 获取当前可以执行的操作(入度为0)
            current_level = [op_id for op_id in remaining_ops if in_degree[op_id] == 0]
            if not current_level:
                # 如果找不到入度为0的操作，说明存在循环依赖
                raise ValueError("检测到循环依赖")

            groups.append(current_level)

            # 更新入度
            for op_id in current_level:
                remaining_ops.remove(op_id)
                # 减少所有依赖者的入度
                for dependent in graph[op_id]:
                    in_degree[dependent] -= 1

        return groups

    def _execute_with_dependencies(
        self,
        data_op_id: int,
        section_config: ScienceCitySectionConfig,
        visited: set = None,
    ) -> None:
        """
        递归执行操作及其所有依赖，支持并行执行

        Args:
            data_op_id: 操作ID
            section_config: 配置参数
            visited: 已访问的操作集合,用于检测循环依赖

        Raises:
            ValueError: 当检测到循环依赖时
        """
        if visited is None:
            visited = set()

        if data_op_id in visited:
            raise ValueError(f"检测到循环依赖: {data_op_id}")

        visited.add(data_op_id)

        # 获取所有依赖
        dependencies = self.get_all_dependencies(data_op_id)

        # 获取可以并行执行的操作组
        parallel_groups = self._get_parallel_groups(dependencies)

        # 使用线程池执行每组操作
        with ThreadPoolExecutor(max_workers=4) as executor:
            for group in parallel_groups:
                # 提交当前组的所有任务
                futures = {
                    executor.submit(
                        self._execute_single_operation,
                        op_id,
                        self.section_config_dict[op_id],
                    ): op_id
                    for op_id in group
                }

                # 等待当前组的所有任务完成
                for future in as_completed(futures):
                    try:
                        future.result()
                    except Exception as e:
                        logger.error(
                            f"Failed to execute operation {futures[future]}: {str(e)}"
                        )
                        raise

        # 执行当前操作
        return self._execute_single_operation(data_op_id, section_config)

    def _execute_single_operation(
        self, op_id: int, section_config: ScienceCitySectionConfig
    ):
        """执行单个操作"""
        method = self.method_mapping[op_id]
        try:
            result = method(section_config)
            return result
        except Exception as e:
            logger.error(f"Failed to execute method for data_op_id {op_id}: {str(e)}")
            raise

    def execute_by_id(self, data_op_id: int, section_config: ScienceCitySectionConfig):
        """
        根据data_op_id执行对应的方法及其所有依赖

        Args:
            data_op_id: 操作ID
            section_config: 配置参数

        Returns:
            方法执行结果

        Raises:
            ValueError: 当data_op_id无效或检测到循环依赖时
        """
        if data_op_id not in self.method_mapping:
            raise ValueError(f"Invalid data_op_id: {data_op_id}")

        # 递归执行操作及其依赖
        return self._execute_with_dependencies(data_op_id, section_config)

    def get_level2_rader_png_url_path(self) -> str:
        """获取二级指标雷达图的url地址和path，如果task id不为空，使用本地path，如果为空，上传到s3后使用s3签名后地址"""
        png_local_path = self._get_rader_png_path(2)
        if self.task_id:
            return png_local_path
        else:
            s3_path = f"science_city_biz/{png_local_path}"
            create_s3_client().upload_file(
                png_local_path, science_city_report_s3_bucket_name, s3_path
            )
            return (
                get_s3_file_system()
                .url(
                    f"{science_city_report_s3_bucket_name}/{s3_path}",
                    expires=ONE_DAY_SECONDS * 2,
                )
                .replace(f"{default_endpoint_url}", PUBLIC_REPORT_URL)
            )

    def get_level3_rader_png_url_path(self) -> str:
        """获取三级指标雷达图的url地址和path，如果task id不为空，使用本地path，如果为空，上传到s3后使用s3签名后地址"""
        png_local_path = self._get_rader_png_path(3)
        if self.task_id:
            return png_local_path
        else:
            s3_path = f"science_city_biz/{png_local_path}"
            create_s3_client().upload_file(
                png_local_path, science_city_report_s3_bucket_name, s3_path
            )
            return (
                get_s3_file_system()
                .url(
                    f"{science_city_report_s3_bucket_name}/{s3_path}",
                    expires=ONE_DAY_SECONDS * 2,
                )
                .replace(f"{default_endpoint_url}", PUBLIC_REPORT_URL)
            )

    def get_pri_node_rader_png_url_path(self, pri_name: str):
        png_local_path = self._get_pri_node_rader_png_path(pri_name)
        if self.task_id:
            return png_local_path
        else:
            s3_path = f"science_city_biz/{png_local_path}"
            create_s3_client().upload_file(
                png_local_path, science_city_report_s3_bucket_name, s3_path
            )
            return (
                get_s3_file_system()
                .url(
                    f"{science_city_report_s3_bucket_name}/{s3_path}",
                    expires=ONE_DAY_SECONDS * 2,
                )
                .replace(f"{default_endpoint_url}", PUBLIC_REPORT_URL)
            )

    def execute_operations_by_ids(self, op_ids: List[int]) -> Dict[int, Any]:
        """
        批量执行多个操作ID，支持并行执行和依赖关系处理

        Args:
            op_ids: 要执行的操作ID列表
            section_config: 配置参数

        Returns:
            Dict[int, Any]: 操作ID到执行结果的映射

        Raises:
            ValueError: 当检测到循环依赖或无效的操作ID时
        """
        # 验证所有操作ID是否有效
        invalid_ops = [op_id for op_id in op_ids if op_id not in self.method_mapping]
        if invalid_ops:
            raise ValueError(f"Invalid data_op_ids: {invalid_ops}")

        # 获取所有依赖的操作
        all_dependencies = set()
        for op_id in op_ids:
            all_dependencies.update(self.get_all_dependencies(op_id))
        all_dependencies.update(op_ids)  # 包含原始操作ID

        # 获取可以并行执行的操作组
        parallel_groups = self._get_parallel_groups(all_dependencies)

        # 存储执行结果
        results = {}

        # 使用线程池执行每组操作
        with ThreadPoolExecutor(max_workers=4) as executor:
            for group in parallel_groups:
                # 提交当前组的所有任务
                futures = {
                    executor.submit(
                        self._execute_single_operation,
                        op_id,
                        self.section_config_dict[op_id],
                    ): op_id
                    for op_id in group
                }

                # 等待当前组的所有任务完成
                for future in as_completed(futures):
                    op_id = futures[future]
                    try:
                        result = future.result()
                        # 只保存原始操作ID的结果
                        if op_id in op_ids:
                            results[op_id] = result
                    except Exception as e:
                        logger.error(f"Failed to execute operation {op_id}: {str(e)}")
                        raise

        return results

    def print_execution_plan(self, op_ids: List[int]) -> None:
        """
        打印执行计划，包括依赖关系和并行执行信息

        Args:
            op_ids: 要执行的操作ID列表
        """
        # 验证所有操作ID是否有效
        invalid_ops = [op_id for op_id in op_ids if op_id not in self.method_mapping]
        if invalid_ops:
            raise ValueError(f"Invalid data_op_ids: {invalid_ops}")

        # 获取所有依赖的操作
        all_dependencies = set()
        for op_id in op_ids:
            all_dependencies.update(self.get_all_dependencies(op_id))
        all_dependencies.update(op_ids)  # 包含原始操作ID

        # 获取可以并行执行的操作组
        parallel_groups = self._get_parallel_groups(all_dependencies)

        # 打印执行计划
        print("\n=== 执行计划 ===")
        print(f"总操作数: {len(all_dependencies)}")
        print(f"原始操作数: {len(op_ids)}")
        print(f"依赖操作数: {len(all_dependencies) - len(op_ids)}")
        print("\n=== 依赖关系 ===")

        # 打印每个操作的依赖关系
        for op_id in sorted(all_dependencies):
            deps = self.get_dependencies(op_id)
            if deps:
                print(f"操作 {op_id} 依赖于: {sorted(deps)}")
            else:
                print(f"操作 {op_id} 无依赖")

        print("\n=== 并行执行计划 ===")
        for i, group in enumerate(parallel_groups, 1):
            print(f"\n第 {i} 轮并行执行:")
            print(f"可并行操作: {sorted(group)}")
            if i < len(parallel_groups):
                next_deps = set()
                for op_id in group:
                    next_deps.update(
                        self.get_dependents(op_id).intersection(all_dependencies)
                    )
                if next_deps:
                    print(f"下一轮依赖: {sorted(next_deps)}")

        print("\n=== 执行顺序 ===")
        for i, group in enumerate(parallel_groups, 1):
            print(f"第 {i} 轮: {sorted(group)}")

        print("\n=== 原始操作执行顺序 ===")
        original_order = []
        for group in parallel_groups:
            original_order.extend(sorted(op_id for op_id in group if op_id in op_ids))
        print(f"执行顺序: {original_order}")
