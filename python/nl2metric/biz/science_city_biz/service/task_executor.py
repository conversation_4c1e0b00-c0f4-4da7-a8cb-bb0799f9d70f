import os
import random
import asyncio
import httpx
from datetime import datetime
from tempfile import TemporaryDirectory
from typing import Optional

from biz.science_city_biz.data.models.report_task import (
    TaskStatus,
    TaskType,
)
from biz.science_city_biz.data.models.async_report_task import (
    get_report_task_by_status_async,
    recover_exception_task_async,
    update_report_task_status_async,
)
# ReportGenerator import removed as it's not directly used in this async version
from biz.science_city_biz.utils.utils import timeit_context
from common.fs.storage import create_s3_client
from common.logging.logger import get_logger
from nl2document.common.utils import save_to_pdf_v2
from biz.science_city_biz.config.config import (
    CALLBACK_URL_AUTH,
    DEFAULT_REPORT_TEMPLATE_ID,
    SELF_HOST_URL,
    TASK_CALLBACK_URL,
    science_city_report_s3_bucket_name,
    executor_worker_num,
)
from nl2document.common.models.report_generate_model import get_template_by_id
from biz.science_city_biz.service.adapter.report_adapter import (
    get_full_report_combine_with_template,
)

logger = get_logger(__name__)


class AsyncTaskExecutor:
    """异步任务执行器,定时扫描并执行待处理的报告生成任务"""

    def __init__(self, scan_interval: int = 10, max_workers: int = executor_worker_num):
        """
        初始化任务执行器
        Args:
            scan_interval: 扫描间隔时间(秒),默认10秒
            max_workers: 最大并发任务数,默认5
        """
        self.scan_interval = scan_interval
        self.is_running = False
        self._task: Optional[asyncio.Task] = None
        self.max_workers = max_workers
        self._semaphore = asyncio.Semaphore(max_workers)  # 控制并发数
        self._running_tasks = set()  # 跟踪正在运行的任务

    async def start(self):
        """启动任务执行器"""
        if self.is_running:
            logger.warning("任务执行器已在运行中")
            return

        self.is_running = True
        self._task = asyncio.create_task(self._run())
        logger.info("异步任务执行器已启动")

    async def stop(self):
        """停止任务执行器"""
        logger.info("开始停止异步任务执行器...")
        self.is_running = False

        # 取消主任务
        if self._task and not self._task.done():
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                logger.info("主任务已取消")

        # 等待所有正在运行的任务完成
        if self._running_tasks:
            logger.info(f"等待 {len(self._running_tasks)} 个任务完成...")
            await asyncio.gather(*self._running_tasks, return_exceptions=True)

        logger.info("异步任务执行器已停止")

    async def _process_single_task(self, task):
        """异步处理单个任务的逻辑"""
        async with self._semaphore:  # 控制并发数
            try:
                logger.info(f"开始处理任务: {task.id}")

                # 执行报告生成
                # 获取模板，确保每个任务处理时都能获取到最新的模板
                loop = asyncio.get_running_loop()
                template = await loop.run_in_executor(
                    None, get_template_by_id, DEFAULT_REPORT_TEMPLATE_ID
                )

                if (
                    task.task_type == TaskType.DISCRETE
                    or task.task_type == TaskType.PROCESS
                ):
                    with timeit_context("generate_full_report"):
                        # 在线程池中执行CPU密集型任务
                        report_content = await loop.run_in_executor(
                            None,
                            get_full_report_combine_with_template,
                            template,
                            task.id,
                            task.task_type,
                        )
                else:
                    raise ValueError(f"不支持的任务类型: {task.task_type}")

                # 保存报告内容到pdf
                report_path = f"science_city_biz/doc_{task.id}/report.pdf"
                with TemporaryDirectory() as temp_dir:
                    pdf_path = os.path.join(temp_dir, f"{task.id}.pdf")
                    # 在线程池中执行文件操作
                    await loop.run_in_executor(None, save_to_pdf_v2, pdf_path, report_content)

                    # 在线程池中执行S3上传
                    s3_client = create_s3_client()
                    await loop.run_in_executor(
                        None,
                        s3_client.upload_file,
                        pdf_path,
                        science_city_report_s3_bucket_name,
                        report_path,
                    )

                # 更新任务状态为已完成
                task.status = TaskStatus.COMPLETED
                task.update_time = datetime.now()

                logger.info(f"任务处理完成: {task.id}")
                await update_report_task_status_async(task.id, task.status)

                # 发送报告URL
                report_url = f"{SELF_HOST_URL}/api/science_city/auth-form?s3_key={report_path}"
                await self._send_report_url_async(task.id, report_url)

            except Exception as e:
                # 记录详细的异常堆栈信息
                logger.exception(f"任务 {task.id} 处理异常的详细堆栈:")
                logger.error(f"任务处理失败: {task.id}, 错误: {str(e)}")
                task.status = TaskStatus.FAILED
                task.update_time = datetime.now()
                await update_report_task_status_async(task.id, task.status)

    async def _run(self):
        """异步执行任务扫描和处理"""
        while self.is_running:
            try:
                # 获取待处理的任务
                await recover_exception_task_async()
                # 每次只获取一个待处理任务，避免资源竞争
                pending_tasks = await get_report_task_by_status_async(
                    [TaskStatus.PENDING, TaskStatus.FAILED], limit=1
                )

                if pending_tasks:
                    task = pending_tasks[0]
                    logger.info(f"发现待处理任务: {task.id}，创建异步任务。")
                    # 立即更新任务状态为处理中，避免其他任务重复处理
                    task.status = TaskStatus.PROCESSING
                    task.update_time = datetime.now()
                    await update_report_task_status_async(task.id, task.status)

                    # 创建异步任务处理
                    task_coroutine = self._process_single_task(task)
                    async_task = asyncio.create_task(task_coroutine)
                    self._running_tasks.add(async_task)

                    # 添加完成回调来清理任务集合
                    async_task.add_done_callback(self._running_tasks.discard)
                else:
                    logger.info("没有待处理任务。")

            except Exception as e:
                logger.error(f"任务扫描异常: {str(e)}")

            await asyncio.sleep(self.scan_interval)

    async def _send_report_url_async(self, task_id: str, report_url: str):
        """异步发送报告URL到外部系统

        Args:
            task_id: 任务ID
            report_url: 报告URL
        """
        try:
            report_url = report_url + f"?#/{random.randint(100,1000)}"

            # 调用外部API
            data = {
                "taskId": task_id,
                "reportUrl": report_url,
            }

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    TASK_CALLBACK_URL,
                    headers={
                        "Authorization": f"Basic {CALLBACK_URL_AUTH}",
                        "Content-Type": "application/json",
                    },
                    json=data,
                )
                logger.info(f"发送报告URL: {data}")
                response.raise_for_status()
                logger.info(f"成功发送报告URL: task_id={task_id}, report_url={report_url}")
        except Exception as e:
            logger.error(f"发送报告URL失败: {str(e)}")


# 创建异步任务执行器实例
task_executor = AsyncTaskExecutor()
