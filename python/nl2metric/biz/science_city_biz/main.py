from dotenv import load_dotenv

# specify path to easily change env in docker
env_file = "/Users/<USER>/github_project/ask-bi/.env"
load_dotenv(dotenv_path=env_file, override=True)
# both app_config and doc_config needs load_dotenv

from contextlib import asynccontextmanager

from backend_stage_reporter.reporter import backend_stage_reporter_monitor_start
from common.http.fastapi_hook import (
    TracerMiddleware,
    ProfilerMiddleware,
    AccessLogMiddleware,
)
from common.logging.logger import get_logger
from config import app_config
from refesher.prompt_refresher import start_prompt_studio_updater
from common.trace import tracer
from fastapi import FastAPI, Request
from common.utils.shared_service_utils import wait_for_shared_service
from biz.science_city_biz.service.task_executor import task_executor
import os
from fastapi.staticfiles import StaticFiles


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup logic
    backend_stage_reporter_monitor_start()
    if app_config.ENABLE_LANGFUSE:
        wait_for_shared_service()
    await task_executor.start()  # 异步启动
    yield  # This will run the application
    await task_executor.stop()   # 异步停止
    log.info("shutdown")


app = FastAPI(lifespan=lifespan)

from fastapi.responses import JSONResponse

start_prompt_studio_updater()

tracer.init_trace_provider(app_config.APP_NAME)

# 注册中间件
app.add_middleware(TracerMiddleware)
app.add_middleware(ProfilerMiddleware)
app.add_middleware(AccessLogMiddleware)
log = get_logger(__name__)


@app.exception_handler(Exception)
async def http_exception_handler(request: Request, exc: Exception):
    log.exception("Request failed: %s", exc)
    return JSONResponse(status_code=200, content={"error": str(exc)})


app.mount(
    "/static",
    StaticFiles(directory=os.path.join(os.path.dirname(__file__), "static")),
    name="static",
)

from biz.science_city_biz.service.science_city_service import science_city_route

from biz.science_city_biz.service.report_service import science_city_report_router

app.include_router(science_city_report_router)

app.include_router(science_city_route)
from biz.science_city_biz.service.verify_service import verify_route

app.include_router(verify_route)

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=9099, workers=1)  # for debug
