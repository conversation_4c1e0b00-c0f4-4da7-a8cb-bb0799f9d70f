#!/usr/bin/env python3
"""
迁移脚本：从同步任务执行器迁移到异步任务执行器

这个脚本帮助验证异步任务执行器的功能，并提供迁移指导。
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from biz.science_city_biz.data.models.async_report_task import (
    get_report_task_by_status_async,
    recover_exception_task_async,
    update_report_task_status_async,
    init_async_db
)
from biz.science_city_biz.data.models.report_task import TaskStatus, TaskType
from biz.science_city_biz.service.task_executor import AsyncTaskExecutor
from common.logging.logger import get_logger

logger = get_logger(__name__)


async def test_async_database_operations():
    """测试异步数据库操作"""
    logger.info("🔍 测试异步数据库操作...")
    
    try:
        # 初始化数据库
        await init_async_db()
        logger.info("✅ 数据库初始化成功")
        
        # 测试恢复异常任务
        await recover_exception_task_async()
        logger.info("✅ 恢复异常任务功能正常")
        
        # 测试获取任务列表
        tasks = await get_report_task_by_status_async([TaskStatus.PENDING, TaskStatus.FAILED], limit=5)
        logger.info(f"✅ 获取到 {len(tasks)} 个待处理任务")
        
        return True
    except Exception as e:
        logger.error(f"❌ 数据库操作测试失败: {e}")
        return False


async def test_async_task_executor():
    """测试异步任务执行器"""
    logger.info("🔍 测试异步任务执行器...")
    
    try:
        # 创建执行器实例
        executor = AsyncTaskExecutor(scan_interval=5, max_workers=2)
        logger.info("✅ 异步任务执行器创建成功")
        
        # 测试启动
        await executor.start()
        logger.info("✅ 异步任务执行器启动成功")
        
        # 运行一小段时间
        await asyncio.sleep(2)
        
        # 测试停止
        await executor.stop()
        logger.info("✅ 异步任务执行器停止成功")
        
        return True
    except Exception as e:
        logger.error(f"❌ 异步任务执行器测试失败: {e}")
        return False


async def check_dependencies():
    """检查依赖项"""
    logger.info("🔍 检查依赖项...")
    
    dependencies = [
        ('asyncio', 'Python内置'),
        ('httpx', '异步HTTP客户端'),
        ('sqlalchemy', 'SQLAlchemy ORM'),
        ('aiomysql', '异步MySQL驱动'),
        ('aiosqlite', '异步SQLite驱动'),
    ]
    
    missing_deps = []
    
    for dep_name, description in dependencies:
        try:
            if dep_name == 'asyncio':
                import asyncio
            elif dep_name == 'httpx':
                import httpx
            elif dep_name == 'sqlalchemy':
                import sqlalchemy
                from sqlalchemy.ext.asyncio import AsyncSession
            elif dep_name == 'aiomysql':
                import aiomysql
            elif dep_name == 'aiosqlite':
                import aiosqlite
            
            logger.info(f"✅ {dep_name} ({description}) - 已安装")
        except ImportError:
            logger.error(f"❌ {dep_name} ({description}) - 未安装")
            missing_deps.append(dep_name)
    
    if missing_deps:
        logger.error(f"缺少依赖项: {', '.join(missing_deps)}")
        logger.info("请运行: pip install " + " ".join(missing_deps))
        return False
    
    return True


async def migration_checklist():
    """迁移检查清单"""
    logger.info("📋 异步任务执行器迁移检查清单:")
    
    checklist = [
        "✅ 已将 TaskExecutor 重命名为 AsyncTaskExecutor",
        "✅ 已将所有方法转换为 async/await",
        "✅ 已创建异步数据库操作函数",
        "✅ 已将 requests 替换为 httpx",
        "✅ 已将 ThreadPoolExecutor 替换为 asyncio.create_task",
        "✅ 已更新 main.py 中的 lifespan 函数",
        "✅ 已添加必要的异步依赖项",
        "✅ 已创建测试用例",
    ]
    
    for item in checklist:
        logger.info(f"  {item}")
    
    logger.info("\n🔧 迁移后的主要改进:")
    improvements = [
        "真正的异步执行，避免阻塞事件循环",
        "更好的并发控制和资源管理",
        "优雅的关闭处理，避免事件循环关闭错误",
        "异步数据库操作，提高性能",
        "异步HTTP请求，减少等待时间",
        "使用信号量控制并发数，防止资源耗尽",
    ]
    
    for improvement in improvements:
        logger.info(f"  • {improvement}")


async def main():
    """主函数"""
    logger.info("🚀 开始异步任务执行器迁移验证...")
    
    # 检查依赖项
    if not await check_dependencies():
        logger.error("❌ 依赖项检查失败，请先安装缺少的依赖项")
        return False
    
    # 测试异步数据库操作
    if not await test_async_database_operations():
        logger.error("❌ 异步数据库操作测试失败")
        return False
    
    # 测试异步任务执行器
    if not await test_async_task_executor():
        logger.error("❌ 异步任务执行器测试失败")
        return False
    
    # 显示迁移检查清单
    await migration_checklist()
    
    logger.info("🎉 异步任务执行器迁移验证完成！")
    logger.info("💡 现在可以安全地使用新的异步任务执行器了")
    
    return True


if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行迁移验证
    success = asyncio.run(main())
    
    if success:
        print("\n✅ 迁移验证成功！")
        sys.exit(0)
    else:
        print("\n❌ 迁移验证失败！")
        sys.exit(1)
