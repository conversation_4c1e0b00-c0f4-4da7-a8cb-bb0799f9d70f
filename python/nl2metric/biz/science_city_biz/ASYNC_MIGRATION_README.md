# 异步任务执行器迁移指南

## 概述

本项目已成功将 `TaskExecutor` 从基于线程的同步实现迁移到基于 `asyncio` 的真正异步实现。这次迁移解决了事件循环关闭错误，提高了性能和资源利用率。

## 主要变更

### 1. 任务执行器重构

**之前 (同步版本):**
```python
class TaskExecutor:
    def __init__(self, scan_interval: int = 10, max_workers: int = 5):
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        self._thread = None
    
    def start(self):
        self._thread = Thread(target=self._run, daemon=True)
        self._thread.start()
    
    def _process_single_task(self, task):
        # 同步处理任务
        pass
```

**现在 (异步版本):**
```python
class AsyncTaskExecutor:
    def __init__(self, scan_interval: int = 10, max_workers: int = 5):
        self._semaphore = asyncio.Semaphore(max_workers)
        self._running_tasks = set()
    
    async def start(self):
        self._task = asyncio.create_task(self._run())
    
    async def _process_single_task(self, task):
        async with self._semaphore:
            # 异步处理任务
            pass
```

### 2. 数据库操作异步化

创建了新的异步数据库操作模块 `async_report_task.py`:

- `get_report_task_by_status_async()` - 异步获取任务列表
- `update_report_task_status_async()` - 异步更新任务状态
- `recover_exception_task_async()` - 异步恢复异常任务

### 3. HTTP 请求异步化

**之前:**
```python
import requests

response = requests.post(url, json=data)
```

**现在:**
```python
import httpx

async with httpx.AsyncClient() as client:
    response = await client.post(url, json=data)
```

### 4. 主应用生命周期更新

**main.py 中的变更:**
```python
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动
    await task_executor.start()  # 异步启动
    yield
    # 关闭
    await task_executor.stop()   # 异步停止
```

## 新增依赖

在 `requirements.txt` 中添加了以下依赖：

```
aiosqlite==0.20.0  # 异步 SQLite 支持
```

现有依赖已满足需求：
- `aiomysql==0.2.0` - 异步 MySQL 支持
- `httpx==0.27.0` - 异步 HTTP 客户端
- `sqlalchemy==2.0.25` - 支持异步操作

## 主要改进

### 1. 真正的异步执行
- 不再阻塞事件循环
- 使用 `asyncio.create_task()` 进行并发执行
- 通过信号量控制并发数量

### 2. 优雅的关闭处理
- 避免事件循环关闭错误
- 等待所有运行中的任务完成
- 正确的资源清理

### 3. 更好的错误处理
- 异步异常处理
- 详细的错误日志
- 任务失败时的状态更新

### 4. 性能提升
- 异步数据库操作
- 异步 HTTP 请求
- 减少线程切换开销

## 使用方法

### 启动异步任务执行器

```python
from biz.science_city_biz.service.task_executor import task_executor

# 在异步上下文中启动
await task_executor.start()

# 在异步上下文中停止
await task_executor.stop()
```

### 运行测试

```bash
# 运行异步任务执行器测试
python -m pytest biz/science_city_biz/tests/test_async_task_executor.py -v

# 运行迁移验证脚本
python biz/science_city_biz/scripts/migrate_to_async_executor.py
```

## 兼容性说明

### 向后兼容性
- 任务数据模型保持不变
- 配置参数保持不变
- 外部 API 接口保持不变

### 破坏性变更
- `TaskExecutor` 重命名为 `AsyncTaskExecutor`
- `start()` 和 `stop()` 方法现在是异步的
- 需要在异步上下文中使用

## 故障排除

### 常见问题

1. **事件循环关闭错误**
   - 已通过异步实现解决
   - 确保在异步上下文中调用 `start()` 和 `stop()`

2. **数据库连接问题**
   - 检查异步数据库驱动是否正确安装
   - 确认数据库 URL 格式正确

3. **依赖项缺失**
   - 运行 `pip install aiosqlite` 安装缺失依赖
   - 检查所有异步相关包是否已安装

### 调试技巧

1. **启用详细日志**
   ```python
   import logging
   logging.getLogger('biz.science_city_biz').setLevel(logging.DEBUG)
   ```

2. **监控任务状态**
   ```python
   # 检查运行中的任务数量
   print(f"运行中的任务: {len(task_executor._running_tasks)}")
   ```

3. **验证异步操作**
   ```python
   # 确保在异步上下文中运行
   assert asyncio.get_running_loop() is not None
   ```

## 性能监控

### 关键指标

- 并发任务数量
- 任务处理时间
- 数据库连接池状态
- 内存使用情况

### 监控代码示例

```python
import time
import asyncio

async def monitor_performance():
    while True:
        print(f"活跃任务: {len(task_executor._running_tasks)}")
        print(f"信号量可用: {task_executor._semaphore._value}")
        await asyncio.sleep(10)
```

## 未来改进

1. **添加任务优先级支持**
2. **实现任务重试机制**
3. **添加任务执行指标收集**
4. **支持分布式任务执行**

## 联系方式

如有问题或建议，请联系开发团队或创建 Issue。
