"""测试ReportGenerator的异步修复"""
import asyncio
import pytest
from unittest.mock import MagicMock, patch
import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

from biz.science_city_biz.service.discrete_report.report_generator import ReportGenerator
from biz.science_city_biz.data.models.report_task import TaskType


class MockCompanyInfo:
    """模拟公司信息"""
    def __init__(self):
        self.type = "制造业"
        self.name = "测试公司"
        self.score = 85.0
        self.level = "良好"


class MockAssessmentData:
    """模拟评估数据"""
    def __init__(self):
        self.company_info = MockCompanyInfo()


class MockDAO:
    """模拟DAO"""
    def load_assessment_data(self, task_id):
        return MockAssessmentData()


class MockAssessmentService:
    """模拟评估服务"""
    def build_assessment_tree(self, assessment_data):
        # 创建一个简单的模拟树
        mock_tree = MagicMock()
        mock_tree.final_score = 85.0
        mock_tree.get_metric_level.return_value = ("良好", "发展良好")
        mock_tree.get_nodes_by_level.return_value = []
        
        freq_dict = {"数字化基础": "制造业"}
        return mock_tree, freq_dict


def test_report_generator_init_without_asyncio_run():
    """测试ReportGenerator初始化时不会调用asyncio.run"""
    
    with patch('biz.science_city_biz.service.discrete_report.report_generator.AssessmentDAO') as mock_dao_class, \
         patch('biz.science_city_biz.service.discrete_report.report_generator.AssessmentService') as mock_service_class, \
         patch('biz.science_city_biz.service.discrete_report.report_generator.TemplateService') as mock_template_class, \
         patch('biz.science_city_biz.service.discrete_report.report_generator.ChartGenerator') as mock_chart_class, \
         patch('biz.science_city_biz.service.discrete_report.report_generator.LLMService') as mock_llm_class:
        
        # 设置模拟对象
        mock_dao_class.return_value = MockDAO()
        mock_service_class.return_value = MockAssessmentService()
        mock_template_class.return_value = MagicMock()
        mock_chart_class.return_value = MagicMock()
        mock_llm_class.return_value = MagicMock()
        
        # 这应该不会抛出异常，即使在有运行中事件循环的环境中
        try:
            generator = ReportGenerator(task_id="test-task", task_type=TaskType.DISCRETE)
            print("✅ ReportGenerator 初始化成功，没有 asyncio.run 冲突")
            
            # 验证plan_and_route还未初始化
            assert not generator._plan_and_route_initialized
            print("✅ plan_and_route 延迟初始化正常")
            
            return True
        except Exception as e:
            print(f"❌ ReportGenerator 初始化失败: {e}")
            return False


def test_plan_and_route_lazy_initialization():
    """测试plan_and_route的延迟初始化"""
    
    with patch('biz.science_city_biz.service.discrete_report.report_generator.AssessmentDAO') as mock_dao_class, \
         patch('biz.science_city_biz.service.discrete_report.report_generator.AssessmentService') as mock_service_class, \
         patch('biz.science_city_biz.service.discrete_report.report_generator.TemplateService') as mock_template_class, \
         patch('biz.science_city_biz.service.discrete_report.report_generator.ChartGenerator') as mock_chart_class, \
         patch('biz.science_city_biz.service.discrete_report.report_generator.LLMService') as mock_llm_class, \
         patch.object(ReportGenerator, '_get_plan_and_route_async') as mock_async_method:
        
        # 设置模拟对象
        mock_dao_class.return_value = MockDAO()
        mock_service_class.return_value = MockAssessmentService()
        mock_template_class.return_value = MagicMock()
        mock_chart_class.return_value = MagicMock()
        mock_llm_class.return_value = MagicMock()
        mock_async_method.return_value = "测试规划内容"
        
        try:
            generator = ReportGenerator(task_id="test-task", task_type=TaskType.DISCRETE)
            
            # 第一次访问plan_and_route应该触发初始化
            with patch.object(generator, '_ensure_plan_and_route_initialized') as mock_init:
                mock_init.return_value = None
                generator._plan_and_route = "测试规划内容"
                generator._plan_and_route_initialized = True
                
                plan_content = generator.plan_and_route
                print(f"✅ plan_and_route 延迟初始化成功: {plan_content}")
                
            return True
        except Exception as e:
            print(f"❌ plan_and_route 延迟初始化失败: {e}")
            return False


@pytest.mark.asyncio
async def test_report_generator_in_async_context():
    """测试在异步上下文中使用ReportGenerator"""
    
    with patch('biz.science_city_biz.service.discrete_report.report_generator.AssessmentDAO') as mock_dao_class, \
         patch('biz.science_city_biz.service.discrete_report.report_generator.AssessmentService') as mock_service_class, \
         patch('biz.science_city_biz.service.discrete_report.report_generator.TemplateService') as mock_template_class, \
         patch('biz.science_city_biz.service.discrete_report.report_generator.ChartGenerator') as mock_chart_class, \
         patch('biz.science_city_biz.service.discrete_report.report_generator.LLMService') as mock_llm_class:
        
        # 设置模拟对象
        mock_dao_class.return_value = MockDAO()
        mock_service_class.return_value = MockAssessmentService()
        mock_template_class.return_value = MagicMock()
        mock_chart_class.return_value = MagicMock()
        mock_llm_class.return_value = MagicMock()
        
        try:
            # 在异步上下文中创建ReportGenerator应该不会有问题
            generator = ReportGenerator(task_id="test-task", task_type=TaskType.DISCRETE)
            print("✅ 在异步上下文中创建 ReportGenerator 成功")
            
            # 模拟访问plan_and_route
            with patch.object(generator, '_ensure_plan_and_route_initialized'):
                generator._plan_and_route = "异步测试规划内容"
                generator._plan_and_route_initialized = True
                
                plan_content = generator.plan_and_route
                print(f"✅ 在异步上下文中访问 plan_and_route 成功: {plan_content}")
            
            return True
        except Exception as e:
            print(f"❌ 在异步上下文中使用 ReportGenerator 失败: {e}")
            return False


def main():
    """运行所有测试"""
    print("🔍 开始测试 ReportGenerator 异步修复...")
    
    # 测试1：基本初始化
    success1 = test_report_generator_init_without_asyncio_run()
    
    # 测试2：延迟初始化
    success2 = test_plan_and_route_lazy_initialization()
    
    # 测试3：异步上下文
    success3 = asyncio.run(test_report_generator_in_async_context())
    
    if success1 and success2 and success3:
        print("🎉 所有测试通过！ReportGenerator 异步修复成功！")
        return True
    else:
        print("❌ 部分测试失败！")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
