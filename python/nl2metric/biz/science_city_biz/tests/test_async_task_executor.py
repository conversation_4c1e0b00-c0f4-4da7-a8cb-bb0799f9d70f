"""测试异步任务执行器"""
import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from biz.science_city_biz.service.task_executor import AsyncTaskExecutor
from biz.science_city_biz.data.models.report_task import TaskStatus, TaskType


class MockTask:
    """模拟任务对象"""
    def __init__(self, task_id: str, task_type: TaskType = TaskType.DISCRETE):
        self.id = task_id
        self.task_type = task_type
        self.status = TaskStatus.PENDING
        self.update_time = datetime.now()


@pytest.mark.asyncio
async def test_async_task_executor_start_stop():
    """测试异步任务执行器的启动和停止"""
    executor = AsyncTaskExecutor(scan_interval=1, max_workers=2)
    
    # 测试启动
    await executor.start()
    assert executor.is_running is True
    assert executor._task is not None
    
    # 等待一小段时间确保任务开始运行
    await asyncio.sleep(0.1)
    
    # 测试停止
    await executor.stop()
    assert executor.is_running is False


@pytest.mark.asyncio
async def test_async_task_executor_process_task():
    """测试异步任务处理"""
    executor = AsyncTaskExecutor(scan_interval=1, max_workers=2)
    
    # 创建模拟任务
    mock_task = MockTask("test-task-1")
    
    # 模拟所有依赖函数
    with patch('biz.science_city_biz.service.task_executor.get_template_by_id') as mock_get_template, \
         patch('biz.science_city_biz.service.task_executor.get_full_report_combine_with_template') as mock_get_report, \
         patch('biz.science_city_biz.service.task_executor.save_to_pdf_v2') as mock_save_pdf, \
         patch('biz.science_city_biz.service.task_executor.create_s3_client') as mock_s3_client, \
         patch('biz.science_city_biz.service.task_executor.update_report_task_status_async') as mock_update_status, \
         patch.object(executor, '_send_report_url_async') as mock_send_url:
        
        # 设置模拟返回值
        mock_get_template.return_value = MagicMock()
        mock_get_report.return_value = "测试报告内容"
        mock_save_pdf.return_value = None
        mock_s3_client.return_value.upload_file.return_value = None
        mock_update_status.return_value = None
        mock_send_url.return_value = None
        
        # 执行任务处理
        await executor._process_single_task(mock_task)
        
        # 验证调用
        mock_get_template.assert_called_once()
        mock_get_report.assert_called_once()
        mock_save_pdf.assert_called_once()
        mock_update_status.assert_called_once()
        mock_send_url.assert_called_once()


@pytest.mark.asyncio
async def test_async_task_executor_concurrent_processing():
    """测试并发任务处理"""
    executor = AsyncTaskExecutor(scan_interval=1, max_workers=2)
    
    # 创建多个模拟任务
    tasks = [MockTask(f"test-task-{i}") for i in range(3)]
    
    # 模拟所有依赖函数
    with patch('biz.science_city_biz.service.task_executor.get_template_by_id') as mock_get_template, \
         patch('biz.science_city_biz.service.task_executor.get_full_report_combine_with_template') as mock_get_report, \
         patch('biz.science_city_biz.service.task_executor.save_to_pdf_v2') as mock_save_pdf, \
         patch('biz.science_city_biz.service.task_executor.create_s3_client') as mock_s3_client, \
         patch('biz.science_city_biz.service.task_executor.update_report_task_status_async') as mock_update_status, \
         patch.object(executor, '_send_report_url_async') as mock_send_url:
        
        # 设置模拟返回值
        mock_get_template.return_value = MagicMock()
        mock_get_report.return_value = "测试报告内容"
        mock_save_pdf.return_value = None
        mock_s3_client.return_value.upload_file.return_value = None
        mock_update_status.return_value = None
        mock_send_url.return_value = None
        
        # 并发执行任务处理
        await asyncio.gather(*[executor._process_single_task(task) for task in tasks])
        
        # 验证所有任务都被处理
        assert mock_get_template.call_count == 3
        assert mock_get_report.call_count == 3
        assert mock_save_pdf.call_count == 3
        assert mock_update_status.call_count == 3
        assert mock_send_url.call_count == 3


@pytest.mark.asyncio
async def test_async_task_executor_error_handling():
    """测试异步任务执行器的错误处理"""
    executor = AsyncTaskExecutor(scan_interval=1, max_workers=2)
    
    # 创建模拟任务
    mock_task = MockTask("test-task-error")
    
    # 模拟函数抛出异常
    with patch('biz.science_city_biz.service.task_executor.get_template_by_id') as mock_get_template, \
         patch('biz.science_city_biz.service.task_executor.update_report_task_status_async') as mock_update_status:
        
        # 设置模拟异常
        mock_get_template.side_effect = Exception("测试异常")
        mock_update_status.return_value = None
        
        # 执行任务处理（应该不抛出异常）
        await executor._process_single_task(mock_task)
        
        # 验证错误状态更新被调用
        mock_update_status.assert_called_once()
        # 验证任务状态被设置为失败
        assert mock_task.status == TaskStatus.FAILED


@pytest.mark.asyncio
async def test_send_report_url_async():
    """测试异步发送报告URL"""
    executor = AsyncTaskExecutor()
    
    with patch('httpx.AsyncClient') as mock_client:
        # 设置模拟响应
        mock_response = MagicMock()
        mock_response.raise_for_status.return_value = None
        mock_client.return_value.__aenter__.return_value.post.return_value = mock_response
        
        # 执行发送URL
        await executor._send_report_url_async("test-task", "http://example.com/report")
        
        # 验证HTTP请求被调用
        mock_client.return_value.__aenter__.return_value.post.assert_called_once()


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_async_task_executor_start_stop())
    print("✅ 异步任务执行器启动停止测试通过")
    
    asyncio.run(test_async_task_executor_process_task())
    print("✅ 异步任务处理测试通过")
    
    asyncio.run(test_async_task_executor_concurrent_processing())
    print("✅ 并发任务处理测试通过")
    
    asyncio.run(test_async_task_executor_error_handling())
    print("✅ 错误处理测试通过")
    
    asyncio.run(test_send_report_url_async())
    print("✅ 异步发送URL测试通过")
    
    print("🎉 所有测试通过！")
