"""异步版本的报告任务数据库操作"""
import asyncio
from datetime import datetime, timedelta
from typing import List, AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker
from sqlalchemy import select, update

from biz.science_city_biz.data.models.report_task import (
    ReportTask,
    TaskStatus,
)
from biz.science_city_biz.data.models.async_db_manager import get_async_session
from common.logging.logger import get_logger

logger = get_logger(__name__)


async def get_report_task_by_status_async(
    status: List[TaskStatus], limit: int = 10
) -> List[ReportTask]:
    """异步根据状态获取报告任务列表"""
    async with get_async_session() as session:
        try:
            stmt = (
                select(ReportTask)
                .where(ReportTask.status.in_(status))
                .order_by(ReportTask.create_time.desc())
                .limit(limit)
            )
            result = await session.execute(stmt)
            tasks = result.scalars().all()
            # 确保所有对象都被加载到内存中，避免延迟加载问题
            return [task for task in tasks]
        except Exception as e:
            logger.error(f"获取任务列表失败: {e}")
            await session.rollback()
            return []


async def get_report_tasks_async(
    page: int = 1, page_size: int = 10
) -> tuple[int, List[ReportTask]]:
    """异步获取报告任务列表
    
    Args:
        page: 页码，从1开始
        page_size: 每页数量
    
    Returns:
        tuple: (总数, 任务列表)
    """
    async with get_async_session() as session:
        # 获取总数
        count_stmt = select(ReportTask)
        count_result = await session.execute(count_stmt)
        total = len(count_result.scalars().all())
        
        # 获取分页数据
        offset = (page - 1) * page_size
        stmt = (
            select(ReportTask)
            .order_by(ReportTask.create_time.desc())
            .offset(offset)
            .limit(page_size)
        )
        result = await session.execute(stmt)
        tasks = result.scalars().all()
        
        return total, tasks


async def recover_exception_task_async():
    """异步恢复异常任务,将超过1小时未更新的processing状态任务重置为pending状态"""
    async with get_async_session() as session:
        try:
            # 获取1小时前的时间点
            one_hour_ago = datetime.now() - timedelta(hours=1)

            # 查询并更新超时的processing任务
            stmt = (
                update(ReportTask)
                .where(
                    ReportTask.status == TaskStatus.PROCESSING,
                    ReportTask.update_time < one_hour_ago,
                )
                .values(status=TaskStatus.PENDING, update_time=datetime.now())
            )
            await session.execute(stmt)
            await session.commit()
        except Exception as e:
            logger.error(f"恢复异常任务失败: {e}")
            await session.rollback()


async def get_report_task_by_id_async(task_id: str) -> ReportTask:
    """异步根据ID获取报告任务"""
    async with get_async_session() as session:
        stmt = select(ReportTask).where(ReportTask.id == task_id)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()


async def update_report_task_status_async(task_id: str, status: str):
    """异步更新报告任务状态"""
    async with get_async_session() as session:
        try:
            stmt = (
                update(ReportTask)
                .where(ReportTask.id == task_id)
                .values(status=status, update_time=datetime.now())
            )
            await session.execute(stmt)
            await session.commit()
        except Exception as e:
            logger.error(f"更新任务状态失败: {e}")
            await session.rollback()


async def create_report_task_async(
    task_type: str, questionnaire_request
) -> str:
    """异步创建报告任务"""
    import uuid
    
    task_id = str(uuid.uuid4())
    report_task = ReportTask(
        id=task_id,
        enterprise_name=questionnaire_request.enterprise_name,
        contact_number=questionnaire_request.contact_number,
        questionnaire_request=questionnaire_request.model_dump_json(),
        status="pending",
        task_type=task_type,
    )
    
    async with get_async_session() as session:
        session.add(report_task)
        await session.commit()
    
    return task_id


# 初始化异步数据库表
async def init_async_db():
    """初始化异步数据库表"""
    try:
        from biz.science_city_biz.data.models.report_task import Base
        from biz.science_city_biz.data.models.async_db_manager import db_manager

        # 获取当前事件循环的引擎
        loop = asyncio.get_running_loop()
        engine = db_manager._get_or_create_engine(loop)

        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info("异步数据库表初始化完成")
    except Exception as e:
        logger.error(f"异步数据库表初始化失败: {e}")


# 数据库初始化标志
_db_initialized = False

async def ensure_db_initialized():
    """确保数据库已初始化（异步版本）"""
    global _db_initialized
    if not _db_initialized:
        await init_async_db()
        _db_initialized = True
