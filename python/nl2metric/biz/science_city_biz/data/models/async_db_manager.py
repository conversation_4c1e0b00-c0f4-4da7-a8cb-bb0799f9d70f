"""异步数据库连接管理器"""
import asyncio
import weakref
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker

from common.logging.logger import get_logger

logger = get_logger(__name__)


class AsyncDBManager:
    """异步数据库连接管理器，确保连接在正确的事件循环中使用"""
    
    _instance: Optional['AsyncDBManager'] = None
    _lock = asyncio.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._engines = weakref.WeakKeyDictionary()  # 每个事件循环一个引擎
        self._session_makers = weakref.WeakKeyDictionary()  # 每个事件循环一个会话工厂
        self._db_url = 'sqlite+aiosqlite:///./science_city.db'
    
    def _get_current_loop(self) -> asyncio.AbstractEventLoop:
        """获取当前事件循环"""
        try:
            return asyncio.get_running_loop()
        except RuntimeError:
            raise RuntimeError("必须在异步上下文中使用数据库管理器")
    
    def _get_or_create_engine(self, loop: asyncio.AbstractEventLoop):
        """为指定事件循环获取或创建引擎"""
        if loop not in self._engines:
            engine = create_async_engine(
                self._db_url,
                echo=False,
                pool_pre_ping=True,
                pool_recycle=3600,
                pool_size=5,
                max_overflow=10,
                # 确保连接在正确的事件循环中创建
                future=loop.create_future,
            )
            self._engines[loop] = engine
            logger.debug(f"为事件循环 {id(loop)} 创建新的数据库引擎")
        
        return self._engines[loop]
    
    def _get_or_create_session_maker(self, loop: asyncio.AbstractEventLoop):
        """为指定事件循环获取或创建会话工厂"""
        if loop not in self._session_makers:
            engine = self._get_or_create_engine(loop)
            session_maker = async_sessionmaker(
                engine,
                expire_on_commit=False,
                class_=AsyncSession
            )
            self._session_makers[loop] = session_maker
            logger.debug(f"为事件循环 {id(loop)} 创建新的会话工厂")
        
        return self._session_makers[loop]
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """获取数据库会话的上下文管理器"""
        loop = self._get_current_loop()
        session_maker = self._get_or_create_session_maker(loop)
        
        async with session_maker() as session:
            try:
                yield session
            except Exception as e:
                logger.error(f"数据库会话异常: {e}")
                await session.rollback()
                raise
            finally:
                # 会话会被自动关闭
                pass
    
    async def close_all(self):
        """关闭所有引擎"""
        async with self._lock:
            for engine in list(self._engines.values()):
                try:
                    await engine.dispose()
                    logger.debug("数据库引擎已关闭")
                except Exception as e:
                    logger.error(f"关闭数据库引擎时出错: {e}")
            
            self._engines.clear()
            self._session_makers.clear()


# 全局数据库管理器实例
db_manager = AsyncDBManager()


@asynccontextmanager
async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """获取异步数据库会话的便捷函数"""
    async with db_manager.get_session() as session:
        yield session


async def close_all_db_connections():
    """关闭所有数据库连接的便捷函数"""
    await db_manager.close_all()
