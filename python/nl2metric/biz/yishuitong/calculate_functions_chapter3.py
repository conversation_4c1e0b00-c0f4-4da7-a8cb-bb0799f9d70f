from datetime import datetime, timedelta

import pandas as pd
from dateutil.relativedelta import relativedelta

from collections import defaultdict

from biz.yishuitong.utils.utils import Utils


class ReportChapter3:
    # 3.1
    def calculate_monthly_stats(self, dt, data, threshold=0.5):
        """
        3.1 增值税发票（专票+普票）用量变动异常检查

        Args:
            current_date: 当前日期，格式为'YYYYMM'
            data: 包含发票数据的字典

        Returns:
            output: 包含每月统计数据的列表，list of list
            risk_desc: 风险描述，格式为''
        """
        # 将current_date转换为datetime对象
        dt_format = datetime.strptime(dt, "%Y%m")
        # end_month = dt_format - relativedelta(months=1)
        # start_month = (dt_format - relativedelta(months=11)).strftime("%Y%m")

        # 初始化结果字典
        monthly_stats = {}
        risk_dict = {}

        # 生成最近12个月的月份列表
        for i in range(1, 13):
            month_key = (dt_format - relativedelta(months=i)).strftime("%Y%m")
            monthly_stats[month_key] = {"count": 0, "amount": 0, "change": 0}

        # 遍历发票数据
        count = 0
        all_zero = True
        for item in data["data"]["items"]:
            # 只统计“销项发票”
            if item["sign"] != "销项":
                continue
            if item["sellerTaxIde"] != self.company_taxid:
                continue
            company_name = item["sellerName"]
            if not item["invoiceTypeName"].__contains__("增值税"):
                continue
            count += 1
            belong_month = item["belongMonth"]

            # 检查是否在最近12个月内
            if belong_month in monthly_stats:
                monthly_stats[belong_month]["count"] += 1
                monthly_stats[belong_month]["amount"] += float(item["amount"])

            if (
                monthly_stats[belong_month]["count"]
                or monthly_stats[belong_month]["amount"]
            ):
                all_zero = False

        if all_zero:
            default_result = [{"所属月份": "", "开票份数": "", "开票金额(含税,元)": "", "用票量变动率": ""}]
            default_risk_desc = """风险描述：该指标项未检测到⻛险"""
            return default_result, default_risk_desc

        # 计算环比变化
        sorted_months = sorted(monthly_stats.keys())
        for i in range(1, len(sorted_months)):
            current_month = sorted_months[i]
            prev_month = sorted_months[i - 1]
            prev_amount = monthly_stats[prev_month]["count"]

            if prev_amount != 0:
                change = (
                    monthly_stats[current_month]["count"] - prev_amount
                ) / prev_amount
                if threshold and abs(change) > threshold:
                    risk_dict[Utils.convert_to_year_month(current_month)] = change
            else:
                change = 0
            monthly_stats[current_month]["change"] = change

        # 对结果进行排序
        sorted_stats = dict(sorted(monthly_stats.items(), reverse=False))

        # 格式化金额，保留2位小数
        for month in sorted_stats:
            sorted_stats[month]["amount"] = "{:,.2f}".format(
                sorted_stats[month]["amount"]
            )

        """
        return sorted_stats
        """
        output = [
            {
                "所属月份": f"{index[:4]}年{index[4:]}月",
                "开票份数": entry["count"],
                "开票金额(含税,元)": entry["amount"],
                "用票量变动率": "{:,.2f}".format(round(entry["change"] * 100, 2)) + "%"
                # "{:.2f}".format(sorted_stats[month]['amount'])
            }
            for index, entry in sorted_stats.items()
        ]
        risk_pos = map(
            lambda x: (f"""{x[0]}({str(round(x[1]*100, 2))+"%"})"""),
            filter(lambda x: x[1] > 0, risk_dict.items()),
        )
        risk_neg = map(
            lambda x: (f"""{x[0]}({str(round(x[1]*100, 2))+"%"})"""),
            filter(lambda x: x[1] < 0, risk_dict.items()),
        )
        if risk_dict:
            risk_desc = f"""风险描述：{'、'.join(risk_dict.keys())}的增值税专用发票用量出现异常波动，环比变动率均超过{str(threshold*100)+"%"}。其中，"""
            if risk_pos:
                risk_desc += f"""{'、'.join(risk_pos)}呈现正向波动，"""
            if risk_neg:
                risk_desc += f"""{'、'.join(risk_neg)}呈现负向波动，"""
            risk_desc += f"可能暗示企业经营状况异常，例如虚开发票、隐匿收入、虚增成本等，可能导致少缴税款，面临税务稽查和处罚的风险。建议关注变动原因，核实业务真实性。"
        else:
            risk_desc = """风险描述：该指标项未检测到⻛险"""
        return output, risk_desc

    # 3.2
    def calculate_top10_items(self, dt, data):
        """
        3.2 前十大销售、采购品目不一致风险检查

        Args:
            current_date: 当前日期，格式为'YYYYMM'
            data: 包含发票数据的字典

        Returns:
            output: list of list
            risk_desc: 风险描述，格式为''
        """

        dt_format = datetime.strptime(dt, "%Y%m")
        end_month = (dt_format - relativedelta(months=1)).strftime("%Y%m")
        start_month = (dt_format - relativedelta(months=13)).strftime("%Y%m")

        # 初始化统计容器
        total_sales = defaultdict(lambda: {"amount": 0.00, "taxMoney": 0.00})
        total_purchase = defaultdict(lambda: {"amount": 0.00, "taxMoney": 0.00})

        # 数据处理逻辑（保持不变）
        all_zero = True
        for item in data.get("data", {}).get("items", []):
            belong_month = item.get("belongMonth", "")
            # print(belong_month, start_month, end_month)
            if (
                not belong_month
                or belong_month < start_month
                or belong_month > end_month
            ):
                continue
            sign = item.get("sign", "").strip()
            if sign not in ["销项", "进项"]:
                continue
            for detail in item.get("details", []):
                # jshj： 即价税合计，含税金额：单个品类的含税金额
                # money：即金额(不含税)
                # taxMoney: 税额
                item_title = detail.get("itemTitle", "").strip()
                if not item_title:
                    continue
                amount = float(detail.get("jshj", 0.00))
                tax_amount = float(detail.get("taxMoney", 0.00))
                if sign == "销项" and item["sellerTaxIde"] == self.company_taxid:
                    total_sales[item_title]["amount"] += amount
                    total_sales[item_title]["taxMoney"] += tax_amount
                elif sign == "进项" and item["buyerTaxIde"] == self.company_taxid:
                    total_purchase[item_title]["amount"] += amount
                    total_purchase[item_title]["taxMoney"] += tax_amount
                if (
                    total_sales[item_title]["amount"]
                    or total_sales[item_title]["taxMoney"]
                    or total_purchase[item_title]["amount"]
                    or total_purchase[item_title]["taxMoney"]
                ):
                    all_zero = False

        if all_zero:
            default_result = [
                {
                    "序号": "",
                    "销售品目": "",
                    "交易金额(含税,元)": "",
                    "销售税额(元)": "",
                    "采购类目": "",
                    "采购交易金额(含税,元)": "",
                    "采购税额(元)": "",
                }
            ]
            default_risk_desc = "风险描述：该指标项未检测到⻛险"
            return default_result, default_risk_desc
        # 生成销售和采购Top10列表（补足10项）
        sales_top10 = sorted(
            total_sales.items(), key=lambda x: x[1]["amount"], reverse=True
        )[:10]
        purchase_top10 = sorted(
            total_purchase.items(), key=lambda x: x[1]["amount"], reverse=True
        )[:10]

        # 补足至10项
        max_len = max(len(sales_top10), len(purchase_top10))
        sales_top10 += [("--", {"amount": "--", "taxMoney": "--"})] * (10 - max_len)
        purchase_top10 += [("--", {"amount": "--", "taxMoney": "--"})] * (10 - max_len)

        # 合并为指定格式
        # merged_data = []
        result = []
        # merged_data.append(["序号","销售品目","交易金额(含税,元)", "税额(元)", "采购类目", "交易金额(含税,元)", "税额(元)"])
        for idx in range(max_len):
            sales_item = sales_top10[idx]
            purchase_item = purchase_top10[idx]
            new_row = {
                "序号": idx + 1,
                "销售品目": sales_item[0] if sales_item[0] != "--" else "--",
                "交易金额(含税,元)": "{:,.2f}".format(sales_item[1]["amount"], 2)
                if isinstance(sales_item[1]["amount"], float)
                else "--",
                "销售税额(元)": "{:,.2f}".format(sales_item[1]["taxMoney"], 2)
                if isinstance(sales_item[1]["taxMoney"], float)
                else "--",
                "采购类目": purchase_item[0] if purchase_item[0] != "--" else "--",
                "采购交易金额(含税,元)": "{:,.2f}".format(purchase_item[1]["amount"], 2)
                if isinstance(purchase_item[1]["amount"], float)
                else "--",
                "采购税额(元)": "{:,.2f}".format(purchase_item[1]["taxMoney"], 2)
                if isinstance(purchase_item[1]["taxMoney"], float)
                else "--",
            }
            result.append(new_row)
        risk_desc = """风险描述：该指标项未检测到⻛险"""
        return result, risk_desc

    # 3.3
    def calculate_mutual_invoices(self, dt, data, threshold=0.00):
        """
        3.3 检测近12个月中存在互开发票的企业清单，并输出主要公司及其金额。

        Args:
            current_date (str): 当前日期，格式为'YYYYMM'
            data (dict): 包含发票数据的字典，结构应包含 'data' -> 'items' 列表

        Returns:
            list: 包含主要公司及其金额的列表，每个元素为一个字典，包含以下键：
                - 序号: 序号
                - 主要公司纳税人识别号: 主要公司的纳税人识别号
                - 主要公司名称: 主要公司的名称
                - 开出金额（元）: 主要公司开给对方的金额
                - 开进金额（元）: 对方开给主要公司的金额
        """

        dt_format = datetime.strptime(dt, "%Y%m")
        end_month = (dt_format - relativedelta(months=1)).strftime("%Y%m")
        start_month = (dt_format - relativedelta(months=12)).strftime("%Y%m")
        default_result = [
            {
                "序号": "",
                "销方纳税人名称": "",
                "销方纳税人识别号": "",
                "购方纳税人名称": "",
                "购方纳税人识别号": "",
                "发票金额(含税,元)": "",
            }
        ]
        default_risk_desc = "风险描述： 该指标项未检测到风险"

        # 初始化字典来存储开票金额
        xiao_invoice_map, jin_invoice_map = dict(), dict()

        # 遍历发票数据
        content = data.get("data", {}).get("items", [])
        content_idinfo = [
            {
                "ID": item["sellerTaxIde"],
                "name": item["sellerName"],
                "dt": item["createDate"],
            }
            for item in content
        ]
        content_idinfo += [
            {
                "ID": item["buyerTaxIde"],
                "name": item["buyerName"],
                "dt": item["createDate"],
            }
            for item in content
        ]
        # id_dict = get_max_date_name(content_idinfo, "ID", "name", "dt")

        sell_amount = 0
        mutual_amount = 0
        all_zero = True
        for item in content:
            belong_month = item.get("belongMonth", "")
            if (
                not belong_month
                or belong_month < start_month
                or belong_month > end_month
            ):
                continue

            sign = item.get("sign", "").strip()
            if sign not in ["销项", "进项"]:
                continue  # 只处理销项和进项

            # 获取纳税人识别号和名称
            # kaichu_total = 0
            if sign == "销项":
                if item["sellerTaxIde"] != self.company_taxid:
                    continue
                company_name = item["sellerName"]
                A_tax_id = item.get("sellerTaxIde", "")
                A_name = item.get("sellerName", "")
                B_tax_id = item.get("buyerTaxIde", "")
                B_name = item.get("buyerName", "")
                amount = float(item.get("amount", 0.00))
                sell_amount += amount

                # 记录A开给B的金额
                pair = tuple([A_tax_id, B_tax_id])
                if pair not in xiao_invoice_map:
                    xiao_invoice_map[pair] = {
                        "seller_name": A_name,
                        "seller_tax": A_tax_id,
                        "buyer_name": B_name,
                        "buyer_tax": B_tax_id,
                        "kaichu_money": 0.0,
                        "kaijin_money": 0.0,
                        "trans_cnt": 0,
                    }
                xiao_invoice_map[pair]["kaichu_money"] += amount
                xiao_invoice_map[pair]["trans_cnt"] += 1
                # kaichu_total += amount
                # print("seller_name:", A_name, "buyer_name:", B_name, xiao_invoice_map[pair]['kaichu_money'])

            elif sign == "进项":
                if item["buyerTaxIde"] != self.company_taxid:
                    continue
                company_name = item["buyerName"]
                A_tax_id = item.get("buyerTaxIde", "")
                A_name = item.get("buyerName", "")
                B_tax_id = item.get("sellerTaxIde", "")
                B_name = item.get("sellerName", "")
                amount = float(item.get("amount", 0.0))
                # 记录B开给A的金额
                pair = tuple([A_tax_id, B_tax_id])
                if pair not in jin_invoice_map:
                    jin_invoice_map[pair] = {
                        "seller_name": B_name,
                        "seller_tax": B_tax_id,
                        "buyer_name": A_name,
                        "buyer_tax": A_tax_id,
                        "kaichu_money": 0.0,  # 都是从seller的角度，所以都是对kaichu_money的计算。
                        "kaijin_money": 0.0,
                        "trans_cnt": 0,
                    }
                jin_invoice_map[pair]["kaichu_money"] += amount
                jin_invoice_map[pair]["trans_cnt"] += 1
                # print("seller_name:", B_name, "seller_name:",A_name, jin_invoice_map[pair]['kaichu_money'])
        if jin_invoice_map or xiao_invoice_map:
            all_zero = False

        if all_zero:
            return default_result, default_risk_desc
        # 取互开组合
        # merged_data = []
        mutual_pares = dict()
        insert_pair = set(jin_invoice_map.keys()).intersection(
            set(xiao_invoice_map.keys())
        )
        if not insert_pair:
            return default_result, default_risk_desc
        index = 0

        risk_value = dict()
        risk_company = []
        result = []
        # merged_data.append(['序号', '销方纳税人名称', '销方纳税人识别号', '购方纳税人名称', '购方纳税人识别号', '发票金额(含税,元)'])

        for pair in insert_pair:
            index += 1
            jin_item = jin_invoice_map[pair]
            xiao_item = xiao_invoice_map[pair]
            # mutual_amount = xiao_item['kaichu_money']
            # mutual_pares[pair] = {"jin_item": jin_item['kaichu_money'], "xiao_item": xiao_item['kaichu_money'], "xiao_rate": xiao_item['kaichu_money']/sell_amount}
            # 当企业作为购买方，进项：
            row = {
                "序号": index,
                "销方纳税人名称": jin_item["seller_name"],
                "销方纳税人识别号": jin_item["seller_tax"],
                "购方纳税人名称": jin_item["buyer_name"],
                "购方纳税人识别号": jin_item["buyer_tax"],
                "发票金额(含税,元)": jin_item["kaichu_money"],
            }
            result.append(row)
            # merged_data.append([index, jin_item['seller_name'], jin_item['seller_tax'], jin_item['buyer_name'], jin_item['buyer_tax'], "{:,.2f}".format(jin_item['kaichu_money'], 2)])
            index += 1
            # 当企业作为销售方，销项：
            # merged_data.append([index, xiao_item['seller_name'],xiao_item['seller_tax'], xiao_item['buyer_name'], xiao_item['buyer_tax'], "{:,.2f}".format(xiao_item['kaichu_money'], 2)])
            # print(xiao_item['kaichu_money'], sell_amount, xiao_item['kaichu_money']/sell_amount, threshold)
            row = {
                "序号": index,
                "销方纳税人名称": xiao_item["seller_name"],
                "销方纳税人识别号": xiao_item["seller_tax"],
                "购方纳税人名称": xiao_item["buyer_name"],
                "购方纳税人识别号": xiao_item["buyer_tax"],
                "发票金额(含税,元)": xiao_item["kaichu_money"],
            }
            result.append(row)
            if xiao_item["kaichu_money"] / sell_amount > threshold:
                risk_value[xiao_item["buyer_name"]] = {
                    "sell_name": xiao_item["seller_name"],
                    "kaichu": "{:,.2f}".format(xiao_item["kaichu_money"], 2),
                    "sell_amount": "{:,.2f}".format(sell_amount, 2),
                    "rate": "{:,.2f}".format(
                        xiao_item["kaichu_money"] / sell_amount * 100, 2
                    )
                    + "%",
                }

        if risk_value:
            risk_desc = f"""风险描述：近12个月内，{company_name}与{','.join(risk_value.keys())}存在频繁的互开发票行为。其中，"""
            for k, v in risk_value.items():
                risk_desc += f"""开出给{k}的发票金额(含税)占据总开票金额的{str(v['rate'])}({str(v['kaichu'])}/{str(v['sell_amount'])}), """
            risk_desc += f"""可能涉嫌虚开发票、循环开票等违法行为，旨在虚增成本或虚列收入，存在较大的税务稽查风险。建议审查交易的真实性，避免与不具备真实业务往来的企业进行发票交易。"""
        else:
            risk_desc = "风险描述： 该指标项未检测到风险"

        return result, risk_desc

    # 3.4
    def calculate_top10_customers(self, dt, data):
        """
        3.4 销售发票top10客户

        Args:
            current_date (str): 当前日期，格式为'YYYYMM'
            data (dict): 包含发票数据的字典，结构应包含 'data' -> 'items' 列表

        Returns:
            list: 包含销售额排名前十的企业的列表，每个元素为一个字典，包含以下键：
                - 序号
                - 纳税人名称
                - 金额（元）
                - 占比
            str: 风险描述
        """
        # 将current_date转换为datetime对象
        dt_format = datetime.strptime(dt, "%Y%m")
        end_month = (dt_format - relativedelta(months=1)).strftime("%Y%m")
        start_month = (dt_format - relativedelta(months=12)).strftime("%Y%m")

        # 初始化字典来存储每个客户的销售额
        customer_sales = defaultdict(float)
        total_sales = 0.0
        all_zero = True

        # 遍历发票数据
        for item in data.get("data", {}).get("items", []):
            belong_month = item.get("belongMonth", "")
            if (
                not belong_month
                or belong_month < start_month
                or belong_month > end_month
            ):
                continue

            sign = item.get("sign", "").strip()
            if sign != "销项":
                continue  # 只处理销项发票

            if item["sellerTaxIde"] != self.company_taxid:
                continue
            company_name = item["sellerName"]
            # 获取纳税人名称和金额
            taxpayer_name = item.get("buyerName", "").strip()
            amount = float(item.get("amount", 0.0))

            # 累加销售额
            customer_sales[taxpayer_name] += amount
            total_sales += amount
            if customer_sales[taxpayer_name]:
                all_zero = False

        if all_zero:
            default_result = [{"序号": "", "纳税人名称": "", "金额(含税,元)": "", "占比": ""}]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc

        # 计算销售额排名前十的客户
        sorted_customers = sorted(
            customer_sales.items(), key=lambda x: x[1], reverse=True
        )[:10]

        # 计算占比并构建结果
        # top10_customers = []
        risk_info = []  # 记录风险信息
        result = []
        # top10_customers.append(['序号', '纳税人名称', '金额(含税,元)', '占比'])
        for index, (name, amount) in enumerate(sorted_customers, start=1):
            percentage = (amount / total_sales) * 100 if total_sales > 0 else 0
            rounded_amount = "{:,.2f}".format(amount, 2)
            rounded_percentage = "{:,.2f}".format(percentage, 2)

            # 记录占比超过50%的企业
            if percentage > 50:
                risk_info.append((name, f"{rounded_amount}元", f"{rounded_percentage}%"))
            result.append(
                {
                    "序号": index,
                    "纳税人名称": name,
                    "金额(含税,元)": rounded_amount,
                    "占比": f"{rounded_percentage}%",
                }
            )
            # top10_customers.append([index, name, rounded_amount, f"{rounded_percentage}%"])
            # top10_customers.append({
            #     '序号': index,
            #     '纳税人名称': name,
            #     '金额（元）': rounded_amount,
            #     '占比': f"{rounded_percentage}%"
            # })

        # 构造风险描述
        if risk_info:
            risk_desc = f"风险描述：近12月，{company_name}销售额排名前十的企业中，有{len(risk_info)}家企业销售额占总销售额的比重超过50%："
            for info in risk_info:
                risk_desc += f"\n纳税人名称：{info[0]}，销售额：{info[1]}，占比：{info[2]}"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 3.5
    def calculate_top10_products_by_year(self, dt, data, threshold=0.8):
        """
        3.5 检测近四年劳务销售发票中交易金额排名前十的商品种类，并按年份分开标注。

        Args:
            current_date (str): 当前日期，格式为'YYYYMM'
            data (dict): 包含发票数据的字典，结构应包含 'data' -> 'items' 列表

        Returns:
            list: 包含近四年每年交易金额排名前十的商品种类的列表，每个元素为一个字典，格式为:
            [
                {
                    '项目': '销售top1商品种类',
                    '2024年度': '种类1',
                    '2023年度': '种类2',
                    '2022年度': '种类3',
                    '2021年度': '种类4'
                },
                {
                    '项目': '销售top2商品种类',
                    '2024年度': '种类5',
                    '2023年度': '种类6',
                    '2022年度': '种类7',
                    '2021年度': '种类8'
                },
                ...
            ]
        """
        # 将current_date转换为datetime对象
        dt_format = datetime.strptime(dt, "%Y%m")

        end_month = (dt_format - relativedelta(months=1)).strftime("%Y%m")
        start_month = (dt_format - relativedelta(months=12)).strftime("%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=4)).strftime("%Y")
        range_years = [
            (dt_format - relativedelta(years=i)).strftime("%Y") for i in range(1, 5)
        ][::-1]

        # 初始化字典来存储每年的商品种类交易金额
        yearly_product_sales = {year: defaultdict(float) for year in range_years}

        all_zero = True
        # 遍历发票数据
        for item in data.get("data", {}).get("items", []):
            belong_month = item.get("belongMonth", "")
            belong_year = datetime.strptime(belong_month, "%Y%m").strftime("%Y")
            if not belong_month or belong_year < start_year or belong_year > end_year:
                continue  # 跳过没有belongMonth的发票
            if item["sign"] != "销项":
                continue
            if item["sellerTaxIde"] != self.company_taxid:
                continue
            company_name = item["sellerName"]
            # contain_detail = item.get('containDetail', 0)
            # if contain_detail == 1:
            #     continue  # 跳过containDetail为1的发票

            # 获取商品种类和交易金额
            for detail in item.get("details", []):
                product_name = detail.get("itemTitle", "").strip()
                if not product_name:
                    continue  # 跳过没有商品名的明细
                amount = float(detail.get("jshj", 0.00))
                # 累加交易金额
                yearly_product_sales[belong_year][product_name] += amount
                if yearly_product_sales[belong_year][product_name]:
                    all_zero = False

        # 获取近四年的年份列表
        years = sorted(yearly_product_sales.keys(), reverse=True)
        if all_zero:
            default_result = [{"项目": "", **{str(year) + "年度": "" for year in years}}]
            default_risk_desc = "风险描述：该指标项未检测到⻛险"
            return default_result, default_risk_desc
        # 初始化一个字典来存储每个商品的四年数据
        yearly_top10_products = {year: [] for year in years}
        yearly_top10_products_rate = {year: 0.00 for year in years}
        yearly_products_total = {year: 0 for year in years}
        # 填充每个商品的四年数据
        for year in years:
            if yearly_product_sales[year]:
                sorted_products = sorted(
                    yearly_product_sales[year].items(), key=lambda x: x[1], reverse=True
                )[:10]
                yearly_top10_products[year] = [
                    product_name for product_name, _ in sorted_products
                ]
                yearly_top10_products_rate[year] = round(
                    sum([product_amount for _, product_amount in sorted_products])
                    / sum(yearly_product_sales[year].values()),
                    2,
                )
        latest_products_rate = yearly_top10_products_rate[max(years)]
        if latest_products_rate and latest_products_rate > threshold:
            formatted_latest_products_rate = (
                "{:,.2f}".format(latest_products_rate * 100, 2) + "%"
            )
            formatted_threshold = "{:,.2f}".format(threshold * 100, 2) + "%"
            risk_desc = f"""风险描述：{company_name}最近1年（{start_month}-{end_month}），top10商品种类的含税金额在总销售金额的占比超过{formatted_threshold}({formatted_latest_products_rate})，说明公司过度依赖少数客户，可能导致企业经营风险加大。此外，客户构成或交易金额的异常变动，可能暗示存在关联交易或虚开发票等问题，需要关注潜在的税务风险。"""
        else:
            risk_desc = """风险描述：该指标项未检测到⻛险"""
        # 构建结果
        top10_products = []

        for rank in range(1, 11):
            year_flag = 0
            result_item = {"项目": f"销售top{rank}商品种类"}
            for year in years:
                if rank <= len(yearly_top10_products[year]):
                    result_item[f"{year}年度"] = yearly_top10_products[year][rank - 1]
                    year_flag = 1
                else:
                    result_item[f"{year}年度"] = "--"
            if year_flag:
                top10_products.append(result_item)

        return top10_products, risk_desc

    # 3.6
    def calculate_top10_suppliers(self, dt, data, threshold=0.8):
        """
        3.6 检测近12个月供应商中采购额排名前十的企业的交易情况。

        Args:
            current_date (str): 当前日期，格式为'YYYYMM'
            data (dict): 包含发票数据的字典，结构应包含 'data' -> 'items' 列表

        Returns:
            list: 包含采购额排名前十的供应商的列表，每个元素为一个字典，包含以下键：
                - index: 排名序号
                - seller_name: 销方名称
                - total_amount: 总采购额
                - percentage: 采购额占比
        """
        # 将current_date转换为datetime对象
        dt_format = datetime.strptime(dt, "%Y%m")

        end_month = (dt_format - relativedelta(months=1)).strftime("%Y%m")
        start_month = (dt_format - relativedelta(months=12)).strftime("%Y%m")

        # 计算近12个月的起始日期
        supplier_purchases = defaultdict(float)
        total_purchases = 0.0  # 总采购额

        # 遍历发票数据
        all_zero = True
        for item in data.get("data", {}).get("items", []):
            belong_month = item.get("belongMonth", "")
            if (
                not belong_month
                or belong_month < start_month
                or belong_month > end_month
            ):
                continue
            sign = item.get("sign", "").strip()
            if sign != "进项":
                continue
            if item["buyerTaxIde"] != self.company_taxid:
                continue
            company_name = item["buyerName"]
            # 获取销方名称和采购额
            seller_name = item.get("sellerName", "").strip()
            amount = float(item.get("amount", 0.0))

            # 累加采购额
            supplier_purchases[seller_name] += amount
            total_purchases += amount
            if supplier_purchases[seller_name]:
                all_zero = False

        if all_zero:
            default_result = [{"序号": "", "纳税人名称": "", "金额(含税,元)": "", "占比": ""}]
            default_risk_desc = "风险描述：该指标项未检测到⻛险"
            return default_result, default_risk_desc

        # 计算采购额排名前十的供应商
        sorted_top10_suppliers = sorted(
            supplier_purchases.items(), key=lambda x: x[1], reverse=True
        )[:10]
        sorted_top10_suppliers_rate = round(
            sum([item[1] for item in sorted_top10_suppliers])
            / sum(supplier_purchases.values()),
            2,
        )
        risk_desc = (
            f"风险描述：{company_name}最近一年（{start_month}-{end_month}），排名前10供应商的含税金额占总额比例超过{threshold}（{sorted_top10_suppliers_rate}）。说明公司过度依赖少数供应商，可能影响企业供应链稳定性。此外，供应商构成或交易金额的异常变动，可能暗示存在关联交易或虚开发票等问题，需要关注潜在的税务风险。"
            if sorted_top10_suppliers_rate > threshold
            else "风险描述：该指标项未检测到⻛险"
        )
        # print("sorted_top10_suppliers_rate", sorted_top10_suppliers_rate)
        # 构建结果，加入index
        top10_suppliers = [
            {
                "序号": index + 1,  # 排名序号从1开始
                "纳税人名称": name,
                "金额(含税,元)": "{:,.2f}".format(amount, 2),
                "占比": "{:,.2f}".format((amount / total_purchases) * 100, 2) + "%"
                if total_purchases > 0
                else "0%",
            }
            for index, (name, amount) in enumerate(sorted_top10_suppliers)
        ]

        return top10_suppliers, risk_desc

    # 3.7
    def calculate_top10_procurement_items(self, dt, data, threshold1=8, threshold2=5):
        """
        3.7 计算采购发票top10商品种类

        Args:
            current_date: 当前日期，格式为'YYYYMM'
            data: 包含发票数据的字典
            threshold1: 对称差的阈值
            threshold2： 被淘汰商品排名变化

        Returns:
            list: 包含近四年统计数据的列表，格式为:
            [
                {
                    "项目": "采购top1商品种类",
                    "2024年度": "*文化服务*服务费",
                    "2023年度": "*文化服务*服务费",
                    "2022年度": "*文化服务*服务费",
                    "2021年度": "*文化服务*服务费"
                },
                ...
            ]
        """

        dt_format = datetime.strptime(dt, "%Y%m")
        # end_month = (dt_format - relativedelta(months=1)).strftime("%Y%m")
        # start_month = (dt_format - relativedelta(months=12)).strftime("%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=4)).strftime("%Y")
        range_years = [
            (dt_format - relativedelta(years=i)).strftime("%Y") for i in range(1, 5)
        ][::-1]

        # 初始化字典来存储每年的商品种类交易金额
        yearly_product_sales = defaultdict(lambda: defaultdict(float))

        # 遍历发票数据
        all_zero = True
        # yearly_totals = dict()
        yearly_totals = {year: defaultdict(float) for year in range_years}

        for item in data.get("data", {}).get("items", []):
            belong_month = item.get("belongMonth", "")
            belong_year = datetime.strptime(belong_month, "%Y%m").strftime("%Y")
            if not belong_month or belong_year < start_year or belong_year > end_year:
                continue  # 跳过没有belongMonth的发票

            if item["sign"] != "进项":
                continue
            if item["buyerTaxIde"] != self.company_taxid:
                continue
            company_name = item["buyerName"]

            for detail in item.get("details", []):
                item_title = detail.get("itemTitle", "")
                item_money = float(detail.get("jshj", 0))
                yearly_totals[belong_year][item_title] += item_money
                if yearly_totals[belong_year][item_title]:
                    all_zero = False

        # 处理空年份并生成排名数据
        valid_years = []
        top10_set = {}
        year_ranks = {}
        top10_items = {}
        years = sorted(yearly_totals.keys(), reverse=True)
        if all_zero:
            default_result = [{"项目": "", **{str(year) + "年度": "" for year in years}}]
            default_risk_desc = "风险描述：该指标项未检测到⻛险"
            return default_result, default_risk_desc
        for year in years:
            items = yearly_totals[year]
            sorted_items = sorted(items.items(), key=lambda x: x[1], reverse=True)[:10]
            top10_set[year] = {item[0] for item in sorted_items}
            # 生成商品到排名的映射
            year_ranks[year] = {
                item: rank + 1 for rank, (item, _) in enumerate(sorted_items)
            }
            # 填充top10_items
            for i, (item_title, _) in enumerate(sorted_items):
                key = f"top{i + 1}"
                if key not in top10_items:
                    top10_items[key] = {}
                top10_items[key][year] = item_title

        # 构建结果表格
        result = []
        for i in range(1, 11):
            year_flag = 0
            row = {"项目": f"采购top{i}商品种类"}
            for year in years:
                row[f"{year}年度"] = top10_items.get(f"top{i}", {}).get(year, "--")
                if row[f"{year}年度"] != "--":
                    year_flag = 1
            # # 补充缺失年份为"--"
            # for y in years:
            #     if f"{y}年度" not in row:
            #         row[f"{y}年度"] = "--"
            if year_flag:
                result.append(row)

        # 风险检测逻辑
        risk_info = []
        sorted_years = sorted(valid_years, key=lambda x: int(x))
        for i in range(1, len(sorted_years)):
            year = sorted_years[i]
            prev_year = str(int(year) - 1)
            if prev_year not in sorted_years or sorted_years[i - 1] != prev_year:
                continue  # 确保连续年份

            # 条件1：集合对称差
            current_set = top10_set[year]
            prev_set = top10_set.get(prev_year, set())
            diff = current_set.symmetric_difference(prev_set)

            condition1 = len(diff) > threshold1

            # 条件2：排名变动≥threshold2
            condition2 = False
            current_rank_map = year_ranks.get(year, {})
            prev_rank_map = year_ranks.get(prev_year, {})

            # 检查当前年的商品
            for item in current_rank_map:
                prev_rank = prev_rank_map.get(item, 11)
                if abs(current_rank_map[item] - prev_rank) >= threshold2:
                    condition2 = True
                    break
            if not condition2:
                # 检查前一年的商品是否在当前年排名外
                for item in prev_rank_map:
                    if item not in current_rank_map:
                        current_rank = 11
                        if abs(current_rank - prev_rank_map[item]) >= threshold2:
                            condition2 = True
                            break

            if condition1 or condition2:
                reasons = []
                if condition1:
                    reasons.append(f"变动（新增+淘汰）的商品数量为{len(diff)}")
                if condition2:
                    reasons.append(f"淘汰商品的排名变动超过{threshold2}名")
                risk_info.append((year, reasons))

        # 生成风险描述
        if risk_info:
            risk_descs = []
            for year, reasons in risk_info:
                desc = f"{year}年相比前一年" + "，".join(reasons)
                risk_descs.append(desc)
            risk_desc = (
                "风险描述：最近4年以来，采购商品种类变化较大。其中，"
                + "；".join(risk_descs)
                + "。这些变化可能反映了企业生产经营调整或市场变化。但如果与实际生产经营不符，则需要关注是否存在虚开发票或其他异常交易的风险。"
            )
        else:
            risk_desc = "风险描述：该指标项未检测到⻛险"

        return result, risk_desc

    # 3.8
    def calculate_voided_ratio(self, dt, data, threshold1=0.05, threshold2=0.5):
        """
        3.8 计算近 12 个月的作废金额比例

        Args:
            current_date (str): 当前日期，格式为 'YYYYMM'
            data (dict): 包含发票数据的字典

        Returns:
            tuple: 近 12 个月作废金额比例的列表和风险描述
        """
        dt_format = datetime.strptime(dt, "%Y%m")
        # end_month = (dt_format - relativedelta(months=1)).strftime("%Y%m")
        # start_month = (dt_format - relativedelta(months=12)).strftime("%Y%m")

        # 生成近 12 个月的月份列表
        months = [
            (dt_format - relativedelta(months=i)).strftime("%Y%m") for i in range(1, 13)
        ][::-1]
        result = []
        risk_diff = {}
        risk_over = {}
        all_zero = True
        # invStatus_dict = {"0": "正常", "01": "作废", "2": "红冲", "3": "失控", "4": "异常"}
        invStatus_dict = {"0": "正常", "1": "作废", "2": "红冲", "3": "失控", "4": "异常"}
        for month_str in months:
            voided_count = 0
            total_count = 0
            invalid_amount = 0.0
            total_amount = 0.0

            # 统计当前月数据
            for item in data["data"]["items"]:
                belong_month = item.get("belongMonth", "").replace("/", "")
                sign = item.get("sign", "").strip()
                if sign != "销项":
                    continue  # 只处理销项发票
                if item["sellerTaxIde"] != self.company_taxid:
                    continue
                company_name = item["sellerName"]
                if belong_month != month_str:
                    continue
                if not item["invoiceTypeName"].__contains__("增值税"):
                    continue
                total_count += 1
                invalid_sign = item.get("invalidSign", "false")
                inv_status = item["invStatus"]
                if invalid_sign == "true" or inv_status == "01":
                    voided_count += 1
                    invalid_amount += float(item.get("amount", 0))
                total_amount += float(item.get("amount", 0))

            # 计算比例
            amount_ratio = invalid_amount / total_amount if total_amount else 0
            cnt_ratio = voided_count / total_count if total_count else 0

            # 风险条件1: 计算环比
            if len(result) > 0:
                res = result[-1]
                prev_avg = res["作废金额(含税,元)"] / res["作废份数"] if res["作废份数"] else 0
                curr_avg = invalid_amount / voided_count if voided_count else 0
                monthly_avg = (curr_avg - prev_avg) / prev_avg if prev_avg else 0
                if abs(monthly_avg) > threshold2:
                    risk_diff[month_str] = {
                        "作废比例": f"{round(cnt_ratio * 100, 2)}%",
                        "变动率": f"{round(monthly_avg * 100, 2)}%",
                    }

            # 风险条件2: 比较阈值
            if cnt_ratio > threshold1:
                risk_over[month_str] = {"作废比例": f"{round(cnt_ratio * 100, 2)}%"}

            # 存储结果
            if voided_count or total_count or invalid_amount:
                all_zero = False
            result.append(
                {
                    "所属月份": f"{month_str[:4]}年{month_str[4:]}月",
                    "作废份数": voided_count,
                    "总份数": total_count,
                    "作废金额(含税,元)": "{:,.2f}".format(invalid_amount, 2),
                    "作废比例": "{:,.2f}".format(cnt_ratio * 100, 2) + "%"
                    if cnt_ratio
                    else "0.00%",
                }
            )
        if all_zero:
            default_result = [{"所属月份": "", "作废份数": "", "总份数": "", "作废比例": ""}]
            default_risk_desc = "风险描述：该指标项未检测到⻛险"
            return default_result, default_risk_desc

        # 生成风险描述
        risk_desc = "风险描述："
        if risk_diff or risk_over:
            risk_desc += "连续12个月中，"
            # 输出环比异常
            if risk_diff:
                invalid_desc = ", ".join(
                    [
                        f"{Utils.convert_to_year_month(key)}发票作废率{value['作废比例']}，环比变动{value['变动率']}"
                        for key, value in risk_diff.items()
                    ]
                )
                risk_desc += f"{invalid_desc}。"
            # 输出阈值异常
            if risk_over:
                if risk_diff:
                    risk_desc += "此外，"
                over_desc = ", ".join(
                    [
                        f"{Utils.convert_to_year_month(key)}发票作废率{value['作废比例']}超过阈值{threshold1}"
                        for key, value in risk_over.items()
                    ]
                )
                risk_desc += f"{over_desc}。"
            # 风险总结
            risk_desc += "这些异常暗示企业可能存在发票管理混乱、虚开发票或其他违规操作，增加税务稽查风险。建议加强发票管理，规范开票流程。"
        else:
            risk_desc += "该指标项未检测到⻛险"

        return result, risk_desc

    # 3.9
    def calculate_hc_ratio(self, dt, data, threshold1=0.05, threshold2=0.5):
        """
        计算近 12 个月的红冲金额比例
        3.9 增值税发票大量红冲疑点检查

        Args:
            current_date (str): 当前日期，格式为 'YYYYMM'
            data (dict): 包含发票数据的字典

        Returns:
            list: 包含近 12 个月红冲金额比例的列表，格式为:
                [
                    {
                        "所属月份": 202403,
                        "红冲份数": 2,
                        "总份数": 10,
                        "红冲金额（元）": 1234.5,
                        "红冲比例": "20%"
                    },
                    ...
                ]
        """
        # 将 current_date 转换为 datetime 对象
        # current = datetime.strptime(current_date, '%Y%m')
        dt_format = datetime.strptime(dt, "%Y%m")
        # 初始化近 12 个月的月份列表
        # months = [(current - relativedelta(months=i)).strftime("%Y%m") for i in range(12)]
        months = [
            (dt_format - relativedelta(months=i)).strftime("%Y%m") for i in range(1, 13)
        ][::-1]
        # 初始化结果列表
        result = []
        risk_diff = dict()
        risk_over = dict()
        all_zero = True

        # 遍历近 12 个月的月份
        for month in months:
            # 初始化统计变量
            hc_count = 0  # 红冲份数
            total_count = 0  # 总份数
            hc_amount = 0.0  # 红冲金额
            total_amount = 0.0  # 总金额

            # 遍历发票数据
            for item in data["data"]["items"]:
                # 获取 belongMonth
                belong_month = item.get("belongMonth", "")
                if not belong_month:
                    continue
                # 将 'YYYY/MM' 格式的 belongMonth 转换为 'YYYYMM'
                belong_month = belong_month.replace("/", "")

                # 如果 belongMonth 不是当前月份，跳过
                if belong_month != month:
                    continue

                sign = item.get("sign", "").strip()
                if sign != "销项":
                    continue  # 只处理销项发票
                if item["sellerTaxIde"] != self.company_taxid:
                    continue
                company_name = item["sellerName"]
                if not item["invoiceTypeName"].__contains__("增值税"):
                    continue

                # 统计总份数
                total_count += 1

                # 判断是否作废
                hc_sign = item.get("hc", "false")
                inv_status = item["invStatus"]

                # invStatus_dict = {'0':'正常','1':'作废','2':'红冲','3':'失控','4':'异常'}
                if hc_sign == "是" or inv_status == "2":
                    hc_count += 1
                    hc_amount += float(item.get("amount", 0))
                total_amount += float(item.get("amount", 0))

            # 计算作废比例
            amount_ratio = (hc_amount / total_amount) if total_amount > 0 else 0
            cnt_ratio = (hc_count / total_count) if total_count > 0 else 0

            # 风险条件1:计算环比
            if month > min(months):
                # prev_month = result[-1]["所属月份"]
                prev_voided_count = result[-1]["红冲份数"]
                prev_invalid_amount = result[-1]["红冲金额(含税,元)"]
                pre_avg = (
                    prev_invalid_amount / prev_voided_count
                    if prev_voided_count
                    else 0.0
                )
                current_avg = hc_amount / hc_count if hc_count else 0.0
                monthly_avg = (current_avg - pre_avg) / pre_avg if pre_avg else 0.0
                if abs(monthly_avg) > threshold2:
                    risk_diff[month] = {
                        "红冲比例": "{:,.2f}".format(cnt_ratio * 100, 2) + "%"
                        if cnt_ratio is not None
                        else "0.00%",
                        "变动率": "{:,.2f}".format(monthly_avg * 100, 2) + "%"
                        if cnt_ratio is not None
                        else "0.00%",
                    }
            # 风险条件2: 比较阈值
            if amount_ratio > threshold1:
                risk_over[month] = {
                    "红冲比例": "{:,.2f}".format(cnt_ratio * 100, 2) + "%"
                    if cnt_ratio is not None
                    else "0.00%"
                }

            if hc_count or total_count or hc_amount:
                all_zero = False

            # 将结果存入列表
            result.append(
                {
                    "所属月份": f"{month[:4]}年{month[4:]}月",  # 转换为整数
                    "红冲份数": hc_count,
                    "总份数": total_count,
                    "红冲金额(含税,元)": "{:,.2f}".format(hc_amount, 2),
                    "红冲比例": "{:,.2f}".format(cnt_ratio * 100, 2) + "%"
                    if cnt_ratio is not None
                    else "0.00%",  # 保留一位小数并添加百分号
                }
            )
        if all_zero:
            default_result = [
                {"所属月份": "", "红冲份数": "", "总份数": "", "红冲金额(含税,元)": "", "红冲比例": ""}
            ]
            default_risk_desc = "风险描述：该指标项未检测到⻛险"
            return default_result, default_risk_desc
        risk_desc = "风险描述："
        if risk_diff or risk_over:
            risk_desc += "连续12个月中，"
            if risk_diff:
                invalid_desc = ""
                for key, value in risk_diff.items():
                    invalid_desc += f"""，{Utils.convert_to_year_month(key)}的发票红冲率是{value["红冲比例"]}，环比变动{value["变动率"]}"""
                risk_desc += f"""{'、'.join([Utils.convert_to_year_month(x) for x in risk_diff.keys()])}的发票红冲率出现了异常波动。其中{invalid_desc}。"""
            if risk_over:
                invalid_desc = "、".join(
                    [
                        f"""{Utils.convert_to_year_month(x[0])}({x[1]["红冲比例"]})"""
                        for x in risk_over.items()
                    ]
                )
                if risk_diff:
                    risk_desc += "此外，"
                risk_desc += f"""{invalid_desc}的发票红冲率超过了预设阈值{threshold1}。"""
            risk_desc += f"""异常波动，可能反映企业存在交易错误、发票管理混乱或其他潜在问题，增加税务风险。建议加强内部控制，规范业务流程。"""
        else:
            risk_desc += "该指标项未检测到⻛险"
        return result, risk_desc

    # 3.10
    def calculate_zero_tax_ratio(self, dt, data, threshold1=0.1, threshold2=100000):
        """
        3.10 大量取得税额为零的发票风险检查
        计算近 12 个月的零税额开票金额占比，按年份统计，并识别风险

        Args:
            current_date (str): 当前日期，格式为 'YYYYMM'
            data (dict): 包含发票数据的字典

        Returns:
            tuple: 包含返回值：
                - list: 包含近 12 个月零税额开票金额占比的列表
                - str: 风险描述语句，例如：'风险描述： 该指标项未检测到风险' 或 '风险描述： 高比例的零税额发票可能存在风险...'
        """
        # 将 current_date 转换为 datetime 对象
        dt_format = datetime.strptime(dt, "%Y%m")
        months = [
            (dt_format - relativedelta(months=i)).strftime("%Y%m") for i in range(1, 13)
        ][::-1]
        default_result = [
            {"所属月份": "", "零税额开票金额(含税,元)": "", "非零税额开票金额(含税,元)": "", "零税额开票金额占比": ""}
        ]
        default_risk_desc = "风险描述：该指标项未检测到风险"
        # 初始化结果列表和风险信息
        result = []
        risk = {}  # 用于记录风险年份及其占比
        all_zero = True
        # 遍历每个年度
        for month in months:
            amount_zero = 0.0  # 零税额发票金额
            amount_non_zero = 0.0  # 非零税额发票金额

            # 遍历发票数据
            for item in data["data"]["items"]:
                belong_month = item.get("belongMonth", "")
                if not belong_month:
                    continue
                belong_month = belong_month.replace("/", "")
                if belong_month != month:
                    continue

                sign = item.get("sign", "").strip()
                if sign != "进项":
                    continue
                if item["buyerTaxIde"] != self.company_taxid:
                    continue
                company_name = item["buyerName"]

                # 获取发票金额
                invoice_amount = float(item.get("amount", 0))  # 含税金额
                tax_money = float(item.get("taxMoney", 0))  # 税额
                # print("invoice_amount", item)

                if tax_money == 0:
                    amount_zero += invoice_amount
                else:
                    amount_non_zero += invoice_amount

            # 计算总金额
            total_amount = amount_zero + amount_non_zero
            # if total_amount < 100000: continue
            ratio = amount_zero / total_amount if total_amount > 0 else None
            formatted_ratio = (
                "{:,.2f}".format(ratio * 100, 2) + "%" if ratio is not None else "--"
            )

            if amount_zero or amount_non_zero:
                all_zero = False

            # 检查总金额是否超过 10 万元
            if total_amount > threshold2:
                # 添加到结果列表
                result.append(
                    {
                        "所属月份": f"{month[:4]}年{month[4:]}月",
                        "零税额开票金额(含税,元)": "{:,.2f}".format(amount_zero, 2),
                        "非零税额开票金额(含税,元)": "{:,.2f}".format(amount_non_zero, 2),
                        "零税额开票金额占比": formatted_ratio,
                    }
                )
                if ratio and ratio > threshold1:
                    risk[month] = formatted_ratio
        if all_zero:
            return default_result, default_risk_desc
        if not result:
            return default_result, default_risk_desc
        # 生成风险描述语句
        formatted_threshold = "{:,.2f}".format(threshold1 * 100, 2) + "%"
        if risk:
            risk_desc = f"风险描述：近12个月内，在取得发票金额合计超过{threshold2}元的情况下，"
            risk_parts = [
                f"{month[:4]}年{month[4:]}月（{ratio}）" for month, ratio in risk.items()
            ]
            risk_desc += (
                "、".join(risk_parts) + f"取得税额为零的发票金额占比超过预设阈值（{formatted_threshold}）。"
            )
            risk_desc += " 大量取得税额为零的发票，可能与企业实际经营情况不符，可能存在虚开发票、取得不合规发票等风险，需要进一步核查。"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 3.11
    def calculate_outside_province_ratio(
        self, dt, data, threshold1=0.5, threshold2=100000
    ):
        """
        3.11 向外省企业开具发票占比过多检查
        计算近 12 个月的省外发票金额占比，并识别风险

        Args:
            current_date (str): 当前日期，格式为 'YYYYMM'
            data (dict): 包含发票数据的字典

        Returns:
            tuple: 包含返回值：
                - list: 包含近 12 个月省外发票金额占比的列表
                - str: 风险描述语句，例如：'风险描述： 该指标项未检测到风险' 或 '风险描述： 高比例的省外发票可能存在风险...'
        """
        # 预设阈值
        # THRESHOLD = 50  # 百分比，例如 50%

        # 将 current_date 转换为 datetime 对象
        # current = datetime.datetime.strptime(current_date, '%Y%m')
        dt_format = datetime.strptime(dt, "%Y%m")
        months = [
            (dt_format - relativedelta(months=i)).strftime("%Y%m") for i in range(1, 13)
        ][::-1]

        # 获取涉及的年份
        # years = set([month[:4] for month in months])  # 提取年份并去重
        all_zero = True
        default_result = [
            {
                "所属月份": "",
                "省内发票份数": "",
                "省内发票金额(含税,元)": "",
                "省外发票份数": "",
                "省外发票金额(含税,元)": "",
                "省外发票金额占比": "",
            }
        ]
        default_risk_desc = "风险描述：该指标项未检测到风险"
        # 初始化结果字典
        result = {
            month: {
                "local_count": 0,
                "local_amount": 0.0,
                "outside_count": 0,
                "outside_amount": 0.0,
                "ratio": None,
            }
            for month in months
        }

        # 遍历发票数据
        for item in data["data"]["items"]:
            # 获取进项销项信息
            sign = item.get("sign", "").strip()
            if sign != "销项":
                continue  # 只处理销项发票
            if item["sellerTaxIde"] != self.company_taxid:
                continue
            company_name = item["sellerName"]

            # 获取 belongMonth
            belong_month = item.get("belongMonth", "")
            if not belong_month:
                continue
            # 将 'YYYY/MM' 格式的 belongMonth 转换为 'YYYYMM'
            belong_month = belong_month.replace("/", "")

            # 如果 belongMonth 不在近 12 个月的月份列表中，跳过
            if belong_month not in months:
                continue

            # 获取税号（统一社会信用代码）
            seller_id = item.get("sellerTaxIde", "")
            buyer_id = item.get("buyerTaxIde", "")

            # 提取行政区划码（第3位至第8位）
            def extract_province_code(tax_id):
                if tax_id and len(tax_id) >= 8:  # 确保税号长度足够
                    return tax_id[2:4]  # 提取第3位至第4位 表示省份
                return None

            seller_province_code = extract_province_code(seller_id)
            buyer_province_code = extract_province_code(buyer_id)

            # 判断是否省内
            is_local = False  # 默认认为是省内
            if seller_province_code and buyer_province_code:
                # 如果行政区划码相同，则为省内
                if seller_province_code == buyer_province_code:
                    is_local = True
                else:
                    is_local = False

            # 获取发票金额
            invoice_amount = float(item.get("amount", 0))

            # 根据是否是省内累加份数和金额
            if is_local:
                result[belong_month]["local_count"] += 1
                result[belong_month]["local_amount"] += invoice_amount
            else:
                # print(item['sellerName'], item['buyerName'], belong_month, result[belong_month]['outside_count'], result[belong_month]['outside_amount'])
                result[belong_month]["outside_count"] += 1
                result[belong_month]["outside_amount"] += invoice_amount

        # 计算每个年份的省外发票金额占比
        final_result = []
        risk = []  # 用于记录风险年份及其占比

        for month in months:
            total_amount = (
                result[month]["local_amount"] + result[month]["outside_amount"]
            )
            ratio = (
                (result[month]["outside_amount"] / total_amount)
                if total_amount > 0
                else 0
            )
            formatted_ratio = (
                "{:,.2f}".format(ratio * 100) + "%" if ratio is not None else "0.00%"
            )
            if (
                result[month]["local_amount"]
                or result[month]["local_count"]
                or result[month]["outside_count"]
                or result[month]["outside_amount"]
            ):
                all_zero = False
            if total_amount > threshold2:
                # 添加到结果列表,只添加10w以上的。
                final_result.append(
                    {
                        "所属月份": f"{month[:4]}年{month[4:]}月",
                        "省内发票份数": result[month]["local_count"],
                        "省内发票金额(含税,元)": "{:,.2f}".format(
                            result[month]["local_amount"], 2
                        ),
                        "省外发票份数": result[month]["outside_count"],
                        "省外发票金额(含税,元)": "{:,.2f}".format(
                            result[month]["outside_amount"], 2
                        ),
                        "省外发票金额占比": formatted_ratio,
                    }
                )
                # 风险识别逻辑
                if ratio and ratio > threshold1:
                    risk.append(f"{month[:4]}年{month[4:]}月（{formatted_ratio}）")
        if all_zero:
            return default_result, default_risk_desc
        if not final_result:
            return default_result, default_risk_desc
        # 生成风险描述语句
        formatted_threshold1 = "{:,.2f}".format(threshold1 * 100) + "%"
        if risk:
            risk_desc = (
                f"风险描述：近12个月内，在取得发票金额合计超过{threshold2}元的条件下，"
                + "、".join(risk)
                + f"向省外企业开具发票金额占比超过预设阈值（{formatted_threshold1}），且与企业经营范围或客户群体不符。"
            )
            risk_desc += "省外销售占比过高且不符合常理，可能存在虚开发票、转移利润等风险，需要关注交易的真实性和合理性。"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return final_result, risk_desc

    # 3.12
    def calculate_proxy_invoice_ratio(
        self, dt, data, threshold1=0.1, threshold2=100000
    ):
        """
        3.12 收取代开发票过多检查
        计算近 12 个月的接收代开发票金额占比，并识别风险

        Args:
            current_date (str): 当前日期，格式为 'YYYYMM'
            data (dict): 包含发票数据的字典

        Returns:
            tuple: 包含返回值：
                - list: 近 12 个月接收代开发票金额占比的列表，格式为:
                    [
                        {
                            "所属年度": "2021",
                            "非代开发票份数": 40,
                            "非代开发票金额（元）": 1234.5,
                            "接收代开发票份数": 10,
                            "接收代开发票金额（元）": 1234.5,
                            "接收代开发票金额占比": "50.1%"
                        },
                        ...
                    ]
                - str: 风险描述语句，例如：'风险描述： 该指标项未检测到风险' 或 '风险描述： 高比例的代开发票可能存在风险...'
        """
        # 将 current_date 转换为 datetime 对象
        # current = datetime.datetime.strptime(current_date, '%Y%m')
        dt_format = datetime.strptime(dt, "%Y%m")
        months = [
            (dt_format - relativedelta(months=i)).strftime("%Y%m") for i in range(1, 13)
        ][::-1]

        # # 初始化近 12 个月的月份列表
        # months = []
        # for i in range(12):
        #     month = current - datetime.timedelta(days=30 * i)
        #     months.append(month.strftime('%Y%m'))

        # 获取涉及的年份
        # years = set([month[:4] for month in months])  # 提取年份并去重

        # 初始化结果字典
        all_zero = True
        result = {
            month: {
                "proxy_count": 0,
                "proxy_amount": 0.0,
                "non_proxy_count": 0,
                "non_proxy_amount": 0.0,
                "ratio": None,
            }
            for month in months
        }

        # 遍历发票数据
        for item in data["data"]["items"]:
            # 获取 belongMonth
            belong_month = item.get("belongMonth", "")
            if not belong_month:
                continue

            # 将 'YYYY/MM' 格式的 belongMonth 转换为 'YYYYMM'
            belong_month = belong_month.replace("/", "")

            # 如果 belongMonth 不在近 12 个月的月份列表中，跳过
            if belong_month not in months:
                continue
            sign = item.get("sign", "").strip()
            if sign != "进项":
                continue
            if item["buyerTaxIde"] != self.company_taxid:
                continue
            company_name = item["buyerName"]

            # 判断是否是代开发票
            is_proxy = "代开" in str(item.get("remark", "")) or "代开" in str(
                item.get("sellerName", "")
            )

            # 获取发票金额
            invoice_amount = float(item.get("amount", 0))
            # 根据是否是代开发票累加份数和金额
            if is_proxy:
                result[belong_month]["proxy_count"] += 1
                result[belong_month]["proxy_amount"] += invoice_amount
            else:
                result[belong_month]["non_proxy_count"] += 1
                result[belong_month]["non_proxy_amount"] += invoice_amount
            if (
                result[belong_month]["non_proxy_amount"]
                or result[belong_month]["non_proxy_count"]
                or result[belong_month]["proxy_count"]
                or result[belong_month]["proxy_amount"]
            ):
                all_zero = False
        # 计算每个月份的接收代开发票金额占比
        final_result = []
        risk = {}  # 用于记录风险年份及其占比

        for month in sorted(result.keys()):
            total_amount = (
                result[month]["proxy_amount"] + result[month]["non_proxy_amount"]
            )
            ratio = (
                (result[month]["proxy_amount"] / total_amount)
                if total_amount > 0
                else 0
            )
            formatted_ratio = "{:,.2f}".format(ratio * 100, 2) + "%"
            # if total_amount >= threshold2:  # 检查总金额是否超过 10 万元
            # ratio = (result[year]['proxy_amount'] / total_amount) if total_amount > 0 else 0
            # 风险识别逻辑
            if total_amount > threshold2:
                final_result.append(
                    {
                        "所属年度": f"{month[:4]}年{month[4:]}月",
                        "非代开发票份数": result[month]["non_proxy_count"],
                        "非代开发票金额(含税,元)": "{:,.2f}".format(
                            result[month]["non_proxy_amount"], 2
                        ),
                        "接收代开发票份数": result[month]["proxy_count"],
                        "接收代开发票金额(含税,元)": "{:,.2f}".format(
                            result[month]["proxy_amount"], 2
                        ),
                        "接收代开发票金额占比": formatted_ratio
                        if ratio is not None
                        else "--",  # 保留一位小数并添加百分号
                    }
                )

            if ratio > threshold1:  # 预设阈值为 10%
                risk[month] = formatted_ratio

        if all_zero:
            default_result = [
                {
                    "所属年度": "",
                    "非代开发票份数": "",
                    "非代开发票金额(含税,元)": "",
                    "接收代开发票份数": "",
                    "接收代开发票金额(含税,元)": "",
                    "接收代开发票金额占比": "",
                }
            ]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc

        # 生成风险描述语句
        formatted_threshold1 = "{:,.2f}".format(threshold1 * 100, 2) + "%"
        if risk:
            risk_desc = f"风险描述：近12个月内，在取得发票金额合计超过{threshold2}元的条件下"
            risk_parts = [
                f"{month[:4]}年{month[4:]}月（{ratio}）" for year, ratio in risk.items()
            ]
            risk_desc += (
                "、".join(risk_parts) + f"的收取代开发票金额占比超过阈值{formatted_threshold1}。"
            )
            risk_desc += "高比例的代开发票可能暗示企业存在业务不合规、无法取得正规发票等问题，可能导致无法正常抵扣进项税额，增加税务风险。"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"
        return final_result, risk_desc

    # 3.13
    def calculate_service_fee_check(self, dt, data, threshold1=0.3, threshold2=100000):
        # 3.13 取得“咨询服务费”“会议服务费”“手续费”发票风险检查
        # 将 current_date 转换为 datetime 对象
        dt_format = datetime.strptime(dt, "%Y%m")
        # end_month = (dt_format - relativedelta(months=1)).strftime("%Y%m")
        # start_month = (dt_format - relativedelta(months=13)).strftime("%Y%m")

        months = [
            (dt_format - relativedelta(months=i)).strftime("%Y%m") for i in range(1, 13)
        ][::-1]
        result = []
        risk_flag = 0

        # 遍历发票数据
        all_zero = True
        serve_amount = {
            month: {
                "咨询服务费": defaultdict(lambda: 0.00),
                "会议服务费": defaultdict(lambda: 0.00),
                "手续费": defaultdict(lambda: 0.00),
                "其他现代服务费": defaultdict(lambda: 0.00),
            }
            for month in months
        }
        # serve_type_amount = {month: defaultdict(lambda: 0.00) for month in months}
        total_amount_dict = {month: 0.00 for month in months}
        specific_amount_dict = {month: 0.00 for month in months}
        for item in data["data"]["items"]:
            # 获取 belongMonth
            belong_month = item.get("belongMonth", "").replace("/", "")
            if belong_month not in months:
                continue
            sign = item.get("sign", "").strip()
            if sign != "进项":
                continue
            if item["buyerTaxIde"] != self.company_taxid:
                continue
            company_name = item["buyerName"]
            for detail in item.get("details", []):
                item_title = detail.get("itemTitle", "").strip()
                amount = float(detail.get("jshj", 0.00))
                remark = str(item.get("remark", ""))
                total_amount_dict[belong_month] += amount
                if not item_title:
                    continue
                if "咨询" in item_title or "咨询" in remark:
                    serve_amount[belong_month]["咨询服务费"][item_title] += amount
                    specific_amount_dict[belong_month] += amount
                elif "会议" in item_title or "会议" in remark:
                    serve_amount[belong_month]["会议服务费"][item_title] += amount
                    specific_amount_dict[belong_month] += amount
                elif "手续" in item_title or "手续" in remark:
                    serve_amount[belong_month]["手续费"][item_title] += amount
                    specific_amount_dict[belong_month] += amount
                elif "服务费" in item_title or "服务费" in remark:
                    serve_amount[belong_month]["其他现代服务费"][item_title] += amount
                    specific_amount_dict[belong_month] += amount
        risk1 = {month: "" for month in months}
        risk2 = {month: "" for month in months}
        formatted_threshold1 = "{:,.2f}".format(threshold1 * 100, 2) + "%"
        for month in months:
            flag = 0
            # 明细
            if serve_amount[month].values:
                for _type, server_dict in serve_amount[month].items():
                    if server_dict:
                        flag = 1
                        all_zero = False
                        _type_amount = sum([amt for _, amt in server_dict.items()])
                        result.append(
                            {
                                "所属月份": f"{month[:4]}年{month[4:]}月",
                                "服务大类": _type,
                                "服务明细": ",".join(
                                    [
                                        f"{server}({str('{:,.2f}'.format(amt, 2))})"
                                        for server, amt in server_dict.items()
                                    ]
                                ),
                                "服务大类开票金额(含税,元)": "{:,.2f}".format(_type_amount, 2),
                                "服务总金额(含税,元)": "{:,.2f}".format(
                                    specific_amount_dict[month], 2
                                ),
                                "总金额(含税,元)": "{:,.2f}".format(
                                    total_amount_dict[month], 2
                                ),
                            }
                        )
            if not flag:
                result.append(
                    {
                        "所属月份": f"{month[:4]}年{month[4:]}月",
                        "服务大类": "",
                        "服务明细": "",
                        "服务大类开票金额(含税,元)": "0.00",
                        "服务总金额(含税,元)": "0.00",
                        "总金额(含税,元)": "0.00",
                    }
                )
            # 服务金额：
            serve_ratio = (
                specific_amount_dict[month] / total_amount_dict[month]
                if total_amount_dict[month]
                else 0.00
            )
            formatted_serve_ratio = "{:,.2f}".format(serve_ratio * 100, 2) + "%"
            formatted_specific_amt = "{:,.2f}".format(specific_amount_dict[month], 2)

            if serve_ratio > threshold1:
                risk_flag = 1
                risk1[
                    month
                ] = f"服务费(含税)占比高于{formatted_threshold1}（{formatted_serve_ratio}）"
                # print( f"服务费(含税)占比高于{formatted_threshold1}（{formatted_serve_ratio}）")
            if specific_amount_dict[month] > threshold2:
                risk_flag = 1
                risk2[month] = f"服务费总金额超过{threshold2}元({formatted_specific_amt})"
                # print(f"服务费总金额超过{threshold2}元({formatted_specific_amt})")
        if all_zero:
            default_result = [
                {
                    "所属月份": "",
                    "服务大类": "",
                    "服务明细": "",
                    "服务大类开票金额(含税,元)": "",
                    "服务总金额(含税,元)": "",
                    "总金额(含税,元)": "",
                }
            ]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc

        if risk_flag:
            risk_desc = f"风险描述： 近12个月内，“咨询服务费”、“会议费”、“手续费”“其他现代服务费”等服务品目发票金额较大。其中，\n"
            risk_parts = [
                f"{month[:4]}年{month[4:]}月, {','.join([risk1[month],risk2[month]])}"
                for month in months
                if risk1[month] or risk2[month]
            ]
            risk_desc += ";".join(risk_parts) + "。"
            risk_desc += (
                " 服务类发票金额较大且缺乏佐证，容易被税务机关质疑其真实性，可能被认定为虚开发票，带来税务风险。建议加强合同管理，保留服务内容的证明材料。"
            )

        else:
            risk_desc = "风险描述： 该指标项未检测到风险"
        # print(risk_desc)
        # 风险判定
        # risk_desc = "风险描述："
        # if total_amount > threshold2 :  # 累计金额超过10万元
        #     risk_desc += "\n近12月累计开票金额超过10万元，服务类发票金额较大且缺乏佐证，容易被税务机关质疑其真实性，可能被认定为虚开发票，带来税务风险。建议加强合同管理，保留服务内容的证明材料。"
        # else:
        #     risk_desc += "\n未检测到风险。"

        return result, risk_desc

    # 3.14
    def check_invoice_limit_risk(self, dt, data, threshold1=100000, threshold2=300000):
        # 3.14 顶额开票异常检查
        """
        检查发票限额风险，并返回风险发票信息和风险描述

        参数:
            current_date (str): 指定的年月，格式为 'YYYYMM'
            data (dict): 包含发票数据的字典

        返回:
            tuple: 包含两个元素：
                - list: 风险发票信息列表
                - str: 风险描述语句
        """
        dt_format = datetime.strptime(dt, "%Y%m")
        default_result = [{"发票状态": "", "开票日期": "", "发票种类": "", "发票总金额(含税,元)": ""}]
        default_risk_desc = "风险描述：该指标项未检测到风险"
        # end_month = (dt_format - relativedelta(months=1)).strftime("%Y%m")
        # start_month = (dt_format - relativedelta(months=13)).strftime("%Y%m")

        months = [
            (dt_format - relativedelta(months=i)).strftime("%Y%m") for i in range(1, 13)
        ][::-1]
        # 定义发票限额和阈值比例
        # INVOICE_LIMITS = {
        #     "09c": 100000,  # 数电票（普通发票）
        #     "10": 100000,   # 增值税电子普通发票
        #     "04": 100000,   # 增值税普通发票
        #     "09s": 100000,  # 数电票（增值税专用发票）
        #     "11": 100000    # 增值税普通发票（卷式）
        # }
        # THRESHOLD_RATIO = 0.95

        # risk_invoices = []
        result = []
        all_zero = True
        monthly_totals = {}  # 用于存储各月份的开票总金额
        # invStatus_dict = {"0": "正常", "01": "作废", "2": "红冲", "3": "失控", "4": "异常"}
        invStatus_dict = {"0": "正常", "1": "作废", "2": "红冲", "3": "失控", "4": "异常"}
        for item in data["data"]["items"]:
            belong_month = item.get("belongMonth", "").replace("/", "")
            if belong_month not in months:
                continue
            inv_type_name = item["invoiceTypeName"]  # 发票种类

            inv_status = invStatus_dict.get(item["invStatus"], "")  # 发票状态
            # invoice_status = item['invoiceStatus'] # 状态
            amount = float(item.get("amount", 0.00))  # 含税金额
            if amount:
                all_zero = False
            create_date = item["createDate"]  # 开票日期
            # month_key = create_date[:7]  # YYYY-MM格式

            if item["sign"] != "销项":
                continue
            if item["sellerTaxIde"] != self.company_taxid:
                continue
            company_name = item["sellerName"]

            # 记录各月份的开票总金额
            if belong_month not in monthly_totals:
                monthly_totals[belong_month] = 0.00
            monthly_totals[belong_month] += amount

            # 检查单张发票的金额是否超过限额
            # if inv_type in INVOICE_LIMITS:
            #     # limit = INVOICE_LIMITS[inv_type]
            # result.append({
            #         '发票状态': inv_status,
            #         '开票日期': item['createDate'],
            #         '发票种类': item['invoiceTypeName'],
            #         '发票总金额(含税,元)': "{:,.2f}".format(amount, 2) + f"(大额)" if amount >= threshold1 else "{:,.2f}".format(amount, 2)
            #     })
            # # 只展示定额发票
            if amount >= threshold1:
                result.append(
                    {
                        "发票状态": inv_status,
                        "开票日期": item["createDate"],
                        "发票种类": item["invoiceTypeName"],
                        "发票总金额(含税,元)": "{:,.2f}".format(amount, 2),
                    }
                )

        # 按开票日期升序排序
        # risk_invoices = sorted(risk_invoices, key=lambda x: x['开票日期'])
        result = sorted(result, key=lambda x: x["开票日期"])

        # 风险识别规则：单个月开票总金额超过30万元
        risky_months = []
        for month, total in monthly_totals.items():
            if total >= threshold2:
                formatted_total = "{:,.2f}".format(total)
                risky_months.append(f"{month[:4]}年{month[4:]}月（{formatted_total}元）")

        # 生成风险描述语句
        if all_zero:
            return default_result, default_risk_desc
        if not result:
            return default_result, default_risk_desc
        risk_desc = "风险描述："
        if risky_months:
            formatted_threshold1 = "{:,.2f}".format(threshold1)
            formatted_threshold2 = "{:,.2f}".format(threshold2)
            risk_desc += f"近12个月内，以上单张发票金额（含税）均超过{formatted_threshold1}元(大额)。其中{'、'.join(risky_months)}的金额超过了月上限{formatted_threshold2}元。"
            risk_desc += "频繁顶额开票可能为了逃避税务监管，例如为了避免开具专票而人为拆分交易，存在较大的税务风险。"
            risk_desc += "建议规范开票行为，避免人为拆分交易。"
        else:
            risk_desc += "该指标项未检测到风险"

        return result, risk_desc

    # 3.15
    def analyze_monthly_invoice(self, dt, data, threshold=0.2):
        """
        3.15 收取个体工商户发票检查
        按月份分析发票数据，并识别风险

        参数:
            currentDate (str): 指定的年月，格式为 'YYYYMM'
            data (Dict): 包含发票数据的字典

        返回:
            tuple: 包含返回值：
                - list: 按月份统计的发票金额和占比
                - str: 风险描述语句
        """

        dt_format = datetime.strptime(dt, "%Y%m")
        # end_month = (dt_format - relativedelta(months=1)).strftime("%Y%m")
        # start_month = (dt_format - relativedelta(months=13)).strftime("%Y%m")

        months = [
            (dt_format - relativedelta(months=i)).strftime("%Y%m") for i in range(1, 13)
        ][::-1]
        risk = {}  # 用于记录风险年份及其占比
        all_zero = True
        monthly_stats = {
            month: {
                "individual_amount": 0.0,
                "non_individual_amount": 0.0,
                "ratio": None,
            }
            for month in months
        }

        # 遍历发票数据
        for item in data["data"]["items"]:
            belong_month = item["belongMonth"].replace(
                "/", ""
            )  # 将 'YYYY/MM' 转换为 'YYYYMM'
            if item["sign"] != "进项":
                continue
            if item["buyerTaxIde"] != self.company_taxid:
                continue
            if not belong_month in months:
                continue
            # 检查是否在最近12个月内
            is_business_flag = False
            company_name = item["buyerName"]
            if item["sign"] == "进项" and str(item["sellerTaxIde"][1:2]) == "2":
                is_business_flag = True
            # elif item['sign'] == '销项' and str(item['buyerTaxIde'][1:2]) == '2':
            #     is_business_flag = True

            # 累加金额
            amount = float(item["amount"])
            if is_business_flag:
                monthly_stats[belong_month]["individual_amount"] += amount
            else:
                monthly_stats[belong_month]["non_individual_amount"] += amount

        # 计算每个年份的个体户发票金额占比
        result = []
        for month, stats in monthly_stats.items():
            individual_amount = stats["individual_amount"]
            non_individual_amount = stats["non_individual_amount"]
            total_amount = individual_amount + non_individual_amount
            if individual_amount or non_individual_amount:
                all_zero = False

            # 提取年份
            # year = month[:4]
            ratio = (individual_amount / total_amount) if total_amount > 0 else 0
            # 风险识别逻辑
            if ratio > threshold:
                risk[month] = "{:,.2f}".format(ratio * 100, 2) + "%"

            # 添加到结果列表
            formatted_ratio = "{:,.2f}".format(ratio * 100, 2) + "%"
            result.append(
                {
                    "所属月份": f"{month[:4]}年{month[4:]}月",
                    "个体户发票金额(含税,元)": "{:,.2f}".format(individual_amount, 2),
                    "非个体户发票金额(含税,元)": "{:,.2f}".format(non_individual_amount, 2),
                    "个体户发票金额占比": formatted_ratio if ratio is not None else "0.00%",
                }
            )

        # 按月份升序排序
        result = sorted(result, key=lambda x: x["所属月份"])
        formatted_threshold = "{:,.2f}".format(threshold * 100, 2) + "%"
        if all_zero:
            default_result = [
                {"所属月份": "", "个体户发票金额(含税,元)": "", "非个体户发票金额(含税,元)": "", "个体户发票金额占比": ""}
            ]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        # 生成风险描述语句
        if risk:
            risk_desc = "风险描述：近12个月内，"
            risk_parts = [
                f"{month[:4]}年{month[4:]}月（{ratio}）" for month, ratio in risk.items()
            ]
            risk_desc += (
                "，".join(risk_parts)
                + f"收取个体工商户发票金额占比超过预设阈值（{formatted_threshold}），且交易金额较大。"
            )
            risk_desc += " 大量收取个体工商户发票，可能存在无法取得正规发票、交易不合规等风险，影响进项税额抵扣，增加税务风险。需要关注交易的真实性和发票的合规性，进一步核查发票来源和交易背景。"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"
        return result, risk_desc

    # 3.16
    def check_large_amounts(self, dt, data, threshold=20000):
        """检查大额整数发票"""
        # 3.16 进项发票中单张较大金额且价税合计为整数
        # 3.16 进项发票中单张较大金额且价税合计为整数
        """数据预处理"""
        dt_format = datetime.strptime(dt, "%Y%m")
        months = [
            (dt_format - relativedelta(months=i)).strftime("%Y%m") for i in range(1, 13)
        ][::-1]
        data = data["data"]["items"]
        df = pd.DataFrame(data)

        all_zero = True

        # 筛选近12个月数据
        df["amount"] = pd.to_numeric(df["amount"], errors="coerce")
        # 筛选条件：
        # 1. 进项发票
        # 2. 金额大于等于阈值
        # 3. 金额为整数
        filtered_df = df[
            (df["buyerTaxIde"] == self.company_taxid)
            & (df["belongMonth"].isin(months))
            & (df["sign"] == "进项")
            & (df["amount"] >= threshold)
            & (df["amount"].apply(Utils.is_integer_amount))
        ]
        # 提取需要的字段
        result = []
        if not filtered_df.empty:
            all_zero = False
        # invStatus_dict = {"0":"正常","01":"作废","2":"红冲","3":"失控","4":"异常"}
        invStatus_dict = {"0": "正常", "1": "作废", "2": "红冲", "3": "失控", "4": "异常"}
        for _, row in filtered_df.iterrows():
            result.append(
                {
                    "销售方名称": row["sellerName"],
                    "发票状态": invStatus_dict[str(row["invStatus"])],
                    "开票日期": row["createDate"],
                    "发票种类": row["invoiceTypeName"],
                    "发票总金额(含税,元)": "{:,.2f}".format(row["amount"], 2),
                }
            )
        formatted_threshold = "{:,.2f}".format(threshold, 2)

        if all_zero:
            default_result = [
                {"销售方名称": "", "发票状态": "", "开票日期": "", "发票种类": "", "发票总金额(含税,元)": ""}
            ]
            default_risk_desc = "⻛险描述：该指标项未检测到⻛险"
            return default_result, default_risk_desc

        if result:
            risk_desc = f"风险描述：进项发票中存在大额整数发票({formatted_threshold}元以上)可能暗示人为操纵，可能存在虚开发票的风险，需要进一步核查发票的来源和真实性。"
        else:
            risk_desc = "⻛险描述：该指标项未检测到⻛险"

        return result, risk_desc

    # 3.17
    def check_invalid_invoices(self, dt, data, threshold=20000):
        # 3.17 收取大额作废发票
        """检查大额整数发票"""
        dt_format = datetime.strptime(dt, "%Y%m")
        months = [
            (dt_format - relativedelta(months=i)).strftime("%Y%m") for i in range(1, 13)
        ][::-1]
        data = data["data"]["items"]
        df = pd.DataFrame(data)

        # 筛选近12个月数据
        df["amount"] = pd.to_numeric(df["amount"], errors="coerce")
        all_zero = True
        # 筛选条件：
        # 1. 进项发票
        # 2. 金额大于等于阈值
        # 3. 发票状态是否作废
        # invStatus_dict = {"0": "正常", "01": "作废", "2": "红冲", "3": "失控", "4": "异常"}
        invStatus_dict = {"0": "正常", "1": "作废", "2": "红冲", "3": "失控", "4": "异常"}
        filtered_df = df[
            (df["buyerTaxIde"] == self.company_taxid)
            & (df["belongMonth"].isin(months))
            & (df["sign"] == "进项")
            & (df["amount"] >= threshold)
            & (df["invStatus"] == "1")
        ]
        if not filtered_df.empty:
            all_zero = False
        # 提取需要的字段
        result = []
        for _, row in filtered_df.iterrows():
            result.append(
                {
                    "销售方名称": row["sellerName"],
                    "发票状态": invStatus_dict[str(row["invStatus"])],
                    "开票日期": row["createDate"],
                    "发票种类": row["invoiceTypeName"],
                    "发票总金额(含税,元)": "{:,.2f}".format(row["amount"], 2),
                }
            )

        formatted_threshold = "{:,.2f}".format(threshold, 2)
        if all_zero:
            default_result = [
                {"销售方名称": "", "发票状态": "", "开票日期": "", "发票种类": "", "发票总金额(含税,元)": ""}
            ]
            default_risk_desc = "⻛险描述：该指标项未检测到⻛险"
            return default_result, default_risk_desc

        if result:
            risk_desc = f"风险描述：存在大额（{formatted_threshold}元以上）进项作废发票可能反映交易存在重大问题，或存在虚开发票后作废以掩盖痕迹的可能，需要重点关注并核实原因。"
        else:
            risk_desc = "⻛险描述：该指标项未检测到⻛险"

        return result, risk_desc

    # 3.18
    def calculate_receive_outside_province_ratio(
        self, dt, data, threshold1=0.5, threshold2=100000
    ):
        """
        3.18 计算近 12 个月的接收省外发票金额占比，并识别风险

        Args:
            current_date (str): 当前日期，格式为 'YYYYMM'
            data (dict): 包含发票数据的字典

        Returns:
            tuple: 包含返回值：
                - list: 包含近 12 个月省外发票金额占比的列表
                - str: 风险描述语句，例如：'风险描述： 该指标项未检测到风险' 或 '风险描述： 高比例的省外发票可能存在风险...'
        """
        # 预设阈值
        # THRESHOLD = 50  # 百分比，例如 50%

        # 将 current_date 转换为 datetime 对象
        dt_format = datetime.strptime(dt, "%Y%m")
        months = [
            (dt_format - relativedelta(months=i)).strftime("%Y%m") for i in range(1, 13)
        ][::-1]

        # 获取涉及的年份
        # years = set([month[:4] for month in months])  # 提取年份并去重
        all_zero = True
        # 初始化结果字典
        result = {
            month: {
                "local_count": 0,
                "local_amount": 0.0,
                "outside_count": 0,
                "outside_amount": 0.0,
                "ratio": None,
            }
            for month in months
        }

        # 遍历发票数据
        for item in data["data"]["items"]:
            # 获取进项销项信息
            sign = item.get("sign", "")
            if sign != "进项":
                continue
            if item["buyerTaxIde"] != self.company_taxid:
                continue
            company_name = item["buyerName"]
            # 获取 belongMonth
            belong_month = item.get("belongMonth", "")
            if not belong_month:
                continue
            # 将 'YYYY/MM' 格式的 belongMonth 转换为 'YYYYMM'
            belong_month = belong_month.replace("/", "")
            # 如果 belongMonth 不在近 12 个月的月份列表中，跳过
            if belong_month not in months:
                continue

            # 获取年份和月份
            # year = belong_month[:4]
            # month = belong_month[4:6]

            # 获取税号（统一社会信用代码）
            seller_id = item.get("sellerTaxIde", "")
            buyer_id = item.get("buyerTaxIde", "")

            # 提取行政区划码（第3位至第8位）
            def extract_province_code(tax_id):
                if len(tax_id) >= 8:  # 确保税号长度足够
                    return tax_id[2:4]  # 提取第3位至第4位 表示省份
                return None

            seller_province_code = extract_province_code(seller_id)
            buyer_province_code = extract_province_code(buyer_id)

            # 判断是否省内
            is_local = False  # 默认认为是省内
            if seller_province_code and buyer_province_code:
                # 如果行政区划码相同，则为省内
                if seller_province_code == buyer_province_code:
                    is_local = True
                else:
                    is_local = False

            # 获取发票金额
            invoice_amount = float(item.get("amount", 0))

            # 根据是否是省内累加份数和金额
            if is_local:
                result[belong_month]["local_count"] += 1
                result[belong_month]["local_amount"] += invoice_amount
            else:
                result[belong_month]["outside_count"] += 1
                result[belong_month]["outside_amount"] += invoice_amount
            if (
                result[belong_month]["local_count"]
                or result[belong_month]["local_amount"]
                or result[belong_month]["outside_count"]
                or result[belong_month]["outside_amount"]
            ):
                all_zero = False
        # 计算每个年份的省外发票金额占比
        final_result = []
        risk = []  # 用于记录风险年份及其占比

        for month in sorted(result.keys()):
            total_amount = (
                result[month]["local_amount"] + result[month]["outside_amount"]
            )
            if total_amount > threshold2:  # 检查总金额是否超过 10 万元
                ratio = (
                    (result[month]["outside_amount"] / total_amount)
                    if total_amount > 0
                    else 0
                )
                formatted_ratio = (
                    "{:,.2f}".format(ratio * 100, 2) + "%" if ratio else "0.00%"
                )
                final_result.append(
                    {
                        "所属月份": f"{month[:4]}年{month[4:]}月",
                        "省内发票份数": result[month]["local_count"],
                        "省内发票金额(含税,元)": "{:,.2f}".format(
                            result[month]["local_amount"], 2
                        ),
                        "省外发票份数": result[month]["outside_count"],
                        "省外发票金额(含税,元)": "{:,.2f}".format(
                            result[month]["outside_amount"], 2
                        ),
                        "省外发票金额占比": formatted_ratio,
                    }
                )
                # 风险识别逻辑
                if ratio > threshold1:
                    risk.append(f"{month[:4]}年{month[4:]}月（{formatted_ratio}）")

        if all_zero:
            default_result = [
                {
                    "所属月份": "",
                    "省内发票份数": "",
                    "省内发票金额(含税,元)": "",
                    "省外发票份数": "",
                    "省外发票金额(含税,元)": "",
                    "省外发票金额占比": "",
                }
            ]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc

        # 生成风险描述语句
        formatted_threshold = "{:,.2f}".format(threshold1 * 100, 2) + "%"
        if risk:
            risk_desc = (
                f"风险描述：{company_name}在近12个月内，"
                + "、".join(risk)
                + f"接收省外企业开具发票金额占比超过预设阈值（{formatted_threshold}）。"
            )
            risk_desc += "省外进项占比过高且不符合常理，可能存在虚开发票、虚增成本等风险，需要关注交易的真实性和合理性。"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return final_result, risk_desc
