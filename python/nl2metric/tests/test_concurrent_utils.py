import pytest
from concurrent.futures import ThreadPoolExecutor
import time
from common.utils.concurrent_utils import synchronized_lru_cache, run_concurrently
import functools
import threading


def test_cache_basic_behavior():
    @synchronized_lru_cache(maxsize=2)
    def square(x):
        return x * x

    assert square(2) == 4  # miss
    assert square(2) == 4  # hit
    info = square.cache_info()
    assert info.hits == 1
    assert info.misses == 1
    assert info.currsize == 1


def test_dict_argument_handling():
    @synchronized_lru_cache()
    def process_dict(d):
        assert isinstance(d, dict), f"实际收到类型：{type(d)}"
        return sum(d.values())

    d1 = {"a": 1, "b": 2}
    d2 = {"a": 1, "b": 2}
    assert process_dict(d1) == 3
    assert process_dict(d2) == 3  # 应命中缓存
    assert process_dict.cache_info().hits == 1


def test_thread_safety():
    counter = 0

    @synchronized_lru_cache()
    def unsafe_operation(n):
        nonlocal counter
        current = counter
        time.sleep(0.01)
        counter = current + 1
        return counter

    def run(x):  # 使用不同参数
        return unsafe_operation(x)  # 每次调用使用不同参数

    with ThreadPoolExecutor(max_workers=10) as executor:
        results = list(executor.map(run, range(10)))  # 传入0-9作为参数

    # 验证每个结果都是唯一的递增序列
    assert results == list(range(1, 11))
    assert counter == 10


def test_cache_eviction():
    @synchronized_lru_cache(maxsize=2)
    def square(x):
        return x * x

    assert square(1) == 1  # miss
    assert square(2) == 4  # miss
    assert square(1) == 1  # hit
    assert square(3) == 9  # miss，触发缓存淘汰
    assert square.cache_info().misses == 3
    assert square.cache_info().currsize == 2


def test_mixed_arguments():
    @synchronized_lru_cache()
    def mixed_args(a, b=0):
        return a + b

    # 第一次调用（miss）
    assert mixed_args(1) == 1

    # 以下调用应全部命中缓存
    assert mixed_args(1, 0) == 1  # 位置参数
    assert mixed_args(a=1) == 1  # 关键字参数
    assert mixed_args(b=0, a=1) == 1  # 不同顺序关键字参数

    info = mixed_args.cache_info()
    assert info.hits == 3, f"实际命中次数：{info.hits}"
    assert info.misses == 1, f"实际未命中次数：{info.misses}"


def test_run__concurrently_simple():
    def task_func(x):
        time.sleep(0.1)
        return x * x

    tasks = [functools.partial(task_func, i) for i in range(10)]
    results = run_concurrently(tasks, max_workers=2, timeout_seconds=60)
    assert results == [i * i for i in range(10)]


def test_run__concurrently_with_different_funcs():
    def task_func(x):
        time.sleep(0.1)
        return x * x

    def task_func2():
        time.sleep(0.1)
        return

    tasks = [functools.partial(task_func2), functools.partial(task_func, 1)]
    assert run_concurrently(tasks, max_workers=2, timeout_seconds=60) == [None, 1]


def test_cache_with_unhashable_arguments():
    # 测试不可哈希类型参数处理
    class CustomClass:
        def __init__(self, x):
            self.x = x

    @synchronized_lru_cache(maxsize=2)
    def process(obj):
        return obj.x * 2

    obj1 = CustomClass(1)
    obj2 = CustomClass(2)

    # 首次调用
    assert process(obj1) == 2  # miss
    assert process(obj1) == 2  # hit
    assert process.cache_info().hits == 1

    # 不同对象调用
    assert process(obj2) == 4  # miss
    assert process.cache_info().misses == 2


def test_nested_structures():
    @synchronized_lru_cache()
    def complex_args(data):
        # 修正递归展开逻辑
        def flatten(items):
            """递归展开嵌套的可迭代对象"""
            for item in items:
                if isinstance(item, dict):
                    yield from flatten(item.values())  # 处理字典类型
                elif isinstance(item, (list, tuple)):
                    yield from flatten(item)  # 处理列表/元组
                else:
                    yield item

        return sum(flatten([data]))  # 从根节点开始展开

    # 测试数据结构保持不变
    data1 = {"a": [1, 2], "b": {"c": [3, 4]}}
    data2 = {"a": [1, 2], "b": {"c": [3, 4]}}  # 相同内容不同对象

    data3 = {"x": [[5], 6], "y": {"z": [7, 8]}}  # 混合嵌套结构

    assert complex_args(data1) == 10  # 1+2+3+4=10 (miss)
    assert complex_args(data2) == 10  # hit
    assert complex_args(data3) == 26  # 5+6+7+8=26 (miss)
    assert complex_args.cache_info().hits == 1


def test_argument_order_independence():
    @synchronized_lru_cache()
    def key_test(a, b, c=3):
        return a + b + c

    # 不同参数顺序应视为相同调用
    assert key_test(1, 2) == 6  # miss
    assert key_test(a=1, b=2) == 6  # hit
    assert key_test(b=2, a=1) == 6  # hit
    assert key_test(1, 2, 3) == 6  # hit
    assert key_test.cache_info().hits == 3


def test_collectionschema_handling():
    # 模拟Milvus Schema使用场景
    from pymilvus import CollectionSchema, FieldSchema, DataType

    schema1 = CollectionSchema(
        [
            FieldSchema("id", DataType.INT64, is_primary=True),
            FieldSchema("vec", DataType.FLOAT_VECTOR, dim=128),
        ]
    )

    schema2 = CollectionSchema(
        [
            FieldSchema("vec", DataType.FLOAT_VECTOR, dim=128),
            FieldSchema("id", DataType.INT64, is_primary=True),
        ]
    )  # 不同顺序但相同语义

    @synchronized_lru_cache()
    def schema_processor(schema):
        return hash(str(schema))

    # 相同schema不同实例
    assert schema_processor(schema1) == schema_processor(schema1)  # hit
    # 不同schema实例
    assert schema_processor(schema1) != schema_processor(schema2)  # 不同缓存条目
