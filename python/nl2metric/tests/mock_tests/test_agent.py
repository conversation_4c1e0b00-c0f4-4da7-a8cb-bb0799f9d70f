from utils import init_mock_env, start_langfuse

init_mock_env()

import json

from http_router.api.params_extract_handler import (
    params_extract_handler,
    ParamsExtractRequest,
)


# ATTATION: python3 main_shared_service.py before this ut
# ATTATION: python3 main_embedding_service.py before this ut
def test_agent():
    fake_passthrough_prompts_q = {
        "nl2metric_group_bys": {"groupBys": ["bch_nme"], "notExistGroupBys": []},
        "nl2metric_time_query": {
            "timeStartFunction": {"year": 2023, "month": 1, "day": 1},
            "timeEndFunction": {"year": 2025, "month": 1, "day": 24},
            "timeGranularity": "month",
        },
        "nl2metric_metrics": {
            "metrics": ["demo_7000_i7000"],
            "notExistMetricNames": [],
        },
        "nl2metric_order_bys": {"limit": 1, "orderBys": ["demo_7000_i7000 desc"]},
        "nl2metric_where": {
            "limit": 1,
            "where": "",
            "select": "bch_nme, demo_7000_i7000",
            "groupby": "bch_nme",
            "orderby": "demo_7000_i7000 desc",
            "where_time": "dt >= '2023-01-01'",
        },
        "nl2intent_by_tag_v2": {
            "intent_list": [
                "与召回相关",
                "问码值",
                "需同环比",
            ]
        },
        "period_on_period": {
            "a": ["lookup_data('23年2月各分行的经营利润')", None],
            "b": ["lookup_data('23年1月各分行的经营利润')", None],
            "c": ["table_tools('请根据提供的表格计算各分行23年2月的经营利润环比，并找出最高环比')", ["a", "b"]],
        },
        "table_tools": {
            "table1": [
                "select([{'name': '分行名称', 'expression_type': 'COLUMN', 'expression': '分行名称'}, {'name': '经营利润2', 'expression_type': 'COLUMN', 'expression': '经营利润'}], group_by=['分行名称'])",
                ["a"],
            ],
            "table2": [
                "select([{'name': '分行名称', 'expression_type': 'COLUMN', 'expression': '分行名称'}, {'name': '经营利润1', 'expression_type': 'COLUMN', 'expression': '经营利润'}], group_by=['分行名称'])",
                ["b"],
            ],
            "table3": [
                "join('table1', 'table2', 'inner_join', '分行名称', '分行名称')",
                ["table1", "table2"],
            ],
            "table4": [
                "select([{'name': '分行名称', 'expression_type': 'COLUMN', 'expression': '分行名称'}, {'name': '环比增长', 'expression_type': 'CALCULATE', 'expression': '(经营利润2 - 经营利润1) / 经营利润1'}], group_by=['分行名称'])",
                ["table3"],
            ],
            "table5": [
                "select([{'name': '分行名称', 'expression_type': 'COLUMN', 'expression': '分行名称'}, {'name': '最高环比', 'expression_type': 'MAX', 'expression': '环比增长'}])",
                ["table4"],
            ],
        },
    }

    fake_passthrough_prompts_jan = {
        "nl2metric_group_bys": {"groupBys": ["bch_nme"], "notExistGroupBys": []},
        "nl2metric_time_query": {
            "timeStartFunction": {"year": 2023, "month": 1, "day": 1},
            "timeEndFunction": {"year": 2023, "month": 1, "day": 31},
            "timeGranularity": "total",
        },
        "nl2metric_metrics": {
            "metrics": ["demo_7000_i7000"],
            "notExistMetricNames": [],
        },
        "nl2metric_where": {
            "limit": 0,
            "where": "",
            "select": "bch_nme, demo_7000_i7000",
            "groupby": "bch_nme",
            "orderby": "",
            "where_time": "dt >= '2023-01-01' and dt <= '2023-01-31'",
        },
    }

    fake_passthrough_prompts_feb = {
        "nl2metric_group_bys": {"groupBys": ["bch_nme"], "notExistGroupBys": []},
        "nl2metric_time_query": {
            "timeStartFunction": {"year": 2023, "month": 2, "day": 1},
            "timeEndFunction": {"year": 2023, "month": 2, "day": 28},
            "timeGranularity": "total",
        },
        "nl2metric_metrics": {
            "metrics": ["demo_7000_i7000"],
            "notExistMetricNames": [],
        },
        "nl2metric_where": {
            "limit": 0,
            "where": "",
            "select": "bch_nme, demo_7000_i7000",
            "groupby": "bch_nme",
            "orderby": "",
            "where_time": "dt >= '2023-01-01' and dt <= '2023-01-31'",
        },
    }

    q = "23年各分行谁的经营利润环比最高"
    req = ParamsExtractRequest(
        messages=[{"role": "user", "content": q}],
        model_id="CYLuY5VklxU2UTK2",
        model_type="fake_passthrough",
        additional_info={
            "fake_passthrough_prompts": {
                q: fake_passthrough_prompts_q,
                "23年1月各分行的经营利润": fake_passthrough_prompts_jan,
                "23年2月各分行的经营利润": fake_passthrough_prompts_feb,
            },
            "fake_passthrough_prompts_mode": "by_question",
        },
    )
    resp = params_extract_handler(req, "test_agent")
    print(json.dumps(resp, indent=2, ensure_ascii=False))
    assert resp["code"] == 0
    assert resp["data"]["type"] == "period_on_period"


if __name__ == "__main__":
    test_agent()
