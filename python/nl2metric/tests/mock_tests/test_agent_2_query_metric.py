from utils import init_mock_env

init_mock_env()

import json

from http_router.api.params_extract_handler import (
    params_extract_handler,
    ParamsExtractRequest,
)


# ATTATION: python3 main_shared_service.py before this ut
# ATTATION: python3 main_embedding_service.py before this ut
def test_agent_2_query_metric():
    fake_passthrough_prompts = {
        "nl2metric_group_bys": [{}, {"groupBys": ["bch_nme"], "notExistGroupBys": []}],
        "nl2metric_time_query": [
            {},
            {
                "timeStartFunction": {"year": 2023, "month": 1, "day": 1},
                "timeEndFunction": {"year": 2023, "month": 12, "day": 31},
                "timeGranularity": "total",
            },
        ],
        "nl2metric_order_bys": [{"orderBys": ["demo_7000_i4000 asc"]}],
        "nl2metric_metrics": [
            {
                "metrics": ["demo_7000_i4000"],
                "notExistMetricNames": [],
            }
        ],
        "nl2metric_where": [
            {
                "where": "",
                "where_time": "dt >= '2023-01-01'",
                "select": "bch_nme, demo_7000_i4000",
                "groupby": "bch_nme",
                "orderby": "demo_7000_i4000 asc",
                "limit": 0,
            }
        ],
        "nl2intent_by_tag_v2": [
            {
                "intent_list": [
                    "与召回相关",
                    "问码值",
                    "需同环比",
                ]
            }
        ],
        "period_on_period": [
            {
                "a": ["lookup_data('23年各分行的营业利润，按照分行名称分组，按照营业利润生序排列')", None],
            }
        ],
    }
    q = "23年各分行的营业利润，按照分行名称分组，按照营业利润生序排列"
    req = ParamsExtractRequest(
        messages=[{"role": "user", "content": q}],
        model_id="CYLuY5VklxU2UTK2",
        model_type="fake_passthrough",
        additional_info={
            "fake_passthrough_prompts": fake_passthrough_prompts,
            "fake_passthrough_prompts_mode": "list",
        },
    )
    resp = params_extract_handler(req, None)
    print(json.dumps(resp, indent=2, ensure_ascii=False))

    # this logic no longer exist in multi agent
    assert resp["code"] == 0
    assert resp["data"]["type"] == "period_on_period"
    # assert resp == {
    #     "data": {
    #         "type": "query-metric",
    #         "query_metric": {
    #             "groupBys": ["bch_nme"],
    #             "metricNames": ["demo_7000_i4000", "demo_7000_i7000"],
    #             "isMetricNamesExactMatch": False,
    #             "orderBys": ["demo_7000_i4000 asc"],
    #             "where": "",
    #             "where_json": {},
    #             "isWhereExactMatch": None,
    #             "limit": None,
    #             "timeQueryParams": {
    #                 "timeStartFunction": {
    #                     "type": "specificDate",
    #                     "year": 2023,
    #                     "month": 1,
    #                     "day": 1,
    #                     "quarter": None,
    #                 },
    #                 "timeEndFunction": {
    #                     "type": "specificDate",
    #                     "year": 2023,
    #                     "month": 12,
    #                     "day": 31,
    #                     "quarter": None,
    #                 },
    #                 "timeGranularity": "total",
    #                 "timeDimensionName": None,
    #             },
    #             "notExistMetricNames": None,
    #             "notExistGroupBys": None,
    #             "notExistOrderBys": None,
    #         },
    #         "query_metric_list": [],
    #         "attribution_analysis": None,
    #         "chit_chat": None,
    #         "meta_result": None,
    #         "calculator": None,
    #         "extra_info": {
    #             "is_sub": False,
    #             "groupbys_with_level": [],
    #             "metric_scores": {"demo_7000_i7000": 0.75, "demo_7000_i4000": 1.0},
    #         },
    #     },
    #     "code": 0,
    # }


if __name__ == "__main__":
    test_agent_2_query_metric()
