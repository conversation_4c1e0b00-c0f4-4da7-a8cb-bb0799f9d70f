/**
 * 一键部署宝武BAK环境
 */
import dayjs from 'dayjs'
import { execSync } from 'child_process'

const cwd = process.cwd()
const now = dayjs()
const run = (c) => {
  console.log(`\n\n==> ${c}`)
  execSync(c, { stdio: 'inherit' })
}

run('rm -rf dist-package-*')
run(`npm run tar-all`)

const pkgName = execSync(`ls ${cwd}`)
  .toString()
  .split('\n')
  .filter((v) => v.startsWith('dist-package'))
  .sort((a, b) => a.localeCompare(b))
  .at(-1)

run(`sshpass -p '!QAZxsw2' scp ${pkgName} root@***********:/dipeak2`)

const cmds = [
  `cd /dipeak2`,
  `sshpass -p 'i4J7e7QJFp' scp ${pkgName} baoadmin@***********:/dipeak`,
  `./deploy-baowu-admin.sh ${pkgName}`,
]
console.log('===[SERVER CMD]===')
console.log(cmds.join('\n'))

run(`sshpass -p '!QAZxsw2' ssh -T root@*********** "${cmds.join('; ')}"`)
