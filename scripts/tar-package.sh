#!/bin/bash
set -e

# 获取 Git 信息
if git rev-parse --is-inside-work-tree >/dev/null 2>&1; then
  username=$(git log -1 --pretty=format:"%an")
  branchName=$(git rev-parse --abbrev-ref HEAD)
  commitId=$(git rev-parse --short HEAD)
  commitTime=$(git log -1 --pretty=format:"%cd" --date=iso)
  currentTime=$(date +"%Y-%m-%d %H:%M:%S")
  buildAuthor=$(git config user.email)

  echo "Last commit time: ${commitTime}, branch name: ${branchName}, commit id: ${commitId}, author: ${username}, current time: ${currentTime}"
  echo "Project name: ASKBI \nBuild package author: ${buildAuthor}\nLast commit time: ${commitTime}\nBranch name: ${branchName}\nCommit id: ${commitId}\nAuthor: ${username}\nCurrent time: ${currentTime}" > version.txt
else
  echo "非 git 项目，获取不到相关信息"
  echo "非 git 项目，获取不到相关信息" > version.txt
fi

tar_name="dist-package-$(date +"%Y-%m-%d-%H-%M-%S").tar.gz"

# 判断环境变量 ONLY_TAR_PYTHON 是否为 true
if [ "$ONLY_TAR_PYTHON" == "true" ]; then
  echo "打包 python/nl2metric 文件夹..."
  tar --exclude='./python/nl2metric/cached_server' \
      --exclude='./python/nl2metric/__pycache__' \
      --exclude='./python/nl2metric/.pytest_cache' \
      --exclude='./python/nl2metric/logs' \
      --exclude='./python/nl2metric/.env' \
      -czf ./$tar_name \
      ./python/nl2metric \
      version.txt
else
  echo "打包 dist-client、dist-server、python/nl2metric 文件夹..."
  tar --exclude='./python/nl2metric/cached_server' \
      --exclude='./python/nl2metric/__pycache__' \
      --exclude='./python/nl2metric/.pytest_cache' \
      --exclude='./python/nl2metric/logs' \
      --exclude='./python/nl2metric/.env' \
      -czf ./$tar_name \
      ./dist-client \
      ./dist-server \
      ./prisma \
      ./python/nl2metric \
      version.txt
fi

rm version.txt
echo $tar_name
echo "Succeeded. Decompression command: tar -xzf $tar_name -C /path/to/folder"